<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'infobip\\api\\model\\sms\\mt\\send\\textual\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual'),
    'infobip\\api\\model\\sms\\mt\\send\\binary\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary'),
    'infobip\\api\\model\\sms\\mt\\send\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send'),
    'infobip\\api\\model\\sms\\mt\\reports\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports'),
    'infobip\\api\\model\\sms\\mt\\logs\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs'),
    'infobip\\api\\model\\sms\\mt\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt'),
    'infobip\\api\\model\\sms\\mo\\reports\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports'),
    'infobip\\api\\model\\sms\\mo\\logs\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs'),
    'infobip\\api\\model\\sms\\mo\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo'),
    'infobip\\api\\model\\sms\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms'),
    'infobip\\api\\model\\nc\\query\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/query'),
    'infobip\\api\\model\\nc\\notify\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify'),
    'infobip\\api\\model\\nc\\logs\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs'),
    'infobip\\api\\model\\nc\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc'),
    'infobip\\api\\model\\account\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/account'),
    'infobip\\api\\model\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/model'),
    'infobip\\api\\configuration\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/configuration'),
    'infobip\\api\\client\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api/client'),
    'infobip\\api\\' => array($vendorDir . '/infobip/infobip-api-php-client/infobip/api'),
    'Wikimedia\\Composer\\Merge\\V2\\' => array($vendorDir . '/wikimedia/composer-merge-plugin/src'),
    'WeDevs\\Reimbursement\\' => array($baseDir . '/modules/hrm/reimbursement/includes'),
    'WeDevs\\Recruitment\\' => array($baseDir . '/modules/hrm/recruitment/includes'),
    'WeDevs\\Payroll\\' => array($baseDir . '/modules/hrm/payroll/includes'),
    'WeDevs\\PaymentGateway\\' => array($baseDir . '/modules/accounting/payment-gateway/includes'),
    'WeDevs\\HrTraining\\' => array($baseDir . '/modules/hrm/hr-training/includes'),
    'WeDevs\\HrFrontend\\' => array($baseDir . '/modules/pro/hr-frontend/includes'),
    'WeDevs\\HelpScout\\' => array($baseDir . '/modules/pro/help-scout/includes'),
    'WeDevs\\Erp_Inventory\\' => array($baseDir . '/modules/accounting/inventory/includes'),
    'WeDevs\\WSL_ERP\\PRO\\' => array($baseDir . '/modules/pro'),
    'WeDevs\\WSL_ERP\\HRM\\' => array($baseDir . '/modules/hrm'),
    'WeDevs\\WSL_ERP\\CRM\\' => array($baseDir . '/modules/crm'),
    'WeDevs\\WSL_ERP\\Accounting\\' => array($baseDir . '/modules/accounting'),
    'WeDevs\\WSL_ERP\\' => array($baseDir . '/includes'),
    'WeDevs\\ERP\\Zendesk\\' => array($baseDir . '/modules/pro/zendesk/includes'),
    'WeDevs\\ERP\\Workflow\\' => array($baseDir . '/modules/hrm/workflow/includes'),
    'WeDevs\\ERP\\Salesforce\\' => array($baseDir . '/modules/pro/salesforce/includes'),
    'WeDevs\\ERP\\SMS\\' => array($baseDir . '/modules/hrm/sms-notification/includes'),
    'WeDevs\\ERP\\Mailchimp\\' => array($baseDir . '/modules/pro/mailchimp/includes'),
    'WeDevs\\ERP\\Hubspot\\' => array($baseDir . '/modules/pro/hubspot/includes'),
    'WeDevs\\ERP\\Accounting\\Reimbursement\\' => array($baseDir . '/modules/hrm/reimbursement/deprecated/includes'),
    'WeDevs\\DocumentManager\\' => array($baseDir . '/modules/hrm/document-manager/includes'),
    'WeDevs\\Deals\\' => array($baseDir . '/modules/crm/deals/includes'),
    'WeDevs\\CustomFieldBuilder\\' => array($baseDir . '/modules/hrm/custom-field-builder/includes'),
    'WeDevs\\AwesomeSupport\\' => array($baseDir . '/modules/pro/awesome-support/includes'),
    'WeDevs\\Attendance\\' => array($baseDir . '/modules/hrm/attendance/includes'),
    'WeDevs\\AssetManagement\\' => array($baseDir . '/modules/hrm/asset-management/includes'),
    'WeDevs\\AdvancedLeave\\' => array($baseDir . '/modules/pro/advanced-leave/includes'),
    'Twilio\\' => array($vendorDir . '/twilio/sdk/src/Twilio'),
    'Stripe\\' => array($vendorDir . '/stripe/stripe-php/lib'),
    'Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\' => array($vendorDir . '/dealerdirect/phpcodesniffer-composer-installer/src'),
    'Clickatell\\' => array($vendorDir . '/arcturial/clickatell/src', $vendorDir . '/arcturial/clickatell/test'),
);
