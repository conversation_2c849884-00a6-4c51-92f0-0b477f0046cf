<?php
return [
	__( 'Purchase Return', 'erp-pro' ),
	__( 'Back', 'erp-pro' ),
	__( 'Purchase Return Invoice', 'erp-pro' ),
	__( 'Bill to', 'erp-pro' ),
	__( 'Return Date', 'erp-pro' ),
	__( 'Voucher No', 'erp-pro' ),
	__( 'Transaction Date', 'erp-pro' ),
	__( 'Due Date', 'erp-pro' ),
	__( 'Created At', 'erp-pro' ),
	__( 'Amount Due', 'erp-pro' ),
	__( 'Select', 'erp-pro' ),
	__( 'Sl', 'erp-pro' ),
	__( 'Product', 'erp-pro' ),
	__( 'Qty', 'erp-pro' ),
	__( 'Unit Discount', 'erp-pro' ),
	__( 'Unit Price', 'erp-pro' ),
	__( 'Amount', 'erp-pro' ),
	__( 'Action', 'erp-pro' ),
	__( 'Returned', 'erp-pro' ),
	__( 'Subtotal', 'erp-pro' ),
	__( 'Discount', 'erp-pro' ),
	__( 'VAT', 'erp-pro' ),
	__( 'Total', 'erp-pro' ),
	__( 'Return Reason', 'erp-pro' ),
	__( 'Save', 'erp-pro' ),
	__( 'No voucher found!', 'erp-pro' ),
	__( 'Please Select minimum one item', 'erp-pro' ),
	__( 'Please choose a Return date', 'erp-pro' ),
	__( 'Are you sure?', 'erp-pro' ),
	__( 'You won\'t be able to revert this!', 'erp-pro' ),
	__( 'Yes, Confirm!', 'erp-pro' ),
	__( 'Invoice Saved successfully', 'erp-pro' ),
	__( 'Purchase Returns', 'erp-pro' ),
	__( 'Purchase Voucher No.', 'erp-pro' ),
	__( 'Search', 'erp-pro' ),
	__( 'Trash', 'erp' ),
	__( 'Purchase Return', 'erp' ),
	__( 'Voucher No.', 'erp' ),
	__( 'Type', 'erp' ),
	__( 'Customer', 'erp' ),
	__( 'Trn Date', 'erp' ),
	__( 'Total', 'erp' ),
	__( 'Status', 'erp' ),
	__( 'Purchase', 'erp-pro' ),
	__( 'Payment', 'erp-pro' ),
	__( 'Receive', 'erp-pro' ),
	__( 'Create Return Invoice', 'erp-pro' ),
	__( 'No actions found', 'erp' ),
	__( 'Enter a voucher number', 'erp-pro' ),
	__( 'Are you sure to delete?', 'erp-pro' ),
	__( 'Are you sure to void the transaction?', 'erp-pro' ),
	__( 'Transaction has been void!', 'erp-pro' ),
	__( 'Purchase Order', 'erp-pro' ),
	__( 'Cancel', 'erp-pro' ),
	__( 'Purchase Return Invoice', 'erp' ),
	__( 'Print', 'erp' ),
	__( 'More Action', 'erp' ),
	__( 'Export as PDF', 'erp' ),
	__( 'Send Mail', 'erp' ),
	__( 'Voucher No', 'erp' ),
	__( 'Purchase Voucher No', 'erp' ),
	__( 'Reference No', 'erp' ),
	__( 'Transaction Date', 'erp' ),
	__( 'Due Date', 'erp' ),
	__( 'Created At', 'erp' ),
	__( 'Sl.', 'erp' ),
	__( 'Item name', 'erp' ),
	__( 'Qty', 'erp' ),
	__( 'Unit Price', 'erp' ),
	__( 'Amount', 'erp' ),
	__( 'Subtotal', 'erp' ),
	__( 'VAT', 'erp' ),
	__( 'Discount', 'erp' ),
	__( 'Attachments', 'erp' ),
	__( 'Purchase Return Report', 'erp-pro' ),
	__( 'Filter', 'erp-pro' ),
	__( 'Print', 'erp-pro' ),
	__( 'to', 'erp-pro' ),
	__( 'Date', 'erp-pro' ),
	__( 'Vendor', 'erp-pro' ),
	__( 'Quantity', 'erp-pro' ),
	__( 'It generates report based on the purchases that have been returned for the current financial cycle/year', 'erp-pro' ),
	__( 'View Report', 'erp-pro' ),
	__( 'Agency Name', 'erp-pro' ),
	__( 'Currency', 'erp-pro' ),
	__( 'Tax Amount', 'erp-pro' ),
	__( 'Category Name', 'erp-pro' ),
	__( 'Purchase VAT Reports', 'erp-pro' ),
	__( 'Agency Based', 'erp-pro' ),
	__( 'It generates vat on purchase report based on agencies', 'erp-pro' ),
	__( 'Transaction Based', 'erp-pro' ),
	__( 'It generates vat on purchase report based on transactions', 'erp-pro' ),
	__( 'Vendor Based', 'erp-pro' ),
	__( 'It generates vat on purchase report based on vendors', 'erp-pro' ),
	__( 'Category Based', 'erp-pro' ),
	__( 'It generates vat on purchase report based on categories', 'erp-pro' ),
	__( 'Purchase VAT', 'erp-pro' ),
	__( 'It generates report based on the VAT on purchases charged or paid for the current financial cycle/year', 'erp-pro' ),
	__( 'Tax Amount', 'erp' ),
	__( 'Vendor Name', 'erp-pro' ),
	__( 'Sales Return Report', 'erp-pro' ),
	__( 'Customer', 'erp-pro' ),
	__( 'Tax', 'erp-pro' ),
	__( 'Sales Return', 'erp-pro' ),
	__( 'It generates report based on the sales that have been returned for the current financial cycle/year', 'erp-pro' ),
	__( 'Sales Return Invoice', 'erp-pro' ),
	__( 'Sales Returns', 'erp-pro' ),
	__( 'Invoice Voucher No.', 'erp-pro' ),
	__( 'Sales Return Invoice', 'erp' ),
	__( 'Inventory Products', 'erp-pro' ),
	__( 'Product Name', 'erp' ),
	__( 'Sale Price', 'erp' ),
	__( 'Cost Price', 'erp' ),
	__( 'Stock', 'erp' ),
	__( 'Product Category', 'erp' ),
	__( 'Tax Category', 'erp' ),
	__( 'Vendor', 'erp' ),
	__( 'Inventory Report', 'erp-pro' ),
	__( 'Inventory Report Name', 'erp-pro' ),
	__( 'For the period of ( Transaction date )', 'erp-pro' ),
	__( 'Inventory Item List', 'erp-pro' ),
	__( 'Inventory Item Summary', 'erp-pro' ),
	__( 'Item Name', 'erp-pro' ),
	__( 'Unit Cost price', 'erp-pro' ),
	__( 'Unit Sale Price', 'erp-pro' ),
	__( 'Quantity on Hand', 'erp-pro' ),
	__( 'Purchases', 'erp-pro' ),
	__( 'COGS', 'erp-pro' ),
	__( 'Sales', 'erp-pro' ),
	__( 'Inventory Sales Report', 'erp-pro' ),
	__( 'Date', 'erp' ),
	__( 'Product', 'erp' ),
	__( 'Quantity', 'erp' ),
	__( 'Tax Rate', 'erp' ),
	__( 'Inventory Purchase Report', 'erp-pro' ),
	_n( ' + valueExpression + ' ),
	__( 'Product purchase and sales history will be shown here with date between facility.', 'erp-pro' ),
	__( 'Product Purchase', 'erp-pro' ),
	__( 'Product Purchases history will be shown here with date between facility', 'erp-pro' ),
	__( 'Product Sales', 'erp-pro' ),
	__( 'Product Sales history will be shown here with date between facility', 'erp-pro' ),
	__( 'This process synchronizes your existing WooCommerce orders with ERP WooCommerce related data.', 'erp-pro' ),
	__( 'Don\'t worry, any existing orders will not be deleted.', 'erp-pro' ),
	__( 'But please note that Orders by Guests will not be synchronized.', 'erp-pro' ),
	__( 'Don\'t close this window, until the process has been completed.', 'erp-pro' ),
	__( 'Synchronize Orders', 'erp-pro' ),
	__( 'Completed', 'erp-pro' ),
	__( 'This process synchronizes your existing WooCommerce Products with Accounting Products.', 'erp-pro' ),
	__( 'Don\'t worry, any existing products will not be deleted.', 'erp-pro' ),
	__( 'Don\'t close this window, until the process has been completed', 'erp-pro' ),
	__( 'Default product type', 'erp-pro' ),
	__( 'Default product owner', 'erp-pro' ),
	__( 'Default product category', 'erp-pro' ),
	__( 'Default tax category', 'erp-pro' ),
	__( 'Update Existing Products?', 'erp-pro' ),
	__( 'Synchronize Products', 'erp-pro' ),
	__( 'Please choose into which pipeline these deals should be moved after deletion.', 'erp-pro' ),
	__( 'Please choose into which stage these deals should be moved after deletion.', 'erp-pro' ),
	__( 'Generate Shift', 'erp-pro' ),
	__( 'Generated Users of this Shift', 'erp-pro' ),
	__( 'Start Date', 'erp-pro' ),
	__( 'End Date', 'erp-pro' ),
	__( 'Generate this Shift for', 'erp-pro' ),
	__( 'Overwrite Employees', 'erp-pro' ),
	__( 'Overwrite previously assigned employees to this or any other shifts.', 'erp-pro' ),
	__( 'Shift name:', 'erp-pro' ),
	__( 'Holidays:', 'erp-pro' ),
	__( 'Time:', 'erp-pro' ),
	__( 'All Employees of this shift', 'erp-pro' ),
	__( 'Selected Employees of this shift', 'erp-pro' ),
	__( 'Move to Trash', 'erp-pro' ),
	__( 'Name', 'erp-pro' ),
	__( 'Designation', 'erp-pro' ),
	__( 'Department', 'erp-pro' ),
	__( 'Shift Start', 'erp-pro' ),
	__( 'Shift End', 'erp-pro' ),
	__( 'Employees', 'erp-pro' ),
	__( 'Warning', 'erp-pro' ),
	__( 'Start date should be ', 'erp-pro' ),
	__( ' OR after any date', 'erp-pro' ),
	__( 'End date should be greater OR equal than start date', 'erp-pro' ),
	__( 'Are you sure to overwrite employees?', 'erp-pro' ),
	__( 'Confirm', 'erp-pro' ),
	__( 'Success', 'erp-pro' ),
	__( 'Shift generate successful', 'erp-pro' ),
	__( 'Attendance Record', 'erp-pro' ),
	__( 'Edit', 'erp-pro' ),
	__( 'Emp. ID', 'erp-pro' ),
	__( 'Employee Name', 'erp-pro' ),
	__( 'Shift', 'erp-pro' ),
	__( 'Status', 'erp-pro' ),
	__( 'Checkin', 'erp-pro' ),
	__( 'Checkout', 'erp-pro' ),
	__( 'Worktime', 'erp-pro' ),
	__( 'Attendance', 'erp-attendance' ),
	__( 'Add New', 'erp-attendance' ),
	__( 'Go to', 'erp-attendance' ),
	__( 'Filter', 'erp-attendance' ),
	__( 'Date', 'erp-attendance' ),
	__( 'Attended', 'erp-attendance' ),
	__( 'Absent', 'erp-attendance' ),
	__( 'Presence', 'erp-attendance' ),
	__( 'Actions', 'erp-attendance' ),
	__( 'Are you sure to delete?', 'erp-attendance' ),
	__( 'Save grace time', 'erp-pro' ),
	__( 'Data migration from previous version', 'erp-pro' ),
	__( 'Migration', 'erp-pro' ),
	__( 'Note : Please do not close the browser OR tab during data migration.', 'erp-pro' ),
	__( 'Export Attendance', 'erp-pro' ),
	__( 'You can export data from employee based report.', 'erp-pro' ),
	__( 'Import Attendance', 'erp-pro' ),
	__( 'Download sample file', 'erp-pro' ),
	__( 'Select a shift', 'erp-pro' ),
	__( 'Data import faild. Please provide a csv file with correct format', 'erp-pro' ),
	__( 'Edit Attendance', 'erp-pro' ),
	__( 'New Attendance', 'erp-pro' ),
	__( 'Date:', 'erp-pro' ),
	__( 'Set all present', 'erp-pro' ),
	__( 'Set all absent', 'erp-pro' ),
	__( 'Present', 'erp-pro' ),
	__( 'Absent', 'erp-pro' ),
	__( 'More Details', 'erp-pro' ),
	__( 'Employee ID', 'erp-pro' ),
	__( 'Modify Details', 'erp-pro' ),
	__( 'Shift Name', 'erp-pro' ),
	__( 'Start Time', 'erp-pro' ),
	__( 'End Time', 'erp-pro' ),
	__( 'Holidays', 'erp-pro' ),
	__( 'Update', 'erp-pro' ),
	__( 'Sunday', 'erp-pro' ),
	__( 'Monday', 'erp-pro' ),
	__( 'Tuesday', 'erp-pro' ),
	__( 'Wednesday', 'erp-pro' ),
	__( 'Thursday', 'erp-pro' ),
	__( 'Friday', 'erp-pro' ),
	__( 'Saturday', 'erp-pro' ),
	__( 'Shift create successful', 'erp-pro' ),
	__( 'Shift name is required.', 'erp-pro' ),
	__( 'Start time is required.', 'erp-pro' ),
	__( 'End time is required.', 'erp-pro' ),
	__( 'Shifts', 'erp-pro' ),
	__( 'Add New Shift', 'erp-pro' ),
	__( 'No Holiday', 'erp-pro' ),
	__( 'Duration', 'erp-pro' ),
	__( 'Actions', 'erp-pro' ),
	__( 'Submit', 'erp-pro' ),
	__( 'Add Pay Item', 'erp' ),
	__( 'Pay Type', 'erp' ),
	__( 'Pay Item', 'erp' ),
	__( 'Amount Type', 'erp' ),
	__( 'Action', 'erp' ),
	__( 'Edit Pay Item', 'erp' ),
	__( 'Are you sure to delete the pay item ?', 'erp' ),
	__( 'Save Changes', 'erp-pro' ),
	__( 'New Reimbursement', 'erp-pro' ),
	__( 'Enter Amount', 'erp-pro' ),
	__( 'Voucher Type', 'erp-pro' ),
	__( 'Enter Voucher Type', 'erp-pro' ),
	__( 'Payment Method', 'erp-pro' ),
	__( 'Deposit To', 'erp-pro' ),
	__( 'Payment Date', 'erp-pro' ),
	__( 'Particulars', 'erp-pro' ),
	__( 'People Name', 'erp-pro' ),
	__( 'Reimbursement Report', 'erp-pro' ),
	__( 'Trns Date', 'erp-pro' ),
	__( 'Trns No', 'erp-pro' ),
	__( 'Debit', 'erp-pro' ),
	__( 'Credit', 'erp-pro' ),
	__( 'Balance', 'erp-pro' ),
	__( 'People Transaction', 'erp-pro' ),
	__( 'More Action', 'erp-pro' ),
	__( 'Send Mail', 'erp-pro' ),
	__( 'Attachments', 'erp-pro' ),
	__( 'Reimbursements', 'erp-pro' ),
	__( 'List of Requests', 'erp-pro' ),
	__( 'Debit', 'erp' ),
	__( 'This is a report provides you the transactions of a particular people at a specified date', 'erp-pro' ),
	__( 'Reimbursement', 'erp-pro' ),
	__( 'Pay Now', 'erp-pro' ),
	__( 'Request', 'erp-pro' ),
	__( 'Request No', 'erp-pro' ),
	__( 'Create Receipt', 'erp-pro' ),
	__( 'Reference', 'erp-pro' ),
	__( 'Add Line', 'erp-pro' ),
	__( 'Total Amount', 'erp-pro' ),
	__( 'Attachment', 'erp-pro' ),
	__( 'Outstanding', 'erp' ),
	__( 'Requests', 'erp-pro' ),
	__( 'New Receipt', 'erp-pro' ),
	__( 'Back to Reimbursements', 'erp-pro' ),
	__( 'Could not find the account', 'erp-pro' ),
	__( 'Something went wrong. Please try again.', 'erp-pro' ),
	__( 'Add New Announcement', 'erp-pro' ),
	__( 'Enter Title Here', 'erp-pro' ),
	__( 'Announcement Settings', 'erp-pro' ),
	__( 'Send Announcement to', 'erp-pro' ),
	__( 'All Employees', 'erp-pro' ),
	__( 'Notify All Employees', 'erp-pro' ),
	__( 'Publish', 'erp-pro' ),
	__( 'Employee is required to create an announcement!', 'erp-pro' ),
	__( 'Remove Announcements', 'erp-pro' ),
	__( 'Edit Announcement', 'erp-pro' ),
	__( 'Title', 'erp-pro' ),
	__( 'Description', 'erp-pro' ),
	__( 'No announcement founds', 'erp-pro' ),
	__( 'Do you really want to remove selected announcements?', 'erp-pro' ),
	__( 'Do you really want to remove this announcement?', 'erp-pro' ),
	__( 'Employment Type', 'erp-pro' ),
	__( 'Joined', 'erp-pro' ),
	__( 'No employee founds', 'erp-pro' ),
	__( 'You do not have permission to delete user', 'erp-pro' ),
	__( 'Active', 'erp-pro' ),
	__( 'Terminated', 'erp-pro' ),
	__( 'Deceased', 'erp-pro' ),
	__( 'Resigned', 'erp-pro' ),
	__( 'Trash', 'erp-pro' ),
	__( 'All', 'erp-pro' ),
	__( 'Choose Employee Type', 'erp-pro' ),
	__( 'Select Designation', 'erp-pro' ),
	__( 'Select Department', 'erp-pro' ),
	__( 'Select Location', 'erp-pro' ),
	__( 'Full Time', 'erp-pro' ),
	__( 'Part Time', 'erp-pro' ),
	__( 'On Contract', 'erp-pro' ),
	__( 'Temporary', 'erp-pro' ),
	__( 'Trainee', 'erp-pro' ),
	__( 'Nothing found.', 'erp-pro' ),
	__( 'Attendance Service', 'erp-pro' ),
	__( 'In-time', 'erp-pro' ),
	__( 'Out-time', 'erp-pro' ),
	__( 'Total time', 'erp-pro' ),
	__( 'Hours', 'erp-pro' ),
	__( 'Attendance Status', 'erp-pro' ),
	__( 'Filter by', 'erp-pro' ),
	__( 'Today', 'erp-pro' ),
	__( 'Yesterday', 'erp-pro' ),
	__( 'This Month', 'erp-pro' ),
	__( 'Last Month', 'erp-pro' ),
	__( 'Last Quarter', 'erp-pro' ),
	__( 'Last Year', 'erp-pro' ),
	__( 'Late', 'erp-pro' ),
	__( 'Absents', 'erp-pro' ),
	__( ' Checkin Succesful!', 'erp-pro' ),
	__( ' Checkout Successful!', 'erp-pro' ),
	__( 'Birthday Buddies', 'erp-pro' ),
	__( 'Upcoming Birthdays', 'erp-pro' ),
	__( 'No one has birthdays this week!', 'erp-pro' ),
	__( 'Load', 'erp-pro' ),
	__( 'Few', 'erp-pro' ),
	__( 'My Leave Calendar', 'erp-pro' ),
	__( 'Take a Leave', 'erp-pro' ),
	__( 'Leave Request', 'erp-pro' ),
	__( 'From', 'erp-pro' ),
	__( 'Select date', 'erp-pro' ),
	__( 'Reason', 'erp-pro' ),
	__( 'Type here', 'erp-pro' ),
	__( 'Document', 'erp-pro' ),
	__( 'Send Leave Request', 'erp-pro' ),
	__( 'You have %1$d available leaves under %2$s policy.', 'erp-pro' ),
	__( 'Leave request sent!', 'erp-pro' ),
	__( 'View', 'erp-pro' ),
	__( 'Reports', 'erp-pro' ),
	__( 'Age', 'erp-pro' ),
	__( 'Profile', 'erp-pro' ),
	__( 'Head', 'erp-pro' ),
	__( 'Count', 'erp-pro' ),
	__( 'Gender', 'erp-pro' ),
	__( 'Salary', 'erp-pro' ),
	__( 'History', 'erp-pro' ),
	__( 'Years', 'erp-pro' ),
	__( 'Of Service', 'erp-pro' ),
	__( 'Who is Out', 'erp-pro' ),
	__( 'Who Is Out', 'erp-pro' ),
	__( 'No one is on vacation!', 'erp-pro' ),
	__( 'Previous Page', 'erp-pro' ),
	__( 'Next Page', 'erp-pro' ),
	__( 'Total Employees:', 'erp-pro' ),
	__( 'Age Profile', 'erp-pro' ),
	__( 'Head Count', 'erp-pro' ),
	__( 'Gender Profile', 'erp-pro' ),
	__( 'Year Of Service', 'erp-pro' ),
	__( 'Salary History', 'erp-pro' ),
	__( 'HRM', 'erp-pro' ),
	__( 'Overview', 'erp-pro' ),
	__( 'Departments', 'erp-pro' ),
	__( 'Designations', 'erp-pro' ),
	__( 'Announcements', 'erp-pro' ),
	__( 'Reporting', 'erp-pro' ),
	__( 'Log out', 'erp-pro' ),
	__( 'Basic Info', 'erp-pro' ),
	__( 'First Name', 'erp-pro' ),
	__( 'Last Name', 'erp-pro' ),
	__( 'Email', 'erp-pro' ),
	__( 'Dependents', 'erp-pro' ),
	__( 'Relationship', 'erp-pro' ),
	__( 'Date of Birth', 'erp-pro' ),
	__( 'Add Dependent', 'erp-pro' ),
	__( 'Dependent updated!', 'erp-pro' ),
	__( 'Do you really want to remove this dependent?', 'erp-pro' ),
	__( 'Dependent Removed!', 'erp-pro' ),
	__( 'Dependent added!', 'erp-pro' ),
	__( 'Education', 'erp-pro' ),
	__( 'School Name', 'erp-pro' ),
	__( 'Degree', 'erp-pro' ),
	__( 'Field(s) of Study', 'erp-pro' ),
	__( 'Year of Completion', 'erp-pro' ),
	__( 'Additional Notes', 'erp-pro' ),
	__( 'Interests', 'erp-pro' ),
	__( 'Add Education', 'erp-pro' ),
	__( 'Save Education', 'erp-pro' ),
	__( 'ABC School', 'erp-pro' ),
	__( 'Bachelor in Science', 'erp-pro' ),
	__( 'Field of Study', 'erp-pro' ),
	__( 'Physics', 'erp-pro' ),
	__( 'Notes', 'erp-pro' ),
	__( 'Additional notes', 'erp-pro' ),
	__( 'Education Updated!', 'erp-pro' ),
	__( 'Do you really want to remove this education?', 'erp-pro' ),
	__( 'Education Removed!', 'erp-pro' ),
	__( 'Education added!', 'erp-pro' ),
	__( 'Personal Details', 'erp-pro' ),
	__( 'Street 1', 'erp-pro' ),
	__( 'Street 2', 'erp-pro' ),
	__( 'Address', 'erp-pro' ),
	__( 'City', 'erp-pro' ),
	__( 'Country', 'erp-pro' ),
	__( 'State', 'erp-pro' ),
	__( 'Postal Code', 'erp-pro' ),
	__( 'Mobile', 'erp-pro' ),
	__( 'Phone', 'erp-pro' ),
	__( 'Other Email', 'erp-pro' ),
	__( 'Nationality', 'erp-pro' ),
	__( 'Marital Status', 'erp-pro' ),
	__( 'Driving License', 'erp-pro' ),
	__( 'Hobbies', 'erp-pro' ),
	__( 'Male', 'erp-pro' ),
	__( 'Female', 'erp-pro' ),
	__( 'Other', 'erp-pro' ),
	__( 'Single', 'erp-pro' ),
	__( 'Married', 'erp-pro' ),
	__( 'Widowed', 'erp-pro' ),
	__( 'Driving license', 'erp-pro' ),
	__( 'Work', 'erp-pro' ),
	__( 'Reporting To', 'erp-pro' ),
	__( 'Date of Hire', 'erp-pro' ),
	__( 'Source of Hire', 'erp-pro' ),
	__( 'Employee Status', 'erp-pro' ),
	__( 'Work Phone', 'erp-pro' ),
	__( 'Employee Type', 'erp-pro' ),
	__( 'Work Experience', 'erp-pro' ),
	__( 'Company', 'erp-pro' ),
	__( 'Job Title', 'erp-pro' ),
	__( 'To', 'erp-pro' ),
	__( 'Job Description', 'erp-pro' ),
	__( 'Add Experience', 'erp-pro' ),
	__( 'Save Experience', 'erp-pro' ),
	__( 'ABC Corporation', 'erp-pro' ),
	__( 'Project Manager', 'erp-pro' ),
	__( 'Details about the job', 'erp-pro' ),
	__( 'Do you really want to remove this experience?', 'erp-pro' ),
	__( 'Experience Removed!', 'erp-pro' ),
	__( 'Experience added!', 'erp-pro' ),
	__( 'Compensation', 'erp-pro' ),
	__( 'Pay Rate', 'erp-pro' ),
	__( 'Pay Type', 'erp-pro' ),
	__( 'Change Reason', 'erp-pro' ),
	__( 'Comment', 'erp-pro' ),
	__( 'Add Compensation', 'erp-pro' ),
	__( 'Save Compensation', 'erp-pro' ),
	__( 'No compensations found', 'erp-pro' ),
	__( 'Do you really want to remove this compensation?', 'erp-pro' ),
	__( 'Compensation Removed!', 'erp-pro' ),
	__( 'Compensation added!', 'erp-pro' ),
	__( 'Employment Status', 'erp-pro' ),
	__( 'Add Employment Status', 'erp-pro' ),
	__( 'Save Status', 'erp-pro' ),
	__( 'No employments found', 'erp-pro' ),
	__( 'Do you really want to remove this employment?' ),
	__( 'Status Removed Successfully!', 'erp-pro' ),
	__( 'Status added Successfully!', 'erp-pro' ),
	__( 'Job Information', 'erp-pro' ),
	__( 'Location', 'erp-pro' ),
	__( 'Reports To', 'erp-pro' ),
	__( 'Add Information', 'erp-pro' ),
	__( '-- select --', 'erp-pro' ),
	__( 'Do you really want to remove this information?', 'erp-pro' ),
	__( 'Information removed!', 'erp-pro' ),
	__( 'Information added!', 'erp-pro' ),
	__( 'Balances', 'erp-pro' ),
	__( 'Leave', 'erp-pro' ),
	__( 'Days', 'erp-pro' ),
	__( 'Spent', 'erp-pro' ),
	__( 'Scheduled', 'erp-pro' ),
	__( 'Available', 'erp-pro' ),
	__( 'Period', 'erp-pro' ),
	__( 'Policy', 'erp-pro' ),
	__( 'Nothing Found', 'erp-pro' ),
	__( 'Delete', 'erp-pro' ),
	__( 'No content found', 'erp-pro' ),
	__( 'Performance Comments', 'erp-pro' ),
	__( 'Reviewer', 'erp-pro' ),
	__( 'Comments', 'erp-pro' ),
	__( 'Add Performance Comments', 'erp-pro' ),
	__( 'Reference Date', 'erp-pro' ),
	__( 'No reviews found', 'erp-pro' ),
	__( 'Do you really want to remove this goals?', 'erp-pro' ),
	__( 'Performance Goals', 'erp-pro' ),
	__( 'Set Date', 'erp-pro' ),
	__( 'Completion Date', 'erp-pro' ),
	__( 'Goal Description', 'erp-pro' ),
	__( 'Employee Assessment', 'erp-pro' ),
	__( 'Supervisor', 'erp-pro' ),
	__( 'Supervisor Assessment', 'erp-pro' ),
	__( 'Add Performance Goals', 'erp-pro' ),
	__( 'Performance Reviews', 'erp-pro' ),
	__( 'Job Knowledge', 'erp-pro' ),
	__( 'Work Quality', 'erp-pro' ),
	__( 'Attendance / Punctuality', 'erp-pro' ),
	__( 'Communication / Listening', 'erp-pro' ),
	__( 'Dependability', 'erp-pro' ),
	__( 'Add Performance Reviews', 'erp-pro' ),
	__( 'Review Date', 'erp-pro' ),
	__( 'Attendance/Punctuality', 'erp-pro' ),
	__( 'Communication/Listening', 'erp-pro' ),
	__( 'Very Bad', 'erp-pro' ),
	__( 'Poor', 'erp-pro' ),
	__( 'Average', 'erp-pro' ),
	__( 'Good', 'erp-pro' ),
	__( 'Excellent', 'erp-pro' ),
	__( 'Permission Management', 'erp-pro' ),
	__( 'Latest Announcements', 'erp-pro' ),
	__( 'My Profile', 'erp-pro' ),
	__( 'Oops! No elements found.', 'erp-pro' ),
	__( 'Type to search', 'erp-pro' ),
	__( 'Add New Department', 'erp-pro' ),
	__( 'Remove Departments', 'erp-pro' ),
	__( 'Department Title', 'erp-pro' ),
	__( 'optional', 'erp-pro' ),
	__( 'Department Head', 'erp-pro' ),
	__( 'Parent Department', 'erp-pro' ),
	__( 'No. of Employees', 'erp-pro' ),
	__( 'A', 'erp-pro', 'erp-pro' ),
	__( 'N', 'erp-pro', 'erp-pro' ),
	__( 'S', 'erp-pro', 'erp-pro' ),
	__( 'M', 'erp-pro', 'erp-pro' ),
	__( 'No department founds', 'erp-pro' ),
	__( 'New Department', 'erp-pro' ),
	__( 'Create Department', 'erp-pro' ),
	__( 'Edit Department', 'erp-pro' ),
	__( 'Update Department', 'erp-pro' ),
	__( 'Do you really want to remove this department?', 'erp-pro' ),
	__( 'Do you really want to remove selected departments?', 'erp-pro' ),
	__( 'Department updated!', 'erp-pro' ),
	__( 'Department title is required!', 'erp-pro' ),
	__( 'Department added!', 'erp-pro' ),
	__( 'Add New Designation', 'erp-pro' ),
	__( 'Remove Designations', 'erp-pro' ),
	__( 'Designation Title', 'erp-pro' ),
	__( 'A', 'erp-pro' ),
	__( 'N', 'erp-pro' ),
	__( 'S', 'erp-pro' ),
	__( 'M', 'erp-pro' ),
	__( 'No designation founds' ),
	__( 'New Designation', 'erp-pro' ),
	__( 'Create Designation', 'erp-pro' ),
	__( 'Edit Designation', 'erp-pro' ),
	__( 'Update Designation', 'erp-pro' ),
	__( 'Do you really want to remove this designation?', 'erp-pro' ),
	__( 'Designation updated!', 'erp-pro' ),
	__( 'Designation title is required!', 'erp-pro' ),
	__( 'Designation added!', 'erp-pro' ),
	__( 'Basic info', 'erp-pro' ),
	__( 'first name', 'erp-pro' ),
	__( 'last name' ),
	__( '12332', 'erp-pro' ),
	__( 'Personal details', 'erp-pro' ),
	__( 'Type Here', 'erp-pro' ),
	__( 'Website', 'erp-pro' ),
	__( 'Address 1', 'erp-pro' ),
	__( 'Address 2', 'erp-pro' ),
	__( 'Province/State', 'erp-pro' ),
	__( 'Post/Zip Code', 'erp-pro' ),
	__( 'Biography', 'erp-pro' ),
	__( 'Send the employee an welcome email.', 'erp-pro' ),
	__( 'Send the login details as well. If login_info present.', 'erp-pro' ),
	__( 'Next', 'erp-pro' ),
	__( 'Create Employee', 'erp-pro' ),
	__( 'Employee created!' ),
	__( 'Add New Employee', 'erp-pro' ),
	__( 'HR Management', 'erp-pro' ),
	__( 'No report found', 'erp-pro' ),
	__( 'Under 18 year', 'erp-pro' ),
	__( '18 to 25 year', 'erp-pro' ),
	__( '26 to 35 year', 'erp-pro' ),
	__( '36 to 45 year', 'erp-pro' ),
	__( '46 to 55 year', 'erp-pro' ),
	__( '56 to 65 year', 'erp-pro' ),
	__( '65+ year', 'erp-pro' ),
	__( 'Overall Age Breakdown', 'erp-pro' ),
	__( 'Head Count By Month', 'erp-pro' ),
	__( 'Percentage', 'erp-pro' ),
	__( 'Unspecified', 'erp-pro' ),
	__( 'Employee Gender Ratio By Department', 'erp-pro' ),
	__( 'Others', 'erp-pro' ),
	__( 'Hire Date', 'erp-pro' ),
	__( 'Main Location', 'erp-pro' ),
	__( 'Employee', 'erp-pro' ),
	__( '21+', 'erp-pro' ),
	__( 'Shows age breakdown data in your company in different departments.', 'erp-pro' ),
	__( 'Displays actual number of individuals in your company in different departments.', 'erp-pro' ),
	__( 'Shows differentiation data by age in your company.', 'erp-pro' ),
	__( 'Shows longevity and experience report of the employees of your company.', 'erp-pro' ),
	__( 'Shows Salary History of the employees of your company.', 'erp-pro' ),
	__( 'Terminate', 'erp-pro' ),
	__( 'Terminate Employee', 'erp-pro' ),
	__( 'Terminate' ),
	__( 'Termination Date', 'erp-pro' ),
	__( 'Termination Type', 'erp-pro' ),
	__( '-- Select --', 'erp-pro' ),
	__( 'Voluntary', 'erp-pro' ),
	__( 'Involuntary', 'erp-pro' ),
	__( 'Termination Reason', 'erp-pro' ),
	__( 'Eligible for Rehire', 'erp-pro' ),
	__( 'General Info', 'erp-pro' ),
	__( 'Job', 'erp-pro' ),
	__( 'Performance', 'erp-pro' ),
	__( 'Permissions', 'erp-pro' ),
	__( 'Attendance', 'erp-pro' ),
	__( 'Better Employment Conditions', 'erp-pro' ),
	__( 'Career Prospect', 'erp-pro' ),
	__( 'Death', 'erp-pro' ),
	__( 'Desertion', 'erp-pro' ),
	__( 'Dismissed', 'erp-pro' ),
	__( 'Dissatisfaction with the job', 'erp-pro' ),
	__( 'Higher Pay', 'erp-pro' ),
	__( 'Other Employment', 'erp-pro' ),
	__( 'Personality Conflicts', 'erp-pro' ),
	__( 'Relocation', 'erp-pro' ),
	__( 'Retirement', 'erp-pro' ),
	__( 'Yes', 'erp-pro' ),
	__( 'No', 'erp-pro' ),
	__( 'Upon Review', 'erp-pro' ),
	__( 'Employee Terminated!', 'erp-pro' ),
	__( ' Integration', 'erp-pro' ),
	__( 'Automatic Sync Settings', 'erp-pro' ),
	__( 'Sync From ERP Contacts to Mailchimp', 'erp-pro' ),
	__( 'Sync from Mailchimp to ERP Contacts', 'erp-pro' ),
	__( 'Auto Sync?', 'erp-pro' ),
	__( 'Contact Group', 'erp-pro' ),
	__( 'Email Lists', 'erp-pro' ),
	__( 'Select Email List', 'erp-pro' ),
	__( 'No Contact Groups found', 'erp-pro' ),
	__( 'Email List', 'erp-pro' ),
	__( 'Contact Groups', 'erp-pro' ),
	__( 'Contact Owner', 'erp-pro' ),
	__( 'Life Stage', 'erp-pro' ),
	__( 'Select Contact Groups', 'erp-pro' ),
	__( 'Select Contact Owner', 'erp-pro' ),
	__( 'Select Contact Life Stage', 'erp-pro' ),
	__( 'No email found for this Mailchimp account', 'erp-pro' ),
	__( ' can\'t be empty', 'erp-pro' ),
];