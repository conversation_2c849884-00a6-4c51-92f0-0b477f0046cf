{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///noty.min.js", "webpack:///webpack/bootstrap 9fc5b5bdaf483a649708", "webpack:///./src/utils.js", "webpack:///./src/api.js", "webpack:///./src/button.js", "webpack:///./src/push.js", "webpack:///./~/es6-promise/dist/es6-promise.js", "webpack:///./src/index.js", "webpack:///./~/process/browser.js", "webpack:///(webpack)/buildin/global.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "i", "l", "call", "m", "c", "value", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "inArray", "needle", "haystack", "argStrict", "key", "stopPropagation", "evt", "window", "event", "cancelBubble", "generateID", "prefix", "arguments", "length", "undefined", "id", "replace", "r", "Math", "random", "toString", "outerHeight", "el", "height", "offsetHeight", "style", "getComputedStyle", "parseInt", "marginTop", "marginBottom", "addListener", "events", "cb", "useCapture", "split", "document", "addEventListener", "attachEvent", "hasClass", "element", "classList", "indexOf", "addClass", "oldList", "newList", "className", "substring", "removeClass", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "visibilityChangeFlow", "onVisibilityChange", "API", "PageHidden", "hidden", "handleVisibilityChange", "onBlur", "onFocus", "stopAll", "resumeAll", "setTimeout", "keys", "Store", "for<PERSON>ach", "options", "visibilityControl", "stop", "resume", "queueRenderAll", "visibilityChange", "msHidden", "webkitHidden", "createAudioElements", "ref", "hasSound", "audioElement", "createElement", "sounds", "sources", "source", "src", "type", "getExtension", "append<PERSON><PERSON><PERSON>", "barDom", "querySelector", "volume", "soundPlayed", "play", "onended", "fileName", "match", "css", "deepExtend", "animationEndEvents", "_typeof", "Symbol", "iterator", "obj", "constructor", "_api", "newObj", "default", "out", "Array", "isArray", "camelCase", "string", "letter", "toUpperCase", "getVendorProp", "body", "cssPrefixes", "capName", "char<PERSON>t", "slice", "vendorName", "getStyleProp", "cssProps", "applyCss", "prop", "properties", "args", "getQueueCounts", "queueName", "count", "max", "DefaultMaxVisible", "Queues", "maxVisible", "queue", "closed", "current", "addToQueue", "push", "removeFromQueue", "queueRender", "noty", "shift", "show", "ghostFix", "ghostID", "Utils", "ghost", "setAttribute", "insertAdjacentHTML", "outerHTML", "getElementById", "build", "findOrCreateContainer", "markup", "text", "buildButtons", "theme", "innerHTML", "fire", "hasButtons", "buttons", "dom", "btn", "handleModal", "modal", "DocModalCount", "createModal", "handleModalClose", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "container", "layoutDom", "layoutID", "layout", "queueClose", "timeout", "progressBar", "progressDom", "transition", "width", "clearTimeout", "closeTimer", "close", "dequeueClose", "eventName", "listeners", "apply", "openFlow", "closeFlow", "closing", "querySelectorAll", "titleCount", "conditions", "doc<PERSON><PERSON><PERSON>", "decrement", "De<PERSON>ults", "_utils", "DocTitleProps", "originalTitle", "changed", "timer", "increment", "_update", "_clear", "title", "global", "closeWith", "animation", "open", "force", "killer", "callbacks", "beforeShow", "onShow", "afterShow", "onClose", "afterClose", "onClick", "onHover", "onTemplate", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "NotyButton", "html", "classes", "_this", "attributes", "propertyName", "_createClass", "defineProperties", "target", "props", "descriptor", "writable", "protoProps", "staticProps", "<PERSON><PERSON>", "worker<PERSON><PERSON>", "subData", "onPermissionGranted", "onPermissionDenied", "onSubscriptionSuccess", "onSubscriptionCancel", "onWorkerError", "onWorkerSuccess", "onWorkerNotSupported", "params", "console", "log", "result", "Notification", "webkitNotifications", "navigator", "mozNotification", "external", "msIsSiteMode", "e", "perm", "permissionLevel", "checkPermission", "permission", "toLowerCase", "subscription", "endpoint", "subscriptionId", "serviceWorker", "controller", "state", "self", "getRegistrations", "then", "registrations", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "_step", "_iterator", "next", "done", "unregister", "err", "return", "_this2", "userVisibleOnly", "getPermissionStatus", "register", "ready", "serviceWorkerRegistration", "pushManager", "subscribe", "<PERSON><PERSON><PERSON>", "token", "getEndpoint", "p256dh", "btoa", "String", "fromCharCode", "Uint8Array", "auth", "catch", "unregisterWorker", "requestPermission", "process", "objectOrFunction", "x", "isFunction", "setScheduler", "scheduleFn", "customSchedulerFn", "setAsap", "asapFn", "asap", "useVertxTimer", "vertxNext", "flush", "useSetTimeout", "globalSetTimeout", "len", "callback", "onFulfillment", "onRejection", "_arguments", "parent", "child", "noop", "PROMISE_ID", "makePromise", "_state", "invokeCallback", "_result", "resolve", "promise", "_resolve", "selfFulfillment", "cannotReturnOwn", "getThen", "error", "GET_THEN_ERROR", "tryThen", "fulfillmentH<PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>", "handleForeignThenable", "thenable", "sealed", "fulfill", "reason", "_reject", "_label", "handleOwnThenable", "FULFILLED", "REJECTED", "handleMaybeThenable", "maybeThenable", "then$$", "publishRejection", "_onerror", "publish", "PENDING", "_subscribers", "subscribers", "settled", "detail", "ErrorObject", "tryCatch", "TRY_CATCH_ERROR", "<PERSON><PERSON><PERSON><PERSON>", "succeeded", "failed", "initializePromise", "resolver", "nextId", "Enumerator", "input", "_instanceConstructor", "_input", "_remaining", "_enumerate", "validationError", "Error", "all", "entries", "race", "reject", "_", "needsResolver", "needsNew", "Promise", "polyfill", "local", "Function", "P", "promiseToString", "cast", "_isArray", "arg", "scheduleFlush", "browserWindow", "browserGlobal", "BrowserMutationObserver", "MutationObserver", "WebKitMutationObserver", "isNode", "isWorker", "Uint8ClampedArray", "importScripts", "MessageChannel", "nextTick", "iterations", "observer", "node", "createTextNode", "observe", "characterData", "data", "channel", "port1", "onmessage", "port2", "postMessage", "vertx", "runOnLoop", "runOnContext", "_eachEntry", "entry", "resolve$$", "_then", "_settledAt", "_willSettleAt", "enumerator", "_setScheduler", "_setAsap", "_asap", "_interopRequireWildcard", "_es6Promise", "_es6Promise2", "_button", "_push", "<PERSON><PERSON>", "showing", "shown", "killable", "promises", "on", "closeAll", "queueCounts", "closeButton", "bind", "_t", "_setTimeout", "_x", "ms", "optionsOverride", "_this3", "_this4", "amount", "innerHtml", "defaultSetTimout", "defaultClearTimeout", "runTimeout", "fun", "cachedSetTimeout", "runClearTimeout", "marker", "cachedClearTimeout", "cleanUpNextTick", "draining", "currentQueue", "concat", "queueIndex", "drainQueue", "run", "<PERSON><PERSON>", "array", "browser", "env", "argv", "version", "versions", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "binding", "cwd", "chdir", "dir", "umask", "g", "eval"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,OAAA,UAAAH,GACA,gBAAAC,SACAA,QAAA,KAAAD,IAEAD,EAAA,KAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAE,EAAAF,EACAG,GAAA,EACAV,WAUA,OANAK,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,GAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KA+DA,OAnCAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAG,EAAA,SAAAK,GAA2C,MAAAA,IAG3CR,EAAAS,EAAA,SAAAf,EAAAgB,EAAAC,GACAX,EAAAY,EAAAlB,EAAAgB,IACAG,OAAAC,eAAApB,EAAAgB,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAX,EAAAkB,EAAA,SAAAvB,GACA,GAAAgB,GAAAhB,KAAAwB,WACA,WAA2B,MAAAxB,GAAA,SAC3B,WAAiC,MAAAA,GAEjC,OADAK,GAAAS,EAAAE,EAAA,IAAAA,GACAA,GAIAX,EAAAY,EAAA,SAAAQ,EAAAC,GAAsD,MAAAR,QAAAS,UAAAC,eAAAlB,KAAAe,EAAAC,IAGtDrB,EAAAwB,EAAA,GAGAxB,IAAAyB,EAAA,KDgBM,SAAU9B,EAAQD,EAASM,GAEjC,YE9EO,SAAS0B,GAASC,EAAQC,EAAUC,GACzC,GAAIC,SAGJ,KAFeD,GASb,IAAKC,IAAOF,GACV,GAAIA,EAASL,eAAeO,IAAQF,EAASE,KAASH,EACpD,OAAO,MARX,KAAKG,IAAOF,GACV,GAAIA,EAASL,eAAeO,IAAQF,EAASE,KAASH,EACpD,OAAO,CAUb,QAAO,EAGF,QAASI,GAAiBC,GAC/BA,EAAMA,GAAOC,OAAOC,UAEe,KAAxBF,EAAID,gBACbC,EAAID,kBAEJC,EAAIG,cAAe,EA4BhB,QAASC,KAAyB,GAAbC,GAAaC,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAAJ,GAC/BG,UAAaJ,EAAb,GAQJ,OANAI,IAAM,uCAAuCC,QAAQ,QAAS,SAAUnC,GACtE,GAAIoC,GAAoB,GAAhBC,KAAKC,SAAgB,CAE7B,QADc,MAANtC,EAAYoC,EAAQ,EAAJA,EAAU,GACzBG,SAAS,MAMf,QAASC,GAAaC,GAC3B,GAAIC,GAASD,EAAGE,aACZC,EAAQlB,OAAOmB,iBAAiBJ,EAGpC,OADAC,IAAUI,SAASF,EAAMG,WAAaD,SAASF,EAAMI,cA8DhD,QAASC,GAAaR,EAAIS,EAAQC,GAAwB,GAApBC,GAAoBrB,UAAAC,OAAA,OAAAC,KAAAF,UAAA,IAAAA,UAAA,EAC/DmB,GAASA,EAAOG,MAAM,IACtB,KAAK,GAAIzD,GAAI,EAAGA,EAAIsD,EAAOlB,OAAQpC,IAC7B0D,SAASC,iBACXd,EAAGc,iBAAiBL,EAAOtD,GAAIuD,EAAIC,GAC1BE,SAASE,aAClBf,EAAGe,YAAY,KAAON,EAAOtD,GAAIuD,GAKhC,QAASM,GAAUC,EAASvD,GAEjC,OAD8B,gBAAZuD,GAAuBA,EAAUC,EAAUD,IACjDE,QAAQ,IAAMzD,EAAO,MAAQ,EAGpC,QAAS0D,GAAUH,EAASvD,GACjC,GAAI2D,GAAUH,EAAUD,GACpBK,EAAUD,EAAU3D,CAEpBsD,GAASK,EAAS3D,KAGtBuD,EAAQM,UAAYD,EAAQE,UAAU,IAGjC,QAASC,GAAaR,EAASvD,GACpC,GAAI2D,GAAUH,EAAUD,GACpBK,QAECN,GAASC,EAASvD,KAGvB4D,EAAUD,EAAQ3B,QAAQ,IAAMhC,EAAO,IAAK,KAG5CuD,EAAQM,UAAYD,EAAQE,UAAU,EAAGF,EAAQ/B,OAAS,IAGrD,QAASmC,GAAQT,GAClBA,EAAQU,YACVV,EAAQU,WAAWC,YAAYX,GAI5B,QAASC,GAAWD,GACzB,OAAQ,KAAQA,GAAWA,EAAQM,WAAc,IAAM,KAAK7B,QAC1D,QACA,KAIG,QAASmC,KAed,QAASC,KACPC,EAAIC,WAAanB,SAASoB,GAC1BC,IAGF,QAASC,KACPJ,EAAIC,YAAa,EACjBE,IAGF,QAASE,KACPL,EAAIC,YAAa,EACjBE,IAGF,QAASA,KACHH,EAAIC,WAAYK,IACfC,IAGP,QAASD,KACPE,WACE,WACE1E,OAAO2E,KAAKT,EAAIU,OAAOC,QAAQ,SAAAjD,GACzBsC,EAAIU,MAAMlE,eAAekB,IACvBsC,EAAIU,MAAMhD,GAAIkD,QAAQC,mBACxBb,EAAIU,MAAMhD,GAAIoD,UAKtB,KAIJ,QAASP,KACPC,WACE,WACE1E,OAAO2E,KAAKT,EAAIU,OAAOC,QAAQ,SAAAjD,GACzBsC,EAAIU,MAAMlE,eAAekB,IACvBsC,EAAIU,MAAMhD,GAAIkD,QAAQC,mBACxBb,EAAIU,MAAMhD,GAAIqD,WAIpBf,EAAIgB,kBAEN,KA7DJ,GAAId,UACAe,aAC2B,KAApBnC,SAASoB,QAElBA,EAAS,SACTe,EAAmB,wBACmB,KAAtBnC,SAASoC,UACzBhB,EAAS,WACTe,EAAmB,0BACuB,KAA1BnC,SAASqC,eACzBjB,EAAS,eACTe,EAAmB,0BAsDrBxC,EAAYK,SAAUmC,EAAkBlB,GACxCtB,EAAYvB,OAAQ,OAAQkD,GAC5B3B,EAAYvB,OAAQ,QAASmD,GAGxB,QAASe,GAAqBC,GACnC,GAAIA,EAAIC,SAAU,CAChB,GAAMC,GAAezC,SAAS0C,cAAc,QAE5CH,GAAIT,QAAQa,OAAOC,QAAQf,QAAQ,SAAAjE,GACjC,GAAMiF,GAAS7C,SAAS0C,cAAc,SACtCG,GAAOC,IAAMlF,EACbiF,EAAOE,KAAP,SAAuBC,EAAapF,GACpC6E,EAAaQ,YAAYJ,KAGvBN,EAAIW,OACNX,EAAIW,OAAOD,YAAYR,GAEvBzC,SAASmD,cAAc,QAAQF,YAAYR,GAG7CA,EAAaW,OAASb,EAAIT,QAAQa,OAAOS,OAEpCb,EAAIc,cACPZ,EAAaa,OACbf,EAAIc,aAAc,GAGpBZ,EAAac,QAAU,WACrB1C,EAAO4B,KAKb,QAASO,GAAcQ,GACrB,MAAOA,GAASC,MAAM,cAAc,GF7MtCzG,OAAOC,eAAepB,EAAS,cAC7Bc,OAAO,IAETd,EAAQ6H,IAAM7H,EAAQ8H,WAAa9H,EAAQ+H,uBAAqBjF,EAEhE,IAAIkF,GAA4B,kBAAXC,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUC,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXF,SAAyBE,EAAIC,cAAgBH,QAAUE,IAAQF,OAAOrG,UAAY,eAAkBuG,GAEtQnI,GExFgBgC,UFyFhBhC,EErEgBqC,kBFsEhBrC,EEpCgB0C,aFqChB1C,EEzBgBqD,cF0BhBrD,EEwCgB8D,cFvChB9D,EEkDgBsE,WFjDhBtE,EEsDgB0E,WFrDhB1E,EE+DgB+E,cF9DhB/E,EE2EgBgF,SF1EhBhF,EEgFgBwE,YF/EhBxE,EEsFgBmF,uBFrFhBnF,EE4JgByG,qBAnQhB,IAAA4B,GAAA/H,EAAA,GAAY+E,EF6GZ,SAAiC8C,GAAO,GAAIA,GAAOA,EAAI1G,WAAc,MAAO0G,EAAc,IAAIG,KAAa,IAAW,MAAPH,EAAe,IAAK,GAAI/F,KAAO+F,GAAWhH,OAAOS,UAAUC,eAAelB,KAAKwH,EAAK/F,KAAMkG,EAAOlG,GAAO+F,EAAI/F,GAAgC,OAAtBkG,GAAOC,QAAUJ,EAAYG,GAFhOD,EEzGrBN,sBAAqB,+EAgCrBD,aAAa,QAAbA,GAAuBU,GAClCA,EAAMA,KAEN,KAAK,GAAI/H,GAAI,EAAGA,EAAImC,UAAUC,OAAQpC,IAAK,CACzC,GAAI0H,GAAMvF,UAAUnC,EAEpB,IAAK0H,EAEL,IAAK,GAAI/F,KAAO+F,GACVA,EAAItG,eAAeO,KACjBqG,MAAMC,QAAQP,EAAI/F,IACpBoG,EAAIpG,GAAO+F,EAAI/F,GACc,WAApB4F,EAAOG,EAAI/F,KAAkC,OAAb+F,EAAI/F,GAC7CoG,EAAIpG,GAAO0F,EAAWU,EAAIpG,GAAM+F,EAAI/F,IAEpCoG,EAAIpG,GAAO+F,EAAI/F,IAMvB,MAAOoG,IAuBEX,MAAO,WAIhB,QAASc,GAAWC,GAClB,MAAOA,GACJ5F,QAAQ,QAAS,OACjBA,QAAQ,eAAgB,SAAU4E,EAAOiB,GACxC,MAAOA,GAAOC,gBAIpB,QAASC,GAAe/H,GACtB,GAAIyC,GAAQU,SAAS6E,KAAKvF,KAC1B,IAAIzC,IAAQyC,GAAO,MAAOzC,EAM1B,KAJA,GAAIP,GAAIwI,EAAYpG,OAChBqG,EAAUlI,EAAKmI,OAAO,GAAGL,cAAgB9H,EAAKoI,MAAM,GACpDC,SAEG5I,KAEL,IADA4I,EAAaJ,EAAYxI,GAAKyI,IACZzF,GAAO,MAAO4F,EAGlC,OAAOrI,GAGT,QAASsI,GAActI,GAErB,MADAA,GAAO2H,EAAU3H,GACVuI,EAASvI,KAAUuI,EAASvI,GAAQ+H,EAAc/H,IAG3D,QAASwI,GAAUjF,EAASkF,EAAM3I,GAChC2I,EAAOH,EAAaG,GACpBlF,EAAQd,MAAMgG,GAAQ3I,EAlCxB,GAAImI,IAAe,SAAU,IAAK,MAAO,MACrCM,IAoCJ,OAAO,UAAUhF,EAASmF,GACxB,GAAIC,GAAO/G,UACP6G,SACA3I,QAEJ,IAAoB,IAAhB6I,EAAK9G,OACP,IAAK4G,IAAQC,GACPA,EAAW7H,eAAe4H,QAEd3G,MADdhC,EAAQ4I,EAAWD,KACQC,EAAW7H,eAAe4H,IACnDD,EAASjF,EAASkF,EAAM3I,OAK9B0I,GAASjF,EAASoF,EAAK,GAAIA,EAAK,SFyQhC,SAAU1J,EAAQD,EAASM,GAEjC,YGpSO,SAASsJ,KAAsC,GAAtBC,GAAsBjH,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAAV,SACtCkH,EAAQ,EACRC,EAAMC,CASV,OAPIC,GAAOpI,eAAegI,KACxBE,EAAME,EAAOJ,GAAWK,WACxB/I,OAAO2E,KAAKC,GAAOC,QAAQ,SAAAvF,GACrBsF,EAAMtF,GAAGwF,QAAQkE,QAAUN,GAAc9D,EAAMtF,GAAG2J,QAAQN,QAKhEO,QAASP,EACTI,WAAYH,GAQT,QAASO,GAAY5D,GACrBuD,EAAOpI,eAAe6E,EAAIT,QAAQkE,SACrCF,EAAOvD,EAAIT,QAAQkE,QAAUD,WAAYF,EAAmBG,WAG9DF,EAAOvD,EAAIT,QAAQkE,OAAOA,MAAMI,KAAK7D,GAOhC,QAAS8D,GAAiB9D,GAC/B,GAAIuD,EAAOpI,eAAe6E,EAAIT,QAAQkE,OAAQ,CAC5C,GAAMA,KACNhJ,QAAO2E,KAAKmE,EAAOvD,EAAIT,QAAQkE,OAAOA,OAAOnE,QAAQ,SAAAvF,GAC/CwJ,EAAOvD,EAAIT,QAAQkE,OAAOA,MAAM1J,GAAGsC,KAAO2D,EAAI3D,IAChDoH,EAAMI,KAAKN,EAAOvD,EAAIT,QAAQkE,OAAOA,MAAM1J,MAG/CwJ,EAAOvD,EAAIT,QAAQkE,OAAOA,MAAQA,GAQ/B,QAASM,KAAmC,GAAtBZ,GAAsBjH,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAAV,QACvC,IAAIqH,EAAOpI,eAAegI,GAAY,CACpC,GAAMa,GAAOT,EAAOJ,GAAWM,MAAMQ,OAEjCD,IAAMA,EAAKE,QAOZ,QAASvE,KACdlF,OAAO2E,KAAKmE,GAAQjE,QAAQ,SAAA6D,GAC1BY,EAAYZ,KAQT,QAASgB,GAAUnE,GACxB,GAAMoE,GAAUC,EAAMrI,WAAW,SAC7BsI,EAAQ7G,SAAS0C,cAAc,MACnCmE,GAAMC,aAAa,KAAMH,GACzBC,EAAMlD,IAAImD,GACRzH,OAAQwH,EAAM1H,YAAYqD,EAAIW,QAAU,OAG1CX,EAAIW,OAAO6D,mBAAmB,WAAYF,EAAMG,WAEhDJ,EAAM/F,OAAO0B,EAAIW,QACjB2D,EAAQ7G,SAASiH,eAAeN,GAChCC,EAAMrG,SAASsG,EAAO,2BACtBD,EAAMjH,YAAYkH,EAAOD,EAAMhD,mBAAoB,WACjDgD,EAAM/F,OAAOgG,KAQV,QAASK,GAAO3E,GACrB4E,EAAsB5E,EAEtB,IAAM6E,6BAAmC7E,EAAIT,QAAQuF,KAA/C,SAA4DC,EAAa/E,GAAzE,sCAENA,GAAIW,OAASlD,SAAS0C,cAAc,OACpCH,EAAIW,OAAO4D,aAAa,KAAMvE,EAAI3D,IAClCgI,EAAMrG,SACJgC,EAAIW,OADN,uBAEyBX,EAAIT,QAAQiB,KAFrC,gBAEyDR,EAAIT,QAAQyF,OAGrEhF,EAAIW,OAAOsE,UAAYJ,EAEvBK,EAAKlF,EAAK,cAOL,QAASmF,GAAYnF,GAC1B,SAAUA,EAAIT,QAAQ6F,UAAW3K,OAAO2E,KAAKY,EAAIT,QAAQ6F,SAASjJ,QAOpE,QAAS4I,GAAc/E,GACrB,GAAImF,EAAWnF,GAAM,CACnB,GAAIoF,GAAU3H,SAAS0C,cAAc,MAUrC,OATAkE,GAAMrG,SAASoH,EAAS,gBAExB3K,OAAO2E,KAAKY,EAAIT,QAAQ6F,SAAS9F,QAAQ,SAAA5D,GACvC0J,EAAQ1E,YAAYV,EAAIT,QAAQ6F,QAAQ1J,GAAK2J,OAG/CrF,EAAIT,QAAQ6F,QAAQ9F,QAAQ,SAAAgG,GAC1BF,EAAQ1E,YAAY4E,EAAID,OAEnBD,EAAQX,UAEjB,MAAO,GAOF,QAASc,GAAavF,GACvBA,EAAIT,QAAQiG,QACQ,IAAlBC,GACFC,IAGFpM,EA3POmM,cA2PPA,GAAA,GAQG,QAASE,GAAkB3F,GAChC,GAAIA,EAAIT,QAAQiG,OAASC,EAAgB,IACvCnM,EArQOmM,cAqQPA,GAAA,EAEIA,GAAiB,GAAG,CACtB,GAAMD,GAAQ/H,SAASmD,cAAc,cAEjC4E,KACFnB,EAAMhG,YAAYmH,EAAO,mBACzBnB,EAAMrG,SAASwH,EAAO,oBACtBnB,EAAMjH,YAAYoI,EAAOnB,EAAMhD,mBAAoB,WACjDgD,EAAM/F,OAAOkH,OAUvB,QAASE,KACP,GAAMpD,GAAO7E,SAASmD,cAAc,QAC9B4E,EAAQ/H,SAAS0C,cAAc,MACrCkE,GAAMrG,SAASwH,EAAO,cACtBlD,EAAKsD,aAAaJ,EAAOlD,EAAKuD,YAC9BxB,EAAMrG,SAASwH,EAAO,mBAEtBnB,EAAMjH,YAAYoI,EAAOnB,EAAMhD,mBAAoB,WACjDgD,EAAMhG,YAAYmH,EAAO,qBAQ7B,QAASZ,GAAuB5E,GAC9B,GAAIA,EAAIT,QAAQuG,UAEd,YADA9F,EAAI+F,UAAYtI,SAASmD,cAAcZ,EAAIT,QAAQuG,WAIrD,IAAME,mBAA2BhG,EAAIT,QAAQ0G,MAC7CjG,GAAI+F,UAAYtI,SAASmD,cAAT,OAA8BoF,GAEzChG,EAAI+F,YACP/F,EAAI+F,UAAYtI,SAAS0C,cAAc,OACvCH,EAAI+F,UAAUxB,aAAa,KAAMyB,GACjC3B,EAAMrG,SAASgC,EAAI+F,UAAW,eAC9BtI,SAASmD,cAAc,QAAQF,YAAYV,EAAI+F,YAQ5C,QAASG,GAAYlG,GACtBA,EAAIT,QAAQ4G,UACVnG,EAAIT,QAAQ6G,aAAepG,EAAIqG,aACjChC,EAAMlD,IAAInB,EAAIqG,aACZC,oBAAqBtG,EAAIT,QAAQ4G,QAAjC,YACAI,MAAO,OAIXC,aAAaxG,EAAIyG,YAEjBzG,EAAIyG,WAAatH,WACf,WACEa,EAAI0G,SAEN1G,EAAIT,QAAQ4G,UASX,QAASQ,GAAc3G,GACxBA,EAAIT,QAAQ4G,SAAWnG,EAAIyG,aAC7BD,aAAaxG,EAAIyG,YACjBzG,EAAIyG,YAAc,EAEdzG,EAAIT,QAAQ6G,aAAepG,EAAIqG,aACjChC,EAAMlD,IAAInB,EAAIqG,aACZC,WAAY,mBACZC,MAAO,UAWR,QAASrB,GAAMlF,EAAK4G,GACrB5G,EAAI6G,UAAU1L,eAAeyL,IAC/B5G,EAAI6G,UAAUD,GAAWtH,QAAQ,SAAAhC,GACb,kBAAPA,IACTA,EAAGwJ,MAAM9G,KAUV,QAAS+G,GAAU/G,GACxBkF,EAAKlF,EAAK,aACVkG,EAAWlG,GAEXqE,EAAMjH,YAAY4C,EAAIW,OAAQ,aAAc,WAC1CgG,EAAa3G,KAGfqE,EAAMjH,YAAY4C,EAAIW,OAAQ,aAAc,WAC1CuF,EAAWlG,KAQR,QAASgH,GAAWhH,SAClBX,GAAMW,EAAI3D,IACjB2D,EAAIiH,SAAU,EACd/B,EAAKlF,EAAK,cAEVqE,EAAM/F,OAAO0B,EAAIW,QAGwC,IAAvDX,EAAI+F,UAAUmB,iBAAiB,aAAa/K,QAC3C6D,EAAIT,QAAQuG,WAEbzB,EAAM/F,OAAO0B,EAAI+F,YAIjB1B,EAAM/I,QAAQ,aAAc0E,EAAIT,QAAQ4H,WAAWC,aACnD/C,EAAM/I,QAAQ,YAAa0E,EAAIT,QAAQ4H,WAAWC,cAElDC,EAASC,YAGXvD,EAAY/D,EAAIT,QAAQkE,OHd1BhJ,OAAOC,eAAepB,EAAS,cAC7Bc,OAAO,IAETd,EAAQiO,SAAWjO,EAAQ+F,MAAQ/F,EAAQiK,OAASjK,EAAQgK,kBAAoBhK,EAAQ+N,SAAW/N,EAAQmM,cAAgBnM,EAAQsF,eAAaxC,GAChJ9C,EG3SgB4J,iBH4ShB5J,EGvRgBsK,aHwRhBtK,EG5QgBwK,kBH6QhBxK,EG7PgByK,cH8PhBzK,EGnPgBqG,iBHoPhBrG,EG1OgB6K,WH2OhB7K,EGrNgBqL,QHsNhBrL,EGjMgB6L,aHkMhB7L,EGrKgBiM,cHsKhBjM,EGxJgBqM,mBHyJhBrM,EG/FgB4M,aHgGhB5M,EGxEgBqN,eHyEhBrN,EGtDgB4L,OHuDhB5L,EGzCgByN,WH0ChBzN,EGzBgB0N,WA1YhB,IAAAQ,GAAA5N,EAAA,GAAYyK,EHyaZ,SAAiC5C,GAAO,GAAIA,GAAOA,EAAI1G,WAAc,MAAO0G,EAAc,IAAIG,KAAa,IAAW,MAAPH,EAAe,IAAK,GAAI/F,KAAO+F,GAAWhH,OAAOS,UAAUC,eAAelB,KAAKwH,EAAK/F,KAAMkG,EAAOlG,GAAO+F,EAAI/F,GAAgC,OAAtBkG,GAAOC,QAAUJ,EAAYG,GAF9N4F,GGpazB/B,GADA7G,cAAa,EACb6G,gBAAgB,GAErBgC,GACJC,cAAe,KACftE,MAAO,EACPuE,SAAS,EACTC,OAAQ,GAGGP,cACXQ,UAAW,WACTJ,EAAcrE,QAEdiE,EAASS,WAGXR,UAAW,WAGT,KAFAG,EAAcrE,OAEa,EAEzB,WADAiE,GAASU,QAIXV,GAASS,WAGXA,QAAS,WACP,GAAIE,GAAQvK,SAASuK,KAEhBP,GAAcE,QAKjBlK,SAASuK,MAAT,IAAqBP,EAAcrE,MAAnC,KAA6CqE,EAAcC,eAJ3DD,EAAcC,cAAgBM,EAC9BvK,SAASuK,MAAT,IAAqBP,EAAcrE,MAAnC,KAA6C4E,EAC7CP,EAAcE,SAAU,IAM5BI,OAAQ,WACFN,EAAcE,UAChBF,EAAcrE,MAAQ,EACtB3F,SAASuK,MAAQP,EAAcC,cAC/BD,EAAcE,SAAU,KAKjBrE,sBAAoB,EAEpBC,YACX0E,QACEzE,WAAYF,EACZG,WAISpE,YAEFkI,aACT/G,KAAM,QACNyF,OAAQ,WACRjB,MAAO,OACPF,KAAM,GACNqB,SAAS,EACTC,aAAa,EACb8B,WAAY,SACZC,WACEC,KAAM,oBACN1B,MAAO,sBAETrK,IAAI,EACJgM,OAAO,EACPC,QAAQ,EACR7E,MAAO,SACPqC,WAAW,EACXV,WACAmD,WACEC,WAAY,KACZC,OAAQ,KACRC,UAAW,KACXC,QAAS,KACTC,WAAY,KACZC,QAAS,KACTC,QAAS,KACTC,WAAY,MAEd3I,QACEC,WACAQ,OAAQ,EACRuG,eAEFD,YACEC,eAEF5B,OAAO,EACPhG,mBAAmB,IHiuBf,SAAUjG,EAAQD,EAASM,GAEjC,YAcA,SAASoP,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCAXhH1O,OAAOC,eAAepB,EAAS,cAC7Bc,OAAO,IAETd,EAAQ8P,eAAahN,EI50BrB,IAAAoL,GAAA5N,EAAA,GAAYyK,EJk1BZ,SAAiC5C,GAAO,GAAIA,GAAOA,EAAI1G,WAAc,MAAO0G,EAAc,IAAIG,KAAa,IAAW,MAAPH,EAAe,IAAK,GAAI/F,KAAO+F,GAAWhH,OAAOS,UAAUC,eAAelB,KAAKwH,EAAK/F,KAAMkG,EAAOlG,GAAO+F,EAAI/F,GAAgC,OAAtBkG,GAAOC,QAAUJ,EAAYG,GAF9N4F,EAMnBlO,GIp1BJ8P,WACX,QAAAA,GAAaC,EAAMC,EAAShM,GAAqB,GAAAiM,GAAA7P,KAAjB8P,EAAiBtN,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,KAU/C,OAV+C8M,GAAAtP,KAAA0P,GAC/C1P,KAAK2L,IAAM5H,SAAS0C,cAAc,UAClCzG,KAAK2L,IAAIJ,UAAYoE,EACrB3P,KAAK2C,GAAMmN,EAAWnN,GAAKmN,EAAWnN,IAAMgI,EAAMrI,WAAW,UAC7DtC,KAAK4D,GAAKA,EACV7C,OAAO2E,KAAKoK,GAAYlK,QAAQ,SAAAmK,GAC9BF,EAAKlE,IAAId,aAAakF,EAAcD,EAAWC,MAEjDpF,EAAMrG,SAAStE,KAAK2L,IAAKiE,GAAW,YAE7B5P,OJ81BL,SAAUH,EAAQD,EAASM,GAEjC,YASA,SAASoP,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCANhH1O,OAAOC,eAAepB,EAAS,cAC7Bc,OAAO,GAGT,IAAIsP,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9P,GAAI,EAAGA,EAAI8P,EAAM1N,OAAQpC,IAAK,CAAE,GAAI+P,GAAaD,EAAM9P,EAAI+P,GAAWlP,WAAakP,EAAWlP,aAAc,EAAOkP,EAAWnP,cAAe,EAAU,SAAWmP,KAAYA,EAAWC,UAAW,GAAMtP,OAAOC,eAAekP,EAAQE,EAAWpO,IAAKoO,IAAiB,MAAO,UAAUZ,EAAac,EAAYC,GAAiJ,MAA9HD,IAAYL,EAAiBT,EAAYhO,UAAW8O,GAAiBC,GAAaN,EAAiBT,EAAae,GAAqBf,KAIrhB5P,GKx3BE4Q,KLw3Ba,WKv3BxB,QAAAA,KAAgD,GAAnCC,GAAmCjO,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAAtB,oBAYxB,OAZ8C8M,GAAAtP,KAAAwQ,GAC9CxQ,KAAK0Q,WACL1Q,KAAKyQ,WAAaA,EAClBzQ,KAAKmN,WACHwD,uBACAC,sBACAC,yBACAC,wBACAC,iBACAC,mBACAC,yBAEKjR,KL0lCT,MApNAgQ,GAAaQ,IACXxO,IAAK,KACLtB,MAAO,SKh4BLwM,GAA0B,GAAftJ,GAAepB,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAAV,YAKlB,OAJkB,kBAAPoB,IAAqB5D,KAAKmN,UAAU1L,eAAeyL,IAC5DlN,KAAKmN,UAAUD,GAAW/C,KAAKvG,GAG1B5D,QLq4BPgC,IAAK,OACLtB,MAAO,SKn4BHwM,GAAwB,GAAA2C,GAAA7P,KAAbkR,EAAa1O,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,KACxBxC,MAAKmN,UAAU1L,eAAeyL,IAChClN,KAAKmN,UAAUD,GAAWtH,QAAQ,SAAAhC,GACd,kBAAPA,IACTA,EAAGwJ,MAAHyC,EAAeqB,QL64BrBlP,IAAK,SACLtB,MAAO,WKv4BPyQ,QAAQC,IAAI,0BLg5BZpP,IAAK,cACLtB,MAAO,WK14BP,GAAI2Q,IAAS,CAEb,KACEA,EAASlP,OAAOmP,cACdnP,OAAOoP,qBACPC,UAAUC,iBACTtP,OAAOuP,cAA+ChP,KAAnCP,OAAOuP,SAASC,eACtC,MAAOC,IAET,MAAOP,MLg5BPrP,IAAK,sBACLtB,MAAO,WK14BP,GAAImR,GAAO,SAEX,IAAI1P,OAAOmP,cAAgBnP,OAAOmP,aAAaQ,gBAC7CD,EAAO1P,OAAOmP,aAAaQ,oBACtB,IACL3P,OAAOoP,qBAAuBpP,OAAOoP,oBAAoBQ,gBAEzD,OAAQ5P,OAAOoP,oBAAoBQ,mBACjC,IAAK,GACHF,EAAO,SACP,MACF,KAAK,GACHA,EAAO,SACP,MACF,SACEA,EAAO,aAEF1P,QAAOmP,cAAgBnP,OAAOmP,aAAaU,WACpDH,EAAO1P,OAAOmP,aAAaU,WAClBR,UAAUC,gBACnBI,EAAO,UAEP1P,OAAOuP,cAA+ChP,KAAnCP,OAAOuP,SAASC,iBAEnCE,EAAO1P,OAAOuP,SAASC,eAAiB,UAAY,UAGtD,OAAOE,GAAK7O,WAAWiP,iBL+4BvBjQ,IAAK,cACLtB,MAAO,SK14BIwR,GACX,GAAIC,GAAWD,EAAaC,SACtBC,EAAiBF,EAAaE,cAOpC,OAJIA,KAAwD,IAAtCD,EAAS9N,QAAQ+N,KACrCD,GAAY,IAAMC,GAGbD,KLk5BPnQ,IAAK,iBACLtB,MAAO,WK54BP,IACE,MAAoD,cAA7C8Q,UAAUa,cAAcC,WAAWC,MAC1C,MAAOX,GACP,OAAO,MLs5BT5P,IAAK,mBACLtB,MAAO,WK/4BP,GAAM8R,GAAOxS,IACT,kBAAmBwR,YACrBA,UAAUa,cAAcI,mBAAmBC,KAAK,SAAUC,GAAe,GAAAC,IAAA,EAAAC,GAAA,EAAAC,MAAApQ,EAAA,KACvE,OAAAqQ,GAAAC,EAAyBL,EAAzB9K,OAAAC,cAAA8K,GAAAG,EAAAC,EAAAC,QAAAC,MAAAN,GAAA,EAAwC,CAAAG,EAAArS,MACzByS,aACbX,EAAKhH,KAAK,yBAH2D,MAAA4H,GAAAP,GAAA,EAAAC,EAAAM,EAAA,aAAAR,GAAAI,EAAAK,QAAAL,EAAAK,SAAA,WAAAR,EAAA,KAAAC,ULm7B3E9Q,IAAK,sBACLtB,MAAO,WKx6BoC,GAAA4S,GAAAtT,KAAxBuT,IAAwB/Q,UAAAC,OAAA,OAAAC,KAAAF,UAAA,KAAAA,UAAA,GACrCgQ,EAAOxS,KACPiK,EAAUjK,KAAKwT,sBACf5P,EAAK,SAAAyN,GACM,YAAXA,GACFiC,EAAK9H,KAAK,uBAEN,iBAAmBgG,WACrBA,UAAUa,cAAcoB,SAASH,EAAK7C,YAAYiC,KAAK,WACrDlB,UAAUa,cAAcqB,MAAMhB,KAC5B,SAAUiB,GACRnB,EAAKhH,KAAK,mBACVmI,EAA0BC,YACvBC,WACCN,gBAAiBA,IAElBb,KAAK,SAAUR,GACd,GAAMlQ,GAAMkQ,EAAa4B,OAAO,UAC1BC,EAAQ7B,EAAa4B,OAAO,OAElCtB,GAAK9B,SACHyB,SAAUK,EAAKwB,YAAY9B,GAC3B+B,OAAQjS,EACJG,OAAO+R,KACLC,OAAOC,aAAahH,MAAM,KAAM,GAAIiH,YAAWrS,KAEjD,KACJsS,KAAMP,EACF5R,OAAO+R,KACLC,OAAOC,aAAahH,MAClB,KACA,GAAIiH,YAAWN,KAGnB,MAGNvB,EAAKhH,KAAK,yBAA0BgH,EAAK9B,YAE1C6D,MAAM,SAAUnB,GACfZ,EAAKhH,KAAK,iBAAkB4H,UAMtCZ,EAAKhH,KAAK,yBAEQ,WAAX6F,IACTiC,EAAK9H,KAAK,sBACV8H,EAAKkB,oBAIO,aAAZvK,EACE9H,OAAOmP,cAAgBnP,OAAOmP,aAAamD,kBAC7CtS,OAAOmP,aAAamD,kBAAkB7Q,GAEtCzB,OAAOoP,qBAAuBpP,OAAOoP,oBAAoBQ,iBAEzD5P,OAAOoP,oBAAoBkD,kBAAkB7Q,GAG/CA,EAAGqG,OL+5BAuG,MAKH,SAAU3Q,EAAQD,EAASM,IAEL,SAASwU,EAASnG;;;;;;;CMtmC9C,SAAAA,EAAA5O,GACAE,EAAAD,QAAAD,KAGCK,EAAA,WAAqB,YAEtB,SAAA2U,GAAAC,GACA,wBAAAA,IAAA,gBAAAA,IAAA,OAAAA,EAGA,QAAAC,GAAAD,GACA,wBAAAA,GAkCA,QAAAE,GAAAC,GACAC,EAAAD,EAGA,QAAAE,GAAAC,GACAC,EAAAD,EAqBA,QAAAE,KACA,gBAAAC,EACA,WACAA,EAAAC,IAIAC,IAuBA,QAAAA,KAGA,GAAAC,GAAA/P,UACA,mBACA,MAAA+P,GAAAF,EAAA,IAKA,QAAAA,KACA,OAAAjV,GAAA,EAAiBA,EAAAoV,EAASpV,GAAA,IAI1BqV,EAHA3L,EAAA1J,IACA0J,EAAA1J,EAAA,IAIA0J,EAAA1J,OAAAqC,GACAqH,EAAA1J,EAAA,OAAAqC,GAGA+S,EAAA,EA4BA,QAAA/C,GAAAiD,EAAAC,GACA,GAAAC,GAAArT,UAEAsT,EAAA9V,KAEA+V,EAAA,GAAA/V,MAAAgI,YAAAgO,OAEAtT,KAAAqT,EAAAE,KACAC,EAAAH,EAGA,IAAAI,GAAAL,EAAAK,MAaA,OAXAA,GACA,WACA,GAAAT,GAAAG,EAAAM,EAAA,EACAhB,GAAA,WACA,MAAAiB,GAAAD,EAAAJ,EAAAL,EAAAI,EAAAO,cAIAxC,EAAAiC,EAAAC,EAAAJ,EAAAC,GAGAG,EAkCA,QAAAO,GAAAhV,GAEA,GAAAkO,GAAAxP,IAEA,IAAAsB,GAAA,gBAAAA,MAAA0G,cAAAwH,EACA,MAAAlO,EAGA,IAAAiV,GAAA,GAAA/G,GAAAwG,EAEA,OADAQ,GAAAD,EAAAjV,GACAiV,EAKA,QAAAP,MAQA,QAAAS,KACA,UAAAhH,WAAA,4CAGA,QAAAiH,KACA,UAAAjH,WAAA,wDAGA,QAAAkH,GAAAJ,GACA,IACA,MAAAA,GAAA7D,KACG,MAAAkE,GAEH,MADAC,IAAAD,QACAC,IAIA,QAAAC,GAAApE,EAAAhS,EAAAqW,EAAAC,GACA,IACAtE,EAAAnS,KAAAG,EAAAqW,EAAAC,GACG,MAAApF,GACH,MAAAA,IAIA,QAAAqF,GAAAV,EAAAW,EAAAxE,GACAyC,EAAA,SAAAoB,GACA,GAAAY,IAAA,EACAP,EAAAE,EAAApE,EAAAwE,EAAA,SAAAxW,GACAyW,IAGAA,GAAA,EACAD,IAAAxW,EACA8V,EAAAD,EAAA7V,GAEA0W,EAAAb,EAAA7V,KAEK,SAAA2W,GACLF,IAGAA,GAAA,EAEAG,EAAAf,EAAAc,KACK,YAAAd,EAAAgB,QAAA,sBAELJ,GAAAP,IACAO,GAAA,EACAG,EAAAf,EAAAK,KAEGL,GAGH,QAAAiB,GAAAjB,EAAAW,GACAA,EAAAf,SAAAsB,GACAL,EAAAb,EAAAW,EAAAb,SACGa,EAAAf,SAAAuB,GACHJ,EAAAf,EAAAW,EAAAb,SAEAxC,EAAAqD,MAAAxU,GAAA,SAAAhC,GACA,MAAA8V,GAAAD,EAAA7V,IACK,SAAA2W,GACL,MAAAC,GAAAf,EAAAc,KAKA,QAAAM,GAAApB,EAAAqB,EAAAC,GACAD,EAAA5P,cAAAuO,EAAAvO,aAAA6P,IAAAnF,GAAAkF,EAAA5P,YAAAsO,YACAkB,EAAAjB,EAAAqB,GAEAC,IAAAhB,IACAS,EAAAf,EAAAM,GAAAD,OACAC,GAAAD,MAAA,UACKlU,KAAAmV,EACLT,EAAAb,EAAAqB,GACK/C,EAAAgD,GACLZ,EAAAV,EAAAqB,EAAAC,GAEAT,EAAAb,EAAAqB,GAKA,QAAApB,GAAAD,EAAA7V,GACA6V,IAAA7V,EACA4W,EAAAf,EAAAE,KACG9B,EAAAjU,GACHiX,EAAApB,EAAA7V,EAAAiW,EAAAjW,IAEA0W,EAAAb,EAAA7V,GAIA,QAAAoX,GAAAvB,GACAA,EAAAwB,UACAxB,EAAAwB,SAAAxB,EAAAF,SAGA2B,EAAAzB,GAGA,QAAAa,GAAAb,EAAA7V,GACA6V,EAAAJ,SAAA8B,KAIA1B,EAAAF,QAAA3V,EACA6V,EAAAJ,OAAAsB,GAEA,IAAAlB,EAAA2B,aAAAzV,QACA0S,EAAA6C,EAAAzB,IAIA,QAAAe,GAAAf,EAAAc,GACAd,EAAAJ,SAAA8B,KAGA1B,EAAAJ,OAAAuB,GACAnB,EAAAF,QAAAgB,EAEAlC,EAAA2C,EAAAvB,IAGA,QAAA1C,GAAAiC,EAAAC,EAAAJ,EAAAC,GACA,GAAAsC,GAAApC,EAAAoC,aACAzV,EAAAyV,EAAAzV,MAEAqT,GAAAiC,SAAA,KAEAG,EAAAzV,GAAAsT,EACAmC,EAAAzV,EAAAgV,IAAA9B,EACAuC,EAAAzV,EAAAiV,IAAA9B,EAEA,IAAAnT,GAAAqT,EAAAK,QACAhB,EAAA6C,EAAAlC,GAIA,QAAAkC,GAAAzB,GACA,GAAA4B,GAAA5B,EAAA2B,aACAE,EAAA7B,EAAAJ,MAEA,QAAAgC,EAAA1V,OAAA,CAQA,OAJAsT,OAAArT,GACAgT,MAAAhT,GACA2V,EAAA9B,EAAAF,QAEAhW,EAAA,EAAiBA,EAAA8X,EAAA1V,OAAwBpC,GAAA,EACzC0V,EAAAoC,EAAA9X,GACAqV,EAAAyC,EAAA9X,EAAA+X,GAEArC,EACAK,EAAAgC,EAAArC,EAAAL,EAAA2C,GAEA3C,EAAA2C,EAIA9B,GAAA2B,aAAAzV,OAAA,GAGA,QAAA6V,KACAtY,KAAA4W,MAAA,KAKA,QAAA2B,GAAA7C,EAAA2C,GACA,IACA,MAAA3C,GAAA2C,GACG,MAAAzG,GAEH,MADA4G,IAAA5B,MAAAhF,EACA4G,IAIA,QAAApC,GAAAgC,EAAA7B,EAAAb,EAAA2C,GACA,GAAAI,GAAA5D,EAAAa,GACAhV,MAAAgC,GACAkU,MAAAlU,GACAgW,MAAAhW,GACAiW,MAAAjW,EAEA,IAAA+V,GAWA,GAVA/X,EAAA6X,EAAA7C,EAAA2C,GAEA3X,IAAA8X,IACAG,GAAA,EACA/B,EAAAlW,EAAAkW,MACAlW,EAAAkW,MAAA,MAEA8B,GAAA,EAGAnC,IAAA7V,EAEA,WADA4W,GAAAf,EAAAG,SAIAhW,GAAA2X,EACAK,GAAA,CAGAnC,GAAAJ,SAAA8B,KAEGQ,GAAAC,EACHlC,EAAAD,EAAA7V,GACKiY,EACLrB,EAAAf,EAAAK,GACKwB,IAAAX,GACLL,EAAAb,EAAA7V,GACK0X,IAAAV,IACLJ,EAAAf,EAAA7V,IAIA,QAAAkY,GAAArC,EAAAsC,GACA,IACAA,EAAA,SAAAnY,GACA8V,EAAAD,EAAA7V,IACK,SAAA2W,GACLC,EAAAf,EAAAc,KAEG,MAAAzF,GACH0F,EAAAf,EAAA3E,IAKA,QAAAkH,KACA,MAAAnW,MAGA,QAAAuT,GAAAK,GACAA,EAAAN,IAAAtT,KACA4T,EAAAJ,WAAAzT,GACA6T,EAAAF,YAAA3T,GACA6T,EAAA2B,gBAGA,QAAAa,GAAAvJ,EAAAwJ,GACAhZ,KAAAiZ,qBAAAzJ,EACAxP,KAAAuW,QAAA,GAAA/G,GAAAwG,GAEAhW,KAAAuW,QAAAN,KACAC,EAAAlW,KAAAuW,SAGAjO,EAAA0Q,IACAhZ,KAAAkZ,OAAAF,EACAhZ,KAAAyC,OAAAuW,EAAAvW,OACAzC,KAAAmZ,WAAAH,EAAAvW,OAEAzC,KAAAqW,QAAA,GAAAhO,OAAArI,KAAAyC,QAEA,IAAAzC,KAAAyC,OACA2U,EAAApX,KAAAuW,QAAAvW,KAAAqW,UAEArW,KAAAyC,OAAAzC,KAAAyC,QAAA,EACAzC,KAAAoZ,aACA,IAAApZ,KAAAmZ,YACA/B,EAAApX,KAAAuW,QAAAvW,KAAAqW,WAIAiB,EAAAtX,KAAAuW,QAAA8C,KAIA,QAAAA,KACA,UAAAC,OAAA,2CAiHA,QAAAC,GAAAC,GACA,UAAAT,GAAA/Y,KAAAwZ,GAAAjD,QAoEA,QAAAkD,GAAAD,GAEA,GAAAhK,GAAAxP,IAEA,OAKA,IAAAwP,GALAlH,EAAAkR,GAKA,SAAAlD,EAAAoD,GAEA,OADAjX,GAAA+W,EAAA/W,OACApC,EAAA,EAAqBA,EAAAoC,EAAYpC,IACjCmP,EAAA8G,QAAAkD,EAAAnZ,IAAAqS,KAAA4D,EAAAoD,IAPA,SAAAC,EAAAD,GACA,MAAAA,GAAA,GAAAjK,WAAA,sCA8CA,QAAAiK,GAAArC,GAEA,GAAA7H,GAAAxP,KACAuW,EAAA,GAAA/G,GAAAwG,EAEA,OADAsB,GAAAf,EAAAc,GACAd,EAGA,QAAAqD,KACA,SAAAnK,WAAA,sFAGA,QAAAoK,KACA,SAAApK,WAAA,yHA0GA,QAAAqK,GAAAjB,GACA7Y,KAAAiW,IAAA6C,IACA9Y,KAAAqW,QAAArW,KAAAmW,WAAAzT,GACA1C,KAAAkY,gBAEAlC,IAAA6C,IACA,kBAAAA,IAAAe,IACA5Z,eAAA8Z,GAAAlB,EAAA5Y,KAAA6Y,GAAAgB,KAkPA,QAAAE,KACA,GAAAC,OAAAtX,EAEA,aAAA6L,EACAyL,EAAAzL,MACK,uBAAAiE,MACLwH,EAAAxH,SAEA,KACAwH,EAAAC,SAAA,iBACS,MAAArI,GACT,SAAA0H,OAAA,4EAIA,GAAAY,GAAAF,EAAAF,OAEA,IAAAI,EAAA,CACA,GAAAC,GAAA,IACA,KACAA,EAAApZ,OAAAS,UAAAwB,SAAAzC,KAAA2Z,EAAA5D,WACS,MAAA1E,IAIT,wBAAAuI,IAAAD,EAAAE,KACA,OAIAJ,EAAAF,UArmCA,GAAAO,OAAA3X,EAMA2X,GALAhS,MAAAC,QAKAD,MAAAC,QAJA,SAAAsM,GACA,yBAAA7T,OAAAS,UAAAwB,SAAAzC,KAAAqU,GAMA,IAAAtM,GAAA+R,EAEA5E,EAAA,EACAJ,MAAA3S,GACAsS,MAAAtS,GAEAyS,EAAA,SAAAO,EAAA4E,GACAvQ,EAAA0L,GAAAC,EACA3L,EAAA0L,EAAA,GAAA6E,EAEA,KADA7E,GAAA,KAKAT,EACAA,EAAAM,GAEAiF,MAaAC,EAAA,mBAAArY,mBAAAO,GACA+X,EAAAD,MACAE,EAAAD,EAAAE,kBAAAF,EAAAG,uBACAC,EAAA,mBAAArI,WAAA,KAAAkC,GAAiF,wBAAA1R,SAAAzC,KAAAmU,GAGjFoG,EAAA,mBAAAC,oBAAA,mBAAAC,gBAAA,mBAAAC,gBAmDAlR,EAAA,GAAA1B,OAAA,KA0BAkS,MAAA7X,EAGA6X,GADAM,EA5EA,WAGA,kBACA,MAAAnG,GAAAwG,SAAA5F,OA0ECoF,EA3DD,WACA,GAAAS,GAAA,EACAC,EAAA,GAAAV,GAAApF,GACA+F,EAAAtX,SAAAuX,eAAA,GAGA,OAFAF,GAAAG,QAAAF,GAA0BG,eAAA,IAE1B,WACAH,EAAAI,KAAAN,MAAA,MAsDCL,EAjDD,WACA,GAAAY,GAAA,GAAAT,eAEA,OADAS,GAAAC,MAAAC,UAAAtG,EACA,WACA,MAAAoG,GAAAG,MAAAC,YAAA,WA+CCpZ,KAAA8X,EAnBD,WACA,IACA,GACAuB,GAAA7b,EAAA,EAEA,OADAmV,GAAA0G,EAAAC,WAAAD,EAAAE,aACA7G,IACG,MAAAxD,GACH,MAAA2D,SAeAA,GA0EA,IAAAU,IAAAnT,KAAAC,SAAAC,SAAA,IAAA0B,UAAA,IAIAuT,OAAA,GACAR,GAAA,EACAC,GAAA,EAEAb,GAAA,GAAAyB,GA6KAE,GAAA,GAAAF,GA+DA3V,GAAA,CAyqBA,OA5nBAoW,GAAAvX,UAAA4X,WAAA,WAIA,OAHA3W,GAAAzC,KAAAyC,OACAyW,EAAAlZ,KAAAkZ,OAEA7Y,EAAA,EAAiBL,KAAAmW,SAAA8B,IAAA5X,EAAAoC,EAAuCpC,IACxDL,KAAAkc,WAAAhD,EAAA7Y,OAIA0Y,EAAAvX,UAAA0a,WAAA,SAAAC,EAAA9b,GACA,GAAAI,GAAAT,KAAAiZ,qBACAmD,EAAA3b,EAAA6V,OAEA,IAAA8F,IAAA9F,EAAA,CACA,GAAA+F,GAAA1F,EAAAwF,EAEA,IAAAE,IAAA3J,GAAAyJ,EAAAhG,SAAA8B,GACAjY,KAAAsc,WAAAH,EAAAhG,OAAA9V,EAAA8b,EAAA9F,aACK,sBAAAgG,GACLrc,KAAAmZ,aACAnZ,KAAAqW,QAAAhW,GAAA8b,MACK,IAAA1b,IAAAqZ,EAAA,CACL,GAAAvD,GAAA,GAAA9V,GAAAuV,EACA2B,GAAApB,EAAA4F,EAAAE,GACArc,KAAAuc,cAAAhG,EAAAlW,OAEAL,MAAAuc,cAAA,GAAA9b,GAAA,SAAA2b,GACA,MAAAA,GAAAD,KACO9b,OAGPL,MAAAuc,cAAAH,EAAAD,GAAA9b,IAIA0Y,EAAAvX,UAAA8a,WAAA,SAAA/J,EAAAlS,EAAAK,GACA,GAAA6V,GAAAvW,KAAAuW,OAEAA,GAAAJ,SAAA8B,KACAjY,KAAAmZ,aAEA5G,IAAAmF,GACAJ,EAAAf,EAAA7V,GAEAV,KAAAqW,QAAAhW,GAAAK,GAIA,IAAAV,KAAAmZ,YACA/B,EAAAb,EAAAvW,KAAAqW,UAIA0C,EAAAvX,UAAA+a,cAAA,SAAAhG,EAAAlW,GACA,GAAAmc,GAAAxc,IAEA6T,GAAA0C,MAAA7T,GAAA,SAAAhC,GACA,MAAA8b,GAAAF,WAAA7E,GAAApX,EAAAK,IACG,SAAA2W,GACH,MAAAmF,GAAAF,WAAA5E,GAAArX,EAAAgX,MA8SAyC,EAAAP,MACAO,EAAAL,OACAK,EAAAxD,UACAwD,EAAAJ,SACAI,EAAA2C,cAAA3H,EACAgF,EAAA4C,SAAAzH,EACA6E,EAAA6C,MAAAxH,EAEA2E,EAAAtY,WACAwG,YAAA8R,EAmMApH,OA6BA6B,MAAA,SAAAqB,GACA,MAAA5V,MAAA0S,KAAA,KAAAkD,KAsCAkE,EAAAC,WACAD,YAEAA,MNmnC6BvZ,KAAKX,EAASM,EAAoB,GAAIA,EAAoB,KAIjF,SAAUL,EAAQD,KAMlB,SAAUC,EAAQD,EAASM,GAEjC,YA2BA,SAAS0c,GAAwB7U,GAAO,GAAIA,GAAOA,EAAI1G,WAAc,MAAO0G,EAAc,IAAIG,KAAa,IAAW,MAAPH,EAAe,IAAK,GAAI/F,KAAO+F,GAAWhH,OAAOS,UAAUC,eAAelB,KAAKwH,EAAK/F,KAAMkG,EAAOlG,GAAO+F,EAAI/F,GAAgC,OAAtBkG,GAAOC,QAAUJ,EAAYG,EAIlQ,QAASoH,GAAgBC,EAAUC,GAAe,KAAMD,YAAoBC,IAAgB,KAAM,IAAIC,WAAU,qCA5BhH1O,OAAOC,eAAepB,EAAS,cAC7Bc,OAAO,GAGT,IAAIsP,GAAe,WAAc,QAASC,GAAiBC,EAAQC,GAAS,IAAK,GAAI9P,GAAI,EAAGA,EAAI8P,EAAM1N,OAAQpC,IAAK,CAAE,GAAI+P,GAAaD,EAAM9P,EAAI+P,GAAWlP,WAAakP,EAAWlP,aAAc,EAAOkP,EAAWnP,cAAe,EAAU,SAAWmP,KAAYA,EAAWC,UAAW,GAAMtP,OAAOC,eAAekP,EAAQE,EAAWpO,IAAKoO,IAAiB,MAAO,UAAUZ,EAAac,EAAYC,GAAiJ,MAA9HD,IAAYL,EAAiBT,EAAYhO,UAAW8O,GAAiBC,GAAaN,EAAiBT,EAAae,GAAqBf,KOtwEhiBtP,GAAA,EACA,IAAA2c,GAAA3c,EAAA,GP2wEI4c,EAgBJ,SAAgC/U,GAAO,MAAOA,IAAOA,EAAI1G,WAAa0G,GAAQI,QAASJ,IAhB7C8U,GO1wE1C/O,EAAA5N,EAAA,GAAYyK,EP8wEAiS,EAAwB9O,GO7wEpC7F,EAAA/H,EAAA,GAAY+E,EPixEF2X,EAAwB3U,GOhxElC8U,EAAA7c,EAAA,GACA8c,EAAA9c,EAAA,GAEqB+c,EPyxEV,WOpxET,QAAAA,KAA2B,GAAdpX,GAAcrD,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,KAqCzB,OArCyB8M,GAAAtP,KAAAid,GACzBjd,KAAK6F,QAAU8E,EAAMjD,cAAezC,EAAI4I,SAAUhI,GAClD7F,KAAK2C,GAAK3C,KAAK6F,QAAQlD,IAAMgI,EAAMrI,WAAW,OAC9CtC,KAAK+M,YAAc,EACnB/M,KAAKiH,OAAS,KACdjH,KAAKqM,UAAY,KACjBrM,KAAK2M,YAAc,KACnB3M,KAAKkd,SAAU,EACfld,KAAKmd,OAAQ,EACbnd,KAAKgK,QAAS,EACdhK,KAAKuN,SAAU,EACfvN,KAAKod,SAAWpd,KAAK6F,QAAQ4G,SAAWzM,KAAK6F,QAAQ2I,UAAU/L,OAAS,EACxEzC,KAAKuG,SAAWvG,KAAK6F,QAAQa,OAAOC,QAAQlE,OAAS,EACrDzC,KAAKoH,aAAc,EACnBpH,KAAKmN,WACH2B,cACAC,UACAC,aACAC,WACAC,cACAC,WACAC,WACAC,eAEFrP,KAAKqd,UACH7S,KAAM,KACNwC,MAAO,MAEThN,KAAKsd,GAAG,aAActd,KAAK6F,QAAQgJ,UAAUC,YAC7C9O,KAAKsd,GAAG,SAAUtd,KAAK6F,QAAQgJ,UAAUE,QACzC/O,KAAKsd,GAAG,YAAatd,KAAK6F,QAAQgJ,UAAUG,WAC5ChP,KAAKsd,GAAG,UAAWtd,KAAK6F,QAAQgJ,UAAUI,SAC1CjP,KAAKsd,GAAG,aAActd,KAAK6F,QAAQgJ,UAAUK,YAC7ClP,KAAKsd,GAAG,UAAWtd,KAAK6F,QAAQgJ,UAAUM,SAC1CnP,KAAKsd,GAAG,UAAWtd,KAAK6F,QAAQgJ,UAAUO,SAC1CpP,KAAKsd,GAAG,aAActd,KAAK6F,QAAQgJ,UAAUQ,YAEtCrP,KPytFT,MAlbAgQ,GAAaiN,IACXjb,IAAK,KACLtB,MAAO,SOjyELwM,GAA0B,GAAftJ,GAAepB,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAAV,YAKlB,OAJkB,kBAAPoB,IAAqB5D,KAAKmN,UAAU1L,eAAeyL,IAC5DlN,KAAKmN,UAAUD,GAAW/C,KAAKvG,GAG1B5D,QP2yEPgC,IAAK,OACLtB,MAAO,WOtyED,GAAAmP,GAAA7P,MACsB,IAAxBA,KAAK6F,QAAQ+I,OACfqO,EAAKM,WACmC,gBAAxBvd,MAAK6F,QAAQ+I,QAC7BqO,EAAKM,SAASvd,KAAK6F,QAAQ+I,OAG7B,IAAI4O,GAAcvY,EAAIuE,eAAexJ,KAAK6F,QAAQkE,MAElD,IACEyT,EAAYvT,SAAWuT,EAAY1T,YAClC7E,EAAIC,YAAclF,KAAK6F,QAAQC,kBAmBhC,MAjBAb,GAAIiF,WAAWlK,MAGbiF,EAAIC,YACJlF,KAAKuG,UACLoE,EAAM/I,QAAQ,YAAa5B,KAAK6F,QAAQa,OAAOgH,aAE/C/C,EAAMtE,oBAAoBrG,MAI1BiF,EAAIC,YACJyF,EAAM/I,QAAQ,YAAa5B,KAAK6F,QAAQ4H,WAAWC,aAEnDzI,EAAI0I,SAASQ,YAGRnO,IAST,IANAiF,EAAIU,MAAM3F,KAAK2C,IAAM3C,KAErBiF,EAAIuG,KAAKxL,KAAM,cAEfA,KAAKkd,SAAU,EAEXld,KAAKuN,QAEP,MADAvN,MAAKkd,SAAU,EACRld,IAsET,IAnEAiF,EAAIgG,MAAMjL,MACViF,EAAI4G,YAAY7L,MAEZA,KAAK6F,QAAQ8I,MACf3O,KAAKqM,UAAUH,aAAalM,KAAKiH,OAAQjH,KAAKqM,UAAUF,YAExDnM,KAAKqM,UAAUrF,YAAYhH,KAAKiH,QAIhCjH,KAAKuG,WACJvG,KAAKoH,aACNuD,EAAM/I,QAAQ,aAAc5B,KAAK6F,QAAQa,OAAOgH,aAEhD/C,EAAMtE,oBAAoBrG,MAGxB2K,EAAM/I,QAAQ,aAAc5B,KAAK6F,QAAQ4H,WAAWC,aACtDzI,EAAI0I,SAASQ,YAGfnO,KAAKmd,OAAQ,EACbnd,KAAKgK,QAAS,EAGV/E,EAAIwG,WAAWzL,OACjBe,OAAO2E,KAAK1F,KAAK6F,QAAQ6F,SAAS9F,QAAQ,SAAA5D,GACxC,GAAM4J,GAAMiE,EAAK5I,OAAOC,cAAZ,IACN2I,EAAKhK,QAAQ6F,QAAQ1J,GAAKW,GAEhCgI,GAAMjH,YAAYkI,EAAK,QAAS,SAAAgG,GAC9BjH,EAAM1I,gBAAgB2P,GACtB/B,EAAKhK,QAAQ6F,QAAQ1J,GAAK4B,SAKhC5D,KAAK2M,YAAc3M,KAAKiH,OAAOC,cAAc,qBAEzCyD,EAAM/I,QAAQ,QAAS5B,KAAK6F,QAAQ2I,aACtC7D,EAAMrG,SAAStE,KAAKiH,OAAQ,yBAC5B0D,EAAMjH,YACJ1D,KAAKiH,OACL,QACA,SAAA2K,GACEjH,EAAM1I,gBAAgB2P,GACtB3M,EAAIuG,KAAJqE,EAAe,WACfA,EAAK7C,UAEP,IAIJrC,EAAMjH,YACJ1D,KAAKiH,OACL,aACA,WACEhC,EAAIuG,KAAJqE,EAAe,aAEjB,GAGE7P,KAAK6F,QAAQ4G,SAAS9B,EAAMrG,SAAStE,KAAKiH,OAAQ,oBAClDjH,KAAK6F,QAAQ6G,aACf/B,EAAMrG,SAAStE,KAAKiH,OAAQ,wBAG1B0D,EAAM/I,QAAQ,SAAU5B,KAAK6F,QAAQ2I,WAAY,CACnD7D,EAAMrG,SAAStE,KAAKiH,OAAQ,yBAE5B,IAAMwW,GAAc1Z,SAAS0C,cAAc,MAC3CkE,GAAMrG,SAASmZ,EAAa,qBAC5BA,EAAYlS,UAAY,IACxBvL,KAAKiH,OAAOD,YAAYyW,GAExB9S,EAAMjH,YACJ+Z,EACA,QACA,SAAA7L,GACEjH,EAAM1I,gBAAgB2P,GACtB/B,EAAK7C,UAEP,GAgCJ,MA5BA/H,GAAIuG,KAAKxL,KAAM,UAEqB,OAAhCA,KAAK6F,QAAQ4I,UAAUC,KACzB1O,KAAKqd,SAAS7S,KAAO,GAAAsS,GAAA3U,QAAY,SAAAmO,GAC/BA,MAE8C,kBAAhCtW,MAAK6F,QAAQ4I,UAAUC,KACvC1O,KAAKqd,SAAS7S,KAAO,GAAAsS,GAAA3U,QAAYnI,KAAK6F,QAAQ4I,UAAUC,KAAKgP,KAAK1d,QAElE2K,EAAMrG,SAAStE,KAAKiH,OAAQjH,KAAK6F,QAAQ4I,UAAUC,MACnD1O,KAAKqd,SAAS7S,KAAO,GAAAsS,GAAA3U,QAAY,SAAAmO,GAC/B3L,EAAMjH,YAAYmM,EAAK5I,OAAQ0D,EAAMhD,mBAAoB,WACvDgD,EAAMhG,YAAYkL,EAAK5I,OAAQ4I,EAAKhK,QAAQ4I,UAAUC,MACtD4H,SAKNtW,KAAKqd,SAAS7S,KAAKkI,KAAK,WACtB,GAAMiL,IACNlY,YACE,WACER,EAAIoI,SAASsQ,IAEf,OAIG3d,QP8wEPgC,IAAK,OACLtB,MAAO,WOvwEP,MADAuE,GAAIgI,aAAajN,MACVA,QPixEPgC,IAAK,SACLtB,MAAO,WO1wEP,MADAuE,GAAIuH,WAAWxM,MACRA,QPqxEPgC,IAAK,aACLtB,MAAO,SAAUkd,GACf,QAASnY,GAAWoY,GAClB,MAAOD,GAAYxQ,MAAMpN,KAAMwC,WAOjC,MAJAiD,GAAWzC,SAAW,WACpB,MAAO4a,GAAY5a,YAGdyC,GACP,SOzxEQqY,GAIV,GAHA9d,KAAK+F,OACL/F,KAAK6F,QAAQ4G,QAAUqR,EAEnB9d,KAAKiH,OAAQ,CACXjH,KAAK6F,QAAQ4G,QACf9B,EAAMrG,SAAStE,KAAKiH,OAAQ,oBAE5B0D,EAAMhG,YAAY3E,KAAKiH,OAAQ,mBAGjC,IAAM0W,GAAK3d,IACXyF,YACE,WAEEkY,EAAG3X,UAEL,KAIJ,MAAOhG,UPgyEPgC,IAAK,UACLtB,MAAO,SOzxEAiP,GAA+B,GAAzBoO,GAAyBvb,UAAAC,OAAA,OAAAC,KAAAF,UAAA,IAAAA,UAAA,EAOtC,OANIxC,MAAKiH,SACPjH,KAAKiH,OAAOC,cAAc,cAAcqE,UAAYoE,GAGlDoO,IAAiB/d,KAAK6F,QAAQuF,KAAOuE,GAElC3P,QPqyEPgC,IAAK,UACLtB,MAAO,SO9xEAoG,GAA+B,GAAAwM,GAAAtT,KAAzB+d,EAAyBvb,UAAAC,OAAA,OAAAC,KAAAF,UAAA,IAAAA,UAAA,EACtC,IAAIxC,KAAKiH,OAAQ,CACC0D,EAAMvG,UAAUpE,KAAKiH,QAAQnD,MAAM,KAEzC8B,QAAQ,SAAAnF,GACW,gBAAvBA,EAAEiE,UAAU,EAAG,KACjBiG,EAAMhG,YAAY2O,EAAKrM,OAAQxG,KAInCkK,EAAMrG,SAAStE,KAAKiH,OAApB,cAA0CH,GAK5C,MAFIiX,KAAiB/d,KAAK6F,QAAQiB,KAAOA,GAElC9G,QP4yEPgC,IAAK,WACLtB,MAAO,SOryEC4K,GAAgC,GAAA0S,GAAAhe,KAAzB+d,EAAyBvb,UAAAC,OAAA,OAAAC,KAAAF,UAAA,IAAAA,UAAA,EACxC,IAAIxC,KAAKiH,OAAQ,CACC0D,EAAMvG,UAAUpE,KAAKiH,QAAQnD,MAAM,KAEzC8B,QAAQ,SAAAnF,GACW,iBAAvBA,EAAEiE,UAAU,EAAG,KACjBiG,EAAMhG,YAAYqZ,EAAK/W,OAAQxG,KAInCkK,EAAMrG,SAAStE,KAAKiH,OAApB,eAA2CqE,GAK7C,MAFIyS,KAAiB/d,KAAK6F,QAAQyF,MAAQA,GAEnCtL,QPizEPgC,IAAK,QACLtB,MAAO,WO5yEA,GAAAud,GAAAje,IACP,OAAIA,MAAKgK,OAAehK,KAEnBA,KAAKmd,OAMVlY,EAAIuG,KAAKxL,KAAM,WAEfA,KAAKuN,SAAU,EAEsB,OAAjCvN,KAAK6F,QAAQ4I,UAAUzB,MACzBhN,KAAKqd,SAASrQ,MAAQ,GAAA8P,GAAA3U,QAAY,SAAAmO,GAChCA,MAE+C,kBAAjCtW,MAAK6F,QAAQ4I,UAAUzB,MACvChN,KAAKqd,SAASrQ,MAAQ,GAAA8P,GAAA3U,QACpBnI,KAAK6F,QAAQ4I,UAAUzB,MAAM0Q,KAAK1d,QAGpC2K,EAAMrG,SAAStE,KAAKiH,OAAQjH,KAAK6F,QAAQ4I,UAAUzB,OACnDhN,KAAKqd,SAASrQ,MAAQ,GAAA8P,GAAA3U,QAAY,SAAAmO,GAChC3L,EAAMjH,YAAYua,EAAKhX,OAAQ0D,EAAMhD,mBAAoB,WACnDsW,EAAKpY,QAAQ8I,MACfhE,EAAM/F,OAAOqZ,EAAKhX,QAElBhC,EAAIwF,SAAJwT,GAEF3H,SAKNtW,KAAKqd,SAASrQ,MAAM0F,KAAK,WACvBzN,EAAIqI,UAAJ2Q,GACAhZ,EAAIgH,iBAAJgS,KAGFje,KAAKgK,QAAS,EAEPhK,OArCLiF,EAAImF,gBAAgBpK,MACbA,WP21ETgC,IAAK,WACLtB,MAAO,WO/yE2B,GAAnB+I,GAAmBjH,UAAAC,OAAA,OAAAC,KAAAF,UAAA,IAAAA,UAAA,EAYlC,OAXAzB,QAAO2E,KAAKT,EAAIU,OAAOC,QAAQ,SAAAjD,GACzB8G,EAEAxE,EAAIU,MAAMhD,GAAIkD,QAAQkE,QAAUN,GAAaxE,EAAIU,MAAMhD,GAAIya,UAE3DnY,EAAIU,MAAMhD,GAAIqK,QAEP/H,EAAIU,MAAMhD,GAAIya,UACvBnY,EAAIU,MAAMhD,GAAIqK,UAGXhN,QPwzEPgC,IAAK,mBACLtB,MAAO,SOlzEgBqH,GAEvB,MADA9C,GAAI4I,SAAWlD,EAAMjD,cAAezC,EAAI4I,SAAU9F,GAC3C/H,QP4zEPgC,IAAK,gBACLtB,MAAO,WOrzEmE,GAAtDwd,GAAsD1b,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAA7CyC,EAAI2E,kBAAmBH,EAAsBjH,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAAV,QAMhE,OALKyC,GAAI4E,OAAOpI,eAAegI,KAC7BxE,EAAI4E,OAAOJ,IAAcK,WAAYoU,EAAQnU,WAG/C9E,EAAI4E,OAAOJ,GAAWK,WAAaoU,EAC5Ble,QPo0EPgC,IAAK,SACLtB,MAAO,SO3zEMyd,GAAgD,GAArCvO,GAAqCpN,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,GAA3B,KAAMoB,EAAqBpB,UAAA,GAAjBsN,EAAiBtN,UAAAC,OAAA,OAAAC,KAAAF,UAAA,GAAAA,UAAA,KAC7D,OAAO,IAAAua,GAAArN,WAAeyO,EAAWvO,EAAShM,EAAIkM,MPu0E9C9N,IAAK,UACLtB,MAAO,WOj0EP,MAAO,WP20EPsB,IAAK,OACLtB,MAAO,SOr0EI+P,GACX,MAAO,IAAAuM,GAAAxM,KAASC,OPy0EXwM,IAMTrd,GAAQuI,QOzwFa8U,EA+brBtS,EAAM5F,uBP40ENlF,EAAOD,QAAUA,EAAiB,SAI5B,SAAUC,EAAQD,GQ7wFxB,QAAAwe,KACA,SAAA9E,OAAA,mCAEA,QAAA+E,KACA,SAAA/E,OAAA,qCAsBA,QAAAgF,GAAAC,GACA,GAAAC,IAAA/Y,WAEA,MAAAA,YAAA8Y,EAAA,EAGA,KAAAC,IAAAJ,IAAAI,IAAA/Y,WAEA,MADA+Y,GAAA/Y,WACAA,WAAA8Y,EAAA,EAEA,KAEA,MAAAC,GAAAD,EAAA,GACK,MAAA3M,GACL,IAEA,MAAA4M,GAAAje,KAAA,KAAAge,EAAA,GACS,MAAA3M,GAET,MAAA4M,GAAAje,KAAAP,KAAAue,EAAA,KAMA,QAAAE,GAAAC,GACA,GAAAC,IAAA7R,aAEA,MAAAA,cAAA4R,EAGA,KAAAC,IAAAN,IAAAM,IAAA7R,aAEA,MADA6R,GAAA7R,aACAA,aAAA4R,EAEA,KAEA,MAAAC,GAAAD,GACK,MAAA9M,GACL,IAEA,MAAA+M,GAAApe,KAAA,KAAAme,GACS,MAAA9M,GAGT,MAAA+M,GAAApe,KAAAP,KAAA0e,KAYA,QAAAE,KACAC,GAAAC,IAGAD,GAAA,EACAC,EAAArc,OACAsH,EAAA+U,EAAAC,OAAAhV,GAEAiV,GAAA,EAEAjV,EAAAtH,QACAwc,KAIA,QAAAA,KACA,IAAAJ,EAAA,CAGA,GAAApS,GAAA6R,EAAAM,EACAC,IAAA,CAGA,KADA,GAAApJ,GAAA1L,EAAAtH,OACAgT,GAAA,CAGA,IAFAqJ,EAAA/U,EACAA,OACAiV,EAAAvJ,GACAqJ,GACAA,EAAAE,GAAAE,KAGAF,IAAA,EACAvJ,EAAA1L,EAAAtH,OAEAqc,EAAA,KACAD,GAAA,EACAJ,EAAAhS,IAiBA,QAAA0S,GAAAZ,EAAAa,GACApf,KAAAue,MACAve,KAAAof,QAYA,QAAApJ,MAhKA,GAOAwI,GACAG,EARAjK,EAAA7U,EAAAD,YAgBA,WACA,IAEA4e,EADA,kBAAA/Y,YACAA,WAEA2Y,EAEK,MAAAxM,GACL4M,EAAAJ,EAEA,IAEAO,EADA,kBAAA7R,cACAA,aAEAuR,EAEK,MAAAzM,GACL+M,EAAAN,KAuDA,IAEAS,GAFA/U,KACA8U,GAAA,EAEAG,GAAA,CAyCAtK,GAAAwG,SAAA,SAAAqD,GACA,GAAAhV,GAAA,GAAAlB,OAAA7F,UAAAC,OAAA,EACA,IAAAD,UAAAC,OAAA,EACA,OAAApC,GAAA,EAAuBA,EAAAmC,UAAAC,OAAsBpC,IAC7CkJ,EAAAlJ,EAAA,GAAAmC,UAAAnC,EAGA0J,GAAAI,KAAA,GAAAgV,GAAAZ,EAAAhV,IACA,IAAAQ,EAAAtH,QAAAoc,GACAP,EAAAW,IASAE,EAAA3d,UAAA0d,IAAA,WACAlf,KAAAue,IAAAnR,MAAA,KAAApN,KAAAof,QAEA1K,EAAApG,MAAA,UACAoG,EAAA2K,SAAA,EACA3K,EAAA4K,OACA5K,EAAA6K,QACA7K,EAAA8K,QAAA,GACA9K,EAAA+K,YAIA/K,EAAA4I,GAAAtH,EACAtB,EAAAhR,YAAAsS,EACAtB,EAAAgL,KAAA1J,EACAtB,EAAAiL,IAAA3J,EACAtB,EAAAkL,eAAA5J,EACAtB,EAAAmL,mBAAA7J,EACAtB,EAAAoL,KAAA9J,EACAtB,EAAAqL,gBAAA/J,EACAtB,EAAAsL,oBAAAhK,EAEAtB,EAAAvH,UAAA,SAAAvM,GAAqC,UAErC8T,EAAAuL,QAAA,SAAArf,GACA,SAAA0Y,OAAA,qCAGA5E,EAAAwL,IAAA,WAA2B,WAC3BxL,EAAAyL,MAAA,SAAAC,GACA,SAAA9G,OAAA,mCAEA5E,EAAA2L,MAAA,WAA4B,WR+xFtB,SAAUxgB,EAAQD,GSt9FxB,GAAA0gB,EAGAA,GAAA,WACA,MAAAtgB,QAGA,KAEAsgB,KAAArG,SAAA,qBAAAsG,MAAA,QACC,MAAA3O,GAED,gBAAAzP,UACAme,EAAAne,QAOAtC,EAAAD,QAAA0gB,GT69FM,SAAUzgB,EAAQD", "file": "noty.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Noty\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Noty\"] = factory();\n\telse\n\t\troot[\"Noty\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Noty\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Noty\"] = factory();\n\telse\n\t\troot[\"Noty\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 6);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.css = exports.deepExtend = exports.animationEndEvents = undefined;\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.inArray = inArray;\nexports.stopPropagation = stopPropagation;\nexports.generateID = generateID;\nexports.outerHeight = outerHeight;\nexports.addListener = addListener;\nexports.hasClass = hasClass;\nexports.addClass = addClass;\nexports.removeClass = removeClass;\nexports.remove = remove;\nexports.classList = classList;\nexports.visibilityChangeFlow = visibilityChangeFlow;\nexports.createAudioElements = createAudioElements;\n\nvar _api = __webpack_require__(1);\n\nvar API = _interopRequireWildcard(_api);\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nvar animationEndEvents = exports.animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend';\n\nfunction inArray(needle, haystack, argStrict) {\n  var key = void 0;\n  var strict = !!argStrict;\n\n  if (strict) {\n    for (key in haystack) {\n      if (haystack.hasOwnProperty(key) && haystack[key] === needle) {\n        return true;\n      }\n    }\n  } else {\n    for (key in haystack) {\n      if (haystack.hasOwnProperty(key) && haystack[key] === needle) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nfunction stopPropagation(evt) {\n  evt = evt || window.event;\n\n  if (typeof evt.stopPropagation !== 'undefined') {\n    evt.stopPropagation();\n  } else {\n    evt.cancelBubble = true;\n  }\n}\n\nvar deepExtend = exports.deepExtend = function deepExtend(out) {\n  out = out || {};\n\n  for (var i = 1; i < arguments.length; i++) {\n    var obj = arguments[i];\n\n    if (!obj) continue;\n\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        if (Array.isArray(obj[key])) {\n          out[key] = obj[key];\n        } else if (_typeof(obj[key]) === 'object' && obj[key] !== null) {\n          out[key] = deepExtend(out[key], obj[key]);\n        } else {\n          out[key] = obj[key];\n        }\n      }\n    }\n  }\n\n  return out;\n};\n\nfunction generateID() {\n  var prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n\n  var id = 'noty_' + prefix + '_';\n\n  id += 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = Math.random() * 16 | 0;\n    var v = c === 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n\n  return id;\n}\n\nfunction outerHeight(el) {\n  var height = el.offsetHeight;\n  var style = window.getComputedStyle(el);\n\n  height += parseInt(style.marginTop) + parseInt(style.marginBottom);\n  return height;\n}\n\nvar css = exports.css = function () {\n  var cssPrefixes = ['Webkit', 'O', 'Moz', 'ms'];\n  var cssProps = {};\n\n  function camelCase(string) {\n    return string.replace(/^-ms-/, 'ms-').replace(/-([\\da-z])/gi, function (match, letter) {\n      return letter.toUpperCase();\n    });\n  }\n\n  function getVendorProp(name) {\n    var style = document.body.style;\n    if (name in style) return name;\n\n    var i = cssPrefixes.length;\n    var capName = name.charAt(0).toUpperCase() + name.slice(1);\n    var vendorName = void 0;\n\n    while (i--) {\n      vendorName = cssPrefixes[i] + capName;\n      if (vendorName in style) return vendorName;\n    }\n\n    return name;\n  }\n\n  function getStyleProp(name) {\n    name = camelCase(name);\n    return cssProps[name] || (cssProps[name] = getVendorProp(name));\n  }\n\n  function applyCss(element, prop, value) {\n    prop = getStyleProp(prop);\n    element.style[prop] = value;\n  }\n\n  return function (element, properties) {\n    var args = arguments;\n    var prop = void 0;\n    var value = void 0;\n\n    if (args.length === 2) {\n      for (prop in properties) {\n        if (properties.hasOwnProperty(prop)) {\n          value = properties[prop];\n          if (value !== undefined && properties.hasOwnProperty(prop)) {\n            applyCss(element, prop, value);\n          }\n        }\n      }\n    } else {\n      applyCss(element, args[1], args[2]);\n    }\n  };\n}();\n\nfunction addListener(el, events, cb) {\n  var useCapture = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n  events = events.split(' ');\n  for (var i = 0; i < events.length; i++) {\n    if (document.addEventListener) {\n      el.addEventListener(events[i], cb, useCapture);\n    } else if (document.attachEvent) {\n      el.attachEvent('on' + events[i], cb);\n    }\n  }\n}\n\nfunction hasClass(element, name) {\n  var list = typeof element === 'string' ? element : classList(element);\n  return list.indexOf(' ' + name + ' ') >= 0;\n}\n\nfunction addClass(element, name) {\n  var oldList = classList(element);\n  var newList = oldList + name;\n\n  if (hasClass(oldList, name)) return;\n\n  // Trim the opening space.\n  element.className = newList.substring(1);\n}\n\nfunction removeClass(element, name) {\n  var oldList = classList(element);\n  var newList = void 0;\n\n  if (!hasClass(element, name)) return;\n\n  // Replace the class name.\n  newList = oldList.replace(' ' + name + ' ', ' ');\n\n  // Trim the opening and closing spaces.\n  element.className = newList.substring(1, newList.length - 1);\n}\n\nfunction remove(element) {\n  if (element.parentNode) {\n    element.parentNode.removeChild(element);\n  }\n}\n\nfunction classList(element) {\n  return (' ' + (element && element.className || '') + ' ').replace(/\\s+/gi, ' ');\n}\n\nfunction visibilityChangeFlow() {\n  var hidden = void 0;\n  var visibilityChange = void 0;\n  if (typeof document.hidden !== 'undefined') {\n    // Opera 12.10 and Firefox 18 and later support\n    hidden = 'hidden';\n    visibilityChange = 'visibilitychange';\n  } else if (typeof document.msHidden !== 'undefined') {\n    hidden = 'msHidden';\n    visibilityChange = 'msvisibilitychange';\n  } else if (typeof document.webkitHidden !== 'undefined') {\n    hidden = 'webkitHidden';\n    visibilityChange = 'webkitvisibilitychange';\n  }\n\n  function onVisibilityChange() {\n    API.PageHidden = document[hidden];\n    handleVisibilityChange();\n  }\n\n  function onBlur() {\n    API.PageHidden = true;\n    handleVisibilityChange();\n  }\n\n  function onFocus() {\n    API.PageHidden = false;\n    handleVisibilityChange();\n  }\n\n  function handleVisibilityChange() {\n    if (API.PageHidden) stopAll();else resumeAll();\n  }\n\n  function stopAll() {\n    setTimeout(function () {\n      Object.keys(API.Store).forEach(function (id) {\n        if (API.Store.hasOwnProperty(id)) {\n          if (API.Store[id].options.visibilityControl) {\n            API.Store[id].stop();\n          }\n        }\n      });\n    }, 100);\n  }\n\n  function resumeAll() {\n    setTimeout(function () {\n      Object.keys(API.Store).forEach(function (id) {\n        if (API.Store.hasOwnProperty(id)) {\n          if (API.Store[id].options.visibilityControl) {\n            API.Store[id].resume();\n          }\n        }\n      });\n      API.queueRenderAll();\n    }, 100);\n  }\n\n  addListener(document, visibilityChange, onVisibilityChange);\n  addListener(window, 'blur', onBlur);\n  addListener(window, 'focus', onFocus);\n}\n\nfunction createAudioElements(ref) {\n  if (ref.hasSound) {\n    var audioElement = document.createElement('audio');\n\n    ref.options.sounds.sources.forEach(function (s) {\n      var source = document.createElement('source');\n      source.src = s;\n      source.type = 'audio/' + getExtension(s);\n      audioElement.appendChild(source);\n    });\n\n    if (ref.barDom) {\n      ref.barDom.appendChild(audioElement);\n    } else {\n      document.querySelector('body').appendChild(audioElement);\n    }\n\n    audioElement.volume = ref.options.sounds.volume;\n\n    if (!ref.soundPlayed) {\n      audioElement.play();\n      ref.soundPlayed = true;\n    }\n\n    audioElement.onended = function () {\n      remove(audioElement);\n    };\n  }\n}\n\nfunction getExtension(fileName) {\n  return fileName.match(/\\.([^.]+)$/)[1];\n}\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Defaults = exports.Store = exports.Queues = exports.DefaultMaxVisible = exports.docTitle = exports.DocModalCount = exports.PageHidden = undefined;\nexports.getQueueCounts = getQueueCounts;\nexports.addToQueue = addToQueue;\nexports.removeFromQueue = removeFromQueue;\nexports.queueRender = queueRender;\nexports.queueRenderAll = queueRenderAll;\nexports.ghostFix = ghostFix;\nexports.build = build;\nexports.hasButtons = hasButtons;\nexports.handleModal = handleModal;\nexports.handleModalClose = handleModalClose;\nexports.queueClose = queueClose;\nexports.dequeueClose = dequeueClose;\nexports.fire = fire;\nexports.openFlow = openFlow;\nexports.closeFlow = closeFlow;\n\nvar _utils = __webpack_require__(0);\n\nvar Utils = _interopRequireWildcard(_utils);\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nvar PageHidden = exports.PageHidden = false;\nvar DocModalCount = exports.DocModalCount = 0;\n\nvar DocTitleProps = {\n  originalTitle: null,\n  count: 0,\n  changed: false,\n  timer: -1\n};\n\nvar docTitle = exports.docTitle = {\n  increment: function increment() {\n    DocTitleProps.count++;\n\n    docTitle._update();\n  },\n\n  decrement: function decrement() {\n    DocTitleProps.count--;\n\n    if (DocTitleProps.count <= 0) {\n      docTitle._clear();\n      return;\n    }\n\n    docTitle._update();\n  },\n\n  _update: function _update() {\n    var title = document.title;\n\n    if (!DocTitleProps.changed) {\n      DocTitleProps.originalTitle = title;\n      document.title = '(' + DocTitleProps.count + ') ' + title;\n      DocTitleProps.changed = true;\n    } else {\n      document.title = '(' + DocTitleProps.count + ') ' + DocTitleProps.originalTitle;\n    }\n  },\n\n  _clear: function _clear() {\n    if (DocTitleProps.changed) {\n      DocTitleProps.count = 0;\n      document.title = DocTitleProps.originalTitle;\n      DocTitleProps.changed = false;\n    }\n  }\n};\n\nvar DefaultMaxVisible = exports.DefaultMaxVisible = 5;\n\nvar Queues = exports.Queues = {\n  global: {\n    maxVisible: DefaultMaxVisible,\n    queue: []\n  }\n};\n\nvar Store = exports.Store = {};\n\nvar Defaults = exports.Defaults = {\n  type: 'alert',\n  layout: 'topRight',\n  theme: 'mint',\n  text: '',\n  timeout: false,\n  progressBar: true,\n  closeWith: ['click'],\n  animation: {\n    open: 'noty_effects_open',\n    close: 'noty_effects_close'\n  },\n  id: false,\n  force: false,\n  killer: false,\n  queue: 'global',\n  container: false,\n  buttons: [],\n  callbacks: {\n    beforeShow: null,\n    onShow: null,\n    afterShow: null,\n    onClose: null,\n    afterClose: null,\n    onClick: null,\n    onHover: null,\n    onTemplate: null\n  },\n  sounds: {\n    sources: [],\n    volume: 1,\n    conditions: []\n  },\n  titleCount: {\n    conditions: []\n  },\n  modal: false,\n  visibilityControl: false\n\n  /**\n   * @param {string} queueName\n   * @return {object}\n   */\n};function getQueueCounts() {\n  var queueName = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'global';\n\n  var count = 0;\n  var max = DefaultMaxVisible;\n\n  if (Queues.hasOwnProperty(queueName)) {\n    max = Queues[queueName].maxVisible;\n    Object.keys(Store).forEach(function (i) {\n      if (Store[i].options.queue === queueName && !Store[i].closed) count++;\n    });\n  }\n\n  return {\n    current: count,\n    maxVisible: max\n  };\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction addToQueue(ref) {\n  if (!Queues.hasOwnProperty(ref.options.queue)) {\n    Queues[ref.options.queue] = { maxVisible: DefaultMaxVisible, queue: [] };\n  }\n\n  Queues[ref.options.queue].queue.push(ref);\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction removeFromQueue(ref) {\n  if (Queues.hasOwnProperty(ref.options.queue)) {\n    var queue = [];\n    Object.keys(Queues[ref.options.queue].queue).forEach(function (i) {\n      if (Queues[ref.options.queue].queue[i].id !== ref.id) {\n        queue.push(Queues[ref.options.queue].queue[i]);\n      }\n    });\n    Queues[ref.options.queue].queue = queue;\n  }\n}\n\n/**\n * @param {string} queueName\n * @return {void}\n */\nfunction queueRender() {\n  var queueName = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'global';\n\n  if (Queues.hasOwnProperty(queueName)) {\n    var noty = Queues[queueName].queue.shift();\n\n    if (noty) noty.show();\n  }\n}\n\n/**\n * @return {void}\n */\nfunction queueRenderAll() {\n  Object.keys(Queues).forEach(function (queueName) {\n    queueRender(queueName);\n  });\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction ghostFix(ref) {\n  var ghostID = Utils.generateID('ghost');\n  var ghost = document.createElement('div');\n  ghost.setAttribute('id', ghostID);\n  Utils.css(ghost, {\n    height: Utils.outerHeight(ref.barDom) + 'px'\n  });\n\n  ref.barDom.insertAdjacentHTML('afterend', ghost.outerHTML);\n\n  Utils.remove(ref.barDom);\n  ghost = document.getElementById(ghostID);\n  Utils.addClass(ghost, 'noty_fix_effects_height');\n  Utils.addListener(ghost, Utils.animationEndEvents, function () {\n    Utils.remove(ghost);\n  });\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction build(ref) {\n  findOrCreateContainer(ref);\n\n  var markup = '<div class=\"noty_body\">' + ref.options.text + '</div>' + buildButtons(ref) + '<div class=\"noty_progressbar\"></div>';\n\n  ref.barDom = document.createElement('div');\n  ref.barDom.setAttribute('id', ref.id);\n  Utils.addClass(ref.barDom, 'noty_bar noty_type__' + ref.options.type + ' noty_theme__' + ref.options.theme);\n\n  ref.barDom.innerHTML = markup;\n\n  fire(ref, 'onTemplate');\n}\n\n/**\n * @param {Noty} ref\n * @return {boolean}\n */\nfunction hasButtons(ref) {\n  return !!(ref.options.buttons && Object.keys(ref.options.buttons).length);\n}\n\n/**\n * @param {Noty} ref\n * @return {string}\n */\nfunction buildButtons(ref) {\n  if (hasButtons(ref)) {\n    var buttons = document.createElement('div');\n    Utils.addClass(buttons, 'noty_buttons');\n\n    Object.keys(ref.options.buttons).forEach(function (key) {\n      buttons.appendChild(ref.options.buttons[key].dom);\n    });\n\n    ref.options.buttons.forEach(function (btn) {\n      buttons.appendChild(btn.dom);\n    });\n    return buttons.outerHTML;\n  }\n  return '';\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction handleModal(ref) {\n  if (ref.options.modal) {\n    if (DocModalCount === 0) {\n      createModal(ref);\n    }\n\n    exports.DocModalCount = DocModalCount += 1;\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction handleModalClose(ref) {\n  if (ref.options.modal && DocModalCount > 0) {\n    exports.DocModalCount = DocModalCount -= 1;\n\n    if (DocModalCount <= 0) {\n      var modal = document.querySelector('.noty_modal');\n\n      if (modal) {\n        Utils.removeClass(modal, 'noty_modal_open');\n        Utils.addClass(modal, 'noty_modal_close');\n        Utils.addListener(modal, Utils.animationEndEvents, function () {\n          Utils.remove(modal);\n        });\n      }\n    }\n  }\n}\n\n/**\n * @return {void}\n */\nfunction createModal() {\n  var body = document.querySelector('body');\n  var modal = document.createElement('div');\n  Utils.addClass(modal, 'noty_modal');\n  body.insertBefore(modal, body.firstChild);\n  Utils.addClass(modal, 'noty_modal_open');\n\n  Utils.addListener(modal, Utils.animationEndEvents, function () {\n    Utils.removeClass(modal, 'noty_modal_open');\n  });\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction findOrCreateContainer(ref) {\n  if (ref.options.container) {\n    ref.layoutDom = document.querySelector(ref.options.container);\n    return;\n  }\n\n  var layoutID = 'noty_layout__' + ref.options.layout;\n  ref.layoutDom = document.querySelector('div#' + layoutID);\n\n  if (!ref.layoutDom) {\n    ref.layoutDom = document.createElement('div');\n    ref.layoutDom.setAttribute('id', layoutID);\n    Utils.addClass(ref.layoutDom, 'noty_layout');\n    document.querySelector('body').appendChild(ref.layoutDom);\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction queueClose(ref) {\n  if (ref.options.timeout) {\n    if (ref.options.progressBar && ref.progressDom) {\n      Utils.css(ref.progressDom, {\n        transition: 'width ' + ref.options.timeout + 'ms linear',\n        width: '0%'\n      });\n    }\n\n    clearTimeout(ref.closeTimer);\n\n    ref.closeTimer = setTimeout(function () {\n      ref.close();\n    }, ref.options.timeout);\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction dequeueClose(ref) {\n  if (ref.options.timeout && ref.closeTimer) {\n    clearTimeout(ref.closeTimer);\n    ref.closeTimer = -1;\n\n    if (ref.options.progressBar && ref.progressDom) {\n      Utils.css(ref.progressDom, {\n        transition: 'width 0ms linear',\n        width: '100%'\n      });\n    }\n  }\n}\n\n/**\n * @param {Noty} ref\n * @param {string} eventName\n * @return {void}\n */\nfunction fire(ref, eventName) {\n  if (ref.listeners.hasOwnProperty(eventName)) {\n    ref.listeners[eventName].forEach(function (cb) {\n      if (typeof cb === 'function') {\n        cb.apply(ref);\n      }\n    });\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction openFlow(ref) {\n  fire(ref, 'afterShow');\n  queueClose(ref);\n\n  Utils.addListener(ref.barDom, 'mouseenter', function () {\n    dequeueClose(ref);\n  });\n\n  Utils.addListener(ref.barDom, 'mouseleave', function () {\n    queueClose(ref);\n  });\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction closeFlow(ref) {\n  delete Store[ref.id];\n  ref.closing = false;\n  fire(ref, 'afterClose');\n\n  Utils.remove(ref.barDom);\n\n  if (ref.layoutDom.querySelectorAll('.noty_bar').length === 0 && !ref.options.container) {\n    Utils.remove(ref.layoutDom);\n  }\n\n  if (Utils.inArray('docVisible', ref.options.titleCount.conditions) || Utils.inArray('docHidden', ref.options.titleCount.conditions)) {\n    docTitle.decrement();\n  }\n\n  queueRender(ref.options.queue);\n}\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.NotyButton = undefined;\n\nvar _utils = __webpack_require__(0);\n\nvar Utils = _interopRequireWildcard(_utils);\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar NotyButton = exports.NotyButton = function NotyButton(html, classes, cb) {\n  var _this = this;\n\n  var attributes = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n\n  _classCallCheck(this, NotyButton);\n\n  this.dom = document.createElement('button');\n  this.dom.innerHTML = html;\n  this.id = attributes.id = attributes.id || Utils.generateID('button');\n  this.cb = cb;\n  Object.keys(attributes).forEach(function (propertyName) {\n    _this.dom.setAttribute(propertyName, attributes[propertyName]);\n  });\n  Utils.addClass(this.dom, classes || 'noty_btn');\n\n  return this;\n};\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Push = exports.Push = function () {\n  function Push() {\n    var workerPath = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '/service-worker.js';\n\n    _classCallCheck(this, Push);\n\n    this.subData = {};\n    this.workerPath = workerPath;\n    this.listeners = {\n      onPermissionGranted: [],\n      onPermissionDenied: [],\n      onSubscriptionSuccess: [],\n      onSubscriptionCancel: [],\n      onWorkerError: [],\n      onWorkerSuccess: [],\n      onWorkerNotSupported: []\n    };\n    return this;\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {function} cb\n   * @return {Push}\n   */\n\n\n  _createClass(Push, [{\n    key: 'on',\n    value: function on(eventName) {\n      var cb = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function () {};\n\n      if (typeof cb === 'function' && this.listeners.hasOwnProperty(eventName)) {\n        this.listeners[eventName].push(cb);\n      }\n\n      return this;\n    }\n  }, {\n    key: 'fire',\n    value: function fire(eventName) {\n      var _this = this;\n\n      var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n\n      if (this.listeners.hasOwnProperty(eventName)) {\n        this.listeners[eventName].forEach(function (cb) {\n          if (typeof cb === 'function') {\n            cb.apply(_this, params);\n          }\n        });\n      }\n    }\n  }, {\n    key: 'create',\n    value: function create() {\n      console.log('NOT IMPLEMENTED YET');\n    }\n\n    /**\n     * @return {boolean}\n     */\n\n  }, {\n    key: 'isSupported',\n    value: function isSupported() {\n      var result = false;\n\n      try {\n        result = window.Notification || window.webkitNotifications || navigator.mozNotification || window.external && window.external.msIsSiteMode() !== undefined;\n      } catch (e) {}\n\n      return result;\n    }\n\n    /**\n     * @return {string}\n     */\n\n  }, {\n    key: 'getPermissionStatus',\n    value: function getPermissionStatus() {\n      var perm = 'default';\n\n      if (window.Notification && window.Notification.permissionLevel) {\n        perm = window.Notification.permissionLevel;\n      } else if (window.webkitNotifications && window.webkitNotifications.checkPermission) {\n        switch (window.webkitNotifications.checkPermission()) {\n          case 1:\n            perm = 'default';\n            break;\n          case 0:\n            perm = 'granted';\n            break;\n          default:\n            perm = 'denied';\n        }\n      } else if (window.Notification && window.Notification.permission) {\n        perm = window.Notification.permission;\n      } else if (navigator.mozNotification) {\n        perm = 'granted';\n      } else if (window.external && window.external.msIsSiteMode() !== undefined) {\n        perm = window.external.msIsSiteMode() ? 'granted' : 'default';\n      }\n\n      return perm.toString().toLowerCase();\n    }\n\n    /**\n     * @return {string}\n     */\n\n  }, {\n    key: 'getEndpoint',\n    value: function getEndpoint(subscription) {\n      var endpoint = subscription.endpoint;\n      var subscriptionId = subscription.subscriptionId;\n\n      // fix for Chrome < 45\n      if (subscriptionId && endpoint.indexOf(subscriptionId) === -1) {\n        endpoint += '/' + subscriptionId;\n      }\n\n      return endpoint;\n    }\n\n    /**\n     * @return {boolean}\n     */\n\n  }, {\n    key: 'isSWRegistered',\n    value: function isSWRegistered() {\n      try {\n        return navigator.serviceWorker.controller.state === 'activated';\n      } catch (e) {\n        return false;\n      }\n    }\n\n    /**\n     * @return {void}\n     */\n\n  }, {\n    key: 'unregisterWorker',\n    value: function unregisterWorker() {\n      var self = this;\n      if ('serviceWorker' in navigator) {\n        navigator.serviceWorker.getRegistrations().then(function (registrations) {\n          var _iteratorNormalCompletion = true;\n          var _didIteratorError = false;\n          var _iteratorError = undefined;\n\n          try {\n            for (var _iterator = registrations[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n              var registration = _step.value;\n\n              registration.unregister();\n              self.fire('onSubscriptionCancel');\n            }\n          } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n          } finally {\n            try {\n              if (!_iteratorNormalCompletion && _iterator.return) {\n                _iterator.return();\n              }\n            } finally {\n              if (_didIteratorError) {\n                throw _iteratorError;\n              }\n            }\n          }\n        });\n      }\n    }\n\n    /**\n     * @return {void}\n     */\n\n  }, {\n    key: 'requestSubscription',\n    value: function requestSubscription() {\n      var _this2 = this;\n\n      var userVisibleOnly = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n\n      var self = this;\n      var current = this.getPermissionStatus();\n      var cb = function cb(result) {\n        if (result === 'granted') {\n          _this2.fire('onPermissionGranted');\n\n          if ('serviceWorker' in navigator) {\n            navigator.serviceWorker.register(_this2.workerPath).then(function () {\n              navigator.serviceWorker.ready.then(function (serviceWorkerRegistration) {\n                self.fire('onWorkerSuccess');\n                serviceWorkerRegistration.pushManager.subscribe({\n                  userVisibleOnly: userVisibleOnly\n                }).then(function (subscription) {\n                  var key = subscription.getKey('p256dh');\n                  var token = subscription.getKey('auth');\n\n                  self.subData = {\n                    endpoint: self.getEndpoint(subscription),\n                    p256dh: key ? window.btoa(String.fromCharCode.apply(null, new Uint8Array(key))) : null,\n                    auth: token ? window.btoa(String.fromCharCode.apply(null, new Uint8Array(token))) : null\n                  };\n\n                  self.fire('onSubscriptionSuccess', [self.subData]);\n                }).catch(function (err) {\n                  self.fire('onWorkerError', [err]);\n                });\n              });\n            });\n          } else {\n            self.fire('onWorkerNotSupported');\n          }\n        } else if (result === 'denied') {\n          _this2.fire('onPermissionDenied');\n          _this2.unregisterWorker();\n        }\n      };\n\n      if (current === 'default') {\n        if (window.Notification && window.Notification.requestPermission) {\n          window.Notification.requestPermission(cb);\n        } else if (window.webkitNotifications && window.webkitNotifications.checkPermission) {\n          window.webkitNotifications.requestPermission(cb);\n        }\n      } else {\n        cb(current);\n      }\n    }\n  }]);\n\n  return Push;\n}();\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/* WEBPACK VAR INJECTION */(function(process, global) {var require;/*!\n * @overview es6-promise - a tiny implementation of Promises/A+.\n * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)\n * @license   Licensed under MIT license\n *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n * @version   4.1.0\n */\n\n(function (global, factory) {\n     true ? module.exports = factory() :\n    typeof define === 'function' && define.amd ? define(factory) :\n    (global.ES6Promise = factory());\n}(this, (function () { 'use strict';\n\nfunction objectOrFunction(x) {\n  return typeof x === 'function' || typeof x === 'object' && x !== null;\n}\n\nfunction isFunction(x) {\n  return typeof x === 'function';\n}\n\nvar _isArray = undefined;\nif (!Array.isArray) {\n  _isArray = function (x) {\n    return Object.prototype.toString.call(x) === '[object Array]';\n  };\n} else {\n  _isArray = Array.isArray;\n}\n\nvar isArray = _isArray;\n\nvar len = 0;\nvar vertxNext = undefined;\nvar customSchedulerFn = undefined;\n\nvar asap = function asap(callback, arg) {\n  queue[len] = callback;\n  queue[len + 1] = arg;\n  len += 2;\n  if (len === 2) {\n    // If len is 2, that means that we need to schedule an async flush.\n    // If additional callbacks are queued before the queue is flushed, they\n    // will be processed by this flush that we are scheduling.\n    if (customSchedulerFn) {\n      customSchedulerFn(flush);\n    } else {\n      scheduleFlush();\n    }\n  }\n};\n\nfunction setScheduler(scheduleFn) {\n  customSchedulerFn = scheduleFn;\n}\n\nfunction setAsap(asapFn) {\n  asap = asapFn;\n}\n\nvar browserWindow = typeof window !== 'undefined' ? window : undefined;\nvar browserGlobal = browserWindow || {};\nvar BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\nvar isNode = typeof self === 'undefined' && typeof process !== 'undefined' && ({}).toString.call(process) === '[object process]';\n\n// test for web worker but not in IE10\nvar isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n// node\nfunction useNextTick() {\n  // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n  // see https://github.com/cujojs/when/issues/410 for details\n  return function () {\n    return process.nextTick(flush);\n  };\n}\n\n// vertx\nfunction useVertxTimer() {\n  if (typeof vertxNext !== 'undefined') {\n    return function () {\n      vertxNext(flush);\n    };\n  }\n\n  return useSetTimeout();\n}\n\nfunction useMutationObserver() {\n  var iterations = 0;\n  var observer = new BrowserMutationObserver(flush);\n  var node = document.createTextNode('');\n  observer.observe(node, { characterData: true });\n\n  return function () {\n    node.data = iterations = ++iterations % 2;\n  };\n}\n\n// web worker\nfunction useMessageChannel() {\n  var channel = new MessageChannel();\n  channel.port1.onmessage = flush;\n  return function () {\n    return channel.port2.postMessage(0);\n  };\n}\n\nfunction useSetTimeout() {\n  // Store setTimeout reference so es6-promise will be unaffected by\n  // other code modifying setTimeout (like sinon.useFakeTimers())\n  var globalSetTimeout = setTimeout;\n  return function () {\n    return globalSetTimeout(flush, 1);\n  };\n}\n\nvar queue = new Array(1000);\nfunction flush() {\n  for (var i = 0; i < len; i += 2) {\n    var callback = queue[i];\n    var arg = queue[i + 1];\n\n    callback(arg);\n\n    queue[i] = undefined;\n    queue[i + 1] = undefined;\n  }\n\n  len = 0;\n}\n\nfunction attemptVertx() {\n  try {\n    var r = require;\n    var vertx = __webpack_require__(9);\n    vertxNext = vertx.runOnLoop || vertx.runOnContext;\n    return useVertxTimer();\n  } catch (e) {\n    return useSetTimeout();\n  }\n}\n\nvar scheduleFlush = undefined;\n// Decide what async method to use to triggering processing of queued callbacks:\nif (isNode) {\n  scheduleFlush = useNextTick();\n} else if (BrowserMutationObserver) {\n  scheduleFlush = useMutationObserver();\n} else if (isWorker) {\n  scheduleFlush = useMessageChannel();\n} else if (browserWindow === undefined && \"function\" === 'function') {\n  scheduleFlush = attemptVertx();\n} else {\n  scheduleFlush = useSetTimeout();\n}\n\nfunction then(onFulfillment, onRejection) {\n  var _arguments = arguments;\n\n  var parent = this;\n\n  var child = new this.constructor(noop);\n\n  if (child[PROMISE_ID] === undefined) {\n    makePromise(child);\n  }\n\n  var _state = parent._state;\n\n  if (_state) {\n    (function () {\n      var callback = _arguments[_state - 1];\n      asap(function () {\n        return invokeCallback(_state, child, callback, parent._result);\n      });\n    })();\n  } else {\n    subscribe(parent, child, onFulfillment, onRejection);\n  }\n\n  return child;\n}\n\n/**\n  `Promise.resolve` returns a promise that will become resolved with the\n  passed `value`. It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    resolve(1);\n  });\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.resolve(1);\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  @method resolve\n  @static\n  @param {Any} value value that the returned promise will be resolved with\n  Useful for tooling.\n  @return {Promise} a promise that will become fulfilled with the given\n  `value`\n*/\nfunction resolve(object) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (object && typeof object === 'object' && object.constructor === Constructor) {\n    return object;\n  }\n\n  var promise = new Constructor(noop);\n  _resolve(promise, object);\n  return promise;\n}\n\nvar PROMISE_ID = Math.random().toString(36).substring(16);\n\nfunction noop() {}\n\nvar PENDING = void 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\n\nvar GET_THEN_ERROR = new ErrorObject();\n\nfunction selfFulfillment() {\n  return new TypeError(\"You cannot resolve a promise with itself\");\n}\n\nfunction cannotReturnOwn() {\n  return new TypeError('A promises callback cannot return that same promise.');\n}\n\nfunction getThen(promise) {\n  try {\n    return promise.then;\n  } catch (error) {\n    GET_THEN_ERROR.error = error;\n    return GET_THEN_ERROR;\n  }\n}\n\nfunction tryThen(then, value, fulfillmentHandler, rejectionHandler) {\n  try {\n    then.call(value, fulfillmentHandler, rejectionHandler);\n  } catch (e) {\n    return e;\n  }\n}\n\nfunction handleForeignThenable(promise, thenable, then) {\n  asap(function (promise) {\n    var sealed = false;\n    var error = tryThen(then, thenable, function (value) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n      if (thenable !== value) {\n        _resolve(promise, value);\n      } else {\n        fulfill(promise, value);\n      }\n    }, function (reason) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n\n      _reject(promise, reason);\n    }, 'Settle: ' + (promise._label || ' unknown promise'));\n\n    if (!sealed && error) {\n      sealed = true;\n      _reject(promise, error);\n    }\n  }, promise);\n}\n\nfunction handleOwnThenable(promise, thenable) {\n  if (thenable._state === FULFILLED) {\n    fulfill(promise, thenable._result);\n  } else if (thenable._state === REJECTED) {\n    _reject(promise, thenable._result);\n  } else {\n    subscribe(thenable, undefined, function (value) {\n      return _resolve(promise, value);\n    }, function (reason) {\n      return _reject(promise, reason);\n    });\n  }\n}\n\nfunction handleMaybeThenable(promise, maybeThenable, then$$) {\n  if (maybeThenable.constructor === promise.constructor && then$$ === then && maybeThenable.constructor.resolve === resolve) {\n    handleOwnThenable(promise, maybeThenable);\n  } else {\n    if (then$$ === GET_THEN_ERROR) {\n      _reject(promise, GET_THEN_ERROR.error);\n      GET_THEN_ERROR.error = null;\n    } else if (then$$ === undefined) {\n      fulfill(promise, maybeThenable);\n    } else if (isFunction(then$$)) {\n      handleForeignThenable(promise, maybeThenable, then$$);\n    } else {\n      fulfill(promise, maybeThenable);\n    }\n  }\n}\n\nfunction _resolve(promise, value) {\n  if (promise === value) {\n    _reject(promise, selfFulfillment());\n  } else if (objectOrFunction(value)) {\n    handleMaybeThenable(promise, value, getThen(value));\n  } else {\n    fulfill(promise, value);\n  }\n}\n\nfunction publishRejection(promise) {\n  if (promise._onerror) {\n    promise._onerror(promise._result);\n  }\n\n  publish(promise);\n}\n\nfunction fulfill(promise, value) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n\n  promise._result = value;\n  promise._state = FULFILLED;\n\n  if (promise._subscribers.length !== 0) {\n    asap(publish, promise);\n  }\n}\n\nfunction _reject(promise, reason) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n  promise._state = REJECTED;\n  promise._result = reason;\n\n  asap(publishRejection, promise);\n}\n\nfunction subscribe(parent, child, onFulfillment, onRejection) {\n  var _subscribers = parent._subscribers;\n  var length = _subscribers.length;\n\n  parent._onerror = null;\n\n  _subscribers[length] = child;\n  _subscribers[length + FULFILLED] = onFulfillment;\n  _subscribers[length + REJECTED] = onRejection;\n\n  if (length === 0 && parent._state) {\n    asap(publish, parent);\n  }\n}\n\nfunction publish(promise) {\n  var subscribers = promise._subscribers;\n  var settled = promise._state;\n\n  if (subscribers.length === 0) {\n    return;\n  }\n\n  var child = undefined,\n      callback = undefined,\n      detail = promise._result;\n\n  for (var i = 0; i < subscribers.length; i += 3) {\n    child = subscribers[i];\n    callback = subscribers[i + settled];\n\n    if (child) {\n      invokeCallback(settled, child, callback, detail);\n    } else {\n      callback(detail);\n    }\n  }\n\n  promise._subscribers.length = 0;\n}\n\nfunction ErrorObject() {\n  this.error = null;\n}\n\nvar TRY_CATCH_ERROR = new ErrorObject();\n\nfunction tryCatch(callback, detail) {\n  try {\n    return callback(detail);\n  } catch (e) {\n    TRY_CATCH_ERROR.error = e;\n    return TRY_CATCH_ERROR;\n  }\n}\n\nfunction invokeCallback(settled, promise, callback, detail) {\n  var hasCallback = isFunction(callback),\n      value = undefined,\n      error = undefined,\n      succeeded = undefined,\n      failed = undefined;\n\n  if (hasCallback) {\n    value = tryCatch(callback, detail);\n\n    if (value === TRY_CATCH_ERROR) {\n      failed = true;\n      error = value.error;\n      value.error = null;\n    } else {\n      succeeded = true;\n    }\n\n    if (promise === value) {\n      _reject(promise, cannotReturnOwn());\n      return;\n    }\n  } else {\n    value = detail;\n    succeeded = true;\n  }\n\n  if (promise._state !== PENDING) {\n    // noop\n  } else if (hasCallback && succeeded) {\n      _resolve(promise, value);\n    } else if (failed) {\n      _reject(promise, error);\n    } else if (settled === FULFILLED) {\n      fulfill(promise, value);\n    } else if (settled === REJECTED) {\n      _reject(promise, value);\n    }\n}\n\nfunction initializePromise(promise, resolver) {\n  try {\n    resolver(function resolvePromise(value) {\n      _resolve(promise, value);\n    }, function rejectPromise(reason) {\n      _reject(promise, reason);\n    });\n  } catch (e) {\n    _reject(promise, e);\n  }\n}\n\nvar id = 0;\nfunction nextId() {\n  return id++;\n}\n\nfunction makePromise(promise) {\n  promise[PROMISE_ID] = id++;\n  promise._state = undefined;\n  promise._result = undefined;\n  promise._subscribers = [];\n}\n\nfunction Enumerator(Constructor, input) {\n  this._instanceConstructor = Constructor;\n  this.promise = new Constructor(noop);\n\n  if (!this.promise[PROMISE_ID]) {\n    makePromise(this.promise);\n  }\n\n  if (isArray(input)) {\n    this._input = input;\n    this.length = input.length;\n    this._remaining = input.length;\n\n    this._result = new Array(this.length);\n\n    if (this.length === 0) {\n      fulfill(this.promise, this._result);\n    } else {\n      this.length = this.length || 0;\n      this._enumerate();\n      if (this._remaining === 0) {\n        fulfill(this.promise, this._result);\n      }\n    }\n  } else {\n    _reject(this.promise, validationError());\n  }\n}\n\nfunction validationError() {\n  return new Error('Array Methods must be provided an Array');\n};\n\nEnumerator.prototype._enumerate = function () {\n  var length = this.length;\n  var _input = this._input;\n\n  for (var i = 0; this._state === PENDING && i < length; i++) {\n    this._eachEntry(_input[i], i);\n  }\n};\n\nEnumerator.prototype._eachEntry = function (entry, i) {\n  var c = this._instanceConstructor;\n  var resolve$$ = c.resolve;\n\n  if (resolve$$ === resolve) {\n    var _then = getThen(entry);\n\n    if (_then === then && entry._state !== PENDING) {\n      this._settledAt(entry._state, i, entry._result);\n    } else if (typeof _then !== 'function') {\n      this._remaining--;\n      this._result[i] = entry;\n    } else if (c === Promise) {\n      var promise = new c(noop);\n      handleMaybeThenable(promise, entry, _then);\n      this._willSettleAt(promise, i);\n    } else {\n      this._willSettleAt(new c(function (resolve$$) {\n        return resolve$$(entry);\n      }), i);\n    }\n  } else {\n    this._willSettleAt(resolve$$(entry), i);\n  }\n};\n\nEnumerator.prototype._settledAt = function (state, i, value) {\n  var promise = this.promise;\n\n  if (promise._state === PENDING) {\n    this._remaining--;\n\n    if (state === REJECTED) {\n      _reject(promise, value);\n    } else {\n      this._result[i] = value;\n    }\n  }\n\n  if (this._remaining === 0) {\n    fulfill(promise, this._result);\n  }\n};\n\nEnumerator.prototype._willSettleAt = function (promise, i) {\n  var enumerator = this;\n\n  subscribe(promise, undefined, function (value) {\n    return enumerator._settledAt(FULFILLED, i, value);\n  }, function (reason) {\n    return enumerator._settledAt(REJECTED, i, reason);\n  });\n};\n\n/**\n  `Promise.all` accepts an array of promises, and returns a new promise which\n  is fulfilled with an array of fulfillment values for the passed promises, or\n  rejected with the reason of the first passed promise to be rejected. It casts all\n  elements of the passed iterable to promises as it runs this algorithm.\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = resolve(2);\n  let promise3 = resolve(3);\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // The array here would be [ 1, 2, 3 ];\n  });\n  ```\n\n  If any of the `promises` given to `all` are rejected, the first promise\n  that is rejected will be given as an argument to the returned promises's\n  rejection handler. For example:\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = reject(new Error(\"2\"));\n  let promise3 = reject(new Error(\"3\"));\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // Code here never runs because there are rejected promises!\n  }, function(error) {\n    // error.message === \"2\"\n  });\n  ```\n\n  @method all\n  @static\n  @param {Array} entries array of promises\n  @param {String} label optional string for labeling the promise.\n  Useful for tooling.\n  @return {Promise} promise that is fulfilled when all `promises` have been\n  fulfilled, or rejected if any of them become rejected.\n  @static\n*/\nfunction all(entries) {\n  return new Enumerator(this, entries).promise;\n}\n\n/**\n  `Promise.race` returns a new promise which is settled in the same way as the\n  first passed promise to settle.\n\n  Example:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 2');\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // result === 'promise 2' because it was resolved before promise1\n    // was resolved.\n  });\n  ```\n\n  `Promise.race` is deterministic in that only the state of the first\n  settled promise matters. For example, even if other promises given to the\n  `promises` array argument are resolved, but the first settled promise has\n  become rejected before the other promises became fulfilled, the returned\n  promise will become rejected:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      reject(new Error('promise 2'));\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // Code here never runs\n  }, function(reason){\n    // reason.message === 'promise 2' because promise 2 became rejected before\n    // promise 1 became fulfilled\n  });\n  ```\n\n  An example real-world use case is implementing timeouts:\n\n  ```javascript\n  Promise.race([ajax('foo.json'), timeout(5000)])\n  ```\n\n  @method race\n  @static\n  @param {Array} promises array of promises to observe\n  Useful for tooling.\n  @return {Promise} a promise which settles in the same way as the first passed\n  promise to settle.\n*/\nfunction race(entries) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (!isArray(entries)) {\n    return new Constructor(function (_, reject) {\n      return reject(new TypeError('You must pass an array to race.'));\n    });\n  } else {\n    return new Constructor(function (resolve, reject) {\n      var length = entries.length;\n      for (var i = 0; i < length; i++) {\n        Constructor.resolve(entries[i]).then(resolve, reject);\n      }\n    });\n  }\n}\n\n/**\n  `Promise.reject` returns a promise rejected with the passed `reason`.\n  It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    reject(new Error('WHOOPS'));\n  });\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.reject(new Error('WHOOPS'));\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  @method reject\n  @static\n  @param {Any} reason value that the returned promise will be rejected with.\n  Useful for tooling.\n  @return {Promise} a promise rejected with the given `reason`.\n*/\nfunction reject(reason) {\n  /*jshint validthis:true */\n  var Constructor = this;\n  var promise = new Constructor(noop);\n  _reject(promise, reason);\n  return promise;\n}\n\nfunction needsResolver() {\n  throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n}\n\nfunction needsNew() {\n  throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n}\n\n/**\n  Promise objects represent the eventual result of an asynchronous operation. The\n  primary way of interacting with a promise is through its `then` method, which\n  registers callbacks to receive either a promise's eventual value or the reason\n  why the promise cannot be fulfilled.\n\n  Terminology\n  -----------\n\n  - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n  - `thenable` is an object or function that defines a `then` method.\n  - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n  - `exception` is a value that is thrown using the throw statement.\n  - `reason` is a value that indicates why a promise was rejected.\n  - `settled` the final resting state of a promise, fulfilled or rejected.\n\n  A promise can be in one of three states: pending, fulfilled, or rejected.\n\n  Promises that are fulfilled have a fulfillment value and are in the fulfilled\n  state.  Promises that are rejected have a rejection reason and are in the\n  rejected state.  A fulfillment value is never a thenable.\n\n  Promises can also be said to *resolve* a value.  If this value is also a\n  promise, then the original promise's settled state will match the value's\n  settled state.  So a promise that *resolves* a promise that rejects will\n  itself reject, and a promise that *resolves* a promise that fulfills will\n  itself fulfill.\n\n\n  Basic Usage:\n  ------------\n\n  ```js\n  let promise = new Promise(function(resolve, reject) {\n    // on success\n    resolve(value);\n\n    // on failure\n    reject(reason);\n  });\n\n  promise.then(function(value) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Advanced Usage:\n  ---------------\n\n  Promises shine when abstracting away asynchronous interactions such as\n  `XMLHttpRequest`s.\n\n  ```js\n  function getJSON(url) {\n    return new Promise(function(resolve, reject){\n      let xhr = new XMLHttpRequest();\n\n      xhr.open('GET', url);\n      xhr.onreadystatechange = handler;\n      xhr.responseType = 'json';\n      xhr.setRequestHeader('Accept', 'application/json');\n      xhr.send();\n\n      function handler() {\n        if (this.readyState === this.DONE) {\n          if (this.status === 200) {\n            resolve(this.response);\n          } else {\n            reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n          }\n        }\n      };\n    });\n  }\n\n  getJSON('/posts.json').then(function(json) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Unlike callbacks, promises are great composable primitives.\n\n  ```js\n  Promise.all([\n    getJSON('/posts'),\n    getJSON('/comments')\n  ]).then(function(values){\n    values[0] // => postsJSON\n    values[1] // => commentsJSON\n\n    return values;\n  });\n  ```\n\n  @class Promise\n  @param {function} resolver\n  Useful for tooling.\n  @constructor\n*/\nfunction Promise(resolver) {\n  this[PROMISE_ID] = nextId();\n  this._result = this._state = undefined;\n  this._subscribers = [];\n\n  if (noop !== resolver) {\n    typeof resolver !== 'function' && needsResolver();\n    this instanceof Promise ? initializePromise(this, resolver) : needsNew();\n  }\n}\n\nPromise.all = all;\nPromise.race = race;\nPromise.resolve = resolve;\nPromise.reject = reject;\nPromise._setScheduler = setScheduler;\nPromise._setAsap = setAsap;\nPromise._asap = asap;\n\nPromise.prototype = {\n  constructor: Promise,\n\n  /**\n    The primary way of interacting with a promise is through its `then` method,\n    which registers callbacks to receive either a promise's eventual value or the\n    reason why the promise cannot be fulfilled.\n  \n    ```js\n    findUser().then(function(user){\n      // user is available\n    }, function(reason){\n      // user is unavailable, and you are given the reason why\n    });\n    ```\n  \n    Chaining\n    --------\n  \n    The return value of `then` is itself a promise.  This second, 'downstream'\n    promise is resolved with the return value of the first promise's fulfillment\n    or rejection handler, or rejected if the handler throws an exception.\n  \n    ```js\n    findUser().then(function (user) {\n      return user.name;\n    }, function (reason) {\n      return 'default name';\n    }).then(function (userName) {\n      // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n      // will be `'default name'`\n    });\n  \n    findUser().then(function (user) {\n      throw new Error('Found user, but still unhappy');\n    }, function (reason) {\n      throw new Error('`findUser` rejected and we're unhappy');\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n      // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n    });\n    ```\n    If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n  \n    ```js\n    findUser().then(function (user) {\n      throw new PedagogicalException('Upstream error');\n    }).then(function (value) {\n      // never reached\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // The `PedgagocialException` is propagated all the way down to here\n    });\n    ```\n  \n    Assimilation\n    ------------\n  \n    Sometimes the value you want to propagate to a downstream promise can only be\n    retrieved asynchronously. This can be achieved by returning a promise in the\n    fulfillment or rejection handler. The downstream promise will then be pending\n    until the returned promise is settled. This is called *assimilation*.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // The user's comments are now available\n    });\n    ```\n  \n    If the assimliated promise rejects, then the downstream promise will also reject.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // If `findCommentsByAuthor` fulfills, we'll have the value here\n    }, function (reason) {\n      // If `findCommentsByAuthor` rejects, we'll have the reason here\n    });\n    ```\n  \n    Simple Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let result;\n  \n    try {\n      result = findResult();\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n    findResult(function(result, err){\n      if (err) {\n        // failure\n      } else {\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findResult().then(function(result){\n      // success\n    }, function(reason){\n      // failure\n    });\n    ```\n  \n    Advanced Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let author, books;\n  \n    try {\n      author = findAuthor();\n      books  = findBooksByAuthor(author);\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n  \n    function foundBooks(books) {\n  \n    }\n  \n    function failure(reason) {\n  \n    }\n  \n    findAuthor(function(author, err){\n      if (err) {\n        failure(err);\n        // failure\n      } else {\n        try {\n          findBoooksByAuthor(author, function(books, err) {\n            if (err) {\n              failure(err);\n            } else {\n              try {\n                foundBooks(books);\n              } catch(reason) {\n                failure(reason);\n              }\n            }\n          });\n        } catch(error) {\n          failure(err);\n        }\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findAuthor().\n      then(findBooksByAuthor).\n      then(function(books){\n        // found books\n    }).catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method then\n    @param {Function} onFulfilled\n    @param {Function} onRejected\n    Useful for tooling.\n    @return {Promise}\n  */\n  then: then,\n\n  /**\n    `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n    as the catch block of a try/catch statement.\n  \n    ```js\n    function findAuthor(){\n      throw new Error('couldn't find that author');\n    }\n  \n    // synchronous\n    try {\n      findAuthor();\n    } catch(reason) {\n      // something went wrong\n    }\n  \n    // async with promises\n    findAuthor().catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method catch\n    @param {Function} onRejection\n    Useful for tooling.\n    @return {Promise}\n  */\n  'catch': function _catch(onRejection) {\n    return this.then(null, onRejection);\n  }\n};\n\nfunction polyfill() {\n    var local = undefined;\n\n    if (typeof global !== 'undefined') {\n        local = global;\n    } else if (typeof self !== 'undefined') {\n        local = self;\n    } else {\n        try {\n            local = Function('return this')();\n        } catch (e) {\n            throw new Error('polyfill failed because global object is unavailable in this environment');\n        }\n    }\n\n    var P = local.Promise;\n\n    if (P) {\n        var promiseToString = null;\n        try {\n            promiseToString = Object.prototype.toString.call(P.resolve());\n        } catch (e) {\n            // silently ignored\n        }\n\n        if (promiseToString === '[object Promise]' && !P.cast) {\n            return;\n        }\n    }\n\n    local.Promise = Promise;\n}\n\n// Strange compat..\nPromise.polyfill = polyfill;\nPromise.Promise = Promise;\n\nreturn Promise;\n\n})));\n//# sourceMappingURL=es6-promise.map\n\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(7), __webpack_require__(8)))\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports) {\n\n// removed by extract-text-webpack-plugin\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }(); /* global VERSION */\n\n__webpack_require__(5);\n\nvar _es6Promise = __webpack_require__(4);\n\nvar _es6Promise2 = _interopRequireDefault(_es6Promise);\n\nvar _utils = __webpack_require__(0);\n\nvar Utils = _interopRequireWildcard(_utils);\n\nvar _api = __webpack_require__(1);\n\nvar API = _interopRequireWildcard(_api);\n\nvar _button = __webpack_require__(2);\n\nvar _push = __webpack_require__(3);\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Noty = function () {\n  /**\n   * @param {object} options\n   * @return {Noty}\n   */\n  function Noty() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, Noty);\n\n    this.options = Utils.deepExtend({}, API.Defaults, options);\n    this.id = this.options.id || Utils.generateID('bar');\n    this.closeTimer = -1;\n    this.barDom = null;\n    this.layoutDom = null;\n    this.progressDom = null;\n    this.showing = false;\n    this.shown = false;\n    this.closed = false;\n    this.closing = false;\n    this.killable = this.options.timeout || this.options.closeWith.length > 0;\n    this.hasSound = this.options.sounds.sources.length > 0;\n    this.soundPlayed = false;\n    this.listeners = {\n      beforeShow: [],\n      onShow: [],\n      afterShow: [],\n      onClose: [],\n      afterClose: [],\n      onClick: [],\n      onHover: [],\n      onTemplate: []\n    };\n    this.promises = {\n      show: null,\n      close: null\n    };\n    this.on('beforeShow', this.options.callbacks.beforeShow);\n    this.on('onShow', this.options.callbacks.onShow);\n    this.on('afterShow', this.options.callbacks.afterShow);\n    this.on('onClose', this.options.callbacks.onClose);\n    this.on('afterClose', this.options.callbacks.afterClose);\n    this.on('onClick', this.options.callbacks.onClick);\n    this.on('onHover', this.options.callbacks.onHover);\n    this.on('onTemplate', this.options.callbacks.onTemplate);\n\n    return this;\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {function} cb\n   * @return {Noty}\n   */\n\n\n  _createClass(Noty, [{\n    key: 'on',\n    value: function on(eventName) {\n      var cb = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function () {};\n\n      if (typeof cb === 'function' && this.listeners.hasOwnProperty(eventName)) {\n        this.listeners[eventName].push(cb);\n      }\n\n      return this;\n    }\n\n    /**\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'show',\n    value: function show() {\n      var _this = this;\n\n      if (this.options.killer === true) {\n        Noty.closeAll();\n      } else if (typeof this.options.killer === 'string') {\n        Noty.closeAll(this.options.killer);\n      }\n\n      var queueCounts = API.getQueueCounts(this.options.queue);\n\n      if (queueCounts.current >= queueCounts.maxVisible || API.PageHidden && this.options.visibilityControl) {\n        API.addToQueue(this);\n\n        if (API.PageHidden && this.hasSound && Utils.inArray('docHidden', this.options.sounds.conditions)) {\n          Utils.createAudioElements(this);\n        }\n\n        if (API.PageHidden && Utils.inArray('docHidden', this.options.titleCount.conditions)) {\n          API.docTitle.increment();\n        }\n\n        return this;\n      }\n\n      API.Store[this.id] = this;\n\n      API.fire(this, 'beforeShow');\n\n      this.showing = true;\n\n      if (this.closing) {\n        this.showing = false;\n        return this;\n      }\n\n      API.build(this);\n      API.handleModal(this);\n\n      if (this.options.force) {\n        this.layoutDom.insertBefore(this.barDom, this.layoutDom.firstChild);\n      } else {\n        this.layoutDom.appendChild(this.barDom);\n      }\n\n      if (this.hasSound && !this.soundPlayed && Utils.inArray('docVisible', this.options.sounds.conditions)) {\n        Utils.createAudioElements(this);\n      }\n\n      if (Utils.inArray('docVisible', this.options.titleCount.conditions)) {\n        API.docTitle.increment();\n      }\n\n      this.shown = true;\n      this.closed = false;\n\n      // bind button events if any\n      if (API.hasButtons(this)) {\n        Object.keys(this.options.buttons).forEach(function (key) {\n          var btn = _this.barDom.querySelector('#' + _this.options.buttons[key].id);\n          Utils.addListener(btn, 'click', function (e) {\n            Utils.stopPropagation(e);\n            _this.options.buttons[key].cb();\n          });\n        });\n      }\n\n      this.progressDom = this.barDom.querySelector('.noty_progressbar');\n\n      if (Utils.inArray('click', this.options.closeWith)) {\n        Utils.addClass(this.barDom, 'noty_close_with_click');\n        Utils.addListener(this.barDom, 'click', function (e) {\n          Utils.stopPropagation(e);\n          API.fire(_this, 'onClick');\n          _this.close();\n        }, false);\n      }\n\n      Utils.addListener(this.barDom, 'mouseenter', function () {\n        API.fire(_this, 'onHover');\n      }, false);\n\n      if (this.options.timeout) Utils.addClass(this.barDom, 'noty_has_timeout');\n      if (this.options.progressBar) {\n        Utils.addClass(this.barDom, 'noty_has_progressbar');\n      }\n\n      if (Utils.inArray('button', this.options.closeWith)) {\n        Utils.addClass(this.barDom, 'noty_close_with_button');\n\n        var closeButton = document.createElement('div');\n        Utils.addClass(closeButton, 'noty_close_button');\n        closeButton.innerHTML = '×';\n        this.barDom.appendChild(closeButton);\n\n        Utils.addListener(closeButton, 'click', function (e) {\n          Utils.stopPropagation(e);\n          _this.close();\n        }, false);\n      }\n\n      API.fire(this, 'onShow');\n\n      if (this.options.animation.open === null) {\n        this.promises.show = new _es6Promise2.default(function (resolve) {\n          resolve();\n        });\n      } else if (typeof this.options.animation.open === 'function') {\n        this.promises.show = new _es6Promise2.default(this.options.animation.open.bind(this));\n      } else {\n        Utils.addClass(this.barDom, this.options.animation.open);\n        this.promises.show = new _es6Promise2.default(function (resolve) {\n          Utils.addListener(_this.barDom, Utils.animationEndEvents, function () {\n            Utils.removeClass(_this.barDom, _this.options.animation.open);\n            resolve();\n          });\n        });\n      }\n\n      this.promises.show.then(function () {\n        var _t = _this;\n        setTimeout(function () {\n          API.openFlow(_t);\n        }, 100);\n      });\n\n      return this;\n    }\n\n    /**\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'stop',\n    value: function stop() {\n      API.dequeueClose(this);\n      return this;\n    }\n\n    /**\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'resume',\n    value: function resume() {\n      API.queueClose(this);\n      return this;\n    }\n\n    /**\n     * @param {int|boolean} ms\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'setTimeout',\n    value: function (_setTimeout) {\n      function setTimeout(_x) {\n        return _setTimeout.apply(this, arguments);\n      }\n\n      setTimeout.toString = function () {\n        return _setTimeout.toString();\n      };\n\n      return setTimeout;\n    }(function (ms) {\n      this.stop();\n      this.options.timeout = ms;\n\n      if (this.barDom) {\n        if (this.options.timeout) {\n          Utils.addClass(this.barDom, 'noty_has_timeout');\n        } else {\n          Utils.removeClass(this.barDom, 'noty_has_timeout');\n        }\n\n        var _t = this;\n        setTimeout(function () {\n          // ugly fix for progressbar display bug\n          _t.resume();\n        }, 100);\n      }\n\n      return this;\n    })\n\n    /**\n     * @param {string} html\n     * @param {boolean} optionsOverride\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'setText',\n    value: function setText(html) {\n      var optionsOverride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (this.barDom) {\n        this.barDom.querySelector('.noty_body').innerHTML = html;\n      }\n\n      if (optionsOverride) this.options.text = html;\n\n      return this;\n    }\n\n    /**\n     * @param {string} type\n     * @param {boolean} optionsOverride\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'setType',\n    value: function setType(type) {\n      var _this2 = this;\n\n      var optionsOverride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (this.barDom) {\n        var classList = Utils.classList(this.barDom).split(' ');\n\n        classList.forEach(function (c) {\n          if (c.substring(0, 11) === 'noty_type__') {\n            Utils.removeClass(_this2.barDom, c);\n          }\n        });\n\n        Utils.addClass(this.barDom, 'noty_type__' + type);\n      }\n\n      if (optionsOverride) this.options.type = type;\n\n      return this;\n    }\n\n    /**\n     * @param {string} theme\n     * @param {boolean} optionsOverride\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'setTheme',\n    value: function setTheme(theme) {\n      var _this3 = this;\n\n      var optionsOverride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (this.barDom) {\n        var classList = Utils.classList(this.barDom).split(' ');\n\n        classList.forEach(function (c) {\n          if (c.substring(0, 12) === 'noty_theme__') {\n            Utils.removeClass(_this3.barDom, c);\n          }\n        });\n\n        Utils.addClass(this.barDom, 'noty_theme__' + theme);\n      }\n\n      if (optionsOverride) this.options.theme = theme;\n\n      return this;\n    }\n\n    /**\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'close',\n    value: function close() {\n      var _this4 = this;\n\n      if (this.closed) return this;\n\n      if (!this.shown) {\n        // it's in the queue\n        API.removeFromQueue(this);\n        return this;\n      }\n\n      API.fire(this, 'onClose');\n\n      this.closing = true;\n\n      if (this.options.animation.close === null) {\n        this.promises.close = new _es6Promise2.default(function (resolve) {\n          resolve();\n        });\n      } else if (typeof this.options.animation.close === 'function') {\n        this.promises.close = new _es6Promise2.default(this.options.animation.close.bind(this));\n      } else {\n        Utils.addClass(this.barDom, this.options.animation.close);\n        this.promises.close = new _es6Promise2.default(function (resolve) {\n          Utils.addListener(_this4.barDom, Utils.animationEndEvents, function () {\n            if (_this4.options.force) {\n              Utils.remove(_this4.barDom);\n            } else {\n              API.ghostFix(_this4);\n            }\n            resolve();\n          });\n        });\n      }\n\n      this.promises.close.then(function () {\n        API.closeFlow(_this4);\n        API.handleModalClose(_this4);\n      });\n\n      this.closed = true;\n\n      return this;\n    }\n\n    // API functions\n\n    /**\n     * @param {boolean|string} queueName\n     * @return {Noty}\n     */\n\n  }], [{\n    key: 'closeAll',\n    value: function closeAll() {\n      var queueName = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n      Object.keys(API.Store).forEach(function (id) {\n        if (queueName) {\n          if (API.Store[id].options.queue === queueName && API.Store[id].killable) {\n            API.Store[id].close();\n          }\n        } else if (API.Store[id].killable) {\n          API.Store[id].close();\n        }\n      });\n      return this;\n    }\n\n    /**\n     * @param {Object} obj\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'overrideDefaults',\n    value: function overrideDefaults(obj) {\n      API.Defaults = Utils.deepExtend({}, API.Defaults, obj);\n      return this;\n    }\n\n    /**\n     * @param {int} amount\n     * @param {string} queueName\n     * @return {Noty}\n     */\n\n  }, {\n    key: 'setMaxVisible',\n    value: function setMaxVisible() {\n      var amount = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : API.DefaultMaxVisible;\n      var queueName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'global';\n\n      if (!API.Queues.hasOwnProperty(queueName)) {\n        API.Queues[queueName] = { maxVisible: amount, queue: [] };\n      }\n\n      API.Queues[queueName].maxVisible = amount;\n      return this;\n    }\n\n    /**\n     * @param {string} innerHtml\n     * @param {String} classes\n     * @param {Function} cb\n     * @param {Object} attributes\n     * @return {NotyButton}\n     */\n\n  }, {\n    key: 'button',\n    value: function button(innerHtml) {\n      var classes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      var cb = arguments[2];\n      var attributes = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n\n      return new _button.NotyButton(innerHtml, classes, cb, attributes);\n    }\n\n    /**\n     * @return {string}\n     */\n\n  }, {\n    key: 'version',\n    value: function version() {\n      return \"3.1.3\";\n    }\n\n    /**\n     * @param {String} workerPath\n     * @return {Push}\n     */\n\n  }, {\n    key: 'Push',\n    value: function Push(workerPath) {\n      return new _push.Push(workerPath);\n    }\n  }]);\n\n  return Noty;\n}();\n\n// Document visibility change controller\n\n\nexports.default = Noty;\nUtils.visibilityChangeFlow();\nmodule.exports = exports['default'];\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports) {\n\n// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports) {\n\nvar g;\r\n\r\n// This works in non-strict mode\r\ng = (function() {\r\n\treturn this;\r\n})();\r\n\r\ntry {\r\n\t// This works if eval is allowed (see CSP)\r\n\tg = g || Function(\"return this\")() || (1,eval)(\"this\");\r\n} catch(e) {\r\n\t// This works if the window reference is available\r\n\tif(typeof window === \"object\")\r\n\t\tg = window;\r\n}\r\n\r\n// g can still be undefined, but nothing to do about it...\r\n// We return undefined, instead of nothing here, so it's\r\n// easier to handle this case. if(!global) { ...}\r\n\r\nmodule.exports = g;\r\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports) {\n\n/* (ignored) */\n\n/***/ })\n/******/ ]);\n});\n\n\n// WEBPACK FOOTER //\n// noty.min.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// identity function for calling harmony imports with the correct context\n \t__webpack_require__.i = function(value) { return value; };\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 6);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 9fc5b5bdaf483a649708", "import * as API from 'api'\n\nexport const animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend'\n\nexport function inArray (needle, haystack, argStrict) {\n  let key\n  let strict = !!argStrict\n\n  if (strict) {\n    for (key in haystack) {\n      if (haystack.hasOwnProperty(key) && haystack[key] === needle) {\n        return true\n      }\n    }\n  } else {\n    for (key in haystack) {\n      if (haystack.hasOwnProperty(key) && haystack[key] === needle) {\n        return true\n      }\n    }\n  }\n  return false\n}\n\nexport function stopPropagation (evt) {\n  evt = evt || window.event\n\n  if (typeof evt.stopPropagation !== 'undefined') {\n    evt.stopPropagation()\n  } else {\n    evt.cancelBubble = true\n  }\n}\n\nexport const deepExtend = function (out) {\n  out = out || {}\n\n  for (let i = 1; i < arguments.length; i++) {\n    let obj = arguments[i]\n\n    if (!obj) continue\n\n    for (let key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        if (Array.isArray(obj[key])) {\n          out[key] = obj[key]\n        } else if (typeof obj[key] === 'object' && obj[key] !== null) {\n          out[key] = deepExtend(out[key], obj[key])\n        } else {\n          out[key] = obj[key]\n        }\n      }\n    }\n  }\n\n  return out\n}\n\nexport function generateID (prefix = '') {\n  let id = `noty_${prefix}_`\n\n  id += 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    let r = Math.random() * 16 | 0\n    let v = c === 'x' ? r : r & 0x3 | 0x8\n    return v.toString(16)\n  })\n\n  return id\n}\n\nexport function outerHeight (el) {\n  let height = el.offsetHeight\n  let style = window.getComputedStyle(el)\n\n  height += parseInt(style.marginTop) + parseInt(style.marginBottom)\n  return height\n}\n\nexport let css = (function () {\n  let cssPrefixes = ['Webkit', 'O', 'Moz', 'ms']\n  let cssProps = {}\n\n  function camelCase (string) {\n    return string\n      .replace(/^-ms-/, 'ms-')\n      .replace(/-([\\da-z])/gi, function (match, letter) {\n        return letter.toUpperCase()\n      })\n  }\n\n  function getVendorProp (name) {\n    let style = document.body.style\n    if (name in style) return name\n\n    let i = cssPrefixes.length\n    let capName = name.charAt(0).toUpperCase() + name.slice(1)\n    let vendorName\n\n    while (i--) {\n      vendorName = cssPrefixes[i] + capName\n      if (vendorName in style) return vendorName\n    }\n\n    return name\n  }\n\n  function getStyleProp (name) {\n    name = camelCase(name)\n    return cssProps[name] || (cssProps[name] = getVendorProp(name))\n  }\n\n  function applyCss (element, prop, value) {\n    prop = getStyleProp(prop)\n    element.style[prop] = value\n  }\n\n  return function (element, properties) {\n    let args = arguments\n    let prop\n    let value\n\n    if (args.length === 2) {\n      for (prop in properties) {\n        if (properties.hasOwnProperty(prop)) {\n          value = properties[prop]\n          if (value !== undefined && properties.hasOwnProperty(prop)) {\n            applyCss(element, prop, value)\n          }\n        }\n      }\n    } else {\n      applyCss(element, args[1], args[2])\n    }\n  }\n})()\n\nexport function addListener (el, events, cb, useCapture = false) {\n  events = events.split(' ')\n  for (let i = 0; i < events.length; i++) {\n    if (document.addEventListener) {\n      el.addEventListener(events[i], cb, useCapture)\n    } else if (document.attachEvent) {\n      el.attachEvent('on' + events[i], cb)\n    }\n  }\n}\n\nexport function hasClass (element, name) {\n  let list = typeof element === 'string' ? element : classList(element)\n  return list.indexOf(' ' + name + ' ') >= 0\n}\n\nexport function addClass (element, name) {\n  let oldList = classList(element)\n  let newList = oldList + name\n\n  if (hasClass(oldList, name)) return\n\n  // Trim the opening space.\n  element.className = newList.substring(1)\n}\n\nexport function removeClass (element, name) {\n  let oldList = classList(element)\n  let newList\n\n  if (!hasClass(element, name)) return\n\n  // Replace the class name.\n  newList = oldList.replace(' ' + name + ' ', ' ')\n\n  // Trim the opening and closing spaces.\n  element.className = newList.substring(1, newList.length - 1)\n}\n\nexport function remove (element) {\n  if (element.parentNode) {\n    element.parentNode.removeChild(element)\n  }\n}\n\nexport function classList (element) {\n  return (' ' + ((element && element.className) || '') + ' ').replace(\n    /\\s+/gi,\n    ' '\n  )\n}\n\nexport function visibilityChangeFlow () {\n  let hidden\n  let visibilityChange\n  if (typeof document.hidden !== 'undefined') {\n    // Opera 12.10 and Firefox 18 and later support\n    hidden = 'hidden'\n    visibilityChange = 'visibilitychange'\n  } else if (typeof document.msHidden !== 'undefined') {\n    hidden = 'msHidden'\n    visibilityChange = 'msvisibilitychange'\n  } else if (typeof document.webkitHidden !== 'undefined') {\n    hidden = 'webkitHidden'\n    visibilityChange = 'webkitvisibilitychange'\n  }\n\n  function onVisibilityChange () {\n    API.PageHidden = document[hidden]\n    handleVisibilityChange()\n  }\n\n  function onBlur () {\n    API.PageHidden = true\n    handleVisibilityChange()\n  }\n\n  function onFocus () {\n    API.PageHidden = false\n    handleVisibilityChange()\n  }\n\n  function handleVisibilityChange () {\n    if (API.PageHidden) stopAll()\n    else resumeAll()\n  }\n\n  function stopAll () {\n    setTimeout(\n      function () {\n        Object.keys(API.Store).forEach(id => {\n          if (API.Store.hasOwnProperty(id)) {\n            if (API.Store[id].options.visibilityControl) {\n              API.Store[id].stop()\n            }\n          }\n        })\n      },\n      100\n    )\n  }\n\n  function resumeAll () {\n    setTimeout(\n      function () {\n        Object.keys(API.Store).forEach(id => {\n          if (API.Store.hasOwnProperty(id)) {\n            if (API.Store[id].options.visibilityControl) {\n              API.Store[id].resume()\n            }\n          }\n        })\n        API.queueRenderAll()\n      },\n      100\n    )\n  }\n\n  addListener(document, visibilityChange, onVisibilityChange)\n  addListener(window, 'blur', onBlur)\n  addListener(window, 'focus', onFocus)\n}\n\nexport function createAudioElements (ref) {\n  if (ref.hasSound) {\n    const audioElement = document.createElement('audio')\n\n    ref.options.sounds.sources.forEach(s => {\n      const source = document.createElement('source')\n      source.src = s\n      source.type = `audio/${getExtension(s)}`\n      audioElement.appendChild(source)\n    })\n\n    if (ref.barDom) {\n      ref.barDom.appendChild(audioElement)\n    } else {\n      document.querySelector('body').appendChild(audioElement)\n    }\n\n    audioElement.volume = ref.options.sounds.volume\n\n    if (!ref.soundPlayed) {\n      audioElement.play()\n      ref.soundPlayed = true\n    }\n\n    audioElement.onended = function () {\n      remove(audioElement)\n    }\n  }\n}\n\nfunction getExtension (fileName) {\n  return fileName.match(/\\.([^.]+)$/)[1]\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/utils.js", "import * as Utils from 'utils'\n\nexport let PageHidden = false\nexport let DocModalCount = 0\n\nconst DocTitleProps = {\n  originalTitle: null,\n  count: 0,\n  changed: false,\n  timer: -1\n}\n\nexport const docTitle = {\n  increment: () => {\n    DocTitleProps.count++\n\n    docTitle._update()\n  },\n\n  decrement: () => {\n    DocTitleProps.count--\n\n    if (DocTitleProps.count <= 0) {\n      docTitle._clear()\n      return\n    }\n\n    docTitle._update()\n  },\n\n  _update: () => {\n    let title = document.title\n\n    if (!DocTitleProps.changed) {\n      DocTitleProps.originalTitle = title\n      document.title = `(${DocTitleProps.count}) ${title}`\n      DocTitleProps.changed = true\n    } else {\n      document.title = `(${DocTitleProps.count}) ${DocTitleProps.originalTitle}`\n    }\n  },\n\n  _clear: () => {\n    if (DocTitleProps.changed) {\n      DocTitleProps.count = 0\n      document.title = DocTitleProps.originalTitle\n      DocTitleProps.changed = false\n    }\n  }\n}\n\nexport const DefaultMaxVisible = 5\n\nexport const Queues = {\n  global: {\n    maxVisible: DefaultMaxVisible,\n    queue: []\n  }\n}\n\nexport const Store = {}\n\nexport let Defaults = {\n  type: 'alert',\n  layout: 'topRight',\n  theme: 'mint',\n  text: '',\n  timeout: false,\n  progressBar: true,\n  closeWith: ['click'],\n  animation: {\n    open: 'noty_effects_open',\n    close: 'noty_effects_close'\n  },\n  id: false,\n  force: false,\n  killer: false,\n  queue: 'global',\n  container: false,\n  buttons: [],\n  callbacks: {\n    beforeShow: null,\n    onShow: null,\n    afterShow: null,\n    onClose: null,\n    afterClose: null,\n    onClick: null,\n    onHover: null,\n    onTemplate: null\n  },\n  sounds: {\n    sources: [],\n    volume: 1,\n    conditions: []\n  },\n  titleCount: {\n    conditions: []\n  },\n  modal: false,\n  visibilityControl: false\n}\n\n/**\n * @param {string} queueName\n * @return {object}\n */\nexport function getQueueCounts (queueName = 'global') {\n  let count = 0\n  let max = DefaultMaxVisible\n\n  if (Queues.hasOwnProperty(queueName)) {\n    max = Queues[queueName].maxVisible\n    Object.keys(Store).forEach(i => {\n      if (Store[i].options.queue === queueName && !Store[i].closed) count++\n    })\n  }\n\n  return {\n    current: count,\n    maxVisible: max\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function addToQueue (ref) {\n  if (!Queues.hasOwnProperty(ref.options.queue)) {\n    Queues[ref.options.queue] = {maxVisible: DefaultMaxVisible, queue: []}\n  }\n\n  Queues[ref.options.queue].queue.push(ref)\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function removeFromQueue (ref) {\n  if (Queues.hasOwnProperty(ref.options.queue)) {\n    const queue = []\n    Object.keys(Queues[ref.options.queue].queue).forEach(i => {\n      if (Queues[ref.options.queue].queue[i].id !== ref.id) {\n        queue.push(Queues[ref.options.queue].queue[i])\n      }\n    })\n    Queues[ref.options.queue].queue = queue\n  }\n}\n\n/**\n * @param {string} queueName\n * @return {void}\n */\nexport function queueRender (queueName = 'global') {\n  if (Queues.hasOwnProperty(queueName)) {\n    const noty = Queues[queueName].queue.shift()\n\n    if (noty) noty.show()\n  }\n}\n\n/**\n * @return {void}\n */\nexport function queueRenderAll () {\n  Object.keys(Queues).forEach(queueName => {\n    queueRender(queueName)\n  })\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function ghostFix (ref) {\n  const ghostID = Utils.generateID('ghost')\n  let ghost = document.createElement('div')\n  ghost.setAttribute('id', ghostID)\n  Utils.css(ghost, {\n    height: Utils.outerHeight(ref.barDom) + 'px'\n  })\n\n  ref.barDom.insertAdjacentHTML('afterend', ghost.outerHTML)\n\n  Utils.remove(ref.barDom)\n  ghost = document.getElementById(ghostID)\n  Utils.addClass(ghost, 'noty_fix_effects_height')\n  Utils.addListener(ghost, Utils.animationEndEvents, () => {\n    Utils.remove(ghost)\n  })\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function build (ref) {\n  findOrCreateContainer(ref)\n\n  const markup = `<div class=\"noty_body\">${ref.options.text}</div>${buildButtons(ref)}<div class=\"noty_progressbar\"></div>`\n\n  ref.barDom = document.createElement('div')\n  ref.barDom.setAttribute('id', ref.id)\n  Utils.addClass(\n    ref.barDom,\n    `noty_bar noty_type__${ref.options.type} noty_theme__${ref.options.theme}`\n  )\n\n  ref.barDom.innerHTML = markup\n\n  fire(ref, 'onTemplate')\n}\n\n/**\n * @param {Noty} ref\n * @return {boolean}\n */\nexport function hasButtons (ref) {\n  return !!(ref.options.buttons && Object.keys(ref.options.buttons).length)\n}\n\n/**\n * @param {Noty} ref\n * @return {string}\n */\nfunction buildButtons (ref) {\n  if (hasButtons(ref)) {\n    let buttons = document.createElement('div')\n    Utils.addClass(buttons, 'noty_buttons')\n\n    Object.keys(ref.options.buttons).forEach(key => {\n      buttons.appendChild(ref.options.buttons[key].dom)\n    })\n\n    ref.options.buttons.forEach(btn => {\n      buttons.appendChild(btn.dom)\n    })\n    return buttons.outerHTML\n  }\n  return ''\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function handleModal (ref) {\n  if (ref.options.modal) {\n    if (DocModalCount === 0) {\n      createModal(ref)\n    }\n\n    DocModalCount++\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function handleModalClose (ref) {\n  if (ref.options.modal && DocModalCount > 0) {\n    DocModalCount--\n\n    if (DocModalCount <= 0) {\n      const modal = document.querySelector('.noty_modal')\n\n      if (modal) {\n        Utils.removeClass(modal, 'noty_modal_open')\n        Utils.addClass(modal, 'noty_modal_close')\n        Utils.addListener(modal, Utils.animationEndEvents, () => {\n          Utils.remove(modal)\n        })\n      }\n    }\n  }\n}\n\n/**\n * @return {void}\n */\nfunction createModal () {\n  const body = document.querySelector('body')\n  const modal = document.createElement('div')\n  Utils.addClass(modal, 'noty_modal')\n  body.insertBefore(modal, body.firstChild)\n  Utils.addClass(modal, 'noty_modal_open')\n\n  Utils.addListener(modal, Utils.animationEndEvents, () => {\n    Utils.removeClass(modal, 'noty_modal_open')\n  })\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nfunction findOrCreateContainer (ref) {\n  if (ref.options.container) {\n    ref.layoutDom = document.querySelector(ref.options.container)\n    return\n  }\n\n  const layoutID = `noty_layout__${ref.options.layout}`\n  ref.layoutDom = document.querySelector(`div#${layoutID}`)\n\n  if (!ref.layoutDom) {\n    ref.layoutDom = document.createElement('div')\n    ref.layoutDom.setAttribute('id', layoutID)\n    Utils.addClass(ref.layoutDom, 'noty_layout')\n    document.querySelector('body').appendChild(ref.layoutDom)\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function queueClose (ref) {\n  if (ref.options.timeout) {\n    if (ref.options.progressBar && ref.progressDom) {\n      Utils.css(ref.progressDom, {\n        transition: `width ${ref.options.timeout}ms linear`,\n        width: '0%'\n      })\n    }\n\n    clearTimeout(ref.closeTimer)\n\n    ref.closeTimer = setTimeout(\n      () => {\n        ref.close()\n      },\n      ref.options.timeout\n    )\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function dequeueClose (ref) {\n  if (ref.options.timeout && ref.closeTimer) {\n    clearTimeout(ref.closeTimer)\n    ref.closeTimer = -1\n\n    if (ref.options.progressBar && ref.progressDom) {\n      Utils.css(ref.progressDom, {\n        transition: 'width 0ms linear',\n        width: '100%'\n      })\n    }\n  }\n}\n\n/**\n * @param {Noty} ref\n * @param {string} eventName\n * @return {void}\n */\nexport function fire (ref, eventName) {\n  if (ref.listeners.hasOwnProperty(eventName)) {\n    ref.listeners[eventName].forEach(cb => {\n      if (typeof cb === 'function') {\n        cb.apply(ref)\n      }\n    })\n  }\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function openFlow (ref) {\n  fire(ref, 'afterShow')\n  queueClose(ref)\n\n  Utils.addListener(ref.barDom, 'mouseenter', () => {\n    dequeueClose(ref)\n  })\n\n  Utils.addListener(ref.barDom, 'mouseleave', () => {\n    queueClose(ref)\n  })\n}\n\n/**\n * @param {Noty} ref\n * @return {void}\n */\nexport function closeFlow (ref) {\n  delete Store[ref.id]\n  ref.closing = false\n  fire(ref, 'afterClose')\n\n  Utils.remove(ref.barDom)\n\n  if (\n    ref.layoutDom.querySelectorAll('.noty_bar').length === 0 &&\n    !ref.options.container\n  ) {\n    Utils.remove(ref.layoutDom)\n  }\n\n  if (\n    Utils.inArray('docVisible', ref.options.titleCount.conditions) ||\n    Utils.inArray('docHidden', ref.options.titleCount.conditions)\n  ) {\n    docTitle.decrement()\n  }\n\n  queueRender(ref.options.queue)\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/api.js", "import * as Utils from 'utils'\n\nexport class NotyButton {\n  constructor (html, classes, cb, attributes = {}) {\n    this.dom = document.createElement('button')\n    this.dom.innerHTML = html\n    this.id = (attributes.id = attributes.id || Utils.generateID('button'))\n    this.cb = cb\n    Object.keys(attributes).forEach(propertyName => {\n      this.dom.setAttribute(propertyName, attributes[propertyName])\n    })\n    Utils.addClass(this.dom, classes || 'noty_btn')\n\n    return this\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/button.js", "export class Push {\n  constructor (workerPath = '/service-worker.js') {\n    this.subData = {}\n    this.workerPath = workerPath\n    this.listeners = {\n      onPermissionGranted: [],\n      onPermissionDenied: [],\n      onSubscriptionSuccess: [],\n      onSubscriptionCancel: [],\n      onWorkerError: [],\n      onWorkerSuccess: [],\n      onWorkerNotSupported: []\n    }\n    return this\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {function} cb\n   * @return {Push}\n   */\n  on (eventName, cb = () => {}) {\n    if (typeof cb === 'function' && this.listeners.hasOwnProperty(eventName)) {\n      this.listeners[eventName].push(cb)\n    }\n\n    return this\n  }\n\n  fire (eventName, params = []) {\n    if (this.listeners.hasOwnProperty(eventName)) {\n      this.listeners[eventName].forEach(cb => {\n        if (typeof cb === 'function') {\n          cb.apply(this, params)\n        }\n      })\n    }\n  }\n\n  create () {\n    console.log('NOT IMPLEMENTED YET')\n  }\n\n  /**\n   * @return {boolean}\n   */\n  isSupported () {\n    let result = false\n\n    try {\n      result = window.Notification ||\n        window.webkitNotifications ||\n        navigator.mozNotification ||\n        (window.external && window.external.msIsSiteMode() !== undefined)\n    } catch (e) {}\n\n    return result\n  }\n\n  /**\n   * @return {string}\n   */\n  getPermissionStatus () {\n    let perm = 'default'\n\n    if (window.Notification && window.Notification.permissionLevel) {\n      perm = window.Notification.permissionLevel\n    } else if (\n      window.webkitNotifications && window.webkitNotifications.checkPermission\n    ) {\n      switch (window.webkitNotifications.checkPermission()) {\n        case 1:\n          perm = 'default'\n          break\n        case 0:\n          perm = 'granted'\n          break\n        default:\n          perm = 'denied'\n      }\n    } else if (window.Notification && window.Notification.permission) {\n      perm = window.Notification.permission\n    } else if (navigator.mozNotification) {\n      perm = 'granted'\n    } else if (\n      window.external && window.external.msIsSiteMode() !== undefined\n    ) {\n      perm = window.external.msIsSiteMode() ? 'granted' : 'default'\n    }\n\n    return perm.toString().toLowerCase()\n  }\n\n  /**\n   * @return {string}\n   */\n  getEndpoint (subscription) {\n    let endpoint = subscription.endpoint\n    const subscriptionId = subscription.subscriptionId\n\n    // fix for Chrome < 45\n    if (subscriptionId && endpoint.indexOf(subscriptionId) === -1) {\n      endpoint += '/' + subscriptionId\n    }\n\n    return endpoint\n  }\n\n  /**\n   * @return {boolean}\n   */\n  isSWRegistered () {\n    try {\n      return navigator.serviceWorker.controller.state === 'activated'\n    } catch (e) {\n      return false\n    }\n  }\n\n  /**\n   * @return {void}\n   */\n  unregisterWorker () {\n    const self = this\n    if ('serviceWorker' in navigator) {\n      navigator.serviceWorker.getRegistrations().then(function (registrations) {\n        for (let registration of registrations) {\n          registration.unregister()\n          self.fire('onSubscriptionCancel')\n        }\n      })\n    }\n  }\n\n  /**\n   * @return {void}\n   */\n  requestSubscription (userVisibleOnly = true) {\n    const self = this\n    const current = this.getPermissionStatus()\n    const cb = result => {\n      if (result === 'granted') {\n        this.fire('onPermissionGranted')\n\n        if ('serviceWorker' in navigator) {\n          navigator.serviceWorker.register(this.workerPath).then(function () {\n            navigator.serviceWorker.ready.then(\n              function (serviceWorkerRegistration) {\n                self.fire('onWorkerSuccess')\n                serviceWorkerRegistration.pushManager\n                  .subscribe({\n                    userVisibleOnly: userVisibleOnly\n                  })\n                  .then(function (subscription) {\n                    const key = subscription.getKey('p256dh')\n                    const token = subscription.getKey('auth')\n\n                    self.subData = {\n                      endpoint: self.getEndpoint(subscription),\n                      p256dh: key\n                        ? window.btoa(\n                            String.fromCharCode.apply(null, new Uint8Array(key))\n                          )\n                        : null,\n                      auth: token\n                        ? window.btoa(\n                            String.fromCharCode.apply(\n                              null,\n                              new Uint8Array(token)\n                            )\n                          )\n                        : null\n                    }\n\n                    self.fire('onSubscriptionSuccess', [self.subData])\n                  })\n                  .catch(function (err) {\n                    self.fire('onWorkerError', [err])\n                  })\n              }\n            )\n          })\n        } else {\n          self.fire('onWorkerNotSupported')\n        }\n      } else if (result === 'denied') {\n        this.fire('onPermissionDenied')\n        this.unregisterWorker()\n      }\n    }\n\n    if (current === 'default') {\n      if (window.Notification && window.Notification.requestPermission) {\n        window.Notification.requestPermission(cb)\n      } else if (\n        window.webkitNotifications && window.webkitNotifications.checkPermission\n      ) {\n        window.webkitNotifications.requestPermission(cb)\n      }\n    } else {\n      cb(current)\n    }\n  }\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/push.js", "/*!\n * @overview es6-promise - a tiny implementation of Promises/A+.\n * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)\n * @license   Licensed under MIT license\n *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n * @version   4.1.0\n */\n\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n    typeof define === 'function' && define.amd ? define(factory) :\n    (global.ES6Promise = factory());\n}(this, (function () { 'use strict';\n\nfunction objectOrFunction(x) {\n  return typeof x === 'function' || typeof x === 'object' && x !== null;\n}\n\nfunction isFunction(x) {\n  return typeof x === 'function';\n}\n\nvar _isArray = undefined;\nif (!Array.isArray) {\n  _isArray = function (x) {\n    return Object.prototype.toString.call(x) === '[object Array]';\n  };\n} else {\n  _isArray = Array.isArray;\n}\n\nvar isArray = _isArray;\n\nvar len = 0;\nvar vertxNext = undefined;\nvar customSchedulerFn = undefined;\n\nvar asap = function asap(callback, arg) {\n  queue[len] = callback;\n  queue[len + 1] = arg;\n  len += 2;\n  if (len === 2) {\n    // If len is 2, that means that we need to schedule an async flush.\n    // If additional callbacks are queued before the queue is flushed, they\n    // will be processed by this flush that we are scheduling.\n    if (customSchedulerFn) {\n      customSchedulerFn(flush);\n    } else {\n      scheduleFlush();\n    }\n  }\n};\n\nfunction setScheduler(scheduleFn) {\n  customSchedulerFn = scheduleFn;\n}\n\nfunction setAsap(asapFn) {\n  asap = asapFn;\n}\n\nvar browserWindow = typeof window !== 'undefined' ? window : undefined;\nvar browserGlobal = browserWindow || {};\nvar BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\nvar isNode = typeof self === 'undefined' && typeof process !== 'undefined' && ({}).toString.call(process) === '[object process]';\n\n// test for web worker but not in IE10\nvar isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n// node\nfunction useNextTick() {\n  // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n  // see https://github.com/cujojs/when/issues/410 for details\n  return function () {\n    return process.nextTick(flush);\n  };\n}\n\n// vertx\nfunction useVertxTimer() {\n  if (typeof vertxNext !== 'undefined') {\n    return function () {\n      vertxNext(flush);\n    };\n  }\n\n  return useSetTimeout();\n}\n\nfunction useMutationObserver() {\n  var iterations = 0;\n  var observer = new BrowserMutationObserver(flush);\n  var node = document.createTextNode('');\n  observer.observe(node, { characterData: true });\n\n  return function () {\n    node.data = iterations = ++iterations % 2;\n  };\n}\n\n// web worker\nfunction useMessageChannel() {\n  var channel = new MessageChannel();\n  channel.port1.onmessage = flush;\n  return function () {\n    return channel.port2.postMessage(0);\n  };\n}\n\nfunction useSetTimeout() {\n  // Store setTimeout reference so es6-promise will be unaffected by\n  // other code modifying setTimeout (like sinon.useFakeTimers())\n  var globalSetTimeout = setTimeout;\n  return function () {\n    return globalSetTimeout(flush, 1);\n  };\n}\n\nvar queue = new Array(1000);\nfunction flush() {\n  for (var i = 0; i < len; i += 2) {\n    var callback = queue[i];\n    var arg = queue[i + 1];\n\n    callback(arg);\n\n    queue[i] = undefined;\n    queue[i + 1] = undefined;\n  }\n\n  len = 0;\n}\n\nfunction attemptVertx() {\n  try {\n    var r = require;\n    var vertx = r('vertx');\n    vertxNext = vertx.runOnLoop || vertx.runOnContext;\n    return useVertxTimer();\n  } catch (e) {\n    return useSetTimeout();\n  }\n}\n\nvar scheduleFlush = undefined;\n// Decide what async method to use to triggering processing of queued callbacks:\nif (isNode) {\n  scheduleFlush = useNextTick();\n} else if (BrowserMutationObserver) {\n  scheduleFlush = useMutationObserver();\n} else if (isWorker) {\n  scheduleFlush = useMessageChannel();\n} else if (browserWindow === undefined && typeof require === 'function') {\n  scheduleFlush = attemptVertx();\n} else {\n  scheduleFlush = useSetTimeout();\n}\n\nfunction then(onFulfillment, onRejection) {\n  var _arguments = arguments;\n\n  var parent = this;\n\n  var child = new this.constructor(noop);\n\n  if (child[PROMISE_ID] === undefined) {\n    makePromise(child);\n  }\n\n  var _state = parent._state;\n\n  if (_state) {\n    (function () {\n      var callback = _arguments[_state - 1];\n      asap(function () {\n        return invokeCallback(_state, child, callback, parent._result);\n      });\n    })();\n  } else {\n    subscribe(parent, child, onFulfillment, onRejection);\n  }\n\n  return child;\n}\n\n/**\n  `Promise.resolve` returns a promise that will become resolved with the\n  passed `value`. It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    resolve(1);\n  });\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.resolve(1);\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  @method resolve\n  @static\n  @param {Any} value value that the returned promise will be resolved with\n  Useful for tooling.\n  @return {Promise} a promise that will become fulfilled with the given\n  `value`\n*/\nfunction resolve(object) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (object && typeof object === 'object' && object.constructor === Constructor) {\n    return object;\n  }\n\n  var promise = new Constructor(noop);\n  _resolve(promise, object);\n  return promise;\n}\n\nvar PROMISE_ID = Math.random().toString(36).substring(16);\n\nfunction noop() {}\n\nvar PENDING = void 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\n\nvar GET_THEN_ERROR = new ErrorObject();\n\nfunction selfFulfillment() {\n  return new TypeError(\"You cannot resolve a promise with itself\");\n}\n\nfunction cannotReturnOwn() {\n  return new TypeError('A promises callback cannot return that same promise.');\n}\n\nfunction getThen(promise) {\n  try {\n    return promise.then;\n  } catch (error) {\n    GET_THEN_ERROR.error = error;\n    return GET_THEN_ERROR;\n  }\n}\n\nfunction tryThen(then, value, fulfillmentHandler, rejectionHandler) {\n  try {\n    then.call(value, fulfillmentHandler, rejectionHandler);\n  } catch (e) {\n    return e;\n  }\n}\n\nfunction handleForeignThenable(promise, thenable, then) {\n  asap(function (promise) {\n    var sealed = false;\n    var error = tryThen(then, thenable, function (value) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n      if (thenable !== value) {\n        _resolve(promise, value);\n      } else {\n        fulfill(promise, value);\n      }\n    }, function (reason) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n\n      _reject(promise, reason);\n    }, 'Settle: ' + (promise._label || ' unknown promise'));\n\n    if (!sealed && error) {\n      sealed = true;\n      _reject(promise, error);\n    }\n  }, promise);\n}\n\nfunction handleOwnThenable(promise, thenable) {\n  if (thenable._state === FULFILLED) {\n    fulfill(promise, thenable._result);\n  } else if (thenable._state === REJECTED) {\n    _reject(promise, thenable._result);\n  } else {\n    subscribe(thenable, undefined, function (value) {\n      return _resolve(promise, value);\n    }, function (reason) {\n      return _reject(promise, reason);\n    });\n  }\n}\n\nfunction handleMaybeThenable(promise, maybeThenable, then$$) {\n  if (maybeThenable.constructor === promise.constructor && then$$ === then && maybeThenable.constructor.resolve === resolve) {\n    handleOwnThenable(promise, maybeThenable);\n  } else {\n    if (then$$ === GET_THEN_ERROR) {\n      _reject(promise, GET_THEN_ERROR.error);\n      GET_THEN_ERROR.error = null;\n    } else if (then$$ === undefined) {\n      fulfill(promise, maybeThenable);\n    } else if (isFunction(then$$)) {\n      handleForeignThenable(promise, maybeThenable, then$$);\n    } else {\n      fulfill(promise, maybeThenable);\n    }\n  }\n}\n\nfunction _resolve(promise, value) {\n  if (promise === value) {\n    _reject(promise, selfFulfillment());\n  } else if (objectOrFunction(value)) {\n    handleMaybeThenable(promise, value, getThen(value));\n  } else {\n    fulfill(promise, value);\n  }\n}\n\nfunction publishRejection(promise) {\n  if (promise._onerror) {\n    promise._onerror(promise._result);\n  }\n\n  publish(promise);\n}\n\nfunction fulfill(promise, value) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n\n  promise._result = value;\n  promise._state = FULFILLED;\n\n  if (promise._subscribers.length !== 0) {\n    asap(publish, promise);\n  }\n}\n\nfunction _reject(promise, reason) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n  promise._state = REJECTED;\n  promise._result = reason;\n\n  asap(publishRejection, promise);\n}\n\nfunction subscribe(parent, child, onFulfillment, onRejection) {\n  var _subscribers = parent._subscribers;\n  var length = _subscribers.length;\n\n  parent._onerror = null;\n\n  _subscribers[length] = child;\n  _subscribers[length + FULFILLED] = onFulfillment;\n  _subscribers[length + REJECTED] = onRejection;\n\n  if (length === 0 && parent._state) {\n    asap(publish, parent);\n  }\n}\n\nfunction publish(promise) {\n  var subscribers = promise._subscribers;\n  var settled = promise._state;\n\n  if (subscribers.length === 0) {\n    return;\n  }\n\n  var child = undefined,\n      callback = undefined,\n      detail = promise._result;\n\n  for (var i = 0; i < subscribers.length; i += 3) {\n    child = subscribers[i];\n    callback = subscribers[i + settled];\n\n    if (child) {\n      invokeCallback(settled, child, callback, detail);\n    } else {\n      callback(detail);\n    }\n  }\n\n  promise._subscribers.length = 0;\n}\n\nfunction ErrorObject() {\n  this.error = null;\n}\n\nvar TRY_CATCH_ERROR = new ErrorObject();\n\nfunction tryCatch(callback, detail) {\n  try {\n    return callback(detail);\n  } catch (e) {\n    TRY_CATCH_ERROR.error = e;\n    return TRY_CATCH_ERROR;\n  }\n}\n\nfunction invokeCallback(settled, promise, callback, detail) {\n  var hasCallback = isFunction(callback),\n      value = undefined,\n      error = undefined,\n      succeeded = undefined,\n      failed = undefined;\n\n  if (hasCallback) {\n    value = tryCatch(callback, detail);\n\n    if (value === TRY_CATCH_ERROR) {\n      failed = true;\n      error = value.error;\n      value.error = null;\n    } else {\n      succeeded = true;\n    }\n\n    if (promise === value) {\n      _reject(promise, cannotReturnOwn());\n      return;\n    }\n  } else {\n    value = detail;\n    succeeded = true;\n  }\n\n  if (promise._state !== PENDING) {\n    // noop\n  } else if (hasCallback && succeeded) {\n      _resolve(promise, value);\n    } else if (failed) {\n      _reject(promise, error);\n    } else if (settled === FULFILLED) {\n      fulfill(promise, value);\n    } else if (settled === REJECTED) {\n      _reject(promise, value);\n    }\n}\n\nfunction initializePromise(promise, resolver) {\n  try {\n    resolver(function resolvePromise(value) {\n      _resolve(promise, value);\n    }, function rejectPromise(reason) {\n      _reject(promise, reason);\n    });\n  } catch (e) {\n    _reject(promise, e);\n  }\n}\n\nvar id = 0;\nfunction nextId() {\n  return id++;\n}\n\nfunction makePromise(promise) {\n  promise[PROMISE_ID] = id++;\n  promise._state = undefined;\n  promise._result = undefined;\n  promise._subscribers = [];\n}\n\nfunction Enumerator(Constructor, input) {\n  this._instanceConstructor = Constructor;\n  this.promise = new Constructor(noop);\n\n  if (!this.promise[PROMISE_ID]) {\n    makePromise(this.promise);\n  }\n\n  if (isArray(input)) {\n    this._input = input;\n    this.length = input.length;\n    this._remaining = input.length;\n\n    this._result = new Array(this.length);\n\n    if (this.length === 0) {\n      fulfill(this.promise, this._result);\n    } else {\n      this.length = this.length || 0;\n      this._enumerate();\n      if (this._remaining === 0) {\n        fulfill(this.promise, this._result);\n      }\n    }\n  } else {\n    _reject(this.promise, validationError());\n  }\n}\n\nfunction validationError() {\n  return new Error('Array Methods must be provided an Array');\n};\n\nEnumerator.prototype._enumerate = function () {\n  var length = this.length;\n  var _input = this._input;\n\n  for (var i = 0; this._state === PENDING && i < length; i++) {\n    this._eachEntry(_input[i], i);\n  }\n};\n\nEnumerator.prototype._eachEntry = function (entry, i) {\n  var c = this._instanceConstructor;\n  var resolve$$ = c.resolve;\n\n  if (resolve$$ === resolve) {\n    var _then = getThen(entry);\n\n    if (_then === then && entry._state !== PENDING) {\n      this._settledAt(entry._state, i, entry._result);\n    } else if (typeof _then !== 'function') {\n      this._remaining--;\n      this._result[i] = entry;\n    } else if (c === Promise) {\n      var promise = new c(noop);\n      handleMaybeThenable(promise, entry, _then);\n      this._willSettleAt(promise, i);\n    } else {\n      this._willSettleAt(new c(function (resolve$$) {\n        return resolve$$(entry);\n      }), i);\n    }\n  } else {\n    this._willSettleAt(resolve$$(entry), i);\n  }\n};\n\nEnumerator.prototype._settledAt = function (state, i, value) {\n  var promise = this.promise;\n\n  if (promise._state === PENDING) {\n    this._remaining--;\n\n    if (state === REJECTED) {\n      _reject(promise, value);\n    } else {\n      this._result[i] = value;\n    }\n  }\n\n  if (this._remaining === 0) {\n    fulfill(promise, this._result);\n  }\n};\n\nEnumerator.prototype._willSettleAt = function (promise, i) {\n  var enumerator = this;\n\n  subscribe(promise, undefined, function (value) {\n    return enumerator._settledAt(FULFILLED, i, value);\n  }, function (reason) {\n    return enumerator._settledAt(REJECTED, i, reason);\n  });\n};\n\n/**\n  `Promise.all` accepts an array of promises, and returns a new promise which\n  is fulfilled with an array of fulfillment values for the passed promises, or\n  rejected with the reason of the first passed promise to be rejected. It casts all\n  elements of the passed iterable to promises as it runs this algorithm.\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = resolve(2);\n  let promise3 = resolve(3);\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // The array here would be [ 1, 2, 3 ];\n  });\n  ```\n\n  If any of the `promises` given to `all` are rejected, the first promise\n  that is rejected will be given as an argument to the returned promises's\n  rejection handler. For example:\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = reject(new Error(\"2\"));\n  let promise3 = reject(new Error(\"3\"));\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // Code here never runs because there are rejected promises!\n  }, function(error) {\n    // error.message === \"2\"\n  });\n  ```\n\n  @method all\n  @static\n  @param {Array} entries array of promises\n  @param {String} label optional string for labeling the promise.\n  Useful for tooling.\n  @return {Promise} promise that is fulfilled when all `promises` have been\n  fulfilled, or rejected if any of them become rejected.\n  @static\n*/\nfunction all(entries) {\n  return new Enumerator(this, entries).promise;\n}\n\n/**\n  `Promise.race` returns a new promise which is settled in the same way as the\n  first passed promise to settle.\n\n  Example:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 2');\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // result === 'promise 2' because it was resolved before promise1\n    // was resolved.\n  });\n  ```\n\n  `Promise.race` is deterministic in that only the state of the first\n  settled promise matters. For example, even if other promises given to the\n  `promises` array argument are resolved, but the first settled promise has\n  become rejected before the other promises became fulfilled, the returned\n  promise will become rejected:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      reject(new Error('promise 2'));\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // Code here never runs\n  }, function(reason){\n    // reason.message === 'promise 2' because promise 2 became rejected before\n    // promise 1 became fulfilled\n  });\n  ```\n\n  An example real-world use case is implementing timeouts:\n\n  ```javascript\n  Promise.race([ajax('foo.json'), timeout(5000)])\n  ```\n\n  @method race\n  @static\n  @param {Array} promises array of promises to observe\n  Useful for tooling.\n  @return {Promise} a promise which settles in the same way as the first passed\n  promise to settle.\n*/\nfunction race(entries) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (!isArray(entries)) {\n    return new Constructor(function (_, reject) {\n      return reject(new TypeError('You must pass an array to race.'));\n    });\n  } else {\n    return new Constructor(function (resolve, reject) {\n      var length = entries.length;\n      for (var i = 0; i < length; i++) {\n        Constructor.resolve(entries[i]).then(resolve, reject);\n      }\n    });\n  }\n}\n\n/**\n  `Promise.reject` returns a promise rejected with the passed `reason`.\n  It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    reject(new Error('WHOOPS'));\n  });\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.reject(new Error('WHOOPS'));\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  @method reject\n  @static\n  @param {Any} reason value that the returned promise will be rejected with.\n  Useful for tooling.\n  @return {Promise} a promise rejected with the given `reason`.\n*/\nfunction reject(reason) {\n  /*jshint validthis:true */\n  var Constructor = this;\n  var promise = new Constructor(noop);\n  _reject(promise, reason);\n  return promise;\n}\n\nfunction needsResolver() {\n  throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n}\n\nfunction needsNew() {\n  throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n}\n\n/**\n  Promise objects represent the eventual result of an asynchronous operation. The\n  primary way of interacting with a promise is through its `then` method, which\n  registers callbacks to receive either a promise's eventual value or the reason\n  why the promise cannot be fulfilled.\n\n  Terminology\n  -----------\n\n  - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n  - `thenable` is an object or function that defines a `then` method.\n  - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n  - `exception` is a value that is thrown using the throw statement.\n  - `reason` is a value that indicates why a promise was rejected.\n  - `settled` the final resting state of a promise, fulfilled or rejected.\n\n  A promise can be in one of three states: pending, fulfilled, or rejected.\n\n  Promises that are fulfilled have a fulfillment value and are in the fulfilled\n  state.  Promises that are rejected have a rejection reason and are in the\n  rejected state.  A fulfillment value is never a thenable.\n\n  Promises can also be said to *resolve* a value.  If this value is also a\n  promise, then the original promise's settled state will match the value's\n  settled state.  So a promise that *resolves* a promise that rejects will\n  itself reject, and a promise that *resolves* a promise that fulfills will\n  itself fulfill.\n\n\n  Basic Usage:\n  ------------\n\n  ```js\n  let promise = new Promise(function(resolve, reject) {\n    // on success\n    resolve(value);\n\n    // on failure\n    reject(reason);\n  });\n\n  promise.then(function(value) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Advanced Usage:\n  ---------------\n\n  Promises shine when abstracting away asynchronous interactions such as\n  `XMLHttpRequest`s.\n\n  ```js\n  function getJSON(url) {\n    return new Promise(function(resolve, reject){\n      let xhr = new XMLHttpRequest();\n\n      xhr.open('GET', url);\n      xhr.onreadystatechange = handler;\n      xhr.responseType = 'json';\n      xhr.setRequestHeader('Accept', 'application/json');\n      xhr.send();\n\n      function handler() {\n        if (this.readyState === this.DONE) {\n          if (this.status === 200) {\n            resolve(this.response);\n          } else {\n            reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n          }\n        }\n      };\n    });\n  }\n\n  getJSON('/posts.json').then(function(json) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Unlike callbacks, promises are great composable primitives.\n\n  ```js\n  Promise.all([\n    getJSON('/posts'),\n    getJSON('/comments')\n  ]).then(function(values){\n    values[0] // => postsJSON\n    values[1] // => commentsJSON\n\n    return values;\n  });\n  ```\n\n  @class Promise\n  @param {function} resolver\n  Useful for tooling.\n  @constructor\n*/\nfunction Promise(resolver) {\n  this[PROMISE_ID] = nextId();\n  this._result = this._state = undefined;\n  this._subscribers = [];\n\n  if (noop !== resolver) {\n    typeof resolver !== 'function' && needsResolver();\n    this instanceof Promise ? initializePromise(this, resolver) : needsNew();\n  }\n}\n\nPromise.all = all;\nPromise.race = race;\nPromise.resolve = resolve;\nPromise.reject = reject;\nPromise._setScheduler = setScheduler;\nPromise._setAsap = setAsap;\nPromise._asap = asap;\n\nPromise.prototype = {\n  constructor: Promise,\n\n  /**\n    The primary way of interacting with a promise is through its `then` method,\n    which registers callbacks to receive either a promise's eventual value or the\n    reason why the promise cannot be fulfilled.\n  \n    ```js\n    findUser().then(function(user){\n      // user is available\n    }, function(reason){\n      // user is unavailable, and you are given the reason why\n    });\n    ```\n  \n    Chaining\n    --------\n  \n    The return value of `then` is itself a promise.  This second, 'downstream'\n    promise is resolved with the return value of the first promise's fulfillment\n    or rejection handler, or rejected if the handler throws an exception.\n  \n    ```js\n    findUser().then(function (user) {\n      return user.name;\n    }, function (reason) {\n      return 'default name';\n    }).then(function (userName) {\n      // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n      // will be `'default name'`\n    });\n  \n    findUser().then(function (user) {\n      throw new Error('Found user, but still unhappy');\n    }, function (reason) {\n      throw new Error('`findUser` rejected and we're unhappy');\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n      // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n    });\n    ```\n    If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n  \n    ```js\n    findUser().then(function (user) {\n      throw new PedagogicalException('Upstream error');\n    }).then(function (value) {\n      // never reached\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // The `PedgagocialException` is propagated all the way down to here\n    });\n    ```\n  \n    Assimilation\n    ------------\n  \n    Sometimes the value you want to propagate to a downstream promise can only be\n    retrieved asynchronously. This can be achieved by returning a promise in the\n    fulfillment or rejection handler. The downstream promise will then be pending\n    until the returned promise is settled. This is called *assimilation*.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // The user's comments are now available\n    });\n    ```\n  \n    If the assimliated promise rejects, then the downstream promise will also reject.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // If `findCommentsByAuthor` fulfills, we'll have the value here\n    }, function (reason) {\n      // If `findCommentsByAuthor` rejects, we'll have the reason here\n    });\n    ```\n  \n    Simple Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let result;\n  \n    try {\n      result = findResult();\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n    findResult(function(result, err){\n      if (err) {\n        // failure\n      } else {\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findResult().then(function(result){\n      // success\n    }, function(reason){\n      // failure\n    });\n    ```\n  \n    Advanced Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let author, books;\n  \n    try {\n      author = findAuthor();\n      books  = findBooksByAuthor(author);\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n  \n    function foundBooks(books) {\n  \n    }\n  \n    function failure(reason) {\n  \n    }\n  \n    findAuthor(function(author, err){\n      if (err) {\n        failure(err);\n        // failure\n      } else {\n        try {\n          findBoooksByAuthor(author, function(books, err) {\n            if (err) {\n              failure(err);\n            } else {\n              try {\n                foundBooks(books);\n              } catch(reason) {\n                failure(reason);\n              }\n            }\n          });\n        } catch(error) {\n          failure(err);\n        }\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findAuthor().\n      then(findBooksByAuthor).\n      then(function(books){\n        // found books\n    }).catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method then\n    @param {Function} onFulfilled\n    @param {Function} onRejected\n    Useful for tooling.\n    @return {Promise}\n  */\n  then: then,\n\n  /**\n    `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n    as the catch block of a try/catch statement.\n  \n    ```js\n    function findAuthor(){\n      throw new Error('couldn't find that author');\n    }\n  \n    // synchronous\n    try {\n      findAuthor();\n    } catch(reason) {\n      // something went wrong\n    }\n  \n    // async with promises\n    findAuthor().catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method catch\n    @param {Function} onRejection\n    Useful for tooling.\n    @return {Promise}\n  */\n  'catch': function _catch(onRejection) {\n    return this.then(null, onRejection);\n  }\n};\n\nfunction polyfill() {\n    var local = undefined;\n\n    if (typeof global !== 'undefined') {\n        local = global;\n    } else if (typeof self !== 'undefined') {\n        local = self;\n    } else {\n        try {\n            local = Function('return this')();\n        } catch (e) {\n            throw new Error('polyfill failed because global object is unavailable in this environment');\n        }\n    }\n\n    var P = local.Promise;\n\n    if (P) {\n        var promiseToString = null;\n        try {\n            promiseToString = Object.prototype.toString.call(P.resolve());\n        } catch (e) {\n            // silently ignored\n        }\n\n        if (promiseToString === '[object Promise]' && !P.cast) {\n            return;\n        }\n    }\n\n    local.Promise = Promise;\n}\n\n// Strange compat..\nPromise.polyfill = polyfill;\nPromise.Promise = Promise;\n\nreturn Promise;\n\n})));\n//# sourceMappingURL=es6-promise.map\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/es6-promise/dist/es6-promise.js\n// module id = 4\n// module chunks = 0", "/* global VERSION */\n\nimport 'noty.scss'\nimport Promise from 'es6-promise'\nimport * as Utils from 'utils'\nimport * as API from 'api'\nimport {NotyButton} from 'button'\nimport {Push} from 'push'\n\nexport default class Noty {\n  /**\n   * @param {object} options\n   * @return {Noty}\n   */\n  constructor (options = {}) {\n    this.options = Utils.deepExtend({}, API.Defaults, options)\n    this.id = this.options.id || Utils.generateID('bar')\n    this.closeTimer = -1\n    this.barDom = null\n    this.layoutDom = null\n    this.progressDom = null\n    this.showing = false\n    this.shown = false\n    this.closed = false\n    this.closing = false\n    this.killable = this.options.timeout || this.options.closeWith.length > 0\n    this.hasSound = this.options.sounds.sources.length > 0\n    this.soundPlayed = false\n    this.listeners = {\n      beforeShow: [],\n      onShow: [],\n      afterShow: [],\n      onClose: [],\n      afterClose: [],\n      onClick: [],\n      onHover: [],\n      onTemplate: []\n    }\n    this.promises = {\n      show: null,\n      close: null\n    }\n    this.on('beforeShow', this.options.callbacks.beforeShow)\n    this.on('onShow', this.options.callbacks.onShow)\n    this.on('afterShow', this.options.callbacks.afterShow)\n    this.on('onClose', this.options.callbacks.onClose)\n    this.on('afterClose', this.options.callbacks.afterClose)\n    this.on('onClick', this.options.callbacks.onClick)\n    this.on('onHover', this.options.callbacks.onHover)\n    this.on('onTemplate', this.options.callbacks.onTemplate)\n\n    return this\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {function} cb\n   * @return {Noty}\n   */\n  on (eventName, cb = () => {}) {\n    if (typeof cb === 'function' && this.listeners.hasOwnProperty(eventName)) {\n      this.listeners[eventName].push(cb)\n    }\n\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  show () {\n    if (this.options.killer === true) {\n      Noty.closeAll()\n    } else if (typeof this.options.killer === 'string') {\n      Noty.closeAll(this.options.killer)\n    }\n\n    let queueCounts = API.getQueueCounts(this.options.queue)\n\n    if (\n      queueCounts.current >= queueCounts.maxVisible ||\n      (API.PageHidden && this.options.visibilityControl)\n    ) {\n      API.addToQueue(this)\n\n      if (\n        API.PageHidden &&\n        this.hasSound &&\n        Utils.inArray('docHidden', this.options.sounds.conditions)\n      ) {\n        Utils.createAudioElements(this)\n      }\n\n      if (\n        API.PageHidden &&\n        Utils.inArray('docHidden', this.options.titleCount.conditions)\n      ) {\n        API.docTitle.increment()\n      }\n\n      return this\n    }\n\n    API.Store[this.id] = this\n\n    API.fire(this, 'beforeShow')\n\n    this.showing = true\n\n    if (this.closing) {\n      this.showing = false\n      return this\n    }\n\n    API.build(this)\n    API.handleModal(this)\n\n    if (this.options.force) {\n      this.layoutDom.insertBefore(this.barDom, this.layoutDom.firstChild)\n    } else {\n      this.layoutDom.appendChild(this.barDom)\n    }\n\n    if (\n      this.hasSound &&\n      !this.soundPlayed &&\n      Utils.inArray('docVisible', this.options.sounds.conditions)\n    ) {\n      Utils.createAudioElements(this)\n    }\n\n    if (Utils.inArray('docVisible', this.options.titleCount.conditions)) {\n      API.docTitle.increment()\n    }\n\n    this.shown = true\n    this.closed = false\n\n    // bind button events if any\n    if (API.hasButtons(this)) {\n      Object.keys(this.options.buttons).forEach(key => {\n        const btn = this.barDom.querySelector(\n          `#${this.options.buttons[key].id}`\n        )\n        Utils.addListener(btn, 'click', e => {\n          Utils.stopPropagation(e)\n          this.options.buttons[key].cb()\n        })\n      })\n    }\n\n    this.progressDom = this.barDom.querySelector('.noty_progressbar')\n\n    if (Utils.inArray('click', this.options.closeWith)) {\n      Utils.addClass(this.barDom, 'noty_close_with_click')\n      Utils.addListener(\n        this.barDom,\n        'click',\n        e => {\n          Utils.stopPropagation(e)\n          API.fire(this, 'onClick')\n          this.close()\n        },\n        false\n      )\n    }\n\n    Utils.addListener(\n      this.barDom,\n      'mouseenter',\n      () => {\n        API.fire(this, 'onHover')\n      },\n      false\n    )\n\n    if (this.options.timeout) Utils.addClass(this.barDom, 'noty_has_timeout')\n    if (this.options.progressBar) {\n      Utils.addClass(this.barDom, 'noty_has_progressbar')\n    }\n\n    if (Utils.inArray('button', this.options.closeWith)) {\n      Utils.addClass(this.barDom, 'noty_close_with_button')\n\n      const closeButton = document.createElement('div')\n      Utils.addClass(closeButton, 'noty_close_button')\n      closeButton.innerHTML = '×'\n      this.barDom.appendChild(closeButton)\n\n      Utils.addListener(\n        closeButton,\n        'click',\n        e => {\n          Utils.stopPropagation(e)\n          this.close()\n        },\n        false\n      )\n    }\n\n    API.fire(this, 'onShow')\n\n    if (this.options.animation.open === null) {\n      this.promises.show = new Promise(resolve => {\n        resolve()\n      })\n    } else if (typeof this.options.animation.open === 'function') {\n      this.promises.show = new Promise(this.options.animation.open.bind(this))\n    } else {\n      Utils.addClass(this.barDom, this.options.animation.open)\n      this.promises.show = new Promise(resolve => {\n        Utils.addListener(this.barDom, Utils.animationEndEvents, () => {\n          Utils.removeClass(this.barDom, this.options.animation.open)\n          resolve()\n        })\n      })\n    }\n\n    this.promises.show.then(() => {\n      const _t = this\n      setTimeout(\n        () => {\n          API.openFlow(_t)\n        },\n        100\n      )\n    })\n\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  stop () {\n    API.dequeueClose(this)\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  resume () {\n    API.queueClose(this)\n    return this\n  }\n\n  /**\n   * @param {int|boolean} ms\n   * @return {Noty}\n   */\n  setTimeout (ms) {\n    this.stop()\n    this.options.timeout = ms\n\n    if (this.barDom) {\n      if (this.options.timeout) {\n        Utils.addClass(this.barDom, 'noty_has_timeout')\n      } else {\n        Utils.removeClass(this.barDom, 'noty_has_timeout')\n      }\n\n      const _t = this\n      setTimeout(\n        function () {\n          // ugly fix for progressbar display bug\n          _t.resume()\n        },\n        100\n      )\n    }\n\n    return this\n  }\n\n  /**\n   * @param {string} html\n   * @param {boolean} optionsOverride\n   * @return {Noty}\n   */\n  setText (html, optionsOverride = false) {\n    if (this.barDom) {\n      this.barDom.querySelector('.noty_body').innerHTML = html\n    }\n\n    if (optionsOverride) this.options.text = html\n\n    return this\n  }\n\n  /**\n   * @param {string} type\n   * @param {boolean} optionsOverride\n   * @return {Noty}\n   */\n  setType (type, optionsOverride = false) {\n    if (this.barDom) {\n      let classList = Utils.classList(this.barDom).split(' ')\n\n      classList.forEach(c => {\n        if (c.substring(0, 11) === 'noty_type__') {\n          Utils.removeClass(this.barDom, c)\n        }\n      })\n\n      Utils.addClass(this.barDom, `noty_type__${type}`)\n    }\n\n    if (optionsOverride) this.options.type = type\n\n    return this\n  }\n\n  /**\n   * @param {string} theme\n   * @param {boolean} optionsOverride\n   * @return {Noty}\n   */\n  setTheme (theme, optionsOverride = false) {\n    if (this.barDom) {\n      let classList = Utils.classList(this.barDom).split(' ')\n\n      classList.forEach(c => {\n        if (c.substring(0, 12) === 'noty_theme__') {\n          Utils.removeClass(this.barDom, c)\n        }\n      })\n\n      Utils.addClass(this.barDom, `noty_theme__${theme}`)\n    }\n\n    if (optionsOverride) this.options.theme = theme\n\n    return this\n  }\n\n  /**\n   * @return {Noty}\n   */\n  close () {\n    if (this.closed) return this\n\n    if (!this.shown) {\n      // it's in the queue\n      API.removeFromQueue(this)\n      return this\n    }\n\n    API.fire(this, 'onClose')\n\n    this.closing = true\n\n    if (this.options.animation.close === null) {\n      this.promises.close = new Promise(resolve => {\n        resolve()\n      })\n    } else if (typeof this.options.animation.close === 'function') {\n      this.promises.close = new Promise(\n        this.options.animation.close.bind(this)\n      )\n    } else {\n      Utils.addClass(this.barDom, this.options.animation.close)\n      this.promises.close = new Promise(resolve => {\n        Utils.addListener(this.barDom, Utils.animationEndEvents, () => {\n          if (this.options.force) {\n            Utils.remove(this.barDom)\n          } else {\n            API.ghostFix(this)\n          }\n          resolve()\n        })\n      })\n    }\n\n    this.promises.close.then(() => {\n      API.closeFlow(this)\n      API.handleModalClose(this)\n    })\n\n    this.closed = true\n\n    return this\n  }\n\n  // API functions\n\n  /**\n   * @param {boolean|string} queueName\n   * @return {Noty}\n   */\n  static closeAll (queueName = false) {\n    Object.keys(API.Store).forEach(id => {\n      if (queueName) {\n        if (\n          API.Store[id].options.queue === queueName && API.Store[id].killable\n        ) {\n          API.Store[id].close()\n        }\n      } else if (API.Store[id].killable) {\n        API.Store[id].close()\n      }\n    })\n    return this\n  }\n\n  /**\n   * @param {Object} obj\n   * @return {Noty}\n   */\n  static overrideDefaults (obj) {\n    API.Defaults = Utils.deepExtend({}, API.Defaults, obj)\n    return this\n  }\n\n  /**\n   * @param {int} amount\n   * @param {string} queueName\n   * @return {Noty}\n   */\n  static setMaxVisible (amount = API.DefaultMaxVisible, queueName = 'global') {\n    if (!API.Queues.hasOwnProperty(queueName)) {\n      API.Queues[queueName] = {maxVisible: amount, queue: []}\n    }\n\n    API.Queues[queueName].maxVisible = amount\n    return this\n  }\n\n  /**\n   * @param {string} innerHtml\n   * @param {String} classes\n   * @param {Function} cb\n   * @param {Object} attributes\n   * @return {NotyButton}\n   */\n  static button (innerHtml, classes = null, cb, attributes = {}) {\n    return new NotyButton(innerHtml, classes, cb, attributes)\n  }\n\n  /**\n   * @return {string}\n   */\n  static version () {\n    return VERSION\n  }\n\n  /**\n   * @param {String} workerPath\n   * @return {Push}\n   */\n  static Push (workerPath) {\n    return new Push(workerPath)\n  }\n}\n\n// Document visibility change controller\nUtils.visibilityChangeFlow()\n\n\n\n// WEBPACK FOOTER //\n// ./src/index.js", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/process/browser.js\n// module id = 7\n// module chunks = 0", "var g;\r\n\r\n// This works in non-strict mode\r\ng = (function() {\r\n\treturn this;\r\n})();\r\n\r\ntry {\r\n\t// This works if eval is allowed (see CSP)\r\n\tg = g || Function(\"return this\")() || (1,eval)(\"this\");\r\n} catch(e) {\r\n\t// This works if the window reference is available\r\n\tif(typeof window === \"object\")\r\n\t\tg = window;\r\n}\r\n\r\n// g can still be undefined, but nothing to do about it...\r\n// We return undefined, instead of nothing here, so it's\r\n// easier to handle this case. if(!global) { ...}\r\n\r\nmodule.exports = g;\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// (webpack)/buildin/global.js\n// module id = 8\n// module chunks = 0"], "sourceRoot": ""}