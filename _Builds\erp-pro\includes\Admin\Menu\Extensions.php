<?php
namespace WeDevs\WSL_ERP\Admin\Menu;
use WeDevs\WSL_ERP\Traits\Singleton;

// don't call the file directly
if ( ! defined('ABSPATH') ) {
    exit;
}

use WeDevs\ERP\Framework\Traits\Hooker;

class Extensions {

    use <PERSON>ton;
    use <PERSON>;

    private function __construct() {

    }

    public function on_load_page() {
        $this->action( 'admin_enqueue_scripts', 'admin_scripts' );
    }

    public function admin_scripts() {
        wp_enqueue_script( 'erp-toastr' );
        wp_enqueue_style( 'erp-toastr' );
    }

    public function entry() {
        include_once WPERP_VIEWS . '/modules.php';
    }
}
