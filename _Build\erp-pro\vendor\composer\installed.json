[{"name": "arcturial/clickatell", "version": "2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "https://github.com/arcturial/clickatell.git", "reference": "d348e41a4052547d60d17737bf1993dd794b5ca1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/arcturial/clickatell/zipball/d348e41a4052547d60d17737bf1993dd794b5ca1", "reference": "d348e41a4052547d60d17737bf1993dd794b5ca1", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "4.3.*"}, "suggest": {"symfony/config": "For usage with the Symfony Framework", "symfony/dependency-injection": "For usage with the Symfony Framework", "symfony/http-kernel": "For usage with the Symfony Framework"}, "time": "2016-03-19T21:20:59+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Clickatell\\": ["src", "test"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["GNU General Public License"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Standalone PHP library to integrate with the Clickatell SMS gateway", "homepage": "http://www.cainsvault.com"}, {"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "time": "2022-02-04T12:51:07+00:00", "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}}, {"name": "hoiio/hoiio-php", "version": "v0.06", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hoiio/hoiio-php.git", "reference": "6e927bd04015ff633497aff6280c7fbe9621d7ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoiio/hoiio-php/zipball/6e927bd04015ff633497aff6280c7fbe9621d7ee", "reference": "6e927bd04015ff633497aff6280c7fbe9621d7ee", "shasum": ""}, "require": {"php": ">=5.1.0"}, "suggest": {"ext-curl": "PHP <PERSON>l", "ext-json": "PHP JSON"}, "time": "2014-01-23T06:22:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"hoiio": "Services/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Hoiio API provides telephony services such as call, SMS and interactive voice responses (IVR). This SDK makes using the API a breeze.", "homepage": "http://developer.hoiio.com/", "keywords": ["Conference", "call", "ivr", "sms", "telephony"]}, {"name": "infobip/infobip-api-php-client", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/infobip/infobip-api-php-client.git", "reference": "3146b070b57713feaf4ced9b7cb06315885a7b5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/infobip/infobip-api-php-client/zipball/3146b070b57713feaf4ced9b7cb06315885a7b5a", "reference": "3146b070b57713feaf4ced9b7cb06315885a7b5a", "shasum": ""}, "require": {"netresearch/jsonmapper": "0.9.0"}, "time": "2015-11-18T11:41:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"infobip\\api\\": "infobip/api/", "infobip\\api\\client\\": "infobip/api/client/", "infobip\\api\\configuration\\": "infobip/api/configuration/", "infobip\\api\\model\\": "infobip/api/model/", "infobip\\api\\model\\sms\\": "infobip/api/model/sms/", "infobip\\api\\model\\sms\\mo\\": "infobip/api/model/sms/mo/", "infobip\\api\\model\\sms\\mo\\logs\\": "infobip/api/model/sms/mo/logs/", "infobip\\api\\model\\sms\\mo\\reports\\": "infobip/api/model/sms/mo/reports/", "infobip\\api\\model\\sms\\mt\\": "infobip/api/model/sms/mt/", "infobip\\api\\model\\sms\\mt\\logs\\": "infobip/api/model/sms/mt/logs/", "infobip\\api\\model\\sms\\mt\\reports\\": "infobip/api/model/sms/mt/reports/", "infobip\\api\\model\\sms\\mt\\send\\": "infobip/api/model/sms/mt/send/", "infobip\\api\\model\\sms\\mt\\send\\binary\\": "infobip/api/model/sms/mt/send/binary/", "infobip\\api\\model\\sms\\mt\\send\\textual\\": "infobip/api/model/sms/mt/send/textual/", "infobip\\api\\model\\nc\\": "infobip/api/model/nc/", "infobip\\api\\model\\nc\\logs\\": "infobip/api/model/nc/logs/", "infobip\\api\\model\\nc\\notify\\": "infobip/api/model/nc/notify/", "infobip\\api\\model\\nc\\query\\": "infobip/api/model/nc/query/", "infobip\\api\\model\\account\\": "infobip/api/model/account/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"email": "<EMAIL>"}], "description": "Infobip SMS library for PHP", "homepage": "http://dev.infobip.com", "keywords": ["Infobip", "api", "hlr", "msisdn", "sms"]}, {"name": "netresearch/jsonmapper", "version": "v0.9.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/cweiske/jsonmapper.git", "reference": "1c3803fa6d1be7bd083fb853f1c6af56f96d1833"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweiske/jsonmapper/zipball/1c3803fa6d1be7bd083fb853f1c6af56f96d1833", "reference": "1c3803fa6d1be7bd083fb853f1c6af56f96d1833", "shasum": ""}, "require-dev": {"phpunit/phpunit": "4.2.*", "squizlabs/php_codesniffer": "~1.5"}, "time": "2015-08-14T14:07:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.netresearch.de/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes"}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243", "reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": ""}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "time": "2019-12-27T09:44:58+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git", "reference": "bba5a9dfec7fcfbd679cfaf611d86b4d3759da26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/bba5a9dfec7fcfbd679cfaf611d86b4d3759da26", "reference": "bba5a9dfec7fcfbd679cfaf611d86b4d3759da26", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "time": "2022-10-25T01:46:02+00:00", "type": "phpcodesniffer-standard", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git", "reference": "262f9d81273932315d15d704f69b9d678b939cb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/262f9d81273932315d15d704f69b9d678b939cb3", "reference": "262f9d81273932315d15d704f69b9d678b939cb3", "shasum": ""}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "time": "2023-01-05T13:34:27+00:00", "type": "phpcodesniffer-standard", "installation-source": "source", "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}}, {"name": "squizlabs/php_codesniffer", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "add95a74551c3ba8fc99ef7651ad05f553b3fbbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/add95a74551c3ba8fc99ef7651ad05f553b3fbbf", "reference": "add95a74551c3ba8fc99ef7651ad05f553b3fbbf", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "time": "2022-12-22T21:46:41+00:00", "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "source", "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}}, {"name": "stripe/stripe-php", "version": "v7.128.0", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "c704949c49b72985c76cc61063aa26fefbd2724e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/c704949c49b72985c76cc61063aa26fefbd2724e", "reference": "c704949c49b72985c76cc61063aa26fefbd2724e", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.5.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^5.7 || ^9.0", "squizlabs/php_codesniffer": "^3.3"}, "time": "2022-05-05T17:18:02+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "support": {"issues": "https://github.com/stripe/stripe-php/issues", "source": "https://github.com/stripe/stripe-php/tree/v7.128.0"}}, {"name": "twilio/sdk", "version": "5.42.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/twilio/twilio-php.git", "reference": "0cfcb871b18a9c427dd9e8f0ed7458d43009b48a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twilio/twilio-php/zipball/0cfcb871b18a9c427dd9e8f0ed7458d43009b48a", "reference": "0cfcb871b18a9c427dd9e8f0ed7458d43009b48a", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"apigen/apigen": "^4.1", "guzzlehttp/guzzle": "^6.3", "phpunit/phpunit": ">=4.5"}, "suggest": {"guzzlehttp/guzzle": "An HTTP client to execute the API requests"}, "time": "2020-02-05T19:55:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Twilio\\": "src/<PERSON>wi<PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Twilio API Team", "email": "<EMAIL>"}], "description": "A PHP wrapper for Twilio's API", "homepage": "http://github.com/twilio/twilio-php", "keywords": ["api", "sms", "twi<PERSON>"]}, {"name": "wikimedia/composer-merge-plugin", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/wikimedia/composer-merge-plugin.git", "reference": "a4c4c62faebb112564be086ff6213f6033435fe6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wikimedia/composer-merge-plugin/zipball/a4c4c62faebb112564be086ff6213f6033435fe6", "reference": "a4c4c62faebb112564be086ff6213f6033435fe6", "shasum": ""}, "require": {"composer-plugin-api": "^1.1||^2.0", "php": ">=7.4.0"}, "require-dev": {"composer/composer": "^1.1||^2.0", "ext-json": "*", "mediawiki/mediawiki-phan-config": "0.11.1", "php-parallel-lint/php-parallel-lint": "~1.3.1", "phpspec/prophecy-phpunit": "~2.0.1", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "~3.7.1"}, "time": "2022-12-23T12:57:25+00:00", "type": "composer-plugin", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "class": "Wikimedia\\Composer\\Merge\\V2\\MergePlugin"}, "installation-source": "source", "autoload": {"psr-4": {"Wikimedia\\Composer\\Merge\\V2\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin to merge multiple composer.json files", "support": {"issues": "https://github.com/wikimedia/composer-merge-plugin/issues", "source": "https://github.com/wikimedia/composer-merge-plugin/tree/master"}}, {"name": "wp-coding-standards/wpcs", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git", "reference": "7da1894633f168fe244afc6de00d141f27517b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/7da1894633f168fe244afc6de00d141f27517b62", "reference": "7da1894633f168fe244afc6de00d141f27517b62", "shasum": ""}, "require": {"php": ">=5.4", "squizlabs/php_codesniffer": "^3.3.1"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || ^0.6", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically."}, "time": "2020-05-13T23:57:56+00:00", "type": "phpcodesniffer-standard", "installation-source": "source", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "keywords": ["phpcs", "standards", "wordpress"]}]