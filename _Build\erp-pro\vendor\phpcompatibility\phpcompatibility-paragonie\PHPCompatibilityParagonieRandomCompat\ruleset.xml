<?xml version="1.0"?>
<ruleset name="PHPCompatibilityParagonieRandomCompat">
    <description>PHPCompatibility ruleset for PHP_CodeSniffer which accounts for polyfills provided by the Paragonie random_compat library.</description>

    <rule ref="PHPCompatibility">
        <!-- https://github.com/paragonie/random_compat/blob/master/lib/random.php -->
        <exclude name="PHPCompatibility.Constants.NewConstants.php_version_idFound"/>

        <!-- https://github.com/paragonie/random_compat/blob/master/lib/random_bytes_*.php -->
        <exclude name="PHPCompatibility.FunctionUse.NewFunctions.random_bytesFound"/>

        <!-- https://github.com/paragonie/random_compat/blob/master/lib/random_int.php -->
        <exclude name="PHPCompatibility.FunctionUse.NewFunctions.random_intFound"/>

        <!-- https://github.com/paragonie/random_compat/blob/master/lib/error_polyfill.php -->
        <exclude name="PHPCompatibility.Classes.NewClasses.errorFound"/>
        <exclude name="PHPCompatibility.Classes.NewClasses.typeerrorFound"/>
    </rule>

    <!-- Prevent false positives being thrown when run over the code of random_compat itself. -->
    <rule ref="PHPCompatibility.IniDirectives.RemovedIniDirectives.mbstring_func_overloadDeprecated">
        <exclude-pattern>/random_compat/lib/byte_safe_strings\.php$</exclude-pattern>
    </rule>
    <rule ref="PHPCompatibility.FunctionUse.NewFunctions.stream_set_chunk_sizeFound">
        <exclude-pattern>/random_compat/lib/random_bytes_dev_urandom\.php$</exclude-pattern>
    </rule>
    <rule ref="PHPCompatibility.Constants.RemovedConstants.mcrypt_dev_urandomDeprecatedRemoved">
        <exclude-pattern>/random_compat/lib/random_bytes_mcrypt\.php$</exclude-pattern>
    </rule>
    <rule ref="PHPCompatibility.Extensions.RemovedExtensions.mcryptDeprecatedRemoved">
        <exclude-pattern>/random_compat/lib/random_bytes_mcrypt\.php$</exclude-pattern>
    </rule>
    <rule ref="PHPCompatibility.FunctionUse.RemovedFunctions.mcrypt_create_ivDeprecatedRemoved">
        <exclude-pattern>/random_compat/lib/random_bytes_mcrypt\.php$</exclude-pattern>
    </rule>
    <rule ref="PHPCompatibility.LanguageConstructs.NewLanguageConstructs.t_ns_separatorFound">
        <exclude-pattern>/random_compat/lib/random_bytes_libsodium\.php$</exclude-pattern>
    </rule>

</ruleset>
