!function(e){var t={};function n(a){if(t[a])return t[a].exports;var s=t[a]={i:a,l:!1,exports:{}};return e[a].call(s.exports,s,s.exports,n),s.l=!0,s.exports}n.m=e,n.c=t,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)n.d(a,s,function(t){return e[t]}.bind(null,s));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=773)}({773:function(e,t){window.Erp_Help_Scout=function(e,t,n,a){"use strict";var s={init:function(){if(null!==erpSC.contact_id&&void 0!==erpSC.contact_id){var e={action:"erp_helpscout_contact_hc_activity",contact_id:erpSC.contact_id,nonce:erpSC.nonce};n.post(erpSC.ajaxurl,e,(function(e){n(".erp-helpscout-activity-hs").removeClass("loading");var t=n(".erp-helpscout-activity-hs .inside");console.log(e),e.success?t.html(e.data.msg):e.data.msg&&t.html("<p>"+e.data.msg+"</p>")}))}n("#helpscout-send-message").on("click",(function(e){e.preventDefault(),s.send_message()})),n("#erp_helpscout_sync_form").on("submit",(function(e){e.preventDefault(),s.helpscout_contact_sync()})),n("#mailbox").on("change",(function(e){var t=n("#mailbox").val(),a=n(".user-loader");a.show();var s={action:"get_helpscout_user",mailbox_id:t};n.post(erpSC.ajaxurl,s,(function(e){e.success&&(a.hide(),n("#users").empty(),n.each(e.data,(function(e,t){n("#users").append('<option value="'+t.id+'">'+t.firstName+" "+t.lastName+"</option>")})))}))}))},send_message:function(){var e=n("#mailbox").val(),t=n("#users").val(),a=n("#helpscout_subject").val(),s=n("#helpscout-message").val(),o=n("#customer-email").val(),i=n("input[name=submit-helpscout-message]"),c=n(".sync-loader"),r=n("#response_div");if(""!=e&&""!=t&&""!=a&&""!=s){c.show(),i.attr("disabled","disabled"),r.html("");var l={mailbox:e,user:t,subject:a,message:s,email:o,action:"helpscout_send_message"};n.post(erpSC.ajaxurl,l,(function(e){r.html("<span>"+e.data.msg+"</span>"),c.hide(),i.removeAttr("disabled"),n("#mailbox").val(""),n("#users").val(""),n("#helpscout_subject").val(""),n("#helpscout-message").val("")}))}else r.html("<span> Please fill up all field ! </span>")},helpscout_contact_sync:function(){var e=n("#erp_helpscout_sync_form"),t=e.find("input[name=mailbox]:checked").map((function(){return n(this).val()})).get(),a=e.find("select[name=helpscout_life_stage]").val(),s=e.find("input[name=group_ids]:checked").map((function(){return n(this).val()})).get(),o=e.find("input[type=submit]"),i=e.find(".sync-loader"),c=e.find("#response_div"),r={action:"helpscout_contact_sync",mailboxes:t,contact_group:s,life_stage:a};o.attr("disabled","disabled"),i.show(),n.post(erpSC.ajaxurl,r,(function(e){e.success&&(c.html("<span>"+e.data.message+"</span>"),o.removeAttr("disabled"),i.hide())}))}};n(t).ready(s.init)}(window,document,jQuery)}});