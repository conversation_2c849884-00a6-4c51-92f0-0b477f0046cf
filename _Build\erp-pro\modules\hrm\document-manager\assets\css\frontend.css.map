{"version": 3, "sources": ["frontend.css"], "names": [], "mappings": "AAo5BA,aACI,aAAc,CACd,UACJ,CACA,gBACI,kBAAmB,CACnB,aACJ,CAGA,4CAEI,eAAgB,CAChB,mCAAoC,CACpC,iBACJ,CACA,oBACI,cACJ,CACA,yBACI,iBACJ,CACA,8BACI,SACJ,CACA,sBACI,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,kBACJ,CACA,yBACI,WAAY,CACZ,cAAe,CACf,iBAAkB,CAClB,yBAA0B,CAC1B,UAAW,CACX,aAAc,CACd,iBACJ,CACA,yBACI,aAAc,CACd,UAAW,CACX,aACJ,CACA,YACI,aAAc,CACd,UAAW,CACX,wBAAyB,CACzB,oBACJ,CACA,6BACI,uBACJ,CACA,4BACI,iBAAkB,CAClB,WAAY,CACZ,4CACJ,CACA,0BACI,kBACJ,CACA,6BACI,kBAAmB,CACnB,qBAAsB,CACtB,aACJ,CACA,uBACI,YAAa,CACb,kBACJ,CAGA,qDAEI,UACJ,CACA,sDAEI,WACJ,CAKA,gHAEI,WACJ,CACA,kDAEI,WACJ,CAKA,mGAEI,WACJ,CAGA,wBACI,UAAW,CACX,cACJ,CACA,8BACI,kBACJ,CACA,gCACI,aAAc,CACd,eACJ,CACA,sBACI,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,4CACJ,CACA,sCACI,cAAe,CACf,eAAgB,CAChB,aACJ,CACA,iCACI,qBAAsB,CACtB,aACJ,CACA,0IAII,YACJ,CACA,sCACI,cAAe,CACf,kBAAqB,CACrB,aACJ,CACA,8BACI,aAAc,CACd,cACJ,CACA,mCACI,iBAAkB,CAClB,QAAS,CACT,UACJ,CAGA,mCACI,UAAW,CACX,gBAAiB,CACjB,cACJ,CACA,mCACI,YAAa,CACb,4BAA6B,CAC7B,WAAY,CACZ,aAAc,CACd,mCAAoC,CACpC,cAAe,CACf,WACJ,CACA,qCACI,cACJ,CACA,eACI,wBAGJ,CACA,kCAHI,wBAAyB,CACzB,UAMJ,CAJA,mBACI,wBAGJ,CACA,iBACI,wBAAyB,CACzB,wBAAyB,CACzB,oBACJ,CACA,iBACI,wBAAyB,CACzB,wBAAyB,CACzB,aACJ,CAGA,4CACI,iBACJ,CACA,+CACI,WACJ,CAKA,6IACI,WACJ,CACA,4EAEI,oBACJ,CACA,+BACI,aAAc,CACd,cACJ,CACA,kCACI,aAAc,CACd,cACJ,CACA,+BACI,aAAc,CACd,cACJ,CACA,8BACI,aAAc,CACd,cACJ,CACA,8BACI,SAAc,CACd,cACJ,CACA,gCACI,aAAc,CACd,cACJ,CACA,+BACI,aAAc,CACd,cACJ,CACA,wBACI,aAAc,CACd,cACJ,CACA,UACI,iBAAkB,CAClB,WACJ,CACA,iBACI,cAAe,CACf,mBACJ,CACA,WACI,uBACJ,CACA,aACI,iCAAsC,CACtC,aACJ,CACA,mBACI,oBACJ,CACA,SACI,qBAAsB,CACtB,YAAa,CACb,kBACJ,CACA,gBACI,iBACJ,CACA,4BACI,qBAAsB,CACtB,YAAa,CACb,iBAAkB,CAClB,gBACJ,CACA,eACI,oBACJ,CACA,6CACI,aACJ,CACA,aACI,YACJ,CACA,sCACI,UACJ,CACA,WACI,UAAY,CACZ,wBAAwB,CACxB,eAAgB,CAChB,UAAW,CACX,iBACJ,CACA,iBACI,aAAc,CACd,qBACJ,CACA,mBACI,WAAY,CACZ,iBACJ,CACA,aACI,cAAe,CACf,mBACJ,CACA,8BACI,wBAA0B,CAC1B,UAAa,CACb,WAAa,CACb,iBACJ,CAGA,aACI,iBACJ,CACA,uBACI,eAAgB,CAChB,YAAa,CACb,WAAY,CACZ,YAAa,CACb,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CACX,qBAAsB,CACtB,SAAU,CACV,yCAA0C,CAC1C,sCAAuC,CACvC,iCACJ,CACA,0BACI,cAAe,CACf,iBAAkB,CAClB,kBACJ,CAIA,yCACI,aACJ,CACA,qDACI,aACJ,CACA,0CACI,aACJ,CACA,oCACI,kBACJ,CACA,qBACI,kBACJ,CACA,SACI,YACJ,CACA,wBACI,UACJ,CAGA,eACE,UAAW,CACX,WAAY,CACZ,mFAAoF,CACpF,2BAA4B,CAC5B,4BAA6B,CAE7B,4BACF,CAIA,qGACE,2BACF", "file": "frontend.css", "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/** List box headers */\n.list-header {\n    display: table;\n    width: 100%;\n}\n.list-header li {\n    display: table-cell;\n    color: #9E9FA3;\n}\n\n/** List and Grid section */\n.dash-list .list-body,\n.dash-list .grid-body {\n    background: #fff;\n    box-shadow: 3px 2px 12px 7px #f4f5fc;\n    position: relative;\n}\n.dash-list .columns {\n    flex-wrap: wrap;\n}\n.dash-list .has-checkbox {\n    padding-left: 20px;\n}\n.dash-list .is-checkbox + label {\n    padding: 0;\n}\n.dash-list .thumbnail {\n    width: 40px;\n    height: 40px;\n    margin-right: 15px;\n    border-radius: 100%;\n}\n.dash-list .box-btn span {\n    padding: 5px;\n    font-size: 14px;\n    border-radius: 3px;\n    text-transform: capitalize;\n    width: 90px;\n    display: block;\n    text-align: center;\n}\n.grid-body .box-btn span {\n    margin: 0 auto;\n    width: 74px;\n    padding: 4px 0;\n}\n.list-table {\n    display: table;\n    width: 100%;\n    border-collapse: separate;\n    border-spacing: 0 1em;\n}\n.list-table .list-table-body {\n    display: table-row-group;\n}\n.list-table .list-table-row {\n    display: table-row;\n    height: 80px;\n    box-shadow: 0 2px 10px 0 rgba(202,210,240,0.2);\n}\n.list-table .row-selected {\n    background: #D6E8FA;\n}\n.list-table .list-table-cell {\n    display: table-cell;\n    vertical-align: middle;\n    color: #98A0B4;\n}\n.list-body .align-part {\n    display: flex;\n    align-items: center;\n}\n\n/*start width*/\n.list-header li:first-child,\n.list-body .has-checkbox {\n    width: 70px;\n}\n.list-header li:nth-child(2),\n.list-body .img-and-name {\n    width: 270px;\n}\n.list-header li:nth-child(3),\n.list-body .box-designtion {\n    width: 170px;\n}\n.list-header li:nth-child(4),\n.list-body .box-department {\n    width: 170px;\n}\n.list-header li:nth-child(5),\n.list-body .box-type {\n    width: 140px;\n}\n.list-header li:nth-child(6),\n.list-body .box-date {\n    width: 120px;\n}\n.list-header li:nth-child(7),\n.list-body .box-btn {\n    width: 120px;\n}\n\n/*end width*/\n.list-body .box-actions {\n    width: 30px;\n    font-size: 20px;\n}\n.list-body .img-and-name span {\n    padding-right: 10px;\n}\n.list-body .img-and-name span a {\n    color: #27313a;\n    font-weight: 600;\n}\n.dash-list .grid-body {\n    text-align: center;\n    padding: 20px 0 28px;\n    border-radius: 3px;\n    box-shadow: 0 2px 10px 0 rgba(202,210,240,0.2);\n}\n.dash-list .grid-body .img-and-name a {\n    font-size: 15px;\n    font-weight: 600;\n    color: #18222C;\n}\n.dash-list .grid-body .thumbnail {\n    margin: 10px auto 20px;\n    display: block;\n}\n.dash-list .grid-body .box-department,\n.dash-list .grid-body .has-checkbox,\n.dash-list .grid-body .box-date,\n.dash-list .grid-body .box-type {\n    display: none;\n}\n.dash-list .grid-body .box-designtion {\n    font-size: 13px;\n    padding: 5px 0 15px 0;\n    color: #9AA1B4;\n}\n.grid-body .action-dropdown i {\n    color: #DEE2EA;\n    font-size: 20px;\n}\n.dash-list .grid-body .box-actions {\n    position: absolute;\n    top: 10px;\n    right: 10px;\n}\n\n/** List item edit/trash options */\n.action-dropdown .dropdown-trigger {\n    width: 10px;\n    padding-left: 3px;\n    cursor: pointer;\n}\n.action-dropdown .dropdown-content {\n    display: flex;\n    justify-content: space-evenly;\n    width: 100px;\n    color: #BACED8;\n    box-shadow: 3px 2px 12px 7px #f4f5fc;\n    font-size: 16px;\n    left: -100px;\n}\n.action-dropdown .dropdown-content i {\n    cursor: pointer;\n}\n.status-active {\n    background-color: #23d160;\n    border-color: transparent;\n    color: #fff;\n}\n.status-terminated {\n    background-color: #ff3860;\n    border-color: transparent;\n    color: #fff;\n}\n.status-resigned {\n    background-color: #ffdd57;\n    border-color: transparent;\n    color: rgba(0, 0, 0, 0.7);\n}\n.status-deceased {\n    background-color: #363636;\n    border-color: transparent;\n    color: whitesmoke;\n}\n\n/* Role based design */\n.role-normal-user .list-table .img-and-name {\n    padding-left: 25px;\n}\n.role-normal-user .list-header li:nth-child(2) {\n    width: 220px;\n}\n.role-normal-user .list-header li:nth-child(3),\n.role-normal-user .list-header li:nth-child(4) {\n    width: 170px;\n}\n.role-normal-user .list-header li:nth-child(5) {\n    width: 170px;\n}\n.is-radio[type=\"radio\"] + label::before,\n.is-checkbox[type=\"checkbox\"] + label::before {\n    border-color: #DEE2EA;\n}\n.align-part .fa.fa-folder-open{\n    color: #ffbc00;\n    font-size: 20px;\n}\n.align-part .fa.fa-file-picture-o{\n    color: #ff00b1;\n    font-size: 20px;\n}\n.align-part .fa.fa-file-text-o{\n    color: #7a00ff;\n    font-size: 20px;\n}\n.align-part .fa.fa-file-zip-o{\n    color: #ff00eb;\n    font-size: 20px;\n}\n.align-part .fa.fa-file-pdf-o{\n    color: #ff0000;\n    font-size: 20px;\n}\n.align-part .fa.fa-file-excel-o{\n    color: #00ff5a;\n    font-size: 20px;\n}\n.align-part .fa.fa-file-word-o{\n    color: #0400ff;\n    font-size: 20px;\n}\n.align-part .fa.fa-file{\n    color: #0072ff;\n    font-size: 20px;\n}\n.box-view {\n    text-align: center;\n    width: 200px;\n}\n.box-view.action {\n    font-size: 30px;\n    padding-bottom: 15px;\n}\n.ash-white {\n    color: #9e9fa3 !important;\n}\n.light-black {\n    border-color: rgba(229, 233, 242, 0.6);\n    color: #363636;\n}\n.light-black:hover {\n    border-color: #B5B5B5;\n}\n.md-cont {\n    background-color: #fff;\n    padding: 45px;\n    border-radius: 10px;\n}\n.folder_tree ul{\n    padding-left: 15px;\n}\n.single_employee_class_wrap {\n    background-color: #fff;\n    padding: 15px;\n    border-radius: 5px;\n    min-height: 370px;\n}\n.operator span{\n    display: inline-block;\n}\n.columns .column .grid-body .align-part span{\n    display: block;\n}\n.hide_header {\n    display: none;\n}\n.columns .column .grid-body .box-view{\n    width: 100%;\n}\n.bc_single {\n    color: white;\n    background-color:#276cda;\n    padding: 2px 5px;\n    margin: 2px;\n    border-radius: 2px;\n}\n.bc_single:hover {\n    color: #276cda;\n    background-color: white;\n}\n.folder_tree ul li {\n    padding: 2px;\n    margin-bottom: 3px;\n}\n.modal_title {\n    font-size: 20px;\n    padding-bottom: 25px;\n}\n.current, .folder_tree a:hover {\n    background-color : #3273dc;\n    color : white;\n    padding : 2px;\n    border-radius:3px;\n}\n\n/****************/\na.doc_action {\n    position: relative\n}\nul.doc_action_dropdown {\n    margin-top: -2px;\n    display: none;\n    width: 135px;\n    padding: 10px;\n    text-align: left;\n    position: absolute;\n    left: -50px;\n    background-color: #fff;\n    z-index: 1;\n    -webkit-box-shadow: 0 0 35px -13px #302c30;\n    -moz-box-shadow: 0 0 35px -13px #302c30;\n    box-shadow: 0 0 35px -13px #302c30;\n}\nul.doc_action_dropdown li {\n    font-size: 15px;\n    padding-left: 10px;\n    padding-bottom: 7px;\n}\nul.doc_action_dropdown li a{\n    color: #98a0b4;\n}\na.doc_action {\n    color: #98a0b4;\n}\na.doc_action:hover, ul.doc_action_dropdown li a:hover{\n    color: #3273dc;\n}\na.doc_action:hover ul.doc_action_dropdown {\n    display: block;\n}\nul.doc_action_dropdown li a span.fa {\n    padding-right : 10px;\n}\n.share_files_control {\n    margin-bottom: 15px;\n}\n.no-show {\n    display: none;\n}\n#erp-doc-share-template {\n    width: 100%;\n}\n\n\n.icon-document {\n  width: 33px;\n  height: 33px;\n  background: url(../css/assets/images/icons-for-overview-update-2x.png) no-repeat 0 0;\n  background-size: 193px 225px;\n  background-position-y: -192px;\n  /*background-position-x: -94px;*/\n  background-position-x: -159px;\n}\n.erp-side-menu ul li a:hover .icon-document {\n  background-position-x: -94px;\n}\n.erp-side-menu ul li.router-link-active a .icon-document {\n  background-position-x: -94px;\n}\n"]}