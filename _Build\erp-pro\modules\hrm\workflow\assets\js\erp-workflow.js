/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./modules/hrm/workflow/assets/js/main.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./modules/hrm/workflow/assets/js/components/action-container/index.js":
/*!*****************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/action-container/index.js ***!
  \*****************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/action-container/template.html\"),\n  props: ['actions'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      current_action: '',\n      action_edit_mode: false,\n      current_editing_action_id: '',\n      model: {},\n      user_roles: user_roles\n    };\n  },\n\n  methods: {\n    showShortCodes(element) {\n      var content = '<p>' + this.sharedState.i18n.text_variables_help_text + '</p>';\n      var selectedText = '';\n\n      if (this.sharedState.conditions_list) {\n        this.sharedState.conditions_list.forEach(function (condition) {\n          content += '<span class=\"code text_variables_tag\">[' + condition.key + ']</span> ';\n        });\n      }\n\n      jQuery.erpPopup({\n        title: this.sharedState.i18n.text_variables,\n        button: this.sharedState.i18n.insert,\n        id: 'erp-wf-shortcodes',\n        content: content,\n        extraClass: 'smaller',\n        onReady: function () {\n          var modal = this;\n          jQuery('span.text_variables_tag').click(function (event) {\n            var range = document.createRange();\n            range.selectNodeContents(this);\n            var textSelectionObj = window.getSelection();\n            textSelectionObj.addRange(range);\n            selectedText = textSelectionObj.toString();\n          });\n        },\n        onSubmit: function (modal) {\n          document.execCommand('copy');\n          jQuery(element).focus();\n          document.execCommand('insertText', false, selectedText);\n          modal.closeModal();\n        }\n      });\n    },\n\n    changeAction(name, title) {\n      this.current_action = name;\n      this.action_edit_mode = false;\n      var modelDefaultData = this.getModelDefaultData(name);\n      this.model = modelDefaultData;\n    },\n\n    addNewAction(action) {\n      // Update action\n      if (this.action_edit_mode) {\n        var current_editing_action_id = this.current_editing_action_id;\n\n        for (var key in action) {\n          this.actions[current_editing_action_id][key] = action[key];\n        }\n\n        this.current_action = '';\n        this.action_edit_mode = false; // New action\n      } else {\n        if (this.current_action) {\n          var new_action = Vue.util.extend({}, action);\n          this.actions.push(new_action);\n          this.current_action = '';\n        }\n      }\n    },\n\n    editAction(action) {\n      this.current_action = action.name;\n      this.action_edit_mode = true;\n      this.current_editing_action_id = this.actions.indexOf(action);\n\n      if (action.name == 'send_email' || action.name == 'send_invoice' || action.name == 'send_sms') {\n        action.send_itself = JSON.parse(action.send_itself);\n      }\n\n      if (action.name == 'schedule_meeting') {\n        action.all_day = JSON.parse(action.all_day);\n        action.allow_notification = JSON.parse(action.allow_notification);\n      }\n\n      this.model = action;\n    },\n\n    closeAction() {\n      this.action_edit_mode = false;\n      this.current_action = '';\n    },\n\n    removeAction(action) {\n      this.actions.$remove(action);\n      this.action_edit_mode = false;\n      this.current_action = '';\n    },\n\n    getModelDefaultData(name) {\n      var data = {\n        add_activity: {\n          log_type: 'call',\n          subject: '',\n          message: '',\n          start_date: moment().format('YYYY-MM-DD'),\n          start_time: moment().format('LT'),\n          invite_contact: []\n        },\n        add_user_role: {\n          role: 'administrator'\n        },\n        assign_task: {\n          user: '',\n          contact_id: '',\n          task_title: '',\n          message: '',\n          task_date: moment().format('YYYY-MM-DD'),\n          task_time: moment().format('LT')\n        },\n        schedule_meeting: {\n          message: '',\n          schedule_title: '',\n          start_date: moment().format('YYYY-MM-DD'),\n          start_time: moment().format('LT'),\n          end_date: moment().format('YYYY-MM-DD'),\n          end_time: moment().format('LT'),\n          all_day: false,\n          invite_contact: [],\n          schedule_type: 'call',\n          notification_via: 'email',\n          allow_notification: false,\n          notification_time_interval: '',\n          notification_time: 'minute'\n        },\n        send_email: {\n          subject: '',\n          message: '',\n          user: '',\n          send_itself: false\n        },\n        send_invoice: {\n          subject: '',\n          message: '',\n          user: '',\n          send_itself: false\n        },\n        trigger_action_hook: {\n          hook_name: ''\n        },\n        update_field: {\n          field_name: '',\n          field_value: ''\n        },\n        send_sms: {\n          message: '',\n          user: '',\n          send_itself: false\n        }\n      };\n      return data[name];\n    }\n\n  },\n  computed: {\n    assets_url() {\n      return pluginURL + '/assets/';\n    },\n\n    action_chunk_size() {\n      if (screen.width >= 1600) {\n        return 6;\n      }\n\n      if (screen.width <= 768) {\n        return 2;\n      }\n\n      return 4;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/action-container/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/action-container/template.html":
/*!**********************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/action-container/template.html ***!
  \**********************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row row-padding\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"row\\\">\\n            <div class=\\\"col-md-6 col-xl-4\\\">\\n                <div class=\\\"actions-list\\\">\\n                    <label v-if=\\\"actions.length === 0\\\"><strong>{{ sharedState.i18n.no_action_added }}!</strong></label>\\n                    <br />\\n\\n                    <div v-for=\\\"action in actions\\\" class=\\\"row action\\\" @click.prevent=\\\"editAction(action)\\\">\\n                        <div class=\\\"col-md-10 col-xs-8\\\">\\n                            <img alt=\\\"\\\" itemprop=\\\"image\\\" :src=\\\"assets_url + 'images/icons/small/' + action.name + '.png'\\\"> <label>{{ action.title }}</label>\\n                        </div>\\n                        <div class=\\\"col-md-2 col-xs-4 pull-right\\\" style=\\\"text-align: right;\\\">\\n                            <label>\\n                                <a href=\\\"#\\\" @click.prevent=\\\"editAction(action)\\\"><span class=\\\"dashicons dashicons-edit\\\"></span></a>\\n                                <a href=\\\"#\\\" @click.prevent=\\\"removeAction(action)\\\"><span class=\\\"dashicons dashicons-dismiss remove\\\"></span></a>\\n                            </label>\\n                        </div>\\n                    </div>\\n                </div>\\n                <div v-if=\\\"current_action\\\" id=\\\"action-container\\\">\\n                    <send-email-action v-if=\\\"current_action == 'send_email'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model\\n                    =\\\"model\\\"></send-email-action>\\n                    <assign-task-action v-if=\\\"current_action == 'assign_task'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model=\\\"model\\\"></assign-task-action>\\n                    <trigger-action-hook-action v-if=\\\"current_action == 'trigger_action_hook'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model=\\\"model\\\"></trigger-action-hook-action>\\n                    <update-field-action v-if=\\\"current_action == 'update_field'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model=\\\"model\\\"></update-field-action>\\n                    <add-user-role-action v-if=\\\"current_action == 'add_user_role'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :user_roles=\\\"user_roles\\\" :model=\\\"model\\\"></add-user-role-action>\\n                    <add-activity-action v-if=\\\"current_action == 'add_activity'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model\\n                    =\\\"model\\\"></add-activity-action>\\n                    <schedule-meeting-action v-if=\\\"current_action == 'schedule_meeting'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model=\\\"model\\\"></schedule-meeting-action>\\n                    <send-invoice-action v-if=\\\"current_action == 'send_invoice'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model\\n                    =\\\"model\\\"></send-invoice-action>\\n                    <send-receipt-action v-if=\\\"current_action == 'send_receipt'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model\\n                    =\\\"model\\\"></send-receipt-action>\\n                    <send-sms-action v-if=\\\"current_action == 'send_sms'\\\" :action_edit_mode=\\\"action_edit_mode\\\" :model\\n                    =\\\"model\\\"></send-sms-action>\\n                </div>\\n            </div>\\n            <div class=\\\"col-md-6 col-xl-6\\\">\\n                <div class=\\\"row\\\">\\n                    <div class=\\\"col-md-12\\\">\\n                        <label><strong>{{ sharedState.i18n.choose_an_action }}</strong></label>\\n                        <br />\\n                        <div v-for=\\\"actions in sharedState.actions_list | chunk action_chunk_size\\\" class=\\\"row\\\">\\n                            <div class=\\\"col-xs-6 col-md-3 col-xl-2\\\" v-for=\\\"action in actions\\\" @click.prevent=\\\"changeAction(action.key, action.label)\\\" style=\\\"text-align: center; cursor: pointer;\\\">\\n                                <img alt=\\\"\\\" itemprop=\\\"image\\\" :src=\\\"assets_url + 'images/icons/' + action.key + '.png'\\\" style=\\\"width: 64px;\\\">\\n                                <p>{{ action.label }}</p>\\n                            </div>\\n                        </div>\\n                    </div>\\n                </div>\\n            </div>\\n        </div>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/action-container/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/add-activity-action/index.js":
/*!********************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/add-activity-action/index.js ***!
  \********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n/* harmony import */ var _dateTimeMixin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../.././dateTimeMixin */ \"./modules/hrm/workflow/assets/js/dateTimeMixin.js\");\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/add-activity-action/template.html\"),\n  mixins: [_dateTimeMixin__WEBPACK_IMPORTED_MODULE_1__[\"default\"]],\n  props: ['action_edit_mode', 'crm_users', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'add_activity',\n      title: 'Add Activity',\n      timer: 0,\n      isLoading: false,\n      errors: {\n        start_date: false,\n        start_time: false,\n        subject: false,\n        invite_contact: false,\n        message: false\n      }\n    };\n  },\n\n  ready() {\n    var self = this;\n    jQuery.get(ajaxurl + '?action=erp_wf_get_crm_users&_wpnonce=' + nonces.fetch_users, function (response) {\n      if (response.success) {\n        self.crm_users = response.data;\n      }\n    });\n  },\n\n  computed: {\n    tinymceSettings() {\n      var shortcodes = {};\n      this.sharedState.conditions_list.forEach(function (condition) {\n        shortcodes[condition.key] = condition.label;\n      });\n      return {\n        pluginURL: pluginURL,\n        shortcodes: shortcodes\n      };\n    }\n\n  },\n  methods: {\n    showShortCodes(element) {\n      this.$parent.showShortCodes(element);\n    },\n\n    addNewAction(e) {\n      if (!this.model.start_date) {\n        this.errors.start_date = true;\n        return;\n      } else {\n        this.errors.start_date = false;\n      }\n\n      if (!this.model.start_time) {\n        this.errors.start_time = true;\n        return;\n      } else {\n        this.errors.start_time = false;\n      }\n\n      if (this.model.log_type == 'email' && !this.model.subject) {\n        this.errors.subject = true;\n        return;\n      } else {\n        this.errors.subject = false;\n      }\n\n      if (this.model.log_type == 'meeting' && this.model.invite_contact.length === 0) {\n        this.errors.invite_contact = true;\n        return;\n      } else {\n        this.errors.invite_contact = false;\n      }\n\n      if (!this.model.message) {\n        this.errors.message = true;\n        return;\n      } else {\n        this.errors.message = false;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        log_type: this.model.log_type,\n        subject: this.model.subject,\n        message: this.model.message,\n        start_date: this.model.start_date,\n        start_time: this.model.start_time,\n        invite_contact: this.model.invite_contact\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    },\n\n    searchUsers(search) {\n      this.isLoading = true;\n\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n\n      this.timer = setTimeout(() => {\n        var self = this;\n        jQuery.get(ajaxurl + '?action=erp_wf_get_crm_users&s=' + search + '&_wpnonce=' + nonces.fetch_users, function (response) {\n          if (response.success) {\n            self.crm_users = response.data;\n          }\n        });\n        this.isLoading = false;\n      }, 1000);\n    },\n\n    updateCrmUserSelect(value) {\n      this.model.invite_contact = value;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/add-activity-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/add-activity-action/template.html":
/*!*************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/add-activity-action/template.html ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"log_type\\\">{{ sharedState.i18n.log_type }}</label><br />\\n        <singleselect :selected.sync=\\\"model.log_type\\\" :options=\\\"[{key: 'call', label: 'Log a Call'}, {key: 'meeting', label: 'Log a Meeting'}, {key: 'email', label: 'Log an Email'}, {key: 'sms', label: 'Log an SMS'}]\\\"></singleselect>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"start_date\\\">{{ sharedState.i18n.date }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-date-field full-width {{ errors.start_date ? 'v-error' : '' }}\\\" v-model=\\\"model.start_date\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.start_date\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"start_time\\\">{{ sharedState.i18n.time }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-time-field full-width {{ errors.start_time ? 'v-error' : '' }}\\\" v-model=\\\"model.start_time\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.start_time\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\" v-if=\\\"model.log_type == 'email'\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"subject\\\">{{ sharedState.i18n.subject }}</label><br />\\n        <input type=\\\"text\\\" name=\\\"subject\\\" id=\\\"subject\\\" class=\\\"full-width {{ errors.subject ? 'v-error' : '' }}\\\" v-model=\\\"model.subject\\\" style=\\\"width: 95%;\\\"> <a href=\\\"#\\\" @click.prevent=\\\"showShortCodes('#subject')\\\" title=\\\"{{ sharedState.i18n.text_variables }}\\\"><i class=\\\"mce-ico mce-i-shortcode\\\"></i></a>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.subject\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\" v-if=\\\"model.log_type == 'meeting'\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"invite_contact\\\">{{ sharedState.i18n.invite_crm_user }}</label><br />\\n        <multiselect :selected=\\\"model.invite_contact\\\" :options=\\\"crm_users\\\" :multiple=\\\"true\\\" :searchable=\\\"true\\\" @search-change=\\\"searchUsers\\\" :loading=\\\"isLoading\\\" :close-on-select=\\\"true\\\" :clear-on-select=\\\"false\\\" :limit=\\\"2\\\" :placeholder=\\\"sharedState.i18n.select_crm_users\\\" key=\\\"id\\\" label=\\\"name\\\" @update=\\\"updateCrmUserSelect\\\"></multiselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.invite_contact\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"message\\\">{{ sharedState.i18n.description }}</label><br />\\n        <text-editor :content.sync=\\\"model.message\\\" :tinymce-settings=\\\"tinymceSettings\\\"></text-editor>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.message\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/add-activity-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/add-user-role-action/index.js":
/*!*********************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/add-user-role-action/index.js ***!
  \*********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/add-user-role-action/template.html\"),\n  props: ['action_edit_mode', 'user_roles', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'add_user_role',\n      title: 'Add User Role'\n    };\n  },\n\n  methods: {\n    addNewAction(e) {\n      var action = {\n        name: this.name,\n        title: this.title,\n        role: this.model.role\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/add-user-role-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/add-user-role-action/template.html":
/*!**************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/add-user-role-action/template.html ***!
  \**************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"role\\\">{{ sharedState.i18n.role }}</label><br />\\n        <singleselect :selected.sync=\\\"model.role\\\" :options=\\\"user_roles\\\"></singleselect>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/add-user-role-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/assign-task-action/index.js":
/*!*******************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/assign-task-action/index.js ***!
  \*******************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n/* harmony import */ var _dateTimeMixin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../.././dateTimeMixin */ \"./modules/hrm/workflow/assets/js/dateTimeMixin.js\");\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/assign-task-action/template.html\"),\n  mixins: [_dateTimeMixin__WEBPACK_IMPORTED_MODULE_1__[\"default\"]],\n  props: ['action_edit_mode', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'assign_task',\n      title: 'Assign Task',\n      display_choose_contact: false,\n      users: [],\n      contacts: [],\n      timer: 0,\n      isLoading: false,\n      errors: {\n        task_title: false,\n        contact_id: false,\n        user: false,\n        task_date: false,\n        task_time: false,\n        message: false\n      }\n    };\n  },\n\n  created() {\n    if (this.$parent.$parent.events_group != 'crm' && this.$parent.$parent.events_group != 'imap') {\n      this.display_choose_contact = true;\n    } else {\n      this.display_choose_contact = false;\n    }\n  },\n\n  ready() {\n    var self = this;\n    jQuery.get(ajaxurl + '?action=erp_wf_get_employees&_wpnonce=' + nonces.fetch_users, function (response) {\n      if (response.success) {\n        self.users = response.data;\n      }\n    });\n\n    if (this.display_choose_contact) {\n      jQuery.get(ajaxurl + '?action=erp_wf_get_contacts&_wpnonce=' + nonces.fetch_users, function (response) {\n        if (response.success) {\n          self.contacts = response.data;\n        }\n      });\n    }\n  },\n\n  computed: {\n    tinymceSettings() {\n      var shortcodes = {};\n      this.sharedState.conditions_list.forEach(function (condition) {\n        shortcodes[condition.key] = condition.label;\n      });\n      return {\n        pluginURL: pluginURL,\n        shortcodes: shortcodes\n      };\n    }\n\n  },\n  methods: {\n    showShortCodes(element) {\n      this.$parent.showShortCodes(element);\n    },\n\n    addNewAction(e) {\n      if (!this.model.task_title) {\n        this.errors.task_title = true;\n        return;\n      } else {\n        this.errors.task_title = false;\n      }\n\n      if (this.display_choose_contact && !this.model.contact_id) {\n        this.errors.contact_id = true;\n        return;\n      } else {\n        this.errors.contact_id = false;\n      }\n\n      if (!this.model.user) {\n        this.errors.user = true;\n        return;\n      } else {\n        this.errors.user = false;\n      }\n\n      if (!this.model.task_date) {\n        this.errors.task_date = true;\n        return;\n      } else {\n        this.errors.task_date = false;\n      }\n\n      if (!this.model.task_time) {\n        this.errors.task_time = true;\n        return;\n      } else {\n        this.errors.task_time = false;\n      }\n\n      if (!this.model.message) {\n        this.errors.message = true;\n        return;\n      } else {\n        this.errors.message = false;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        user: this.model.user,\n        contact_id: this.model.contact_id,\n        task_title: this.model.task_title,\n        message: this.model.message,\n        task_date: this.model.task_date,\n        task_time: this.model.task_time\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    },\n\n    searchUsers(search) {\n      this.isLoading = true;\n\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n\n      this.timer = setTimeout(() => {\n        var self = this;\n        jQuery.get(ajaxurl + '?action=erp_wf_get_employees&s=' + search + '&_wpnonce=' + nonces.fetch_users, function (response) {\n          if (response.success) {\n            self.users = response.data;\n          }\n        });\n        this.isLoading = false;\n      }, 1000);\n    },\n\n    updateUserSelect(value) {\n      this.model.user = value;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/assign-task-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/assign-task-action/template.html":
/*!************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/assign-task-action/template.html ***!
  \************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"task_title\\\">{{ sharedState.i18n.task_title }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"full-width {{ errors.task_title ? 'v-error' : '' }}\\\" id=\\\"task_title\\\" v-model=\\\"model.task_title\\\" style=\\\"width: 95%;\\\"> <a href=\\\"#\\\" @click.prevent=\\\"showShortCodes('#task_title')\\\" title=\\\"{{ sharedState.i18n.text_variables }}\\\"><i class=\\\"mce-ico mce-i-shortcode\\\"></i></a>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.task_title\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\" v-if=\\\"display_choose_contact\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label>{{ sharedState.i18n.choose_a_contact }}</label><br />\\n        <singleselect :selected.sync=\\\"model.contact_id\\\" :options=\\\"contacts\\\" key=\\\"id\\\" label=\\\"name\\\"></singleselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.contact_id\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"to\\\">{{ sharedState.i18n.employees }}</label><br />\\n        <multiselect :selected=\\\"model.user\\\" :options=\\\"users\\\" :multiple=\\\"true\\\" :searchable=\\\"true\\\" @search-change=\\\"searchUsers\\\" :loading=\\\"isLoading\\\" :close-on-select=\\\"true\\\" :clear-on-select=\\\"false\\\" :limit=\\\"2\\\" :placeholder=\\\"sharedState.i18n.select_employees\\\" key=\\\"id\\\" label=\\\"name\\\" @update=\\\"updateUserSelect\\\"></multiselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.user\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"task_date\\\">{{ sharedState.i18n.date }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-date-field full-width {{ errors.task_date ? 'v-error' : '' }}\\\" v-model=\\\"model.task_date\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.task_date\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"task_time\\\">{{ sharedState.i18n.time }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-time-field full-width {{ errors.task_time ? 'v-error' : '' }}\\\" v-model=\\\"model.task_time\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.task_time\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"message\\\">{{ sharedState.i18n.description }}</label><br>\\n        <text-editor :content.sync=\\\"model.message\\\" :tinymce-settings=\\\"tinymceSettings\\\"></text-editor>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.message\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/assign-task-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/condition/index.js":
/*!**********************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/condition/index.js ***!
  \**********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/condition/template.html\"),\n  props: ['id', 'conditions_group', 'condition', 'events_list'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      has_value: true,\n      condition_name: '',\n      operator: '=',\n      value: ''\n    };\n  },\n\n  ready() {\n    if (this.$parent.workflow_edit_mode && typeof this.id != \"undefined\") {\n      this.condition_name = this.$parent.conditions[this.id].condition_name ? this.$parent.conditions[this.id].condition_name : '';\n      this.operator = this.$parent.conditions[this.id].operator ? this.$parent.conditions[this.id].operator : '=';\n      this.value = this.$parent.conditions[this.id].value ? this.$parent.conditions[this.id].value : this.$parent.conditions[this.id].value;\n    }\n  },\n\n  methods: {\n    removeCondition(condition) {\n      this.$parent.removeCondition(condition);\n    },\n\n    changeCondition(value) {\n      this.$parent.conditions[this.id].condition_name = this.condition_name;\n      this.$parent.conditions[this.id].operator = this.operator;\n      this.$parent.conditions[this.id].value = this.value;\n    },\n\n    changeOperator(value) {\n      this.$parent.conditions[this.id].operator = this.operator;\n      this.$parent.conditions[this.id].value = this.value;\n    },\n\n    changeValue(value) {\n      this.$parent.conditions[this.id].value = this.value;\n    }\n\n  },\n  watch: {\n    'sharedState.conditions_list': function (val, oldVal) {\n      this.condition_name = this.sharedState.conditions_list && this.sharedState.conditions_list.hasOwnProperty(this.condition_name) ? this.condition_name : '';\n    }\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/condition/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/condition/template.html":
/*!***************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/condition/template.html ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"col-md-1 col-and-or\\\" v-if=\\\"id > 0\\\">\\n    <span style=\\\"float: right;\\\" class=\\\"{{ conditions_group }}\\\">{{ conditions_group | uppercase }}</span>\\n</div>\\n\\n<div class=\\\"col-md-1 col-and-or\\\" style=\\\"text-align: center;\\\" v-else>\\n    <span>&nbsp;</span>\\n</div>\\n\\n<div class=\\\"col-md-3 col-xl-2\\\">\\n    <singleselect :selected.sync=\\\"condition_name\\\" :options=\\\"sharedState.conditions_list\\\" @update=\\\"changeCondition\\\" :placeholder=\\\"sharedState.i18n.select_condition\\\"></singleselect>\\n</div>\\n\\n<div class=\\\"col-md-3 col-xl-2\\\" v-if=\\\"condition_name != ''\\\">\\n    <operator :id=\\\"id\\\" :has_value.sync=\\\"has_value\\\" :operator.sync=\\\"operator\\\"></operator>\\n</div>\\n\\n<div class=\\\"col-md-3 col-xl-2\\\" v-if=\\\"condition_name != '' && has_value\\\">\\n    <input type=\\\"text\\\" v-model=\\\"value\\\" v-on:change=\\\"changeValue(id)\\\" class=\\\"full-width\\\" placeholder=\\\"{{ sharedState.i18n.enter_value }}\\\">\\n</div>\\n\\n<div class=\\\"col-md-1\\\" style=\\\"padding-top: 7px;\\\">\\n    <a href=\\\"#\\\" class=\\\"remove\\\" @click.prevent=\\\"removeCondition(condition)\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/condition/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/operator/index.js":
/*!*********************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/operator/index.js ***!
  \*********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/operator/template.html\"),\n  props: ['id', 'operator', 'has_value'],\n\n  data() {\n    return {\n      operators: [{\n        key: '=',\n        label: 'is equal to'\n      }, {\n        key: '!=',\n        label: 'is not equal to'\n      }, {\n        key: '>',\n        label: 'is greater than'\n      }, {\n        key: '<',\n        label: 'is less than'\n      }, {\n        key: '>=',\n        label: 'is greater than or equal'\n      }, {\n        key: '<=',\n        label: 'is less than or equal'\n      }, {\n        key: '~',\n        label: 'contains'\n      }, {\n        key: '!~',\n        label: 'does not contain'\n      }, {\n        key: 'ts',\n        label: 'is a timestamp'\n      }, {\n        key: '!ts',\n        label: 'is not a timestamp'\n      }]\n    };\n  },\n\n  methods: {\n    changeOperator(value) {\n      switch (this.operator) {\n        case '=':\n        case '!=':\n        case '>':\n        case '<':\n        case '>=':\n        case '<=':\n        case '~':\n        case '!~':\n          this.has_value = true;\n          break;\n\n        default:\n          this.has_value = false;\n          break;\n      }\n\n      this.$parent.changeOperator(this.id);\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/operator/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/operator/template.html":
/*!**************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/operator/template.html ***!
  \**************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<singleselect :selected.sync=\\\"operator\\\" :options=\\\"operators\\\" @update=\\\"changeOperator\\\"></singleselect>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/operator/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/schedule-meeting-action/index.js":
/*!************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/schedule-meeting-action/index.js ***!
  \************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n/* harmony import */ var _dateTimeMixin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../.././dateTimeMixin */ \"./modules/hrm/workflow/assets/js/dateTimeMixin.js\");\n\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/schedule-meeting-action/template.html\"),\n  mixins: [_dateTimeMixin__WEBPACK_IMPORTED_MODULE_1__[\"default\"]],\n  props: ['action_edit_mode', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'schedule_meeting',\n      title: 'Schedule Meeting',\n      crm_users: [],\n      timer: 0,\n      isLoading: false,\n      errors: {\n        schedule_title: false,\n        start_date: false,\n        start_time: false,\n        end_date: false,\n        end_time: false,\n        message: false,\n        invite_contact: false,\n        notification_time_interval: false\n      }\n    };\n  },\n\n  ready() {\n    var self = this;\n    jQuery.get(ajaxurl + '?action=erp_wf_get_crm_users&_wpnonce=' + nonces.fetch_users, function (response) {\n      if (response.success) {\n        self.crm_users = response.data;\n      }\n    });\n  },\n\n  computed: {\n    tinymceSettings() {\n      var shortcodes = {};\n      this.sharedState.conditions_list.forEach(function (condition) {\n        shortcodes[condition.key] = condition.label;\n      });\n      return {\n        pluginURL: pluginURL,\n        shortcodes: shortcodes\n      };\n    }\n\n  },\n  methods: {\n    showShortCodes(element) {\n      this.$parent.showShortCodes(element);\n    },\n\n    addNewAction(e) {\n      if (!this.model.schedule_title) {\n        this.errors.schedule_title = true;\n        return;\n      } else {\n        this.errors.schedule_title = false;\n      }\n\n      if (!this.model.start_date) {\n        this.errors.start_date = true;\n        return;\n      } else {\n        this.errors.start_date = false;\n      }\n\n      if (!this.model.all_day && !this.model.start_time) {\n        this.errors.start_time = true;\n        return;\n      } else {\n        this.errors.start_time = false;\n      }\n\n      if (!this.model.end_date) {\n        this.errors.end_date = true;\n        return;\n      } else {\n        this.errors.end_date = false;\n      }\n\n      if (!this.model.all_day && !this.model.end_time) {\n        this.errors.end_time = true;\n        return;\n      } else {\n        this.errors.end_time = false;\n      }\n\n      if (!this.model.message) {\n        this.errors.message = true;\n        return;\n      } else {\n        this.errors.message = false;\n      }\n\n      if (this.model.invite_contact.length === 0) {\n        this.errors.invite_contact = true;\n        return;\n      } else {\n        this.errors.invite_contact = false;\n      }\n\n      if (this.model.allow_notification && !this.model.notification_time_interval) {\n        this.errors.notification_time_interval = true;\n        return;\n      } else {\n        this.errors.notification_time_interval = false;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        message: this.model.message,\n        schedule_title: this.model.schedule_title,\n        start_date: this.model.start_date,\n        start_time: this.model.start_time,\n        end_date: this.model.end_date,\n        end_time: this.model.end_time,\n        all_day: this.model.all_day,\n        invite_contact: this.model.invite_contact,\n        schedule_type: this.model.schedule_type,\n        notification_via: this.model.notification_via,\n        allow_notification: this.model.allow_notification,\n        notification_time_interval: this.model.notification_time_interval,\n        notification_time: this.model.notification_time,\n        client_time_zone: Intl.DateTimeFormat().resolvedOptions().timeZone\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    },\n\n    searchUsers(search) {\n      this.isLoading = true;\n\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n\n      this.timer = setTimeout(() => {\n        var self = this;\n        jQuery.get(ajaxurl + '?action=erp_wf_get_crm_users&s=' + search + '&_wpnonce=' + nonces.fetch_users, function (response) {\n          if (response.success) {\n            self.crm_users = response.data;\n          }\n        });\n        this.isLoading = false;\n      }, 1000);\n    },\n\n    updateCrmUserSelect(value) {\n      this.model.invite_contact = value;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/schedule-meeting-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/schedule-meeting-action/template.html":
/*!*****************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/schedule-meeting-action/template.html ***!
  \*****************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"schedule_title\\\">{{ sharedState.i18n.schedule_title }}</label><br />\\n        <input type=\\\"text\\\" v-model=\\\"model.schedule_title\\\" class=\\\"full-width {{ errors.schedule_title ? 'v-error' : '' }}\\\" id=\\\"schedule_title\\\" style=\\\"width: 95%;\\\"> <a href=\\\"#\\\" @click.prevent=\\\"showShortCodes('#schedule_title')\\\" title=\\\"{{ sharedState.i18n.text_variables }}\\\"><i class=\\\"mce-ico mce-i-shortcode\\\"></i></a>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.schedule_title\\\">This field is required</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"start_date\\\">{{ sharedState.i18n.start_date }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-date-field full-width {{ errors.start_date ? 'v-error' : '' }}\\\" v-model=\\\"model.start_date\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.start_date\\\">This field is required</label>\\n    </div>\\n    <div class=\\\"col-md-6\\\" v-if=\\\"!model.all_day\\\">\\n        <label for=\\\"start_time\\\">{{ sharedState.i18n.time }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-time-field full-width {{ errors.start_time ? 'v-error' : '' }}\\\" v-model=\\\"model.start_time\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.start_time\\\">This field is required</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"end_date\\\">{{ sharedState.i18n.end_date }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-date-field full-width {{ errors.end_date ? 'v-error' : '' }}\\\" v-model=\\\"model.end_date\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.end_date\\\">This field is required</label>\\n    </div>\\n    <div class=\\\"col-md-6\\\" v-if=\\\"!model.all_day\\\">\\n        <label for=\\\"end_time\\\">{{ sharedState.i18n.time }}</label><br />\\n        <input type=\\\"text\\\" class=\\\"erp-time-field full-width {{ errors.end_time ? 'v-error' : '' }}\\\" v-model=\\\"model.end_time\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.end_time\\\">This field is required</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <input type=\\\"checkbox\\\" v-model=\\\"model.all_day\\\">{{ sharedState.i18n.all_day }}\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"message\\\">{{ sharedState.i18n.message }} </label>\\n        <text-editor :content.sync=\\\"model.message\\\" :tinymce-settings=\\\"tinymceSettings\\\"></text-editor>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.message\\\">This field is required</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"invite_contact\\\">{{ sharedState.i18n.invite_crm_user }}</label><br />\\n        <multiselect :selected=\\\"model.invite_contact\\\" :options=\\\"crm_users\\\" :multiple=\\\"true\\\" :searchable=\\\"true\\\" @search-change=\\\"searchUsers\\\" :loading=\\\"isLoading\\\" :close-on-select=\\\"true\\\" :clear-on-select=\\\"false\\\" :limit=\\\"2\\\" :placeholder=\\\"sharedState.i18n.select_crm_users\\\" key=\\\"id\\\" label=\\\"name\\\" @update=\\\"updateCrmUserSelect\\\"></multiselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.invite_contact\\\">This field is required</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"schedule_type\\\">{{ sharedState.i18n.schedule_type }}</label><br />\\n        <singleselect :selected.sync=\\\"model.schedule_type\\\" :options=\\\"[{key: 'call', label: 'Call'}, {key: 'meeting', label: 'Meeting'}]\\\"></singleselect>\\n    </div>\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"allow_notification\\\">{{ sharedState.i18n.notification }}</label><br />\\n        <input type=\\\"checkbox\\\" v-model=\\\"model.allow_notification\\\" class=\\\"full-width\\\">{{ sharedState.i18n.allow }}\\n    </div>\\n</div>\\n<div class=\\\"row\\\" v-if=\\\"model.allow_notification\\\">\\n    <div class=\\\"col-md-6\\\">\\n        <label for=\\\"notification_via\\\">{{ sharedState.i18n.notify_via }}</label><br />\\n        <singleselect :selected.sync=\\\"model.notification_via\\\" :options=\\\"[{key: 'email', label: 'Email'}]\\\"></singleselect>\\n    </div>\\n    <div class=\\\"col-md-6\\\">\\n        <label>{{ sharedState.i18n.notify_before }}</label>\\n        <div class=\\\"row\\\">\\n            <div class=\\\"col-md-6\\\">\\n                <input type=\\\"text\\\" v-model=\\\"model.notification_time_interval\\\" class=\\\"full-width {{ errors.notification_time_interval ? 'v-error' : '' }}\\\" />\\n                <label class=\\\"v-warn\\\" v-if=\\\"errors.notification_time_interval\\\">This field is required</label>\\n            </div>\\n            <div class=\\\"col-md-6\\\">\\n                <singleselect :selected.sync=\\\"model.notification_time\\\" :options=\\\"[{key: 'minute', label: 'Minute'}, {key: 'hour', label: 'Hour'}, {key: 'day', label: 'Day'}]\\\"></singleselect>\\n            </div>\\n        </div>\\n    </div>\\n</div>\\n\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\\n\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/schedule-meeting-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/send-email-action/index.js":
/*!******************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/send-email-action/index.js ***!
  \******************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/send-email-action/template.html\"),\n  props: ['action_edit_mode', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'send_email',\n      title: 'Send Email',\n      timer: 0,\n      isLoading: false,\n      send_itself_display: false,\n      users: [],\n      errors: {\n        subject: false,\n        user: false,\n        message: false\n      }\n    };\n  },\n\n  created() {\n    if (this.$parent.$parent.event.match(/contact/gi) || this.$parent.$parent.event.match(/user/gi) || this.$parent.$parent.events_group == 'accounting') {\n      switch (this.$parent.$parent.event) {\n        case 'created_user':\n          this.user_type = 'user';\n          break;\n\n        case 'created_customer':\n        case 'deleted_customer':\n        case 'created_vendor':\n        case 'deleted_vendor':\n        case 'added_sale':\n        case 'added_expense':\n        case 'added_check':\n        case 'added_bill':\n        case 'added_purchase_order':\n        case 'added_purchase':\n          this.user_type = 'customer';\n          break;\n\n        default:\n          this.user_type = 'contact';\n      }\n\n      this.send_itself_display = true;\n    } else {\n      this.send_itself_display = false;\n    }\n  },\n\n  ready() {\n    var self = this;\n    jQuery.get(ajaxurl + '?action=erp_wf_get_employees&_wpnonce=' + nonces.fetch_users, function (response) {\n      if (response.success) {\n        self.users = response.data;\n      }\n    });\n  },\n\n  computed: {\n    tinymceSettings() {\n      var shortcodes = {};\n\n      if (this.sharedState.conditions_list) {\n        this.sharedState.conditions_list.forEach(function (condition) {\n          shortcodes[condition.key] = condition.label;\n        });\n      }\n\n      return {\n        pluginURL: pluginURL,\n        shortcodes: shortcodes\n      };\n    }\n\n  },\n  methods: {\n    showShortCodes(element) {\n      this.$parent.showShortCodes(element);\n    },\n\n    addNewAction(e) {\n      if (!this.model.subject) {\n        this.errors.subject = true;\n        return;\n      } else {\n        this.errors.subject = false;\n      }\n\n      if (!this.model.user && !this.model.send_itself) {\n        this.errors.user = true;\n        return;\n      } else {\n        this.errors.user = false;\n      }\n\n      if (!this.model.message) {\n        this.errors.message = true;\n        return;\n      } else {\n        this.errors.message = false;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        subject: this.model.subject,\n        message: this.model.message,\n        user: this.model.user,\n        send_itself: this.model.send_itself\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    },\n\n    searchUsers(search) {\n      this.isLoading = true;\n\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n\n      this.timer = setTimeout(() => {\n        var self = this;\n        jQuery.get(ajaxurl + '?action=erp_wf_get_employees&s=' + search + '&_wpnonce=' + nonces.fetch_users, function (response) {\n          if (response.success) {\n            self.users = response.data;\n          }\n        });\n        this.isLoading = false;\n      }, 1000);\n    },\n\n    updateUserSelect(value) {\n      this.model.user = value;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/send-email-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/send-email-action/template.html":
/*!***********************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/send-email-action/template.html ***!
  \***********************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"subject\\\">{{ sharedState.i18n.subject }}</label><br />\\n        <input type=\\\"text\\\" name=\\\"subject\\\" id=\\\"subject\\\" style=\\\"width: 95%;\\\" class=\\\"full-width {{ errors.subject ? 'v-error' : '' }}\\\" v-model=\\\"model.subject\\\"> <a href=\\\"#\\\" @click.prevent=\\\"showShortCodes('#subject')\\\" title=\\\"{{ sharedState.i18n.text_variables }}\\\"><i class=\\\"mce-ico mce-i-shortcode\\\"></i></a>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.subject\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"to\\\">{{ sharedState.i18n.to }}</label>\\n        <label v-if=\\\"send_itself_display\\\"> <input type=\\\"checkbox\\\" v-model=\\\"model.send_itself\\\" />{{ sharedState.i18n[user_type + '_itself'] }}</label>\\n        <label v-if=\\\"send_itself_display && !model.send_itself\\\"><br />{{ sharedState.i18n.or }}</label>\\n        <multiselect v-if=\\\"!model.send_itself\\\" :selected=\\\"model.user\\\" :options=\\\"users\\\" :multiple=\\\"true\\\" :searchable=\\\"true\\\" @search-change=\\\"searchUsers\\\" :loading=\\\"isLoading\\\" :close-on-select=\\\"true\\\" :clear-on-select=\\\"false\\\" :limit=\\\"2\\\" :placeholder=\\\"sharedState.i18n.select_employees\\\" key=\\\"id\\\" label=\\\"name\\\" @update=\\\"updateUserSelect\\\"></multiselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.user\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"message\\\">{{ sharedState.i18n.message }}</label><br />\\n        <text-editor :content.sync=\\\"model.message\\\" :tinymce-settings=\\\"tinymceSettings\\\"></text-editor>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.message\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/send-email-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/send-invoice-action/index.js":
/*!********************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/send-invoice-action/index.js ***!
  \********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/send-invoice-action/template.html\"),\n  props: ['action_edit_mode', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'send_invoice',\n      title: 'Send Invoice',\n      users: [],\n      timer: 0,\n      isLoading: false,\n      errors: {\n        subject: false,\n        user: false,\n        message: false\n      }\n    };\n  },\n\n  ready() {\n    var self = this;\n    jQuery.get(ajaxurl + '?action=erp_wf_get_employees&_wpnonce=' + nonces.fetch_users, function (response) {\n      if (response.success) {\n        self.users = response.data;\n      }\n    });\n  },\n\n  computed: {\n    tinymceSettings() {\n      var shortcodes = {};\n\n      if (this.sharedState.conditions_list) {\n        this.sharedState.conditions_list.forEach(function (condition) {\n          shortcodes[condition.key] = condition.label;\n        });\n      }\n\n      return {\n        pluginURL: pluginURL,\n        shortcodes: shortcodes\n      };\n    }\n\n  },\n  methods: {\n    showShortCodes(element) {\n      this.$parent.showShortCodes(element);\n    },\n\n    addNewAction(e) {\n      if (!this.model.subject) {\n        this.errors.subject = true;\n        return;\n      } else {\n        this.errors.subject = false;\n      }\n\n      if (!this.model.user && !this.model.send_itself) {\n        this.errors.user = true;\n        return;\n      } else {\n        this.errors.user = false;\n      }\n\n      if (!this.model.message) {\n        this.errors.message = true;\n        return;\n      } else {\n        this.errors.message = false;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        user: this.model.user,\n        subject: this.model.subject,\n        message: this.model.message,\n        send_itself: this.model.send_itself\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    },\n\n    searchUsers(search) {\n      this.isLoading = true;\n\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n\n      this.timer = setTimeout(() => {\n        var self = this;\n        jQuery.get(ajaxurl + '?action=erp_wf_get_employees&s=' + search + '&_wpnonce=' + nonces.fetch_users, function (response) {\n          if (response.success) {\n            self.users = response.data;\n          }\n        });\n        this.isLoading = false;\n      }, 1000);\n    },\n\n    updateUserSelect(value) {\n      this.model.user = value;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/send-invoice-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/send-invoice-action/template.html":
/*!*************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/send-invoice-action/template.html ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"subject\\\">{{ sharedState.i18n.subject }}</label><br />\\n        <input type=\\\"text\\\" name=\\\"subject\\\" id=\\\"subject\\\" class=\\\"full-width {{ errors.subject ? 'v-error' : '' }}\\\" v-model=\\\"model.subject\\\" style=\\\"width: 95%;\\\"> <a href=\\\"#\\\" @click.prevent=\\\"showShortCodes('#subject')\\\" title=\\\"{{ sharedState.i18n.text_variables }}\\\"><i class=\\\"mce-ico mce-i-shortcode\\\"></i></a>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.subject\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"to\\\">{{ sharedState.i18n.to }}</label>\\n        <label> <input type=\\\"checkbox\\\" v-model=\\\"model.send_itself\\\" />{{ sharedState.i18n.customer_itself }}</label>\\n        <label v-if=\\\"!model.send_itself\\\"><br />{{ sharedState.i18n.or }}</label>\\n        <multiselect v-if=\\\"!model.send_itself\\\" :selected=\\\"model.user\\\" :options=\\\"users\\\" :multiple=\\\"true\\\" :searchable=\\\"true\\\" @search-change=\\\"searchUsers\\\" :loading=\\\"isLoading\\\" :close-on-select=\\\"true\\\" :clear-on-select=\\\"false\\\" :limit=\\\"2\\\" :placeholder=\\\"sharedState.i18n.select_employees\\\" key=\\\"id\\\" label=\\\"name\\\" @update=\\\"updateUserSelect\\\"></multiselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.user\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"message\\\">{{ sharedState.i18n.message }}</label><br />\\n        <text-editor :content.sync=\\\"model.message\\\" :tinymce-settings=\\\"tinymceSettings\\\"></text-editor>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.message\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/send-invoice-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/send-sms-action/index.js":
/*!****************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/send-sms-action/index.js ***!
  \****************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/send-sms-action/template.html\"),\n  props: ['action_edit_mode', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'send_sms',\n      title: 'Send SMS',\n      users: [],\n      timer: 0,\n      isLoading: false,\n      errors: {\n        user: false,\n        message: false\n      }\n    };\n  },\n\n  created() {\n    if (this.$parent.$parent.event.match(/contact/gi) || this.$parent.$parent.events_group == 'accounting' || this.$parent.$parent.event == 'created_employee') {\n      switch (this.$parent.$parent.event) {\n        case 'added_sale':\n        case 'added_expense':\n        case 'added_check':\n        case 'added_bill':\n        case 'added_purchase_order':\n        case 'added_purchase':\n          this.user_type = 'customer';\n          break;\n\n        case 'created_employee':\n          this.user_type = 'employee';\n          break;\n\n        default:\n          this.user_type = 'contact';\n      }\n\n      this.send_itself_display = true;\n    } else {\n      this.send_itself_display = false;\n    }\n  },\n\n  ready() {\n    var self = this;\n    jQuery.get(ajaxurl + '?action=erp_wf_get_employees&_wpnonce=' + nonces.fetch_users, function (response) {\n      if (response.success) {\n        self.users = response.data;\n      }\n    });\n  },\n\n  methods: {\n    showShortCodes(element) {\n      this.$parent.showShortCodes(element);\n    },\n\n    addNewAction(e) {\n      if (!this.model.user && !this.model.send_itself) {\n        this.errors.user = true;\n        return;\n      } else {\n        this.errors.user = false;\n      }\n\n      if (!this.model.message) {\n        this.errors.message = true;\n        return;\n      } else {\n        this.errors.message = false;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        user: this.model.user,\n        message: this.model.message,\n        send_itself: this.model.send_itself\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    },\n\n    searchUsers(search) {\n      this.isLoading = true;\n\n      if (this.timer) {\n        clearTimeout(this.timer);\n      }\n\n      this.timer = setTimeout(() => {\n        var self = this;\n        jQuery.get(ajaxurl + '?action=erp_wf_get_employees&s=' + search + '&_wpnonce=' + nonces.fetch_users, function (response) {\n          if (response.success) {\n            self.users = response.data;\n          }\n        });\n        this.isLoading = false;\n      }, 1000);\n    },\n\n    updateUserSelect(value) {\n      this.model.user = value;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/send-sms-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/send-sms-action/template.html":
/*!*********************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/send-sms-action/template.html ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"to\\\">{{ sharedState.i18n.to }}</label>\\n        <label v-if=\\\"send_itself_display\\\"> <input type=\\\"checkbox\\\" v-model=\\\"model.send_itself\\\" />{{ sharedState.i18n[user_type + '_itself'] }}</label>\\n        <label v-if=\\\"send_itself_display && !model.send_itself\\\"><br />{{ sharedState.i18n.or }}</label>\\n        <multiselect v-if=\\\"!model.send_itself\\\" :selected=\\\"model.user\\\" :options=\\\"users\\\" :multiple=\\\"true\\\" :searchable=\\\"true\\\" @search-change=\\\"searchUsers\\\" :loading=\\\"isLoading\\\" :close-on-select=\\\"true\\\" :clear-on-select=\\\"false\\\" :limit=\\\"2\\\" :placeholder=\\\"sharedState.i18n.select_employees\\\" key=\\\"id\\\" label=\\\"name\\\" @update=\\\"updateUserSelect\\\"></multiselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.user\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"message\\\">{{ sharedState.i18n.message }} <a href=\\\"#\\\" @click.prevent=\\\"showShortCodes('#message')\\\" title=\\\"{{ sharedState.i18n.text_variables }}\\\"><i class=\\\"mce-ico mce-i-shortcode\\\"></i></a></label><br />\\n        <textarea id=\\\"message\\\" rows=\\\"6\\\" class=\\\"full-width\\\" v-model=\\\"model.message\\\">{{ model.message }}</textarea>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.message\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/send-sms-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/singleselect/index.js":
/*!*************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/singleselect/index.js ***!
  \*************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/singleselect/template.html\"),\n  props: {\n    selected: {\n      type: String,\n      default: ''\n    },\n    options: {\n      type: Array,\n      default: () => {\n        return [];\n      }\n    },\n    placeholder: {\n      type: String,\n      default: 'Pick one'\n    },\n    key: {\n      type: String,\n      default: 'key'\n    },\n    label: {\n      type: String,\n      default: 'label'\n    }\n  },\n\n  data() {\n    return {\n      default: ''\n    };\n  },\n\n  methods: {\n    updateSelected(value) {\n      this.selected = value && value[this.key] ? value[this.key] : value;\n      this.$emit('update', this.selected);\n    }\n\n  },\n  computed: {\n    default() {\n      var tmp = '';\n\n      if (this.selected && this.options) {\n        if (typeof this.options[0] === 'object') {\n          var selected = this.selected;\n          this.options.forEach(function (option) {\n            if (option.key == selected) {\n              tmp = option;\n            }\n          });\n        } else {\n          if (this.options.indexOf(this.selected) !== -1) {\n            tmp = this.selected;\n          }\n        }\n      }\n\n      return tmp;\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/singleselect/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/singleselect/template.html":
/*!******************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/singleselect/template.html ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<multiselect :selected.sync=\\\"default\\\" :options=\\\"options\\\" :multiple=\\\"false\\\" :searchable=\\\"false\\\" :close-on-select=\\\"true\\\" @update=\\\"updateSelected\\\" :show-labels=\\\"false\\\" :placeholder=\\\"placeholder\\\" :allow-empty=\\\"false\\\" :key=\\\"key\\\" :label=\\\"label\\\"></multiselect>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/singleselect/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/text-editor/index.js":
/*!************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/text-editor/index.js ***!
  \************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/text-editor/template.html\"),\n  props: ['content', 'tinymceSettings'],\n  data: function () {\n    return {\n      editorId: this._uid\n    };\n  },\n  computed: {\n    shortcodes: function () {\n      return this.tinymceSettings.shortcodes;\n    },\n    pluginURL: function () {\n      return this.tinymceSettings.pluginURL;\n    }\n  },\n  ready: function () {\n    var component = this;\n    window.tinymce.init({\n      selector: 'textarea#vue-text-editor-' + this.editorId,\n      height: 300,\n      menubar: false,\n      convert_urls: false,\n      theme: 'modern',\n      skin: 'lightgray',\n      content_css: component.pluginURL + '/assets/css/text-editor.css',\n      setup: function (editor) {\n        var menuItems = [];\n\n        if (component.shortcodes) {\n          var title;\n          var keys = Object.keys(component.shortcodes);\n          keys.forEach(function (shortcode) {\n            menuItems.push({\n              text: shortcode,\n              onclick: function () {\n                var code = '[' + shortcode + ']';\n                editor.insertContent(code);\n              }\n            });\n          });\n        }\n\n        editor.addButton('shortcodes', {\n          type: 'menubutton',\n          icon: 'shortcode',\n          tooltip: 'Text Variables',\n          menu: menuItems\n        }); // editor change triggers\n\n        editor.on('change', function () {\n          component.$set('content', editor.getContent());\n        });\n        editor.on('keyup', function () {\n          component.$set('content', editor.getContent());\n        });\n        editor.on('NodeChange', function () {\n          component.$set('content', editor.getContent());\n        });\n      },\n      fontsize_formats: '10px 11px 13px 14px 16px 18px 22px 25px 30px 36px 40px 45px 50px 60px 65px 70px 75px 80px',\n      font_formats: 'Arial=arial,helvetica,sans-serif;' + 'Comic Sans MS=comic sans ms,sans-serif;' + 'Courier New=courier new,courier;' + 'Georgia=georgia,palatino;' + 'Lucida=Lucida Sans Unicode, Lucida Grande, sans-serif;' + 'Tahoma=tahoma,arial,helvetica,sans-serif;' + 'Times New Roman=times new roman,times;' + 'Trebuchet MS=trebuchet ms,geneva;' + 'Verdana=verdana,geneva;',\n      plugins: 'textcolor colorpicker wplink wordpress code hr',\n      toolbar1: 'shortcodes bold italic strikethrough bullist numlist alignleft aligncenter alignjustify alignright link',\n      toolbar2: 'formatselect forecolor backcolor underline blockquote hr code',\n      toolbar3: 'fontselect fontsizeselect removeformat undo redo'\n    });\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/text-editor/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/text-editor/template.html":
/*!*****************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/text-editor/template.html ***!
  \*****************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<textarea id=\\\"vue-text-editor-{{ editorId }}\\\" class=\\\"vue-text-editor\\\">{{ content }}</textarea>\\n\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/text-editor/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/index.js":
/*!***************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/index.js ***!
  \***************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/template.html\"),\n  props: ['action_edit_mode', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'trigger_action_hook',\n      title: 'Trigger Action Hook',\n      errors: {\n        hook_name: false\n      }\n    };\n  },\n\n  methods: {\n    addNewAction(e) {\n      if (!this.model.hook_name) {\n        this.errors.hook_name = true;\n        return;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        hook_name: this.model.hook_name\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/template.html":
/*!********************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/template.html ***!
  \********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"hook_name\\\">{{ sharedState.i18n.hook_name }}</label><br />\\n        <input type=\\\"text\\\" name=\\\"hook_name\\\" class=\\\"full-width {{ errors.hook_name ? 'v-error' : '' }}\\\" v-model=\\\"model.hook_name\\\">\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.hook_name\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/update-field-action/index.js":
/*!********************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/update-field-action/index.js ***!
  \********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.././store */ \"./modules/hrm/workflow/assets/js/store.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  template: __webpack_require__(/*! ./template.html */ \"./modules/hrm/workflow/assets/js/components/update-field-action/template.html\"),\n  props: ['action_edit_mode', 'model'],\n\n  data() {\n    return {\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      name: 'update_field',\n      title: 'Update Field',\n      options: [],\n      errors: {\n        field_name: false\n      }\n    };\n  },\n\n  ready() {\n    if (this.$parent.$parent.event == 'created_user') {\n      this.options = _.filter(this.sharedState.conditions_list, function (d) {\n        return d.key != 'roles' && d.key != 'email';\n      });\n    } else {\n      this.options = this.sharedState.conditions_list;\n    }\n  },\n\n  methods: {\n    addNewAction(e) {\n      if (!this.model.field_name) {\n        this.errors.field_name = true;\n        return;\n      }\n\n      var action = {\n        name: this.name,\n        title: this.title,\n        field_name: this.model.field_name,\n        field_value: this.model.field_value\n      };\n      this.$parent.addNewAction(action);\n    },\n\n    closeAction() {\n      this.$parent.closeAction();\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/update-field-action/index.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/update-field-action/template.html":
/*!*************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/update-field-action/template.html ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = \"<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <div class=\\\"action-heading\\\">\\n            <label>{{ title }}</label>\\n            <label class=\\\"pull-right\\\"><a href=\\\"#\\\" @click.prevent=\\\"closeAction\\\"><span class=\\\"dashicons dashicons-dismiss\\\"></span></a></label>\\n        </div>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"field_name\\\">{{ sharedState.i18n.field_name }}</label><br />\\n        <singleselect :selected.sync=\\\"model.field_name\\\" :options=\\\"options\\\"></singleselect>\\n        <label class=\\\"v-warn\\\" v-if=\\\"errors.field_name\\\">{{ sharedState.i18n.required_message }}</label>\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <label for=\\\"field_value\\\">{{ sharedState.i18n.field_value }}</label><br />\\n        <input type=\\\"text\\\" v-model=\\\"model.field_value\\\" class=\\\"full-width\\\">\\n    </div>\\n</div>\\n<div class=\\\"row\\\">\\n    <div class=\\\"col-md-12\\\">\\n        <button class=\\\"button button-secondary pull-right\\\" @click.prevent=\\\"addNewAction\\\">{{ action_edit_mode ? sharedState.i18n.update_this_action : sharedState.i18n.add_this_action }}</button>\\n    </div>\\n</div>\";\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/update-field-action/template.html?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/components/vue-multiselect/lib/vue-multiselect.min.js":
/*!**********************************************************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/components/vue-multiselect/lib/vue-multiselect.min.js ***!
  \**********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("!function (t, e) {\n   true ? module.exports = e() : undefined;\n}(this, function () {\n  return function (t) {\n    function e(i) {\n      if (n[i]) return n[i].exports;\n      var o = n[i] = {\n        exports: {},\n        id: i,\n        loaded: !1\n      };\n      return t[i].call(o.exports, o, o.exports, e), o.loaded = !0, o.exports;\n    }\n\n    var n = {};\n    return e.m = t, e.c = n, e.p = \"/\", e(0);\n  }([function (t, e, n) {\n    \"use strict\";\n\n    function i(t) {\n      return t && t.__esModule ? t : {\n        \"default\": t\n      };\n    }\n\n    Object.defineProperty(e, \"__esModule\", {\n      value: !0\n    }), e.deepClone = e.pointerMixin = e.multiselectMixin = e.Multiselect = void 0;\n    var o = n(80),\n        r = i(o),\n        s = n(28),\n        l = i(s),\n        a = n(29),\n        u = i(a),\n        c = n(30),\n        f = i(c);\n    e[\"default\"] = r[\"default\"], e.Multiselect = r[\"default\"], e.multiselectMixin = l[\"default\"], e.pointerMixin = u[\"default\"], e.deepClone = f[\"default\"];\n  }, function (t, e) {\n    var n = t.exports = \"undefined\" != typeof window && window.Math == Math ? window : \"undefined\" != typeof self && self.Math == Math ? self : Function(\"return this\")();\n    \"number\" == typeof __g && (__g = n);\n  }, function (t, e) {\n    var n = {}.hasOwnProperty;\n\n    t.exports = function (t, e) {\n      return n.call(t, e);\n    };\n  }, function (t, e, n) {\n    var i = n(55),\n        o = n(15);\n\n    t.exports = function (t) {\n      return i(o(t));\n    };\n  }, function (t, e, n) {\n    t.exports = !n(9)(function () {\n      return 7 != Object.defineProperty({}, \"a\", {\n        get: function () {\n          return 7;\n        }\n      }).a;\n    });\n  }, function (t, e, n) {\n    var i = n(6),\n        o = n(13);\n    t.exports = n(4) ? function (t, e, n) {\n      return i.f(t, e, o(1, n));\n    } : function (t, e, n) {\n      return t[e] = n, t;\n    };\n  }, function (t, e, n) {\n    var i = n(11),\n        o = n(34),\n        r = n(25),\n        s = Object.defineProperty;\n    e.f = n(4) ? Object.defineProperty : function (t, e, n) {\n      if (i(t), e = r(e, !0), i(n), o) try {\n        return s(t, e, n);\n      } catch (l) {}\n      if (\"get\" in n || \"set\" in n) throw TypeError(\"Accessors not supported!\");\n      return \"value\" in n && (t[e] = n.value), t;\n    };\n  }, function (t, e, n) {\n    var i = n(23)(\"wks\"),\n        o = n(14),\n        r = n(1).Symbol,\n        s = \"function\" == typeof r,\n        l = t.exports = function (t) {\n      return i[t] || (i[t] = s && r[t] || (s ? r : o)(\"Symbol.\" + t));\n    };\n\n    l.store = i;\n  }, function (t, e) {\n    var n = t.exports = {\n      version: \"2.4.0\"\n    };\n    \"number\" == typeof __e && (__e = n);\n  }, function (t, e) {\n    t.exports = function (t) {\n      try {\n        return !!t();\n      } catch (e) {\n        return !0;\n      }\n    };\n  }, function (t, e, n) {\n    var i = n(39),\n        o = n(16);\n\n    t.exports = Object.keys || function (t) {\n      return i(t, o);\n    };\n  }, function (t, e, n) {\n    var i = n(12);\n\n    t.exports = function (t) {\n      if (!i(t)) throw TypeError(t + \" is not an object!\");\n      return t;\n    };\n  }, function (t, e) {\n    t.exports = function (t) {\n      return \"object\" == typeof t ? null !== t : \"function\" == typeof t;\n    };\n  }, function (t, e) {\n    t.exports = function (t, e) {\n      return {\n        enumerable: !(1 & t),\n        configurable: !(2 & t),\n        writable: !(4 & t),\n        value: e\n      };\n    };\n  }, function (t, e) {\n    var n = 0,\n        i = Math.random();\n\n    t.exports = function (t) {\n      return \"Symbol(\".concat(void 0 === t ? \"\" : t, \")_\", (++n + i).toString(36));\n    };\n  }, function (t, e) {\n    t.exports = function (t) {\n      if (void 0 == t) throw TypeError(\"Can't call method on  \" + t);\n      return t;\n    };\n  }, function (t, e) {\n    t.exports = \"constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf\".split(\",\");\n  }, function (t, e, n) {\n    var i = n(1),\n        o = n(8),\n        r = n(52),\n        s = n(5),\n        l = \"prototype\",\n        a = function (t, e, n) {\n      var u,\n          c,\n          f,\n          p = t & a.F,\n          d = t & a.G,\n          h = t & a.S,\n          m = t & a.P,\n          v = t & a.B,\n          b = t & a.W,\n          g = d ? o : o[e] || (o[e] = {}),\n          y = g[l],\n          x = d ? i : h ? i[e] : (i[e] || {})[l];\n      d && (n = e);\n\n      for (u in n) c = !p && x && void 0 !== x[u], c && u in g || (f = c ? x[u] : n[u], g[u] = d && \"function\" != typeof x[u] ? n[u] : v && c ? r(f, i) : b && x[u] == f ? function (t) {\n        var e = function (e, n, i) {\n          if (this instanceof t) {\n            switch (arguments.length) {\n              case 0:\n                return new t();\n\n              case 1:\n                return new t(e);\n\n              case 2:\n                return new t(e, n);\n            }\n\n            return new t(e, n, i);\n          }\n\n          return t.apply(this, arguments);\n        };\n\n        return e[l] = t[l], e;\n      }(f) : m && \"function\" == typeof f ? r(Function.call, f) : f, m && ((g.virtual || (g.virtual = {}))[u] = f, t & a.R && y && !y[u] && s(y, u, f)));\n    };\n\n    a.F = 1, a.G = 2, a.S = 4, a.P = 8, a.B = 16, a.W = 32, a.U = 64, a.R = 128, t.exports = a;\n  }, function (t, e) {\n    t.exports = {};\n  }, function (t, e) {\n    t.exports = !0;\n  }, function (t, e) {\n    e.f = {}.propertyIsEnumerable;\n  }, function (t, e, n) {\n    var i = n(6).f,\n        o = n(2),\n        r = n(7)(\"toStringTag\");\n\n    t.exports = function (t, e, n) {\n      t && !o(t = n ? t : t.prototype, r) && i(t, r, {\n        configurable: !0,\n        value: e\n      });\n    };\n  }, function (t, e, n) {\n    var i = n(23)(\"keys\"),\n        o = n(14);\n\n    t.exports = function (t) {\n      return i[t] || (i[t] = o(t));\n    };\n  }, function (t, e, n) {\n    var i = n(1),\n        o = \"__core-js_shared__\",\n        r = i[o] || (i[o] = {});\n\n    t.exports = function (t) {\n      return r[t] || (r[t] = {});\n    };\n  }, function (t, e) {\n    var n = Math.ceil,\n        i = Math.floor;\n\n    t.exports = function (t) {\n      return isNaN(t = +t) ? 0 : (t > 0 ? i : n)(t);\n    };\n  }, function (t, e, n) {\n    var i = n(12);\n\n    t.exports = function (t, e) {\n      if (!i(t)) return t;\n      var n, o;\n      if (e && \"function\" == typeof (n = t.toString) && !i(o = n.call(t))) return o;\n      if (\"function\" == typeof (n = t.valueOf) && !i(o = n.call(t))) return o;\n      if (!e && \"function\" == typeof (n = t.toString) && !i(o = n.call(t))) return o;\n      throw TypeError(\"Can't convert object to primitive value\");\n    };\n  }, function (t, e, n) {\n    var i = n(1),\n        o = n(8),\n        r = n(19),\n        s = n(27),\n        l = n(6).f;\n\n    t.exports = function (t) {\n      var e = o.Symbol || (o.Symbol = r ? {} : i.Symbol || {});\n      \"_\" == t.charAt(0) || t in e || l(e, t, {\n        value: s.f(t)\n      });\n    };\n  }, function (t, e, n) {\n    e.f = n(7);\n  }, function (t, e, n) {\n    \"use strict\";\n\n    function i(t) {\n      return t && t.__esModule ? t : {\n        \"default\": t\n      };\n    }\n\n    var o = n(31),\n        r = i(o),\n        s = n(30),\n        l = i(s);\n    t.exports = {\n      data: function () {\n        return {\n          search: \"\",\n          isOpen: !1,\n          value: this.selected ? (0, l[\"default\"])(this.selected) : this.multiple ? [] : null\n        };\n      },\n      props: {\n        localSearch: {\n          type: Boolean,\n          \"default\": !0\n        },\n        options: {\n          type: Array,\n          required: !0\n        },\n        multiple: {\n          type: Boolean,\n          \"default\": !1\n        },\n        selected: {},\n        key: {\n          type: String,\n          \"default\": !1\n        },\n        label: {\n          type: String,\n          \"default\": !1\n        },\n        searchable: {\n          type: Boolean,\n          \"default\": !0\n        },\n        clearOnSelect: {\n          type: Boolean,\n          \"default\": !0\n        },\n        hideSelected: {\n          type: Boolean,\n          \"default\": !1\n        },\n        placeholder: {\n          type: String,\n          \"default\": \"Select option\"\n        },\n        maxHeight: {\n          type: Number,\n          \"default\": 300\n        },\n        allowEmpty: {\n          type: Boolean,\n          \"default\": !0\n        },\n        resetAfter: {\n          type: Boolean,\n          \"default\": !1\n        },\n        closeOnSelect: {\n          type: Boolean,\n          \"default\": !0\n        },\n        customLabel: {\n          type: Function,\n          \"default\": function (t, e) {\n            return t && t.isTag ? t.label : e ? t[e] : t;\n          }\n        },\n        taggable: {\n          type: Boolean,\n          \"default\": !1\n        },\n        tagPlaceholder: {\n          type: String,\n          \"default\": \"Press enter to create a tag\"\n        },\n        max: {\n          type: Number,\n          \"default\": 0\n        },\n        id: {\n          \"default\": null\n        },\n        optionsLimit: {\n          type: Number,\n          \"default\": 1e3\n        }\n      },\n      created: function () {\n        this.searchable && this.adjustSearch();\n      },\n      computed: {\n        filteredOptions: function () {\n          var t = this.search || \"\",\n              e = this.hideSelected ? this.options.filter(this.isNotSelected) : this.options;\n          return this.localSearch && (e = this.$options.filters.filterBy(e, this.search)), this.taggable && t.length && !this.isExistingOption(t) && e.unshift({\n            isTag: !0,\n            label: t\n          }), e.slice(0, this.optionsLimit);\n        },\n        valueKeys: function () {\n          var t = this;\n          return this.key ? this.multiple ? this.value.map(function (e) {\n            return e[t.key];\n          }) : this.value[this.key] : this.value;\n        },\n        optionKeys: function () {\n          var t = this;\n          return this.label ? this.options.map(function (e) {\n            return e[t.label];\n          }) : this.options;\n        },\n        currentOptionLabel: function () {\n          var t = this.getOptionLabel(this.value);\n          return t ? t.toString() : \"\";\n        }\n      },\n      watch: {\n        value: function () {\n          this.resetAfter && (this.$set(\"value\", null), this.$set(\"search\", null), this.$set(\"selected\", null)), this.adjustSearch();\n        },\n        search: function () {\n          this.search !== this.currentOptionLabel && this.$emit(\"search-change\", this.search, this.id);\n        },\n        selected: function (t, e) {\n          this.value = (0, l[\"default\"])(this.selected);\n        }\n      },\n      methods: {\n        isExistingOption: function (t) {\n          return this.options ? this.optionKeys.indexOf(t) > -1 : !1;\n        },\n        isSelected: function (t) {\n          if (!this.value && 0 !== this.value) return !1;\n          var e = this.key ? t[this.key] : t;\n          return this.multiple ? this.valueKeys.indexOf(e) > -1 : this.valueKeys === e;\n        },\n        isNotSelected: function (t) {\n          return !this.isSelected(t);\n        },\n        getOptionLabel: function (t) {\n          return \"object\" === (\"undefined\" == typeof t ? \"undefined\" : (0, r[\"default\"])(t)) && null !== t ? this.customLabel(t, this.label) : t ? this.customLabel(t) : \"\";\n        },\n        select: function (t) {\n          if (0 === this.max || !this.multiple || this.value.length !== this.max) if (t.isTag) this.$emit(\"tag\", t.label, this.id), this.search = \"\";else {\n            if (this.multiple) {\n              if (this.isSelected(t)) return void this.removeElement(t);\n              this.value.push(t);\n            } else {\n              var e = this.isSelected(t);\n              if (e && !this.allowEmpty) return;\n              this.value = e ? null : t;\n            }\n\n            this.$emit(\"select\", (0, l[\"default\"])(t), this.id), this.$emit(\"update\", (0, l[\"default\"])(this.value), this.id), this.closeOnSelect && this.deactivate();\n          }\n        },\n        removeElement: function (t) {\n          if (this.allowEmpty || !(this.value.length <= 1)) {\n            if (this.multiple && \"object\" === (\"undefined\" == typeof t ? \"undefined\" : (0, r[\"default\"])(t))) {\n              var e = this.valueKeys.indexOf(t[this.key]);\n              this.value.splice(e, 1);\n            } else this.value.$remove(t);\n\n            this.$emit(\"remove\", (0, l[\"default\"])(t), this.id), this.$emit(\"update\", (0, l[\"default\"])(this.value), this.id);\n          }\n        },\n        removeLastElement: function () {\n          0 === this.search.length && Array.isArray(this.value) && this.removeElement(this.value[this.value.length - 1]);\n        },\n        activate: function () {\n          this.isOpen || (this.isOpen = !0, this.searchable ? (this.search = \"\", this.$els.search.focus()) : this.$el.focus(), this.$emit(\"open\", this.id));\n        },\n        deactivate: function () {\n          this.isOpen && (this.isOpen = !1, this.searchable ? (this.$els.search.blur(), this.adjustSearch()) : this.$el.blur(), this.$emit(\"close\", (0, l[\"default\"])(this.value), this.id));\n        },\n        adjustSearch: function () {\n          var t = this;\n          this.searchable && this.clearOnSelect && this.$nextTick(function () {\n            t.search = t.multiple ? \"\" : t.currentOptionLabel;\n          });\n        },\n        toggle: function () {\n          this.isOpen ? this.deactivate() : this.activate();\n        }\n      }\n    };\n  }, function (t, e) {\n    \"use strict\";\n\n    t.exports = {\n      data: function () {\n        return {\n          pointer: 0,\n          visibleElements: this.maxHeight / this.optionHeight\n        };\n      },\n      props: {\n        showPointer: {\n          type: Boolean,\n          \"default\": !0\n        },\n        optionHeight: {\n          type: Number,\n          \"default\": 40\n        }\n      },\n      computed: {\n        pointerPosition: function () {\n          return this.pointer * this.optionHeight;\n        }\n      },\n      watch: {\n        filteredOptions: function () {\n          this.pointerAdjust();\n        }\n      },\n      methods: {\n        addPointerElement: function () {\n          this.filteredOptions.length > 0 && this.select(this.filteredOptions[this.pointer]), this.pointerReset();\n        },\n        pointerForward: function () {\n          this.pointer < this.filteredOptions.length - 1 && (this.pointer++, this.$els.list.scrollTop <= this.pointerPosition - this.visibleElements * this.optionHeight && (this.$els.list.scrollTop = this.pointerPosition - (this.visibleElements - 1) * this.optionHeight));\n        },\n        pointerBackward: function () {\n          this.pointer > 0 && (this.pointer--, this.$els.list.scrollTop >= this.pointerPosition && (this.$els.list.scrollTop = this.pointerPosition));\n        },\n        pointerReset: function () {\n          this.closeOnSelect && (this.pointer = 0, this.$els.list && (this.$els.list.scrollTop = 0));\n        },\n        pointerAdjust: function () {\n          this.pointer >= this.filteredOptions.length - 1 && (this.pointer = this.filteredOptions.length ? this.filteredOptions.length - 1 : 0);\n        },\n        pointerSet: function (t) {\n          this.pointer = t;\n        }\n      }\n    };\n  }, function (t, e, n) {\n    \"use strict\";\n\n    function i(t) {\n      return t && t.__esModule ? t : {\n        \"default\": t\n      };\n    }\n\n    var o = n(43),\n        r = i(o),\n        s = n(31),\n        l = i(s),\n        a = function u(t) {\n      if (Array.isArray(t)) return t.map(u);\n\n      if (t && \"object\" === (\"undefined\" == typeof t ? \"undefined\" : (0, l[\"default\"])(t))) {\n        for (var e = {}, n = (0, r[\"default\"])(t), i = 0, o = n.length; o > i; i++) {\n          var s = n[i];\n          e[s] = u(t[s]);\n        }\n\n        return e;\n      }\n\n      return t;\n    };\n\n    t.exports = a;\n  }, function (t, e, n) {\n    \"use strict\";\n\n    function i(t) {\n      return t && t.__esModule ? t : {\n        \"default\": t\n      };\n    }\n\n    e.__esModule = !0;\n    var o = n(45),\n        r = i(o),\n        s = n(44),\n        l = i(s),\n        a = \"function\" == typeof l[\"default\"] && \"symbol\" == typeof r[\"default\"] ? function (t) {\n      return typeof t;\n    } : function (t) {\n      return t && \"function\" == typeof l[\"default\"] && t.constructor === l[\"default\"] ? \"symbol\" : typeof t;\n    };\n    e[\"default\"] = \"function\" == typeof l[\"default\"] && \"symbol\" === a(r[\"default\"]) ? function (t) {\n      return \"undefined\" == typeof t ? \"undefined\" : a(t);\n    } : function (t) {\n      return t && \"function\" == typeof l[\"default\"] && t.constructor === l[\"default\"] ? \"symbol\" : \"undefined\" == typeof t ? \"undefined\" : a(t);\n    };\n  }, function (t, e) {\n    var n = {}.toString;\n\n    t.exports = function (t) {\n      return n.call(t).slice(8, -1);\n    };\n  }, function (t, e, n) {\n    var i = n(12),\n        o = n(1).document,\n        r = i(o) && i(o.createElement);\n\n    t.exports = function (t) {\n      return r ? o.createElement(t) : {};\n    };\n  }, function (t, e, n) {\n    t.exports = !n(4) && !n(9)(function () {\n      return 7 != Object.defineProperty(n(33)(\"div\"), \"a\", {\n        get: function () {\n          return 7;\n        }\n      }).a;\n    });\n  }, function (t, e, n) {\n    \"use strict\";\n\n    var i = n(19),\n        o = n(17),\n        r = n(40),\n        s = n(5),\n        l = n(2),\n        a = n(18),\n        u = n(57),\n        c = n(21),\n        f = n(64),\n        p = n(7)(\"iterator\"),\n        d = !([].keys && \"next\" in [].keys()),\n        h = \"@@iterator\",\n        m = \"keys\",\n        v = \"values\",\n        b = function () {\n      return this;\n    };\n\n    t.exports = function (t, e, n, g, y, x, _) {\n      u(n, e, g);\n\n      var w,\n          O,\n          S,\n          k = function (t) {\n        if (!d && t in M) return M[t];\n\n        switch (t) {\n          case m:\n            return function () {\n              return new n(this, t);\n            };\n\n          case v:\n            return function () {\n              return new n(this, t);\n            };\n        }\n\n        return function () {\n          return new n(this, t);\n        };\n      },\n          j = e + \" Iterator\",\n          E = y == v,\n          P = !1,\n          M = t.prototype,\n          L = M[p] || M[h] || y && M[y],\n          T = L || k(y),\n          A = y ? E ? k(\"entries\") : T : void 0,\n          N = \"Array\" == e ? M.entries || L : L;\n\n      if (N && (S = f(N.call(new t())), S !== Object.prototype && (c(S, j, !0), i || l(S, p) || s(S, p, b))), E && L && L.name !== v && (P = !0, T = function () {\n        return L.call(this);\n      }), i && !_ || !d && !P && M[p] || s(M, p, T), a[e] = T, a[j] = b, y) if (w = {\n        values: E ? T : k(v),\n        keys: x ? T : k(m),\n        entries: A\n      }, _) for (O in w) O in M || r(M, O, w[O]);else o(o.P + o.F * (d || P), e, w);\n      return w;\n    };\n  }, function (t, e, n) {\n    var i = n(11),\n        o = n(61),\n        r = n(16),\n        s = n(22)(\"IE_PROTO\"),\n        l = function () {},\n        a = \"prototype\",\n        u = function () {\n      var t,\n          e = n(33)(\"iframe\"),\n          i = r.length,\n          o = \">\";\n\n      for (e.style.display = \"none\", n(54).appendChild(e), e.src = \"javascript:\", t = e.contentWindow.document, t.open(), t.write(\"<script>document.F=Object</script\" + o), t.close(), u = t.F; i--;) delete u[a][r[i]];\n\n      return u();\n    };\n\n    t.exports = Object.create || function (t, e) {\n      var n;\n      return null !== t ? (l[a] = i(t), n = new l(), l[a] = null, n[s] = t) : n = u(), void 0 === e ? n : o(n, e);\n    };\n  }, function (t, e, n) {\n    var i = n(39),\n        o = n(16).concat(\"length\", \"prototype\");\n\n    e.f = Object.getOwnPropertyNames || function (t) {\n      return i(t, o);\n    };\n  }, function (t, e) {\n    e.f = Object.getOwnPropertySymbols;\n  }, function (t, e, n) {\n    var i = n(2),\n        o = n(3),\n        r = n(51)(!1),\n        s = n(22)(\"IE_PROTO\");\n\n    t.exports = function (t, e) {\n      var n,\n          l = o(t),\n          a = 0,\n          u = [];\n\n      for (n in l) n != s && i(l, n) && u.push(n);\n\n      for (; e.length > a;) i(l, n = e[a++]) && (~r(u, n) || u.push(n));\n\n      return u;\n    };\n  }, function (t, e, n) {\n    t.exports = n(5);\n  }, function (t, e, n) {\n    var i = n(15);\n\n    t.exports = function (t) {\n      return Object(i(t));\n    };\n  }, function (t, e, n) {\n    \"use strict\";\n\n    function i(t) {\n      return t && t.__esModule ? t : {\n        \"default\": t\n      };\n    }\n\n    Object.defineProperty(e, \"__esModule\", {\n      value: !0\n    });\n    var o = n(28),\n        r = i(o),\n        s = n(29),\n        l = i(s);\n    e[\"default\"] = {\n      mixins: [r[\"default\"], l[\"default\"]],\n      props: {\n        optionPartial: {\n          type: String,\n          \"default\": \"\"\n        },\n        selectLabel: {\n          type: String,\n          \"default\": \"Press enter to select\"\n        },\n        selectedLabel: {\n          type: String,\n          \"default\": \"Selected\"\n        },\n        deselectLabel: {\n          type: String,\n          \"default\": \"Press enter to remove\"\n        },\n        showLabels: {\n          type: Boolean,\n          \"default\": !0\n        },\n        limit: {\n          type: Number,\n          \"default\": 99999\n        },\n        limitText: {\n          type: Function,\n          \"default\": function (t) {\n            return \"and \" + t + \" more\";\n          }\n        },\n        loading: {\n          type: Boolean,\n          \"default\": !1\n        },\n        disabled: {\n          type: Boolean,\n          \"default\": !1\n        }\n      },\n      computed: {\n        visibleValue: function () {\n          return this.multiple ? this.value.slice(0, this.limit) : [];\n        }\n      },\n      ready: function () {\n        this.showLabels || (this.deselectLabel = this.selectedLabel = this.selectLabel = \"\");\n      }\n    };\n  }, function (t, e, n) {\n    t.exports = {\n      \"default\": n(46),\n      __esModule: !0\n    };\n  }, function (t, e, n) {\n    t.exports = {\n      \"default\": n(47),\n      __esModule: !0\n    };\n  }, function (t, e, n) {\n    t.exports = {\n      \"default\": n(48),\n      __esModule: !0\n    };\n  }, function (t, e, n) {\n    n(70), t.exports = n(8).Object.keys;\n  }, function (t, e, n) {\n    n(73), n(71), n(74), n(75), t.exports = n(8).Symbol;\n  }, function (t, e, n) {\n    n(72), n(76), t.exports = n(27).f(\"iterator\");\n  }, function (t, e) {\n    t.exports = function (t) {\n      if (\"function\" != typeof t) throw TypeError(t + \" is not a function!\");\n      return t;\n    };\n  }, function (t, e) {\n    t.exports = function () {};\n  }, function (t, e, n) {\n    var i = n(3),\n        o = n(68),\n        r = n(67);\n\n    t.exports = function (t) {\n      return function (e, n, s) {\n        var l,\n            a = i(e),\n            u = o(a.length),\n            c = r(s, u);\n\n        if (t && n != n) {\n          for (; u > c;) if (l = a[c++], l != l) return !0;\n        } else for (; u > c; c++) if ((t || c in a) && a[c] === n) return t || c || 0;\n\n        return !t && -1;\n      };\n    };\n  }, function (t, e, n) {\n    var i = n(49);\n\n    t.exports = function (t, e, n) {\n      if (i(t), void 0 === e) return t;\n\n      switch (n) {\n        case 1:\n          return function (n) {\n            return t.call(e, n);\n          };\n\n        case 2:\n          return function (n, i) {\n            return t.call(e, n, i);\n          };\n\n        case 3:\n          return function (n, i, o) {\n            return t.call(e, n, i, o);\n          };\n      }\n\n      return function () {\n        return t.apply(e, arguments);\n      };\n    };\n  }, function (t, e, n) {\n    var i = n(10),\n        o = n(38),\n        r = n(20);\n\n    t.exports = function (t) {\n      var e = i(t),\n          n = o.f;\n      if (n) for (var s, l = n(t), a = r.f, u = 0; l.length > u;) a.call(t, s = l[u++]) && e.push(s);\n      return e;\n    };\n  }, function (t, e, n) {\n    t.exports = n(1).document && document.documentElement;\n  }, function (t, e, n) {\n    var i = n(32);\n    t.exports = Object(\"z\").propertyIsEnumerable(0) ? Object : function (t) {\n      return \"String\" == i(t) ? t.split(\"\") : Object(t);\n    };\n  }, function (t, e, n) {\n    var i = n(32);\n\n    t.exports = Array.isArray || function (t) {\n      return \"Array\" == i(t);\n    };\n  }, function (t, e, n) {\n    \"use strict\";\n\n    var i = n(36),\n        o = n(13),\n        r = n(21),\n        s = {};\n    n(5)(s, n(7)(\"iterator\"), function () {\n      return this;\n    }), t.exports = function (t, e, n) {\n      t.prototype = i(s, {\n        next: o(1, n)\n      }), r(t, e + \" Iterator\");\n    };\n  }, function (t, e) {\n    t.exports = function (t, e) {\n      return {\n        value: e,\n        done: !!t\n      };\n    };\n  }, function (t, e, n) {\n    var i = n(10),\n        o = n(3);\n\n    t.exports = function (t, e) {\n      for (var n, r = o(t), s = i(r), l = s.length, a = 0; l > a;) if (r[n = s[a++]] === e) return n;\n    };\n  }, function (t, e, n) {\n    var i = n(14)(\"meta\"),\n        o = n(12),\n        r = n(2),\n        s = n(6).f,\n        l = 0,\n        a = Object.isExtensible || function () {\n      return !0;\n    },\n        u = !n(9)(function () {\n      return a(Object.preventExtensions({}));\n    }),\n        c = function (t) {\n      s(t, i, {\n        value: {\n          i: \"O\" + ++l,\n          w: {}\n        }\n      });\n    },\n        f = function (t, e) {\n      if (!o(t)) return \"symbol\" == typeof t ? t : (\"string\" == typeof t ? \"S\" : \"P\") + t;\n\n      if (!r(t, i)) {\n        if (!a(t)) return \"F\";\n        if (!e) return \"E\";\n        c(t);\n      }\n\n      return t[i].i;\n    },\n        p = function (t, e) {\n      if (!r(t, i)) {\n        if (!a(t)) return !0;\n        if (!e) return !1;\n        c(t);\n      }\n\n      return t[i].w;\n    },\n        d = function (t) {\n      return u && h.NEED && a(t) && !r(t, i) && c(t), t;\n    },\n        h = t.exports = {\n      KEY: i,\n      NEED: !1,\n      fastKey: f,\n      getWeak: p,\n      onFreeze: d\n    };\n  }, function (t, e, n) {\n    var i = n(6),\n        o = n(11),\n        r = n(10);\n    t.exports = n(4) ? Object.defineProperties : function (t, e) {\n      o(t);\n\n      for (var n, s = r(e), l = s.length, a = 0; l > a;) i.f(t, n = s[a++], e[n]);\n\n      return t;\n    };\n  }, function (t, e, n) {\n    var i = n(20),\n        o = n(13),\n        r = n(3),\n        s = n(25),\n        l = n(2),\n        a = n(34),\n        u = Object.getOwnPropertyDescriptor;\n    e.f = n(4) ? u : function (t, e) {\n      if (t = r(t), e = s(e, !0), a) try {\n        return u(t, e);\n      } catch (n) {}\n      return l(t, e) ? o(!i.f.call(t, e), t[e]) : void 0;\n    };\n  }, function (t, e, n) {\n    var i = n(3),\n        o = n(37).f,\n        r = {}.toString,\n        s = \"object\" == typeof window && window && Object.getOwnPropertyNames ? Object.getOwnPropertyNames(window) : [],\n        l = function (t) {\n      try {\n        return o(t);\n      } catch (e) {\n        return s.slice();\n      }\n    };\n\n    t.exports.f = function (t) {\n      return s && \"[object Window]\" == r.call(t) ? l(t) : o(i(t));\n    };\n  }, function (t, e, n) {\n    var i = n(2),\n        o = n(41),\n        r = n(22)(\"IE_PROTO\"),\n        s = Object.prototype;\n\n    t.exports = Object.getPrototypeOf || function (t) {\n      return t = o(t), i(t, r) ? t[r] : \"function\" == typeof t.constructor && t instanceof t.constructor ? t.constructor.prototype : t instanceof Object ? s : null;\n    };\n  }, function (t, e, n) {\n    var i = n(17),\n        o = n(8),\n        r = n(9);\n\n    t.exports = function (t, e) {\n      var n = (o.Object || {})[t] || Object[t],\n          s = {};\n      s[t] = e(n), i(i.S + i.F * r(function () {\n        n(1);\n      }), \"Object\", s);\n    };\n  }, function (t, e, n) {\n    var i = n(24),\n        o = n(15);\n\n    t.exports = function (t) {\n      return function (e, n) {\n        var r,\n            s,\n            l = String(o(e)),\n            a = i(n),\n            u = l.length;\n        return 0 > a || a >= u ? t ? \"\" : void 0 : (r = l.charCodeAt(a), 55296 > r || r > 56319 || a + 1 === u || (s = l.charCodeAt(a + 1)) < 56320 || s > 57343 ? t ? l.charAt(a) : r : t ? l.slice(a, a + 2) : (r - 55296 << 10) + (s - 56320) + 65536);\n      };\n    };\n  }, function (t, e, n) {\n    var i = n(24),\n        o = Math.max,\n        r = Math.min;\n\n    t.exports = function (t, e) {\n      return t = i(t), 0 > t ? o(t + e, 0) : r(t, e);\n    };\n  }, function (t, e, n) {\n    var i = n(24),\n        o = Math.min;\n\n    t.exports = function (t) {\n      return t > 0 ? o(i(t), 9007199254740991) : 0;\n    };\n  }, function (t, e, n) {\n    \"use strict\";\n\n    var i = n(50),\n        o = n(58),\n        r = n(18),\n        s = n(3);\n    t.exports = n(35)(Array, \"Array\", function (t, e) {\n      this._t = s(t), this._i = 0, this._k = e;\n    }, function () {\n      var t = this._t,\n          e = this._k,\n          n = this._i++;\n      return !t || n >= t.length ? (this._t = void 0, o(1)) : \"keys\" == e ? o(0, n) : \"values\" == e ? o(0, t[n]) : o(0, [n, t[n]]);\n    }, \"values\"), r.Arguments = r.Array, i(\"keys\"), i(\"values\"), i(\"entries\");\n  }, function (t, e, n) {\n    var i = n(41),\n        o = n(10);\n    n(65)(\"keys\", function () {\n      return function (t) {\n        return o(i(t));\n      };\n    });\n  }, function (t, e) {}, function (t, e, n) {\n    \"use strict\";\n\n    var i = n(66)(!0);\n    n(35)(String, \"String\", function (t) {\n      this._t = String(t), this._i = 0;\n    }, function () {\n      var t,\n          e = this._t,\n          n = this._i;\n      return n >= e.length ? {\n        value: void 0,\n        done: !0\n      } : (t = i(e, n), this._i += t.length, {\n        value: t,\n        done: !1\n      });\n    });\n  }, function (t, e, n) {\n    \"use strict\";\n\n    var i = n(1),\n        o = n(2),\n        r = n(4),\n        s = n(17),\n        l = n(40),\n        a = n(60).KEY,\n        u = n(9),\n        c = n(23),\n        f = n(21),\n        p = n(14),\n        d = n(7),\n        h = n(27),\n        m = n(26),\n        v = n(59),\n        b = n(53),\n        g = n(56),\n        y = n(11),\n        x = n(3),\n        _ = n(25),\n        w = n(13),\n        O = n(36),\n        S = n(63),\n        k = n(62),\n        j = n(6),\n        E = n(10),\n        P = k.f,\n        M = j.f,\n        L = S.f,\n        T = i.Symbol,\n        A = i.JSON,\n        N = A && A.stringify,\n        $ = \"prototype\",\n        B = d(\"_hidden\"),\n        C = d(\"toPrimitive\"),\n        F = {}.propertyIsEnumerable,\n        z = c(\"symbol-registry\"),\n        I = c(\"symbols\"),\n        R = c(\"op-symbols\"),\n        H = Object[$],\n        K = \"function\" == typeof T,\n        D = i.QObject,\n        W = !D || !D[$] || !D[$].findChild,\n        J = r && u(function () {\n      return 7 != O(M({}, \"a\", {\n        get: function () {\n          return M(this, \"a\", {\n            value: 7\n          }).a;\n        }\n      })).a;\n    }) ? function (t, e, n) {\n      var i = P(H, e);\n      i && delete H[e], M(t, e, n), i && t !== H && M(H, e, i);\n    } : M,\n        U = function (t) {\n      var e = I[t] = O(T[$]);\n      return e._k = t, e;\n    },\n        V = K && \"symbol\" == typeof T.iterator ? function (t) {\n      return \"symbol\" == typeof t;\n    } : function (t) {\n      return t instanceof T;\n    },\n        G = function (t, e, n) {\n      return t === H && G(R, e, n), y(t), e = _(e, !0), y(n), o(I, e) ? (n.enumerable ? (o(t, B) && t[B][e] && (t[B][e] = !1), n = O(n, {\n        enumerable: w(0, !1)\n      })) : (o(t, B) || M(t, B, w(1, {})), t[B][e] = !0), J(t, e, n)) : M(t, e, n);\n    },\n        q = function (t, e) {\n      y(t);\n\n      for (var n, i = b(e = x(e)), o = 0, r = i.length; r > o;) G(t, n = i[o++], e[n]);\n\n      return t;\n    },\n        Y = function (t, e) {\n      return void 0 === e ? O(t) : q(O(t), e);\n    },\n        Q = function (t) {\n      var e = F.call(this, t = _(t, !0));\n      return this === H && o(I, t) && !o(R, t) ? !1 : e || !o(this, t) || !o(I, t) || o(this, B) && this[B][t] ? e : !0;\n    },\n        X = function (t, e) {\n      if (t = x(t), e = _(e, !0), t !== H || !o(I, e) || o(R, e)) {\n        var n = P(t, e);\n        return !n || !o(I, e) || o(t, B) && t[B][e] || (n.enumerable = !0), n;\n      }\n    },\n        Z = function (t) {\n      for (var e, n = L(x(t)), i = [], r = 0; n.length > r;) o(I, e = n[r++]) || e == B || e == a || i.push(e);\n\n      return i;\n    },\n        tt = function (t) {\n      for (var e, n = t === H, i = L(n ? R : x(t)), r = [], s = 0; i.length > s;) o(I, e = i[s++]) && (n ? o(H, e) : !0) && r.push(I[e]);\n\n      return r;\n    };\n\n    K || (T = function () {\n      if (this instanceof T) throw TypeError(\"Symbol is not a constructor!\");\n\n      var t = p(arguments.length > 0 ? arguments[0] : void 0),\n          e = function (n) {\n        this === H && e.call(R, n), o(this, B) && o(this[B], t) && (this[B][t] = !1), J(this, t, w(1, n));\n      };\n\n      return r && W && J(H, t, {\n        configurable: !0,\n        set: e\n      }), U(t);\n    }, l(T[$], \"toString\", function () {\n      return this._k;\n    }), k.f = X, j.f = G, n(37).f = S.f = Z, n(20).f = Q, n(38).f = tt, r && !n(19) && l(H, \"propertyIsEnumerable\", Q, !0), h.f = function (t) {\n      return U(d(t));\n    }), s(s.G + s.W + s.F * !K, {\n      Symbol: T\n    });\n\n    for (var et = \"hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables\".split(\",\"), nt = 0; et.length > nt;) d(et[nt++]);\n\n    for (var et = E(d.store), nt = 0; et.length > nt;) m(et[nt++]);\n\n    s(s.S + s.F * !K, \"Symbol\", {\n      \"for\": function (t) {\n        return o(z, t += \"\") ? z[t] : z[t] = T(t);\n      },\n      keyFor: function (t) {\n        if (V(t)) return v(z, t);\n        throw TypeError(t + \" is not a symbol!\");\n      },\n      useSetter: function () {\n        W = !0;\n      },\n      useSimple: function () {\n        W = !1;\n      }\n    }), s(s.S + s.F * !K, \"Object\", {\n      create: Y,\n      defineProperty: G,\n      defineProperties: q,\n      getOwnPropertyDescriptor: X,\n      getOwnPropertyNames: Z,\n      getOwnPropertySymbols: tt\n    }), A && s(s.S + s.F * (!K || u(function () {\n      var t = T();\n      return \"[null]\" != N([t]) || \"{}\" != N({\n        a: t\n      }) || \"{}\" != N(Object(t));\n    })), \"JSON\", {\n      stringify: function (t) {\n        if (void 0 !== t && !V(t)) {\n          for (var e, n, i = [t], o = 1; arguments.length > o;) i.push(arguments[o++]);\n\n          return e = i[1], \"function\" == typeof e && (n = e), !n && g(e) || (e = function (t, e) {\n            return n && (e = n.call(this, t, e)), V(e) ? void 0 : e;\n          }), i[1] = e, N.apply(A, i);\n        }\n      }\n    }), T[$][C] || n(5)(T[$], C, T[$].valueOf), f(T, \"Symbol\"), f(Math, \"Math\", !0), f(i.JSON, \"JSON\", !0);\n  }, function (t, e, n) {\n    n(26)(\"asyncIterator\");\n  }, function (t, e, n) {\n    n(26)(\"observable\");\n  }, function (t, e, n) {\n    n(69);\n\n    for (var i = n(1), o = n(5), r = n(18), s = n(7)(\"toStringTag\"), l = [\"NodeList\", \"DOMTokenList\", \"MediaList\", \"StyleSheetList\", \"CSSRuleList\"], a = 0; 5 > a; a++) {\n      var u = l[a],\n          c = i[u],\n          f = c && c.prototype;\n      f && !f[s] && o(f, s, u), r[u] = r.Array;\n    }\n  }, function (t, e, n) {\n    e = t.exports = n(78)(), e.push([t.id, 'fieldset[disabled] .multiselect{pointer-events:none}.multiselect__spinner{position:absolute;right:1px;top:1px;width:48px;height:35px;background:#fff;display:block}.multiselect__spinner:after,.multiselect__spinner:before{position:absolute;content:\"\";top:50%;left:50%;margin:-8px 0 0 -8px;width:16px;height:16px;border-radius:100%;border-color:#41b883 transparent transparent;border-style:solid;border-width:2px;box-shadow:0 0 0 1px transparent}.multiselect__spinner:before{-webkit-animation:spinning 2.4s cubic-bezier(.41,.26,.2,.62);animation:spinning 2.4s cubic-bezier(.41,.26,.2,.62);-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite}.multiselect__spinner:after{-webkit-animation:spinning 2.4s cubic-bezier(.51,.09,.21,.8);animation:spinning 2.4s cubic-bezier(.51,.09,.21,.8);-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite}.multiselect__loading-transition{-webkit-transition:opacity .4s ease-in-out;transition:opacity .4s ease-in-out;opacity:1}.multiselect__loading-enter,.multiselect__loading-leave{opacity:0}.multiselect,.multiselect__input,.multiselect__single{font-family:inherit;font-size:14px}.multiselect{box-sizing:content-box;display:block;position:relative;width:100%;min-height:40px;text-align:left;color:#35495e}.multiselect *{box-sizing:border-box}.multiselect:focus{outline:none}.multiselect--disabled{pointer-events:none;opacity:.6}.multiselect--active{z-index:50}.multiselect--active .multiselect__current,.multiselect--active .multiselect__input,.multiselect--active .multiselect__tags{border-bottom-left-radius:0;border-bottom-right-radius:0}.multiselect--active .multiselect__select{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.multiselect__input,.multiselect__single{position:relative;display:inline-block;min-height:20px;line-height:20px;border:none;border-radius:5px;background:#fff;padding:1px 0 0 5px;width:100%;-webkit-transition:border .1s ease;transition:border .1s ease;box-sizing:border-box;margin-bottom:8px}.multiselect__tag~.multiselect__input{width:auto}.multiselect__input:hover,.multiselect__single:hover{border-color:#cfcfcf}.multiselect__input:focus,.multiselect__single:focus{border-color:#a8a8a8;outline:none}.multiselect__single{padding-left:6px;margin-bottom:8px}.multiselect__tags{min-height:40px;display:block;padding:8px 40px 0 8px;border-radius:5px;border:1px solid #e8e8e8;background:#fff}.multiselect__tag{position:relative;display:inline-block;padding:4px 26px 4px 10px;border-radius:5px;margin-right:10px;color:#fff;line-height:1;background:#41b883;margin-bottom:8px;white-space:nowrap}.multiselect__tag-icon{cursor:pointer;margin-left:7px;position:absolute;right:0;top:0;bottom:0;font-weight:700;font-style:initial;width:22px;text-align:center;line-height:22px;-webkit-transition:all .2s ease;transition:all .2s ease;border-radius:5px}.multiselect__tag-icon:after{content:\"\\\\D7\";color:#266d4d;font-size:14px}.multiselect__tag-icon:focus,.multiselect__tag-icon:hover{background:#369a6e}.multiselect__tag-icon:focus:after,.multiselect__tag-icon:hover:after{color:#fff}.multiselect__current{min-height:40px;overflow:hidden;padding:8px 12px 0;padding-right:30px;white-space:nowrap;border-radius:5px;border:1px solid #e8e8e8}.multiselect__current,.multiselect__select{line-height:16px;box-sizing:border-box;display:block;margin:0;text-decoration:none;cursor:pointer}.multiselect__select{position:absolute;width:40px;height:38px;right:1px;top:1px;padding:4px 8px;text-align:center;-webkit-transition:-webkit-transform .2s ease;transition:-webkit-transform .2s ease;transition:transform .2s ease;transition:transform .2s ease,-webkit-transform .2s ease}.multiselect__select:before{position:relative;right:0;top:65%;color:#999;margin-top:4px;border-style:solid;border-width:5px 5px 0;border-color:#999 transparent transparent;content:\"\"}.multiselect__placeholder{color:#adadad;display:inline-block;margin-bottom:10px;padding-top:2px}.multiselect--active .multiselect__placeholder{display:none}.multiselect__content{position:absolute;list-style:none;display:block;background:#fff;width:100%;max-height:240px;overflow:auto;padding:0;margin:0;border:1px solid #e8e8e8;border-top:none;border-bottom-left-radius:5px;border-bottom-right-radius:5px;z-index:50}.multiselect__content::webkit-scrollbar{display:none}.multiselect__option{display:block;padding:12px;min-height:40px;line-height:16px;text-decoration:none;text-transform:none;vertical-align:middle;position:relative;cursor:pointer;white-space:nowrap}.multiselect__option:after{top:0;right:0;position:absolute;line-height:40px;padding-right:12px;padding-left:20px}.multiselect__option--highlight{background:#41b883;outline:none;color:#fff}.multiselect__option--highlight:after{content:attr(data-select);background:#41b883;color:#fff}.multiselect__option--selected{background:#f3f3f3;color:#35495e;font-weight:700}.multiselect__option--selected:after{content:attr(data-selected);color:silver}.multiselect__option--selected.multiselect__option--highlight{background:#ff6a6a;color:#fff}.multiselect__option--selected.multiselect__option--highlight:after{background:#ff6a6a;content:attr(data-deselect);color:#fff}.multiselect--disabled{background:#ededed;pointer-events:none}.multiselect--disabled .multiselect__current,.multiselect--disabled .multiselect__select,.multiselect__option--disabled{background:#ededed;color:#a6a6a6}.multiselect__option--disabled{cursor:text;pointer-events:none}.multiselect__option--disabled:visited{color:#a6a6a6}.multiselect__option--disabled:focus,.multiselect__option--disabled:hover{background:#3dad7b}.multiselect-transition{-webkit-transition:all .3s ease;transition:all .3s ease}.multiselect-enter,.multiselect-leave{opacity:0;max-height:0!important}@-webkit-keyframes spinning{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(2turn);transform:rotate(2turn)}}@keyframes spinning{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(2turn);transform:rotate(2turn)}}', \"\"]);\n  }, function (t, e) {\n    t.exports = function () {\n      var t = [];\n      return t.toString = function () {\n        for (var t = [], e = 0; e < this.length; e++) {\n          var n = this[e];\n          n[2] ? t.push(\"@media \" + n[2] + \"{\" + n[1] + \"}\") : t.push(n[1]);\n        }\n\n        return t.join(\"\");\n      }, t.i = function (e, n) {\n        \"string\" == typeof e && (e = [[null, e, \"\"]]);\n\n        for (var i = {}, o = 0; o < this.length; o++) {\n          var r = this[o][0];\n          \"number\" == typeof r && (i[r] = !0);\n        }\n\n        for (o = 0; o < e.length; o++) {\n          var s = e[o];\n          \"number\" == typeof s[0] && i[s[0]] || (n && !s[2] ? s[2] = n : n && (s[2] = \"(\" + s[2] + \") and (\" + n + \")\"), t.push(s));\n        }\n      }, t;\n    };\n  }, function (t, e) {\n    t.exports = '<div tabindex=0 :class=\"{ \\'multiselect--active\\': isOpen, \\'multiselect--disabled\\': disabled }\" @focus=activate() @blur=\"searchable ? false : deactivate()\" @keydown.self.down.prevent=pointerForward() @keydown.self.up.prevent=pointerBackward() @keydown.enter.stop.prevent.self=addPointerElement() @keydown.tab.stop=addPointerElement() @keyup.esc=deactivate() class=multiselect> <div @mousedown.prevent=toggle() class=multiselect__select></div> <div v-el:tags class=multiselect__tags> <span v-for=\"option in visibleValue\" track-by=$index onmousedown=event.preventDefault() class=multiselect__tag> <span v-text=getOptionLabel(option)></span> <i aria-hidden=true tabindex=1 @keydown.enter.prevent=removeElement(option) @mousedown.prevent=removeElement(option) class=multiselect__tag-icon> </i> </span> <template v-if=\"value && value.length > limit\"> <strong v-text=\"limitText(value.length - limit)\"></strong> </template> <div v-show=loading transition=multiselect__loading class=multiselect__spinner></div> <input name=search type=text autocomplete=off :placeholder=placeholder v-el:search v-if=searchable v-model=search :disabled=disabled @focus.prevent=activate() @blur.prevent=deactivate() @keyup.esc=deactivate() @keyup.down=pointerForward() @keyup.up=pointerBackward() @keydown.enter.stop.prevent.self=addPointerElement() @keydown.tab.stop=addPointerElement() @keydown.delete=removeLastElement() class=multiselect__input /> <span v-if=\"!searchable && !multiple\" class=multiselect__single v-text=\"currentOptionLabel || placeholder\"> </span> </div> <ul transition=multiselect :style=\"{ maxHeight: maxHeight + \\'px\\' }\" v-el:list v-show=isOpen @mousedown.stop.prevent=\"\" class=multiselect__content> <slot name=beforeList></slot> <li v-if=\"multiple && max !== 0 && max === value.length\"> <span class=multiselect__option> <slot name=maxElements>Maximum of {{ max }} options selected. First remove a selected option to select another.</slot> </span> </li> <template v-if=\"!max || value.length < max\"> <li v-for=\"option in filteredOptions\" track-by=$index tabindex=0 :class=\"{ \\'multiselect__option--highlight\\': $index === pointer && this.showPointer, \\'multiselect__option--selected\\': !isNotSelected(option) }\" class=multiselect__option @mousedown.prevent=select(option) @mouseenter=pointerSet($index) :data-select=\"option.isTag ? tagPlaceholder : selectLabel\" :data-selected=selectedLabel :data-deselect=deselectLabel> <partial :name=optionPartial v-if=optionPartial.length></partial> <span v-else v-text=getOptionLabel(option)></span> </li> </template> <li v-show=\"filteredOptions.length === 0 && search\"> <span class=multiselect__option> <slot name=noResult>No elements found. Consider changing the search query.</slot> </span> </li> <slot name=afterList></slot> </ul> </div>';\n  }, function (t, e, n) {\n    var i,\n        o,\n        r = {};\n    n(82), i = n(42), o = n(79), t.exports = i || {}, t.exports.__esModule && (t.exports = t.exports[\"default\"]);\n    var s = \"function\" == typeof t.exports ? t.exports.options || (t.exports.options = {}) : t.exports;\n    o && (s.template = o), s.computed || (s.computed = {}), Object.keys(r).forEach(function (t) {\n      var e = r[t];\n\n      s.computed[t] = function () {\n        return e;\n      };\n    });\n  }, function (t, e, n) {\n    function i(t, e) {\n      for (var n = 0; n < t.length; n++) {\n        var i = t[n],\n            o = f[i.id];\n\n        if (o) {\n          o.refs++;\n\n          for (var r = 0; r < o.parts.length; r++) o.parts[r](i.parts[r]);\n\n          for (; r < i.parts.length; r++) o.parts.push(a(i.parts[r], e));\n        } else {\n          for (var s = [], r = 0; r < i.parts.length; r++) s.push(a(i.parts[r], e));\n\n          f[i.id] = {\n            id: i.id,\n            refs: 1,\n            parts: s\n          };\n        }\n      }\n    }\n\n    function o(t) {\n      for (var e = [], n = {}, i = 0; i < t.length; i++) {\n        var o = t[i],\n            r = o[0],\n            s = o[1],\n            l = o[2],\n            a = o[3],\n            u = {\n          css: s,\n          media: l,\n          sourceMap: a\n        };\n        n[r] ? n[r].parts.push(u) : e.push(n[r] = {\n          id: r,\n          parts: [u]\n        });\n      }\n\n      return e;\n    }\n\n    function r(t, e) {\n      var n = h(),\n          i = b[b.length - 1];\n      if (\"top\" === t.insertAt) i ? i.nextSibling ? n.insertBefore(e, i.nextSibling) : n.appendChild(e) : n.insertBefore(e, n.firstChild), b.push(e);else {\n        if (\"bottom\" !== t.insertAt) throw new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n        n.appendChild(e);\n      }\n    }\n\n    function s(t) {\n      t.parentNode.removeChild(t);\n      var e = b.indexOf(t);\n      e >= 0 && b.splice(e, 1);\n    }\n\n    function l(t) {\n      var e = document.createElement(\"style\");\n      return e.type = \"text/css\", r(t, e), e;\n    }\n\n    function a(t, e) {\n      var n, i, o;\n\n      if (e.singleton) {\n        var r = v++;\n        n = m || (m = l(e)), i = u.bind(null, n, r, !1), o = u.bind(null, n, r, !0);\n      } else n = l(e), i = c.bind(null, n), o = function () {\n        s(n);\n      };\n\n      return i(t), function (e) {\n        if (e) {\n          if (e.css === t.css && e.media === t.media && e.sourceMap === t.sourceMap) return;\n          i(t = e);\n        } else o();\n      };\n    }\n\n    function u(t, e, n, i) {\n      var o = n ? \"\" : i.css;\n      if (t.styleSheet) t.styleSheet.cssText = g(e, o);else {\n        var r = document.createTextNode(o),\n            s = t.childNodes;\n        s[e] && t.removeChild(s[e]), s.length ? t.insertBefore(r, s[e]) : t.appendChild(r);\n      }\n    }\n\n    function c(t, e) {\n      var n = e.css,\n          i = e.media,\n          o = e.sourceMap;\n      if (i && t.setAttribute(\"media\", i), o && (n += \"\\n/*# sourceURL=\" + o.sources[0] + \" */\", n += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(o)))) + \" */\"), t.styleSheet) t.styleSheet.cssText = n;else {\n        for (; t.firstChild;) t.removeChild(t.firstChild);\n\n        t.appendChild(document.createTextNode(n));\n      }\n    }\n\n    var f = {},\n        p = function (t) {\n      var e;\n      return function () {\n        return \"undefined\" == typeof e && (e = t.apply(this, arguments)), e;\n      };\n    },\n        d = p(function () {\n      return /msie [6-9]\\b/.test(window.navigator.userAgent.toLowerCase());\n    }),\n        h = p(function () {\n      return document.head || document.getElementsByTagName(\"head\")[0];\n    }),\n        m = null,\n        v = 0,\n        b = [];\n\n    t.exports = function (t, e) {\n      e = e || {}, \"undefined\" == typeof e.singleton && (e.singleton = d()), \"undefined\" == typeof e.insertAt && (e.insertAt = \"bottom\");\n      var n = o(t);\n      return i(n, e), function (t) {\n        for (var r = [], s = 0; s < n.length; s++) {\n          var l = n[s],\n              a = f[l.id];\n          a.refs--, r.push(a);\n        }\n\n        if (t) {\n          var u = o(t);\n          i(u, e);\n        }\n\n        for (var s = 0; s < r.length; s++) {\n          var a = r[s];\n\n          if (0 === a.refs) {\n            for (var c = 0; c < a.parts.length; c++) a.parts[c]();\n\n            delete f[a.id];\n          }\n        }\n      };\n    };\n\n    var g = function () {\n      var t = [];\n      return function (e, n) {\n        return t[e] = n, t.filter(Boolean).join(\"\\n\");\n      };\n    }();\n  }, function (t, e, n) {\n    var i = n(77);\n    \"string\" == typeof i && (i = [[t.id, i, \"\"]]);\n    n(81)(i, {});\n    i.locals && (t.exports = i.locals);\n  }]);\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/components/vue-multiselect/lib/vue-multiselect.min.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/dateTimeMixin.js":
/*!*********************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/dateTimeMixin.js ***!
  \*********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  ready() {\n    jQuery('.erp-date-field').datepicker({\n      dateFormat: 'yy-mm-dd',\n      changeMonth: true,\n      changeYear: true,\n      yearRange: '-100:+0'\n    });\n    jQuery('.erp-time-field').timepicker({\n      'scrollDefault': 'now',\n      'step': 15\n    });\n  }\n\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/dateTimeMixin.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/main.js":
/*!************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/main.js ***!
  \************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./store */ \"./modules/hrm/workflow/assets/js/store.js\");\n/* harmony import */ var _components_text_editor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/text-editor */ \"./modules/hrm/workflow/assets/js/components/text-editor/index.js\");\n/* harmony import */ var _components_vue_multiselect_lib_vue_multiselect_min__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/vue-multiselect/lib/vue-multiselect.min */ \"./modules/hrm/workflow/assets/js/components/vue-multiselect/lib/vue-multiselect.min.js\");\n/* harmony import */ var _components_vue_multiselect_lib_vue_multiselect_min__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_components_vue_multiselect_lib_vue_multiselect_min__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_singleselect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/singleselect */ \"./modules/hrm/workflow/assets/js/components/singleselect/index.js\");\n/* harmony import */ var _components_condition__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/condition */ \"./modules/hrm/workflow/assets/js/components/condition/index.js\");\n/* harmony import */ var _components_operator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/operator */ \"./modules/hrm/workflow/assets/js/components/operator/index.js\");\n/* harmony import */ var _components_action_container__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/action-container */ \"./modules/hrm/workflow/assets/js/components/action-container/index.js\");\n/* harmony import */ var _components_send_email_action__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/send-email-action */ \"./modules/hrm/workflow/assets/js/components/send-email-action/index.js\");\n/* harmony import */ var _components_assign_task_action__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/assign-task-action */ \"./modules/hrm/workflow/assets/js/components/assign-task-action/index.js\");\n/* harmony import */ var _components_trigger_action_hook_action__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/trigger-action-hook-action */ \"./modules/hrm/workflow/assets/js/components/trigger-action-hook-action/index.js\");\n/* harmony import */ var _components_update_field_action__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/update-field-action */ \"./modules/hrm/workflow/assets/js/components/update-field-action/index.js\");\n/* harmony import */ var _components_add_user_role_action__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components/add-user-role-action */ \"./modules/hrm/workflow/assets/js/components/add-user-role-action/index.js\");\n/* harmony import */ var _components_add_activity_action__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./components/add-activity-action */ \"./modules/hrm/workflow/assets/js/components/add-activity-action/index.js\");\n/* harmony import */ var _components_schedule_meeting_action__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./components/schedule-meeting-action */ \"./modules/hrm/workflow/assets/js/components/schedule-meeting-action/index.js\");\n/* harmony import */ var _components_send_invoice_action__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./components/send-invoice-action */ \"./modules/hrm/workflow/assets/js/components/send-invoice-action/index.js\");\n/* harmony import */ var _components_send_sms_action__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/send-sms-action */ \"./modules/hrm/workflow/assets/js/components/send-sms-action/index.js\");\nfunction erp_wf_js_var_extract(variable) {\n  for (var key in variable) {\n    window[key] = variable[key];\n  }\n}\n\nerp_wf_js_var_extract(erp_wf_localize_vars);\nVue.filter('titlecase', function (value) {\n  var str = value.replace(/_/g, ' ');\n  return str.toLowerCase().split(' ').map(function (word) {\n    return word.charAt(0).toUpperCase() + word.slice(1);\n  }).join(' ');\n});\nVue.filter('chunk', function (array, length) {\n  var totalChunks = [];\n  var chunkLength = parseInt(length, 10);\n\n  if (chunkLength <= 0) {\n    return array;\n  }\n\n  for (var i = 0; i < array.length; i += chunkLength) {\n    totalChunks.push(array.slice(i, i + chunkLength));\n  }\n\n  return totalChunks;\n});\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nVue.component('text-editor', _components_text_editor__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nVue.component('multiselect', _components_vue_multiselect_lib_vue_multiselect_min__WEBPACK_IMPORTED_MODULE_2___default.a);\nVue.component('singleselect', _components_singleselect__WEBPACK_IMPORTED_MODULE_3__[\"default\"]);\nVue.component('condition', _components_condition__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\nVue.component('operator', _components_operator__WEBPACK_IMPORTED_MODULE_5__[\"default\"]);\nVue.component('action-container', _components_action_container__WEBPACK_IMPORTED_MODULE_6__[\"default\"]);\nVue.component('send-email-action', _components_send_email_action__WEBPACK_IMPORTED_MODULE_7__[\"default\"]);\nVue.component('assign-task-action', _components_assign_task_action__WEBPACK_IMPORTED_MODULE_8__[\"default\"]);\nVue.component('trigger-action-hook-action', _components_trigger_action_hook_action__WEBPACK_IMPORTED_MODULE_9__[\"default\"]);\nVue.component('update-field-action', _components_update_field_action__WEBPACK_IMPORTED_MODULE_10__[\"default\"]);\nVue.component('add-user-role-action', _components_add_user_role_action__WEBPACK_IMPORTED_MODULE_11__[\"default\"]);\nVue.component('add-activity-action', _components_add_activity_action__WEBPACK_IMPORTED_MODULE_12__[\"default\"]);\nVue.component('schedule-meeting-action', _components_schedule_meeting_action__WEBPACK_IMPORTED_MODULE_13__[\"default\"]);\nVue.component('send-invoice-action', _components_send_invoice_action__WEBPACK_IMPORTED_MODULE_14__[\"default\"]);\nVue.component('send-sms-action', _components_send_sms_action__WEBPACK_IMPORTED_MODULE_15__[\"default\"]);\nvar vm = new Vue({\n  el: '#workflow-app',\n\n  data() {\n    return {\n      workflow_edit_mode: false,\n      nonce: '',\n      sharedState: _store__WEBPACK_IMPORTED_MODULE_0__[\"default\"].state,\n      workflow_name: '',\n      delay_time: 0,\n      delay_period: 'minute',\n      errors: {\n        workflow_name: false,\n        event: false\n      },\n      conditions_group: 'or',\n      events_group: 'general',\n      event_groups_list: [],\n      event: '',\n      events_list: [],\n      default_condition: {},\n      conditions: [],\n      condition_name: '',\n      actions: [],\n      is_display_actions: false\n    };\n  },\n\n  ready() {\n    this.sharedState.i18n = i18n;\n    this.event_groups_list = modules_list;\n    this.events_list = events_list[this.events_group];\n    this.conditions = [this.default_condition];\n    this.fetchData();\n  },\n\n  methods: {\n    fetchData() {\n      var workflow_id = jQuery('#workflow_id').val();\n\n      if (!workflow_id) {\n        return;\n      }\n\n      this.workflow_edit_mode = true;\n      jQuery.get(ajaxurl + '?action=erp_wf_fetch_workflow&id=' + workflow_id + '&_wpnonce=' + nonces.fetch_workflow, function (response) {\n        if (response.success) {\n          this.workflow_name = response.data.name;\n          this.conditions_group = response.data.conditions_group;\n          this.events_group = response.data.events_group;\n          this.event = response.data.event;\n          this.delay_time = response.data.delay_time;\n          this.delay_period = response.data.delay_period;\n          this.sharedState.conditions_list = conditions_list_auto[this.event];\n\n          if (this.event) {\n            this.sharedState.actions_list = actions_list_auto[this.event];\n          }\n\n          if (response.data.conditions.length > 0) {\n            this.conditions = response.data.conditions;\n          }\n\n          this.actions = response.data.actions;\n        }\n      }.bind(this));\n    },\n\n    addNewCondition(e) {\n      var new_condition = Vue.util.extend({}, this.default_condition);\n      this.conditions.push(new_condition);\n    },\n\n    changeEventsGroup(value) {\n      this.events_list = events_list[this.events_group];\n      this.changeEvent();\n    },\n\n    changeEvent(value) {\n      this.sharedState.conditions_list = conditions_list_auto[this.event];\n\n      if (this.event) {\n        this.sharedState.actions_list = actions_list_auto[this.event];\n      }\n    },\n\n    removeCondition(condition) {\n      this.conditions.$remove(condition);\n    },\n\n    scrollTo(e) {\n      jQuery('html, body').animate({\n        scrollTop: jQuery(e.target).offset().top\n      }, 500);\n    },\n\n    saveToDatabase(activate) {\n      if (!this.workflow_name) {\n        this.errors.workflow_name = true;\n        return;\n      } else {\n        this.errors.workflow_name = false;\n      }\n\n      if (!this.event) {\n        this.errors.event = true;\n        return;\n      } else {\n        this.errors.event = false;\n      }\n\n      if (this.actions.length === 0) {\n        swal({\n          title: '',\n          text: this.sharedState.i18n.atleast_an_action,\n          type: 'error',\n          confirmButtonText: 'OK',\n          confirmButtonColor: '#008ec2'\n        }, function () {\n          return;\n        });\n        return;\n      }\n\n      var formData = {\n        action: 'erp_wf_new_workflow',\n        _wpnonce: this.nonce,\n        workflow_name: this.workflow_name,\n        conditions_group: this.conditions_group,\n        events_group: this.events_group,\n        event: this.event,\n        conditions: this.conditions,\n        actions: this.actions,\n        activate: activate,\n        delay_time: this.delay_time,\n        delay_period: this.delay_period\n      };\n\n      if (this.workflow_edit_mode) {\n        formData.action = 'erp_wf_edit_workflow';\n        formData.workflow_id = jQuery('#workflow_id').val();\n      }\n\n      var self = this;\n      var type = '';\n      var message = '';\n      jQuery.post(ajaxurl, formData, function (response) {\n        if (response.success) {\n          type = 'success';\n          message = self.sharedState.i18n.successfully_saved;\n        } else {\n          type = 'error';\n          message = response.data;\n        }\n\n        swal({\n          title: '',\n          text: message,\n          type: type,\n          confirmButtonText: 'OK',\n          confirmButtonColor: '#008ec2'\n        }, function () {\n          window.location.href = site_url + '/wp-admin/admin.php?page=erp-workflow';\n        });\n      });\n    }\n\n  },\n  watch: {\n    'events_group': function (val, oldVal) {\n      this.changeEventsGroup(val);\n\n      if (!this.workflow_edit_mode) {\n        this.event = '';\n      }\n    },\n    'event': function (val, oldVal) {\n      this.changeEvent(val);\n\n      if (this.event != '' && this.conditions.length > 0) {\n        this.is_display_actions = true;\n      } else {\n        this.is_display_actions = false;\n      }\n    },\n    'conditions': function (val, oldVal) {\n      if (this.event != '' && this.conditions.length > 0) {\n        this.is_display_actions = true;\n      } else {\n        this.is_display_actions = false;\n      }\n    }\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/main.js?");

/***/ }),

/***/ "./modules/hrm/workflow/assets/js/store.js":
/*!*************************************************!*\
  !*** ./modules/hrm/workflow/assets/js/store.js ***!
  \*************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  state: {\n    i18n: {},\n    conditions_list: [],\n    actions_list: []\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/workflow/assets/js/store.js?");

/***/ })

/******/ });