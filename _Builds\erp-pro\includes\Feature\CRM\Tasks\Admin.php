<?php
namespace WeDevs\WSL_ERP\Feature\CRM\Tasks;

use WeDevs\ERP\Framework\Traits\Hooker;

/**
 * Class responsible for admin panel functionalities
 *
 * @since 1.0.1
 */
class Admin {

    use <PERSON>;

    /**
     * Constructor for the class
     *
     * Sets up all the appropriate hooks and actions
     *
     * @since 1.0.1
     *
     * @return void
     */
    public function __construct() {

        //load ajax hooks
        if ( defined( 'DOING_AJAX' ) && DOING_AJAX ) {
            new Ajax();
        }

        // Admin hooks
        $this->action( 'admin_enqueue_scripts', 'admin_scripts' );
        $this->action( 'erp_crm_tasks', 'admin_view_tasks' );
        $this->filter( 'erp_crm_tasks_menu_items', 'add_menu_items' );
    }

    /**
     * Load admin scripts
     *
     * @since 1.0.1
     *
     * @param $hook_suffix
     */
    public function admin_scripts( $hook_suffix ) {
        $section     = isset( $_GET['section'] ) ? sanitize_text_field( wp_unslash( $_GET['section'] ) ) : '';
        $sub_section = isset( $_GET['sub-section'] ) ? sanitize_text_field( wp_unslash( $_GET['sub-section'] ) ) : 'tasks';

        if ( 'wp-erp_page_erp-crm' !== $hook_suffix || 'task' !== $section || 'tasks' !== $sub_section ) {
            return;
        }

        $erp_tasks_global = [
            'ajaxurl' => admin_url( 'admin-ajax.php' ),
            'nonce'   => wp_create_nonce( 'erp-crm-tasks' ),
            'i18n'    => $this->i18n()
        ];

        $style_deps = [ 'erp-styles', 'erp-timepicker', 'erp-fontawesome', 'erp-sweetalert', 'erp-nprogress', 'erp-trix-editor' ];

        $script_deps = [ 'jquery', 'erp-sweetalert', 'erp-nprogress', 'underscore' ];

        wp_enqueue_style( 'erp-daterangepicker' );
        wp_enqueue_script( 'erp-daterangepicker' );
        wp_enqueue_style( 'erp-crm-tasks', WSL_ERP_FEATURE_URL . '/CRM/Tasks/assets/css/crm-tasks.css', $style_deps, WSL_ERP_PLUGIN_VERSION );
        wp_enqueue_script( 'erp-crm-tasks', WSL_ERP_FEATURE_URL . '/CRM/Tasks/assets/js/crm-tasks.js', $script_deps, WSL_ERP_PLUGIN_VERSION, true );

        wp_localize_script( 'erp-crm-tasks', 'erpTasks', $erp_tasks_global );
    }

    /**
     * Add admin panel menu item
     *
     * @since 1.1.0
     *
     * @param array $items
     *
     * @return void
     */
    public function add_menu_items( $items ) {
        $dropdown = [ 'tasks' => esc_html__( 'Tasks', 'wsl-erp' ) ];

        return $dropdown + $items;
    }

    /**
     * Tasks Admin Page
     *
     * @since 1.0.1
     *
     * @return void
     */
    public function admin_view_tasks() {
        require_once WSL_ERP_FEATURE_DIR . '/CRM/Tasks/views/tasks-feed.php';
        require_once WSL_ERP_FEATURE_DIR . '/CRM/Tasks/views/task-single.php';
    }

    /**
     * i18n strings for main admin pages
     *
     * @since 1.0.1
     *
     * @return array
     */
    private function i18n() {
        return [
            'task'           => __( 'Task', 'wsl-erp' ),
            'done'           => __( 'Done', 'wsl-erp' ),
            'pending'        => __( 'Pending', 'wsl-erp' ),
            'due'            => __( 'Due', 'wsl-erp' ),
            'contact'        => __( 'Contact', 'wsl-erp' ),
            'status'         => __( 'Status', 'wsl-erp' ),
            'assignedTo'     => __( 'Assigned To', 'wsl-erp' ),
            'assignedBy'     => __( 'Assigned By', 'wsl-erp' ),
            'noTask'         => __( 'No task found.', 'wsl-erp' ),
            'filterStatus'   => __( 'Filter by Status', 'wsl-erp' ),
            'filterContact'  => __( 'Filter by Contact/Company', 'wsl-erp' ),
            'filterUser'     => __( 'Filter by Manager/Agent', 'wsl-erp' ),
            'markComplete'   => __( 'Mark Complete', 'wsl-erp' ),
            'markIncomplete' => __( 'Mark Incomplete', 'wsl-erp' ),
        ];
    }
}
