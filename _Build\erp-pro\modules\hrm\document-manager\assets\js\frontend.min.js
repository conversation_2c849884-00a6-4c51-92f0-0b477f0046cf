!function(e){var t={};function a(i){if(t[i])return t[i].exports;var s=t[i]={i:i,l:!1,exports:{}};return e[i].call(s.exports,s,s.exports,a),s.l=!0,s.exports}a.m=e,a.c=t,a.d=function(e,t,i){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(a.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)a.d(i,s,function(t){return e[t]}.bind(null,s));return i},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=791)}({1:function(e,t,a){"use strict";function i(e,t,a,i,s,o,r,n){var l,d="function"==typeof e?e.options:e;if(t&&(d.render=t,d.staticRenderFns=a,d._compiled=!0),i&&(d.functional=!0),o&&(d._scopeId="data-v-"+o),r?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),s&&s.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(r)},d._ssrRegister=l):s&&(l=n?function(){s.call(this,(d.functional?this.parent:this).$root.$options.shadowRoot)}:s),l)if(d.functional){d._injectStyles=l;var _=d.render;d.render=function(e,t){return l.call(t),_(e,t)}}else{var c=d.beforeCreate;d.beforeCreate=c?[].concat(c,l):[l]}return{exports:e,options:d}}a.d(t,"a",(function(){return i}))},246:function(e,t,a){},326:function(e,t,a){},632:function(e,t,a){"use strict";a(326)},633:function(e,t){window.erpHrHooks.addAction("addRouter","WPDOC",()=>{var e=[{id:1,name:"DocumentManagerIndexed",icon:"icon-document",text:window.erpHr.doc_manager.documents,text_domain:"erp-hr-frontend",is_cap:window.erpHrVue.userCan("erp_hr_manager")}];window.erpHrVue.$store.commit("setStateCommon",t=>{t.addOnNav=e})},10),window.erpHrHooks.addAction("addEmployeeRouter","WPDOC1",()=>{var e=[{id:1,name:"DocumentManagerIndexedEmployee",icon:"fa fa-folder-open",text:window.erpHr.doc_manager.documents,text_domain:"erp-hr-frontend",is_cap:window.erpHrVue.userCan("erp_hr_manager")||window.erpHrVue.isCurrentUserProfile()}];window.erpHrVue.$store.commit("setStateCommon",t=>{t.employeeAddOnNav=e})},10)},791:function(e,t,a){"use strict";a.r(t);var i={getComponenet:(e,t)=>t.filter((function(t){return t.path==e}))[0]},s=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"document-manager"},[t("router-view")],1)};s._withStripped=!0;var o={name:"DocumentManager",data:()=>({}),created(){},mounted(){},methods:{}},r=a(1),n=Object(r.a)(o,s,[],!1,null,"0c5cf9ac",null);n.options.__file="modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue";var l=n.exports,d=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",{class:"hr_frontend_dm "+e.single_employee_class_wrap},[a("div",{staticClass:"dm_container"},[a("div",{staticClass:"columns"},[a("div",{staticClass:"column"},[a("h1",{staticClass:"title"},[e._v("\n                    "+e._s(e.doc_str.documents)+"\n                ")])]),e._v(" "),a("div",{staticClass:"column"},[a("div",{staticClass:"field"},[a("p",{staticClass:"control has-icons-right"},[a("input",{directives:[{name:"model",rawName:"v-model",value:e.skey,expression:"skey"}],staticClass:"input",attrs:{type:"text",placeholder:e.doc_str.search},domProps:{value:e.skey},on:{keyup:e.search_dir,input:function(t){t.target.composing||(e.skey=t.target.value)}}}),e._v(" "),e._m(0)])])])]),e._v(" "),a("div",{staticClass:"columns"},[a("div",{staticClass:"column"},[a("div",{staticClass:"columns"},[a("div",{staticClass:"column operator"},[a("span",[e.can_prompt?a("a",{staticClass:"button light-black is-small",attrs:{href:"javascript:void(0)"},on:{click:function(t){return e.open_folder_create_popup()}}},[a("span",{staticClass:"fa fa-folder-open"}),e._v("   "+e._s(e.doc_str.create_folder))]):e._e(),e._v(" "),e.can_prompt?a("a",{staticClass:"button is-link is-small",attrs:{href:"javascript:void(0)"},on:{click:function(t){return e.get_file()}}},[a("span",{staticClass:"fa fa-upload"}),e._v("   "+e._s(e.doc_str.upload))]):e._e(),e._v(" "),a("form",{attrs:{enctype:"multipart/form-data"}},[a("input",{ref:"upload_file",staticStyle:{display:"none"},attrs:{type:"file",id:"upload_file",multiple:""},on:{change:function(t){return e.upload_file()}}})])]),e._v(" "),a("span",{style:"display:"+e.show_move_or_dlt},[e.can_move?a("button",{staticClass:"button is-small is-info",on:{click:function(t){return e.move_to()}}},[e._v(" "+e._s(e.doc_str.move)+" ")]):e._e(),e._v(" "),e.can_remove?a("button",{staticClass:"button is-small is-danger",on:{click:function(t){return e.delete_data()}}},[e._v(" "+e._s(e.doc_str.move_to_trash)+" ")]):e._e(),e._v(" "),e.can_share?a("button",{staticClass:"button is-small is-primary",on:{click:function(t){return e.share_data()}}},[e._v(" Share ")]):e._e()]),e._v(" "),a("br"),a("br"),a("br"),e._v(" "),e._l(e.breadcrumb,(function(t,i){return a("span",[a("a",{staticClass:"bc_single",attrs:{href:"javascript:void(0)"},domProps:{innerHTML:e._s(t.name)},on:{click:function(a){return e.bread_crumb_action(t,i)}}})])}))],2),e._v(" "),a("div",{staticClass:"column has-text-right"},[a("select",{directives:[{name:"model",rawName:"v-model",value:e.source,expression:"source"}],staticClass:"button is-light ash-white",attrs:{id:"source"},domProps:{innerHTML:e._s(e.doc_option_str)},on:{change:function(t){var a=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){return"_value"in e?e._value:e.value}));e.source=t.target.multiple?a:a[0]}}}),e._v(" "),a("a",{staticClass:"button is-light ash-white",attrs:{href:"javascript:void(0)"},on:{click:function(t){return e.view("list")}}},[a("span",{staticClass:"fa fa-bars"})]),e._v(" "),a("a",{staticClass:"button is-light ash-white",attrs:{href:"javascript:void(0)"},on:{click:function(t){return e.view("grid")}}},[a("span",{staticClass:"fa fa-th-large"})])])])])]),e._v(" "),a("div",{class:"columns "+e.loader},[a("div",{staticClass:"column"},[a("div",{staticClass:"dash-list"},[a("ul",{class:"list-header "+e.is_show},[a("li",[e._v("   ")]),e._v(" "),a("li",[e._v(" "+e._s(e.doc_str.file_name)+" ")]),e._v(" "),a("li",[e._v(" "+e._s(e.doc_str.modified)+" ")]),e._v(" "),a("li",[e._v(" "+e._s(e.doc_str.created_by)+" ")]),e._v(" "),a("li",[e._v(" "+e._s(e.doc_str.file_size)+" ")]),e._v(" "),a("li",[e._v("   ")])]),e._v(" "),a("div",{class:e.view_type_parent_class},[e._l(e.file_data,(function(t){return e.file_data.length>0?a("div",{class:e.view_type_child_class},[a("div",{class:e.view_type_child_content},[a("div",{staticClass:"has-checkbox list-table-cell"},[a("input",{staticClass:"is-checkbox",attrs:{disabled:"shared_with_me"==e.source,type:"checkbox",id:"doc_"+t.dir_id},domProps:{value:t.id},on:{click:function(a){return e.is_checked(t,a)}}}),e._v(" "),a("label",{attrs:{for:"doc_"+t.dir_id}})]),e._v(" "),a("div",{staticClass:"img-and-name list-table-cell"},[a("div",{staticClass:"align-part"},[1!=t.is_dir?[a("span",["image/png"==t.attachment_type?a("i",{staticClass:"fa fa-file-picture-o"}):"text/csv"==t.attachment_type?a("i",{staticClass:"fa fa-file-text-o"}):"application/zip"==t.attachment_type?a("i",{staticClass:"fa fa-file-zip-o"}):"text/plain"==t.attachment_type?a("i",{staticClass:"fa fa-file-text-o"}):"application/pdf"==t.attachment_type?a("i",{staticClass:"fa fa-file-pdf-o"}):"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"==t.attachment_type?a("i",{staticClass:"fa fa-file-excel-o"}):"application/vnd.openxmlformats-officedocument.wordprocessingml.document"==t.attachment_type?a("i",{staticClass:"fa fa-file-word-o"}):a("i",{staticClass:"fa fa-file"})]),e._v(" "),a("span",[a("a",{attrs:{href:t.attactment_url}},[e._v(" "+e._s(t.dir_file_name)+" ")])])]:e._e(),e._v(" "),1==t.is_dir?[e._m(1,!0),e._v(" "),a("span",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.jump_to_folder(t)}}},[e._v(" "+e._s(t.dir_file_name)+" ")])])]:e._e()],2)]),e._v(" "),a("div",{staticClass:"box-view list-table-cell"},[e._v(" "+e._s(t.updated_at)+" ")]),e._v(" "),a("div",{staticClass:"box-view list-table-cell"},[e._v(" "+e._s(t.user_nicename)+" ")]),e._v(" "),1==t.is_dir?a("div",{staticClass:"box-view list-table-cell"}):e._e(),e._v(" "),1!=t.is_dir?a("div",{staticClass:"box-view list-table-cell"},[e._v(" "+e._s(t.file_size)+" ")]):e._e(),e._v(" "),a("div",{staticClass:"box-view action list-table-cell"},[a("a",{staticClass:"doc_action",attrs:{href:"javascript:void(0)"}},[e._v("\n                                       ...\n                                        "),"owned_by_me"==e.source?[a("ul",{staticClass:"doc_action_dropdown"},[1!=t.is_dir?a("li",[a("a",{attrs:{target:"_blank",href:t.attactment_url,download:""}},[a("span",{staticClass:"fa fa-download"}),e._v(" "+e._s(e.doc_str.download))])]):e._e(),e._v(" "),a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.rename(t)}}},[a("span",{staticClass:"fa fa-pencil"}),e._v(" "+e._s(e.doc_str.rename))])]),e._v(" "),a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.delete_item(t)}}},[a("span",{staticClass:"fa fa-trash"}),e._v(" "+e._s(e.doc_str.delete))])])])]:e._e(),e._v(" "),"my_dropbox"==e.source?[a("ul",{staticClass:"doc_action_dropdown"},[1!=t.is_dir?a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.link_clicked(t)}}},[a("span",{staticClass:"fa fa-download"}),e._v(" "+e._s(e.doc_str.download))])]):e._e(),e._v(" "),a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.rename(t)}}},[a("span",{staticClass:"fa fa-pencil"}),e._v(" "+e._s(e.doc_str.rename))])]),e._v(" "),a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.delete_item(t)}}},[a("span",{staticClass:"fa fa-trash"}),e._v(" "+e._s(e.doc_str.delete))])])])]:e._e(),e._v(" "),"shared_with_me"==e.source?[1!=t.is_dir?a("ul",{staticClass:"doc_action_dropdown"},["local"==t.source?a("li",[a("a",{attrs:{target:"_blank",href:t.attactment_url,download:""}},[a("span",{staticClass:"fa fa-download"}),e._v(" "+e._s(e.doc_str.download))])]):e._e(),e._v(" "),"dropbox"==t.source?a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.link_clicked(t)}}},[a("span",{staticClass:"fa fa-download"}),e._v(" "+e._s(e.doc_str.download))])]):e._e()]):e._e()]:e._e()],2)])])]):e._e()})),e._v(" "),0==e.file_data.length?a("div",{class:e.view_type_child_class},[a("div",{staticClass:"list-body list-table-row"},[a("div",{staticClass:"box-view list-table-cell"},[e._m(2),e._v(" "),a("div",[e._v(e._s(e.doc_str.empty_list))])])])]):e._e()],2)])])])]),e._v(" "),a("div",{class:"modal "+e.create_folder_modal_open},[a("div",{staticClass:"modal-background"}),e._v(" "),a("div",{class:"md-cont modal-content "+e.create_folder_modal_open_clipped},[a("div",{staticClass:"columns"},[a("div",{staticClass:"column"},[a("div",{staticClass:"modal_title"},[e._v(" "+e._s(e.doc_str.create_folder)+" ")]),e._v(" "),a("div",{staticClass:"field"},[a("div",{staticClass:"control"},[a("input",{directives:[{name:"model",rawName:"v-model",value:e.dir_name,expression:"dir_name"}],staticClass:"input is-primary",attrs:{type:"text",placeholder:e.doc_str.plz_enter_folder_name},domProps:{value:e.dir_name},on:{keyup:e.create_dir_by_ekey,input:function(t){t.target.composing||(e.dir_name=t.target.value)}}})])]),e._v(" "),a("div",{staticClass:"control"},[a("button",{staticClass:"button is-primary",on:{click:function(t){return e.create_folder()}}},[e._v(e._s(e.doc_str.submit))])])])])]),e._v(" "),a("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(t){return e.open_folder_popup_close()}}})]),e._v(" "),a("div",{class:"modal "+e.move_to_modal_open},[a("div",{staticClass:"modal-background"}),e._v(" "),a("div",{class:"md-cont modal-content "+e.move_to_modal_open_clipped},[a("div",{staticClass:"columns"},[a("div",{staticClass:"column"},[a("div",{staticClass:"modal_title"},[e._v(" "+e._s(e.doc_str.move_folder_to)+" ")]),e._v(" "),a("div",{staticClass:"field"},[e.movable_folder.length>0?a("div",{staticClass:"folder_tree"},[a("ul",e._l(e.movable_folder,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")]),e._v(" "),t.children.length>0?a("ul",e._l(t.children,(function(t){return a("li",[a("a",{attrs:{href:"javascript:void(0)"},on:{click:function(a){return e.set_target_folder(t.id)}}},[a("i",{staticClass:"fa fa-folder-open"}),e._v(" "+e._s(t.text)+" ")])])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0):e._e()])})),0)]):a("div",{staticClass:"folder_tree"},[e._v(" There is no available folder right now. Please create one. ")])]),e._v(" "),a("div",{staticClass:"control"},[a("button",{staticClass:"button is-primary is-pulled-right",on:{click:function(t){return e.move()}}},[e._v(e._s(e.doc_str.move))])])])])]),e._v(" "),a("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(t){return e.move_to_popup_close()}}})]),e._v(" "),a("div",{class:"modal "+e.rename_folder_modal_open},[a("div",{staticClass:"modal-background"}),e._v(" "),a("div",{class:"md-cont modal-content "+e.rename_folder_modal_open_clipped},[a("div",{staticClass:"columns"},[a("div",{staticClass:"column"},[a("div",{staticClass:"modal_title"},[e._v(" "+e._s(e.doc_str.rename_folder_to)+" ")]),e._v(" "),a("div",{staticClass:"field"},[a("div",{staticClass:"control"},[a("input",{directives:[{name:"model",rawName:"v-model",value:e.rename_data.dirName,expression:"rename_data.dirName"}],staticClass:"input is-primary",attrs:{type:"text",placeholder:e.doc_str.plz_enter_folder_name},domProps:{value:e.rename_data.dirName},on:{keyup:e.enter_to_rename,input:function(t){t.target.composing||e.$set(e.rename_data,"dirName",t.target.value)}}})])]),e._v(" "),a("div",{staticClass:"control"},[a("button",{staticClass:"button is-primary",on:{click:function(t){return e.rename_folder()}}},[e._v(e._s(e.doc_str.submit))])])])])]),e._v(" "),a("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(t){return e.rename_folder_popup_close()}}})]),e._v(" "),a("div",{class:"modal "+e.share_to_modal_open},[a("div",{staticClass:"modal-background"}),e._v(" "),a("div",{class:"md-cont modal-content "+e.share_to_modal_open_clipped},[a("div",{staticClass:"columns"},[a("form",{attrs:{id:"erp-doc-share-template"}},[a("div",{staticClass:"column"},[a("div",{staticClass:"modal_title"},[e._v(" Share to ")]),e._v(" "),a("div",{staticClass:"field"},[e._m(3),e._v(" "),a("div",{staticClass:"control share_files_control by_employee no-show"},[a("div",{staticClass:"select is-multiple columns"},[a("label",{staticClass:"column"},[e._v("Selected Employees ")]),e._v(" "),a("select",{staticClass:"column",attrs:{id:"selected_emp[]",name:"selected_emp[]"}},e._l(e.employees,(function(t){return a("option",{domProps:{value:t.user_id}},[e._v(e._s(t.full_name))])})),0)])]),e._v(" "),a("div",{staticClass:"control share_files_control by_department no-show"},[a("div",{staticClass:"select is-multiple columns"},[a("label",{staticClass:"column"},[e._v(" Departments ")]),e._v(" "),a("select",{staticClass:"column",attrs:{id:"department",name:"department"}},[a("option",{attrs:{value:"-1"}},[e._v("All departments")]),e._v(" "),e._l(e.departments,(function(t){return a("option",{domProps:{value:t.id}},[e._v(e._s(t.title))])}))],2)])]),e._v(" "),a("div",{staticClass:"control share_files_control by_designation no-show"},[a("div",{staticClass:"select is-multiple columns"},[a("label",{staticClass:"column"},[e._v(" Designations ")]),e._v(" "),a("select",{staticClass:"column",attrs:{id:"designation",name:"designation"}},[a("option",{attrs:{value:"-1"}},[e._v("All designations")]),e._v(" "),e._l(e.designations,(function(t){return a("option",{domProps:{value:t.id}},[e._v(e._s(t.title))])}))],2)])])]),e._v(" "),a("input",{attrs:{type:"hidden",name:"owner_id"},domProps:{value:e.employee_id}}),e._v(" "),a("div",{staticClass:"control"},[a("button",{staticClass:"button is-primary",attrs:{type:"button"},on:{click:function(t){return e.share_dir_file()}}},[e._v("Share")])])])])])]),e._v(" "),a("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(t){return e.share_to_popup_close()}}})])])};d._withStripped=!0;var _={name:"DocumentManagerIndexed",props:["employee"],data(){return{http:erpHrVue.httpReq(),file_data:[],current_dir_id:0,create_folder_modal_open:"",create_folder_modal_open_clipped:"",rename_folder_modal_open:"",rename_folder_modal_open_clipped:"",move_to_modal_open:"",move_to_modal_open_clipped:"",share_to_modal_open:"",share_to_modal_open_clipped:"",dir_name:"",employee_id:this.$route.params.id?this.$route.params.id:window.erpHrVue.$store.state.user.user_id,upload_files:[],movable:{},move_or_dlt:!1,show_move_or_dlt:"none",movable_folder:[],target_folder:"",single_employee_class_wrap:"",skey:"",view_type:"list",view_type_parent_class:"list-table",view_type_child_class:"list-table-body",view_type_child_content:"list-body list-table-row",is_show:"show_header",breadcrumb:[{dir_id:0,name:'<span class="fa fa-home"></span>',data:{source:""}}],loader:"is-busy",rename_data:{dir_file_type:"",employee_id:"",parent_id:"",target_dir_id:"",dirName:""},doc_str:window.erpHr.doc_manager,doc_option_str:window.erpHr.doc_option_str,employees:[],departments:[],designations:[],source:"owned_by_me",can_upload:!0,can_prompt:!0,can_move:!0,can_remove:!0,can_share:!0}},created(){this.get_employee_dir_files(),this.get_employees(),this.get_departments(),this.get_designations(),this.create_dropbox_employee_folders()},mounted(){this.if_single_employee()},watch:{current_dir_id(e){let t=this;0==e&&t.get_dir_files_by_param(t.current_dir_id,t.employee_id,t.source,"","")},movable(e){},move_or_dlt(e){this.show_move_or_dlt=1==e?"inline-block":"none"},skey(e){},source(e){let t=this;"owned_by_me"==e&&(t.can_upload=!0,t.can_prompt=!0,t.can_move=!0,t.can_remove=!0,t.can_share=!0),"shared_with_me"==e&&(t.can_upload=!1,t.can_prompt=!1,t.can_move=!1,t.can_remove=!1,t.can_share=!1),"my_dropbox"==e&&(t.can_upload=!0,t.can_prompt=!0,t.can_move=!1,t.can_remove=!0,t.can_share=!0),t.employee_id=t.$route.params.id?t.$route.params.id:window.erpHrVue.$store.state.user.user_id,t.current_dir_id=0,t.breadcrumb.splice(1),t.get_employee_dir_files()}},methods:{get_employee_dir_files(){this.get_dir_files_by_param(this.current_dir_id,this.employee_id,this.source,"","")},get_dir_files_by_param(e,t,a,i,s){let o=this;o.loader="is-busy",o.file_data=[];let r=new FormData;r.append("action","wp-erp-rec-get-files-folders"),r.append("employee_id",t),r.append("dir_id",e),r.append("source",a),r.append("search_string",i),r.append("direct_link",s),o.http.post(window.site_url+"/wp-admin/admin-ajax.php",r).then((function(e){e.data.data.directory.forEach((e,t)=>{o.file_data.push(e)}),e.data.data.files.forEach((e,t)=>{o.file_data.push(e)}),o.loader="",o.movable={},o.move_or_dlt=!1}))},link_clicked:function(e){if("dropbox"==e.source){let t=new FormData;t.append("action","wp-erp-download-files-from-dropbox"),t.append("dir_id",e.dir_id),this.http.post(window.site_url+"/wp-admin/admin-ajax.php",t).then((function(e){!0===e.data.success&&(window.location=e.data.data)}))}},search_dir(e){let t=this;13===e.keyCode&&t.get_dir_files_by_param(t.current_dir_id,t.employee_id,t.source,t.skey,"")},create_folder(){let e=this;if(0==e.dir_name.length)return void erpHrVue.notifySuccess("You must provide a folder name");let t=new FormData;t.append("action","wp-erp-rec-createDir"),t.append("employee_id",e.employee_id),t.append("parent_id",e.current_dir_id),t.append("source",e.source),t.append("dirName",e.dir_name),e.http.post(window.site_url+"/wp-admin/admin-ajax.php",t).then((function(t){1==t.data.success?setTimeout((function(){e.dir_name="",e.get_employee_dir_files(),e.open_folder_popup_close(),erpHrVue.notifySuccess(t.data.data)}),1e3):erpHrVue.notifySuccess("Something went wrong. Please try again later.")}))},get_file(){document.getElementById("upload_file").click()},upload_file(){let e=this,t=0,a=this.$refs.upload_file.files;for(let i=0;i<a.length;i++){let s=new FormData;s.append("doc_attachment",a[i]),this.http.post(window.site_url+"/wp-admin/admin-ajax.php?action=file_dir_ajax_upload&_wpnonce="+window.file_upload_nonce+"&employee_id="+e.employee_id+"&parent_id="+e.current_dir_id+"&source="+e.source,s,{headers:{"Content-Type":"multipart/form-data"}}).then((function(i){t++,t==a.length&&(e.get_employee_dir_files(),erpHrVue.notifySuccess("File Uploaded Successfully"))}))}},delete_data(){let e=this,t=new FormData;t.append("action","wp-erp-rec-deleteDirFile"),t.append("parent_id",this.current_dir_id),t.append("employee_id",e.employee_id),t.append("source",e.source);let a=[];for(const[e,t]of Object.entries(this.movable))a.push(t.dir_id);t.append("selected_dir_file_id",JSON.stringify(a)),this.http.post(window.site_url+"/wp-admin/admin-ajax.php",t,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){e.get_employee_dir_files(),erpHrVue.notifySuccess("File deleted Successfully")}))},delete_item(e){let t=this,a=new FormData;a.append("action","wp-erp-rec-deleteDirFile"),a.append("parent_id",this.current_dir_id),a.append("employee_id",t.employee_id),a.append("source",t.source);let i=[];i.push(e.dir_id),a.append("selected_dir_file_id",JSON.stringify(i)),this.http.post(window.site_url+"/wp-admin/admin-ajax.php",a,{headers:{"Content-Type":"multipart/form-data"}}).then((function(e){t.get_employee_dir_files(),erpHrVue.notifySuccess("File deleted Successfully")}))},open_folder_create_popup(){this.create_folder_modal_open="is-active",this.create_folder_modal_open_clipped="is-clipped"},open_folder_popup_close(){this.create_folder_modal_open="",this.create_folder_modal_open_clipped=""},move_to_popup_close(){this.move_to_modal_open="",this.move_to_modal_open_clipped=""},jump_to_folder(e){let t;this.current_dir_id=e.dir_id,"local"==e.source&&(t="owned_by_me"),"dropbox"==e.source&&(t="my_dropbox"),this.get_dir_files_by_param(e.dir_id,e.eid,t,"","yes"),this.breadcrumb.push({dir_id:e.dir_id,name:e.dir_file_name,data:e})},bread_crumb_action(e,t){if(this.current_dir_id=e.dir_id,this.breadcrumb.splice(t+1),0!=e.dir_id){let t;"local"==e.data.source&&(t="owned_by_me"),"dropbox"==e.data.source&&(t="my_dropbox"),this.get_dir_files_by_param(e.data.dir_id,e.data.eid,t,"","yes")}},is_checked(e,t){t.target.checked?this.movable[t.target.id]=e:delete this.movable[t.target.id];let a=Object.keys(this.movable).length;this.move_or_dlt=a>0},move_to(){let e=this;this.http.get(window.site_url+"/wp-admin/admin-ajax.php?action=wp-erp-doc-loadParentNodes&employee_id="+e.employee_id).then((function(t){e.move_to_modal_open="is-active",e.create_folder_modal_open_clipped="is-clipped",e.movable_folder=t.data.children}))},set_target_folder(e){this.target_folder=e,jQuery(".folder_tree a").on("click",(function(){jQuery(".folder_tree a.current").removeClass("current"),jQuery(event.target).addClass("current")}))},move(){let e=this,t=new FormData;t.append("action","wp-erp-doc-moveNow"),t.append("parent_id",this.current_dir_id),t.append("new_parent_id",this.target_folder),t.append("employee_id",this.employee_id);let a=[];for(const[e,t]of Object.entries(this.movable))a.push(t.dir_id);t.append("selectedDirFile",JSON.stringify(a)),this.http.post(window.site_url+"/wp-admin/admin-ajax.php",t,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){e.get_employee_dir_files(),e.move_to_popup_close(),erpHrVue.notifySuccess("File moved Successfully")}))},if_single_employee(){this.employee&&(this.single_employee_class_wrap=" single_employee_class_wrap")},view(e){"list"==e&&(this.view_type_parent_class="list-table",this.view_type_child_class="list-table-body",this.view_type_child_content="list-body list-table-row",this.is_show="show_header"),"grid"==e&&(this.view_type_parent_class="columns",this.view_type_child_class="column is-3",this.view_type_child_content="grid-body",this.is_show="hide_header")},rename(e){this.rename_data.dir_file_type=e.is_dir?"folder":"file",this.rename_data.employee_id=e.eid,this.rename_data.parent_id=0,this.rename_data.target_dir_id=e.dir_id,this.rename_data.dirName=e.dir_file_name,this.rename_folder_modal_open="is-active",this.rename_folder_modal_open_clipped="is-clipped"},rename_folder_popup_close(){this.rename_data.dir_file_type="",this.rename_data.employee_id="",this.rename_data.parent_id="",this.rename_data.target_dir_id="",this.rename_data.dirName="",this.rename_folder_modal_open="",this.rename_folder_modal_open_clipped=""},rename_folder(){let e=this;this.http.get(window.site_url+"/wp-admin/admin-ajax.php?action=wp-erp-rec-renameDirFile&dir_file_type="+e.rename_data.dir_file_type+"&employee_id="+e.rename_data.employee_id+"&parent_id="+e.rename_data.parent_id+"&target_dir_id="+e.rename_data.target_dir_id+"&dirName="+e.rename_data.dirName+"&source="+e.source).then((function(t){erpHrVue.notifySuccess("Renamed Successfully"),e.rename_folder_popup_close(),e.get_employee_dir_files()}))},create_dir_by_ekey(e){let t=this;13===e.keyCode&&t.create_folder()},enter_to_rename(e){let t=this;13===e.keyCode&&t.rename_folder()},share_data(){this.share_to_modal_open="is-active",this.share_to_modal_open_clipped="is-clipped",jQuery("#share_by").change((function(e){jQuery(".share_files_control.no-show").css("display","none"),jQuery(".share_files_control."+e.target.value).css("display","block")}))},share_to_popup_close(){this.share_to_modal_open="",this.share_to_modal_open_clipped=""},get_employees(){let e=this;e.http.get("hrm/employees?per_page=100",{baseURL:window.site_url+"/wp-json/erp/v1",headers:{"X-WP-Nonce":window.rest_nonce}}).then((function(t){e.employees=t.data}))},get_departments(){let e=this;e.http.get("hrm/departments",{baseURL:window.site_url+"/wp-json/erp/v1",headers:{"X-WP-Nonce":window.rest_nonce}}).then((function(t){e.departments=t.data}))},get_designations(){let e=this;e.http.get("hrm/designations",{baseURL:window.site_url+"/wp-json/erp/v1",headers:{"X-WP-Nonce":window.rest_nonce}}).then((function(t){e.designations=t.data}))},share_dir_file(){let e=this,t=jQuery("#erp-doc-share-template").serialize(),a=[];for(const[e,t]of Object.entries(this.movable))a.push(t.dir_id);let i=new FormData;i.append("action","wp-erp-rec-share-files-folders"),i.append("formdata",t),i.append("shared_source",e.source),i.append("selectedDirFile",JSON.stringify(a)),i.append("file_data",JSON.stringify(e.file_data)),i.append("dir_data",JSON.stringify([])),this.http.post(window.site_url+"/wp-admin/admin-ajax.php",i,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){t.data.success&&(e.share_to_popup_close(),erpHrVue.notifySuccess(t.data.data))}))},create_dropbox_employee_folders(){let e=new FormData;e.append("action","wp-erp-sync-employees-dropbox"),e.append("sync","yes"),this.http.post(window.site_url+"/wp-admin/admin-ajax.php",e).then((function(e){}))}}},c=(a(632),Object(r.a)(_,d,[function(){var e=this.$createElement,t=this._self._c||e;return t("span",{staticClass:"icon is-small is-right"},[t("i",{staticClass:"fa fa-search"})])},function(){var e=this.$createElement,t=this._self._c||e;return t("span",[t("i",{staticClass:"fa fa-folder-open"})])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticStyle:{"font-size":"30px"}},[t("i",{staticClass:"fa fa-folder"})])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"control share_files_control"},[a("div",{staticClass:"select is-multiple columns"},[a("label",{staticClass:"column"},[e._v(" Shared by ")]),e._v(" "),a("select",{staticClass:"column",attrs:{id:"share_by",name:"share_by"}},[a("option",{attrs:{value:"all_employees"}},[e._v("All employees")]),e._v(" "),a("option",{attrs:{value:"by_department"}},[e._v("By department")]),e._v(" "),a("option",{attrs:{value:"by_designation"}},[e._v("By designation")]),e._v(" "),a("option",{attrs:{value:"by_employee"}},[e._v("By employee")])])])])}],!1,null,null,null));c.options.__file="modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue";var p=c.exports,u=window.erpHrVue.$router.options.routes,m=i.getComponenet("/employees",u),f=i.getComponenet(":id",m.children);var v=[{path:"/document_manager",component:l,children:[{path:"",name:"DocumentManagerIndexed",component:p}]},{path:"/employees",component:m.component,children:[{path:":id",component:f.component,children:[{path:"employee_documents",name:"DocumentManagerIndexedEmployee",component:p}]}]}];var h={state:{addOnNav:[],employeeAddOnNav:[]}};a(246),a(633);window._.merge(window.erpHrVue.$store.state,h.state),window.erpHrVue.$router.addRoutes(v)}});