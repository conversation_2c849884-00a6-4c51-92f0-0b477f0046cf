<?php
namespace WeDevs\WSL_ERP\PRO\AdvancedLeave;

// don't call the file directly
use WeDevs\AdvancedLeave\Accrual\Accrual;
use WeDevs\AdvancedLeave\Accrual\AccrualBgProcess;
use WeDevs\AdvancedLeave\Forward\Forward;
use WeDevs\AdvancedLeave\Forward\LeaveCarryForwardBgProcess;
use WeDevs\AdvancedLeave\Halfday\Halfday;
use WeDevs\AdvancedLeave\Multilevel\Multilevel;
use WeDevs\AdvancedLeave\Segregation\Segregation;
use WeDevs\AdvancedLeave\Unpaid\Unpaid;

if ( ! defined('ABSPATH') ) {
    exit;
}

/**
 * Leave Class
 */
final class Module {

    /**
     * Add-on Version
     *
     * @since 1.0.0
     * @var  string
     */
    public $version = '1.1.0';

    /**
     * @var
     *
     * @since 1.0.0
     */
    private static $instance = null;


    /**
     * Get instance
     *
     * @since 1.0.0
     *
     * @return object
     */
    public static function init() {
        if ( self::$instance === null ) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * Private constructor
     *
     * @since 1.0.0
     *
     * @return void
     */
    private function __construct() {
        // on activate plugin register hook
        add_action( 'WSL_ERP_activated_module_advanced_leave', array( $this, 'activate' ) );

        add_action( 'erp_hrm_loaded', array( $this, 'plugin_init' ) );
    }

    /**
     * On activation callback
     * @since 1.0.0
     */
    public function activate() {
        // nothing added here
    }

    /**
     * Initialize plugin
     *
     * @since 1.0.0
     */
    public function plugin_init() {
        // load files
        $this->include_files();

        // Initialize the action hooks
        $this->init_actions();

        // Initialize the filter hooks
        $this->init_filters();
    }

    /**
     * include files
     *
     * @since 1.0.0
     *
     * @return void
     */
    protected function include_files() {
        require_once WSL_ERP_MODULE_DIR . '/pro/advanced-leave/includes/common.php';
        new Accrual();
        new Forward();
        new Halfday();
        new Multilevel();
        new Unpaid();
        new Segregation();
        new AccrualBgProcess();
        new LeaveCarryForwardBgProcess();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    public function init_actions() {
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
    }

    /**
     * Initialize filters
     *
     * @since 1.0.0
     */
    public function init_filters() {
        add_filter( 'erp_settings_hr_leave_section_fields', array( $this, 'leave_settings_fields' ) );
    }

    /**
     * enqueue scripts
     *
     * @since 1.0.0
     *
     * @return void
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'pro-leave-css',
            WSL_ERP_MODULE_URL . '/pro/advanced-leave/assets/css/leave.css',
            array(),
            WSL_ERP_PLUGIN_VERSION
        );

        wp_enqueue_script(
            'pro-leave-js',
            WSL_ERP_MODULE_URL . '/pro/advanced-leave/assets/js/leave.js',
            array(),
            WSL_ERP_PLUGIN_VERSION,
            true
        );

        wp_localize_script( 'pro-leave-js', 'wpErpPro', array(
            'nonce'                => wp_create_nonce('wsl-erp-nonce'),
            'export'               => esc_html__( 'Export', 'wsl-erp' ),
            'calculate'            => esc_html__( 'Calculate', 'wsl-erp' ),
            'calculate_title'      => esc_html__( 'Calculate Unpaid Leaves', 'wsl-erp' ),
            'process'              => esc_html__( 'Process', 'wsl-erp' ),
            'forward_modal_title'  => esc_html__( 'Process Forward', 'wsl-erp' ),
            'forward_confirmation' => esc_html__( 'Please be careful, you can\'t undo this action.', 'wsl-erp' ),
            'select_employee'      => esc_html__( 'Please select an employee.', 'wsl-erp' ),
            'segregation_policy_error'    => esc_html__( 'Segregation value needs to be smaller than the policy value.', 'wsl-erp'),
            'segregation_negative_error'    => esc_html__( 'Segregation value can\'t be a negative number.', 'wsl-erp'),
            'req_forward_table'    => array(
                'approved_by'    => esc_html__( 'Approved By', 'wsl-erp' ),
                'date'           => esc_html__( 'Date', 'wsl-erp' ),
                'forward_status' => esc_html__( 'Forward Status', 'wsl-erp' ),
                'forward_to'     => esc_html__( 'Forward To', 'wsl-erp' ),
                'reason'         => esc_html__( 'Reason', 'wsl-erp' ),
            ),
        ) );
    }

    /**
     * leave settings fields
     *
     * @since 1.0.0
     *
     * @param $fields array
     *
     * @return array
     */
    public function leave_settings_fields($fields) {
        $fields['leave'][] = [
            'title' => esc_html__( 'Enable Sandwich Rule', 'wsl-erp' ),
            'type'  => 'checkbox',
            'id'    => 'WSL_ERP_sandwich_leave',
            'desc'  => esc_html__( 'Leave will be deducted for even weekly off days or holidays when in case applying for leaves on previous day to it and post that day.', 'wsl-erp' )
        ];

        return $fields;
    }
}
