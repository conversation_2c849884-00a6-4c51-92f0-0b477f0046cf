<?xml version="1.0" encoding="UTF-8"?>
<package
 version="2.0"
 xmlns="http://pear.php.net/dtd/package-2.0"
 xmlns:tasks="http://pear.php.net/dtd/tasks-1.0"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:schemaLocation="http://pear.php.net/dtd/tasks-1.0
  http://pear.php.net/dtd/tasks-1.0.xsd
  http://pear.php.net/dtd/package-2.0
  http://pear.php.net/dtd/package-2.0.xsd"
>
 <name>JsonMapper</name>
 <channel>pear.nrdev.de</channel>
 <summary>Map nested JSON structures onto PHP classes</summary>
 <description>Map nested JSON structures onto PHP classes</description>

 <lead>
  <name><PERSON></name>
  <user>cweiske</user>
  <email><EMAIL></email>
  <active>yes</active>
 </lead>

 <date>2015-08-14</date>
 <time>00:00:00</time>

 <version>
  <release>0.9.0</release>
  <api>0.9.0</api>
 </version>

 <stability>
  <release>alpha</release>
  <api>alpha</api>
 </stability>

 <license uri="http://opensource.org/licenses/osl-3.0">OSL-3.0</license>

 <notes>
  * Add case-insensitive property matching (issue #40)
  * Add option to disable map() parameter type enforcement (issue #37)
 </notes>

 <contents>
   <dir name="/">
     <file role="doc" name="README.rst" />

     <dir name="src">
       <file role="php" name="JsonMapper.php">
         <tasks:replace from="@version@" to="version" type="package-info" />
       </file>
       <dir name="JsonMapper">
         <file role="php" name="Exception.php" />
       </dir>
     </dir>

     <dir name="tests">
       <file role="test" name="bootstrap.php" />
       <file role="test" name="phpunit.xml" />
       <file role="test" name="JsonMapperTest.php" />
       <dir name="JsonMapperTest">
         <file role="test" name="Broken.php" />
         <file role="test" name="Logger.php" />
         <file role="test" name="PrivateWithSetter.php" />
         <file role="test" name="Simple.php" />
       </dir>
       <dir name="namespacetest">
         <file role="test" name="NamespaceTest.php" />
         <file role="test" name="Unit.php" />
         <file role="test" name="UnitData.php" />
         <dir name="model">
          <file role="test" name="User.php" />
          <file role="test" name="UserList.php" />
         </dir>
       </dir>
       <dir name="othernamespace">
         <file role="test" name="Foo.php" />
       </dir>
     </dir>
   </dir>
 </contents>

 <dependencies>
  <required>
   <php>
    <min>5.3.0</min>
   </php>
   <pearinstaller>
    <min>1.9.1</min>
   </pearinstaller>
  </required>
 </dependencies>

 <phprelease>
  <filelist>
   <install name="src/JsonMapper.php"
            as="JsonMapper.php" />
   <install name="src/JsonMapper/Exception.php"
            as="JsonMapper/Exception.php" />
  </filelist>
 </phprelease>

 <changelog>

  <release>
   <version>
    <release>0.9.0</release>
    <api>0.9.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2015-08-14</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Add case-insensitive property matching (issue #40)
     * Add option to disable map() parameter type enforcement (issue #37)
   </notes>
  </release>

  <release>
   <version>
    <release>0.8.0</release>
    <api>0.8.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2015-07-06</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Add support for seting objects directly if they have the correct type already by @radmen
     * Throw exception when a non-object is passed to map()
   </notes>
  </release>

  <release>
   <version>
    <release>0.7.0</release>
    <api>0.7.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2015-06-19</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Support "mixed" variable type (issue #33)
   </notes>
  </release>

  <release>
   <version>
    <release>0.6.1</release>
    <api>0.6.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2015-05-28</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Fix namespace error with setter type hints
   </notes>
  </release>

  <release>
   <version>
    <release>0.6.0</release>
    <api>0.6.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2015-04-09</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
    * Prefer setter methods over directy property access
    * Change setter method name calculation for properties
      with _ underscores by @msankhala
   </notes>
  </release>

  <release>
   <version>
    <release>0.5.0</release>
    <api>0.3.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2015-03-18</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Add support for nullable types (int|null) by @barryvdh
     * Increase test coverage to 100%
     * Fix float value detection by @sonicgd
   </notes>
  </release>

  <release>
   <version>
    <release>0.4.4</release>
    <api>0.3.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2015-01-08</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Fix bug #23: handle empty variable types
     * Fix bug #24: Namespaced ArrayObject class with namespaced value type does not work
   </notes>
  </release>

  <release>
   <version>
    <release>0.4.3</release>
    <api>0.3.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-12-17</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Change license from AGPL 3.0 to OSL 3.0
   </notes>
  </release>

  <release>
   <version>
    <release>0.4.2</release>
    <api>0.3.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-12-05</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Fix array mapping when value is NULL by @darkgaro
   </notes>
  </release>

  <release>
   <version>
    <release>0.4.1</release>
    <api>0.3.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-11-04</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
    * Fix handling of private properties with public setters
    * Fix handling of simple array types in namespaced files
   </notes>
  </release>

  <release>
   <version>
    <release>0.4.0</release>
    <api>0.3.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-08-20</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Incorporate performance tweaks from @Jalle19
   </notes>
  </release>

  <release>
   <version>
    <release>0.3.0</release>
    <api>0.3.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-06-11</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Optional exceptions for missing or undefined data
   </notes>
  </release>

  <release>
   <version>
    <release>0.2.1</release>
    <api>0.2.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-05-16</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Handle NULL values when mapping simple data types onto objects
   </notes>
  </release>

  <release>
   <version>
    <release>0.2.0</release>
    <api>0.2.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-05-15</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     * Add support for mapping simple data types onto objects
     * Fix tests on phpunit 4.x
   </notes>
  </release>

  <release>
   <version>
    <release>0.1.3</release>
    <api>0.1.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-03-17</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
    * Prevent autoloading classes with ] in its name
   </notes>
  </release>

  <release>
   <version>
    <release>0.1.2</release>
    <api>0.1.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-02-03</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
    * Fix issue #2: Namespace is prepended two times
    * Fix issue #1: Remove declare(encoding="UTF-8") calls
   </notes>
  </release>

  <release>
   <version>
    <release>0.1.1</release>
    <api>0.1.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-01-28</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     Properly resolve namespace for array subtypes
   </notes>
  </release>

  <release>
   <version>
    <release>0.1.0</release>
    <api>0.1.0</api>
   </version>
   <stability>
    <release>alpha</release>
    <api>alpha</api>
   </stability>
   <date>2014-01-28</date>
   <license uri="http://www.gnu.org/licenses/agpl.html">AGPL</license>
   <notes>
     First release
   </notes>
  </release>

 </changelog>
</package>
