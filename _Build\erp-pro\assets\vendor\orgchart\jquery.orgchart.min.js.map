{"version": 3, "sources": ["jquery.orgchart.js"], "names": ["factory", "module", "exports", "require", "window", "document", "j<PERSON><PERSON><PERSON>", "$", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "elem", "opts", "this", "$chartContainer", "defaultOptions", "nodeTitle", "nodeId", "toggleSiblingsResp", "visibleLevel", "chartClass", "exportButton", "exportButtonName", "exportFilename", "exportFileextension", "parentNodeSymbol", "draggable", "direction", "pan", "zoom", "zoominLimit", "zoomoutLimit", "prototype", "init", "that", "options", "extend", "$chart", "remove", "data", "class", "click", "event", "target", "closest", "length", "find", "removeClass", "MutationObserver", "triggerInitEvent", "$root", "append", "type", "buildHierarchy", "buildJsonDS", "children", "ajaxURL", "attachRel", "ajax", "url", "dataType", "done", "textStatus", "jqXHR", "fail", "errorThrown", "console", "log", "always", "attachExportButton", "bind<PERSON>an", "bindZoom", "mo", "mutations", "disconnect", "initTime", "i", "j", "addedNodes", "classList", "contains", "initCompleted", "initEvent", "Event", "trigger", "observe", "childList", "triggerLoadEvent", "$target", "rel", "triggerShowEvent", "triggerHideEvent", "$exportBtn", "text", "e", "preventDefault", "export", "after", "setOptions", "val", "unbind<PERSON>an", "unbind<PERSON>oom", "panStartHandler", "<PERSON><PERSON><PERSON><PERSON>", "touches", "css", "lastX", "lastY", "lastTf", "temp", "split", "indexOf", "parseInt", "startX", "startY", "targetTouches", "pageX", "pageY", "on", "newX", "newY", "matrix", "join", "panEndHandler", "chart", "off", "zoomWheelHandler", "oc", "newScale", "originalEvent", "deltaY", "setChartScale", "zoomStartHandler", "dist", "getPinchDist", "zoo<PERSON><PERSON><PERSON><PERSON>", "zoomEndHandler", "diff", "Math", "sqrt", "clientX", "clientY", "targetScale", "abs", "parseFloat", "$li", "subObj", "name", "contents", "eq", "trim", "relationship", "parent", "is", "siblings", "each", "key", "value", "push", "flags", "for<PERSON>ach", "item", "<PERSON><PERSON><PERSON>", "includeNodeData", "$node", "id", "getHierarchy", "valid", "getNodeState", "relation", "isVerticalNode", "parents", "exist", "visible", "getRelatedNodes", "hideParentEnd", "addClass", "hideParent", "$parent", "hideSiblings", "one", "showParentEnd", "node", "isInAction", "switchVerticalArrow", "showParent", "repaint", "bind", "stopAjax", "$nodeLevel", "isVisibleNode", "index", "hideChildrenEnd", "animatedNodes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$lowerLevel", "$animatedNodes", "filter", "get", "lowerLevel", "showChildrenEnd", "showChildren", "$levels", "isVerticalDesc", "hideSiblingsEnd", "$nodeContainer", "nodeContainer", "$siblings", "prevAll", "nextAll", "end", "switchHorizontalArrow", "showSiblingsEnd", "visibleNodes", "showRelatedParentEnd", "showSiblings", "$upperLevel", "$visibleNodes", "startLoading", "$edge", "not", "prop", "endLoading", "removeAttr", "attr", "$arrow", "toggleClass", "$prevSib", "prev", "$nextSib", "next", "$sibs", "sibsVisible", "style", "offsetWidth", "nodeEnterLeaveHandler", "flag", "$toggleBtn", "$topEdge", "$bottomEdge", "$leftEdge", "nodeClickHandler", "loadNodes", "isEmptyObject", "addParent", "add<PERSON><PERSON><PERSON><PERSON>", "addSiblings", "HideFirstParentEnd", "topEdge", "topEdgeClickHandler", "stopPropagation", "parentState", "isFunction", "bottomEdgeClickHandler", "childrenState", "hEdgeClickHandler", "$hEdge", "siblingsState", "families", "expandVNodesEnd", "vNodes", "collapseVNodesEnd", "toggleVNodes", "$descWrapper", "$descendants", "$children", "createGhostNode", "ghostNode", "nodeCover", "$nodeDiv", "origEvent", "isFirefox", "test", "navigator", "userAgent", "toLowerCase", "querySelector", "createElementNS", "add", "append<PERSON><PERSON><PERSON>", "transValues", "isHorizontal", "scale", "slice", "setAttribute", "outerWidth", "outerHeight", "xOffset", "offsetX", "yOffset", "offsetY", "ghostNodeWrapper", "createElement", "src", "XMLSerializer", "serializeToString", "dataTransfer", "setDragImage", "filterAllowedDropNodes", "$dragged", "draggingNode", "hasClass", "$dragZone", "$dragHier", "dropCriteria", "dragstartHandler", "setData", "dragover<PERSON><PERSON><PERSON>", "dropEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "$dropZone", "dropEvent", "draggedNode", "dragZone", "dropZone", "isDefaultPrevented", "horizontalEdges", "$dropSibs", "prepend", "<PERSON><PERSON><PERSON><PERSON>", "draggedItem", "touchstartHandler", "touchHandled", "touchMoved", "touchmoveHandler", "currentTarget", "touchDragImage", "createDragImage", "moveDragImage", "$touchingNodes", "elementFromPoint", "touchingNodeElement", "touchTargetNode", "touchendHandler", "destroyDragImage", "fakeEventForDropHandler", "firstTouch", "changedTouches", "fakeMouseClickEvent", "createEvent", "initMouseEvent", "screenX", "screenY", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "dispatchEvent", "source", "dragImage", "cloneNode", "copyStyle", "top", "left", "sourceRectangle", "getBoundingClientRect", "sourcePoint", "getTouchPoint", "touchDragImageOffset", "x", "y", "opacity", "body", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "dst", "att", "removeAttribute", "HTMLCanvasElement", "cSrc", "cDst", "width", "height", "getContext", "drawImage", "cs", "getComputedStyle", "pointerEvents", "image", "orgChartMaster", "requestAnimationFrame", "pt", "s", "position", "zIndex", "round", "bindDragDrop", "createNode", "level", "child", "parentId", "className", "nodeTemplate", "nodeContent", "nodeData", "verticalLevel", "Number", "substr", "$appendTo", "parentsUntil", "Object", "keys", "$nodesLayer", "isHidden", "collapsed", "$nodeCell", "buildChildNode", "buildParentNode", "$currentRoot", "$newRootWrapper", "buildSiblingNode", "$nodeChart", "newSiblingCount", "isArray", "existingSibligCount", "siblingCount", "insertPostion", "floor", "before", "addBack", "unwrap", "removeNodes", "$wrapper", "hideDropZones", "showDropZones", "dragged", "processExternalDrop", "exportPDF", "canvas", "doc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "jsPDF", "jspdf", "orientation", "unit", "format", "addImage", "toDataURL", "save", "exportPNG", "isWebkit", "documentElement", "isFf", "sidebar", "isEdge", "appName", "appVersion", "msSaveBlob", "msToBlob", "selector", "$mask", "sourceChart", "html2canvas", "clientHeight", "clientWidth", "onclone", "cloneDoc", "then", "fn", "orgchart"], "mappings": "AAUA,cAEC,SAAUA,GACa,iBAAXC,QAAiD,iBAAnBA,OAAOC,QAC9CF,EAAQG,QAAQ,UAAWC,OAAQC,UAEnCL,EAAQM,OAAQF,OAAQC,UAJ5B,CAME,SAAUE,EAAGH,EAAQC,EAAUG,GAChB,SAAXC,EAAqBC,EAAMC,GAC7BC,KAAKC,gBAAkBN,EAAEG,GACzBE,KAAKD,KAAOA,EACZC,KAAKE,eAAiB,CACpBC,UAAa,OACbC,OAAU,KACVC,oBAAsB,EACtBC,aAAgB,IAChBC,WAAc,GACdC,cAAgB,EAChBC,iBAAoB,SACpBC,eAAkB,WAClBC,oBAAuB,MACvBC,iBAAoB,aACpBC,WAAa,EACbC,UAAa,MACbC,KAAO,EACPC,MAAQ,EACRC,YAAe,EACfC,aAAgB,IAIpBrB,EAASsB,UAAY,CAEnBC,KAAM,SAAUrB,GACd,IAAIsB,EAAOrB,KACXA,KAAKsB,QAAU3B,EAAE4B,OAAO,GAAIvB,KAAKE,eAAgBF,KAAKD,KAAMA,GAE5D,IAAIE,EAAkBD,KAAKC,gBACvBD,KAAKwB,QACPxB,KAAKwB,OAAOC,SAEd,IAAIC,EAAO1B,KAAKsB,QAAQI,KACpBF,EAASxB,KAAKwB,OAAS7B,EAAE,QAAS,CACpC+B,KAAQ,CAAEJ,QAAWtB,KAAKsB,SAC1BK,MAAS,YAA0C,KAA5B3B,KAAKsB,QAAQf,WAAoB,IAAMP,KAAKsB,QAAQf,WAAa,KAAkC,QAA3BP,KAAKsB,QAAQR,UAAsB,IAAMd,KAAKsB,QAAQR,UAAY,IACjKc,MAAS,SAASC,GACXlC,EAAEkC,EAAMC,QAAQC,QAAQ,SAASC,QACpCR,EAAOS,KAAK,iBAAiBC,YAAY,cAIf,oBAArBC,kBACTnC,KAAKoC,mBAEP,IAAIC,EAAQb,EAAOc,OAAO3C,EAAE,uDAAuDsC,KAAK,cAsCxF,MArCqB,WAAjBtC,EAAE4C,KAAKb,GACLA,aAAgB/B,EAClBK,KAAKwC,eAAeH,EAAOrC,KAAKyC,YAAYf,EAAKgB,YAAa,EAAG1C,KAAKsB,SAEtEtB,KAAKwC,eAAeH,EAAOrC,KAAKsB,QAAQqB,QAAUjB,EAAO1B,KAAK4C,UAAUlB,EAAM,QAGhFF,EAAOc,OAAO,2CACd3C,EAAEkD,KAAK,CACLC,IAAOpB,EACPqB,SAAY,SAEbC,KAAK,SAAStB,EAAMuB,EAAYC,GAC/B7B,EAAKmB,eAAeH,EAAOhB,EAAKC,QAAQqB,QAAUjB,EAAOL,EAAKuB,UAAUlB,EAAM,MAAO,EAAGL,EAAKC,WAE9F6B,KAAK,SAASD,EAAOD,EAAYG,GAChCC,QAAQC,IAAIF,KAEbG,OAAO,WACN/B,EAAOkB,SAAS,YAAYjB,YAGhCxB,EAAgBqC,OAAOd,GAGnBxB,KAAKsB,QAAQd,eAAiBb,EAAE,kBAAkBqC,QACpDhC,KAAKwD,qBAGHxD,KAAKsB,QAAQP,KACff,KAAKyD,UAGHzD,KAAKsB,QAAQN,MACfhB,KAAK0D,WAGA1D,MAGToC,iBAAkB,WAChB,IAAIf,EAAOrB,KACP2D,EAAK,IAAIxB,iBAAiB,SAAUyB,GACtCD,EAAGE,aACHC,EACA,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAU5B,OAAQ+B,IACpC,IAAK,IAAIC,EAAI,EAAGA,EAAIJ,EAAUG,GAAGE,WAAWjC,OAAQgC,IAClD,GAAIJ,EAAUG,GAAGE,WAAWD,GAAGE,UAAUC,SAAS,YAAa,CACzD9C,EAAKC,QAAQ8C,eAAuD,mBAA/B/C,EAAKC,QAAQ8C,eACpD/C,EAAKC,QAAQ8C,cAAc/C,EAAKG,QAElC,IAAI6C,EAAY1E,EAAE2E,MAAM,iBACxBjD,EAAKG,OAAO+C,QAAQF,GACpB,MAAMP,KAKdH,EAAGa,QAAQxE,KAAKC,gBAAgB,GAAI,CAAEwE,WAAW,KAEnDC,iBAAkB,SAAUC,EAASC,GACnC,IAAIP,EAAY1E,EAAE2E,MAAM,QAAUM,EAAK,aACvCD,EAAQJ,QAAQF,IAElBQ,iBAAkB,SAAUF,EAASC,GACnC,IAAIP,EAAY1E,EAAE2E,MAAM,QAAUM,EAAM,aACxCD,EAAQJ,QAAQF,IAElBS,iBAAkB,SAAUH,EAASC,GACnC,IAAIP,EAAY1E,EAAE2E,MAAM,QAAUM,EAAM,aACxCD,EAAQJ,QAAQF,IAGlBb,mBAAoB,WAClB,IAAInC,EAAOrB,KACP+E,EAAapF,EAAE,WAAY,CAC7BgC,MAAS,gBACTqD,KAAQhF,KAAKsB,QAAQb,iBACrBmB,MAAS,SAASqD,GAChBA,EAAEC,iBACF7D,EAAK8D,YAGTnF,KAAKC,gBAAgBmF,MAAML,IAE7BM,WAAY,SAAUtF,EAAMuF,GAsC1B,MArCoB,iBAATvF,IACI,QAATA,IACEuF,EACFtF,KAAKyD,UAELzD,KAAKuF,aAGI,SAATxF,IACEuF,EACFtF,KAAK0D,WAEL1D,KAAKwF,eAIS,iBAATzF,IACLA,EAAK2B,KACP1B,KAAKoB,KAAKrB,SAEc,IAAbA,EAAKgB,MACVhB,EAAKgB,IACPf,KAAKyD,UAELzD,KAAKuF,kBAGgB,IAAdxF,EAAKiB,OACVjB,EAAKiB,KACPhB,KAAK0D,WAEL1D,KAAKwF,gBAMNxF,MAGTyF,gBAAiB,SAAUR,GACzB,IAAIzD,EAAS7B,EAAEsF,EAAES,gBACjB,GAAI/F,EAAEsF,EAAEnD,QAAQC,QAAQ,SAASC,QAAWiD,EAAEU,SAA8B,EAAnBV,EAAEU,QAAQ3D,OACjER,EAAOE,KAAK,WAAW,OADzB,CAIEF,EAAOoE,IAAI,SAAU,QAAQlE,KAAK,WAAW,GAE/C,IAAImE,EAAQ,EACRC,EAAQ,EACRC,EAASvE,EAAOoE,IAAI,aACxB,GAAe,SAAXG,EAAmB,CACrB,IAAIC,EAAOD,EAAOE,MAAM,KAGtBH,GAF4B,IAA1BC,EAAOG,QAAQ,OACjBL,EAAQM,SAASH,EAAK,IACdG,SAASH,EAAK,MAEtBH,EAAQM,SAASH,EAAK,KACdG,SAASH,EAAK,MAG1B,IAAII,EAAS,EACTC,EAAS,EACb,GAAKpB,EAAEqB,eAGA,GAA+B,IAA3BrB,EAAEqB,cAActE,OACzBoE,EAASnB,EAAEqB,cAAc,GAAGC,MAAQV,EACpCQ,EAASpB,EAAEqB,cAAc,GAAGE,MAAQV,OAC/B,GAA6B,EAAzBb,EAAEqB,cAActE,OACzB,YANAoE,EAASnB,EAAEsB,MAAQV,EACnBQ,EAASpB,EAAEuB,MAAQV,EAOrBtE,EAAOiF,GAAG,sBAAsB,SAASxB,GACvC,GAAKzD,EAAOE,KAAK,WAAjB,CAGA,IAAIgF,EAAO,EACPC,EAAO,EACX,GAAK1B,EAAEqB,eAGA,GAA+B,IAA3BrB,EAAEqB,cAActE,OACzB0E,EAAOzB,EAAEqB,cAAc,GAAGC,MAAQH,EAClCO,EAAO1B,EAAEqB,cAAc,GAAGE,MAAQH,OAC7B,GAA6B,EAAzBpB,EAAEqB,cAActE,OACzB,YANA0E,EAAOzB,EAAEsB,MAAQH,EACjBO,EAAO1B,EAAEuB,MAAQH,EAOnB,IAAIN,EAASvE,EAAOoE,IAAI,aACxB,GAAe,SAAXG,GAC4B,IAA1BA,EAAOG,QAAQ,MACjB1E,EAAOoE,IAAI,YAAa,sBAAwBc,EAAO,KAAOC,EAAO,KAErEnF,EAAOoE,IAAI,YAAa,gDAAkDc,EAAO,KAAOC,EAAO,eAE5F,CACL,IAAIC,EAASb,EAAOE,MAAM,MACI,IAA1BF,EAAOG,QAAQ,OACjBU,EAAO,GAAK,IAAMF,EAClBE,EAAO,GAAK,IAAMD,EAAO,MAEzBC,EAAO,IAAM,IAAMF,EACnBE,EAAO,IAAM,IAAMD,GAErBnF,EAAOoE,IAAI,YAAagB,EAAOC,KAAK,YAK1CC,cAAe,SAAU7B,GACnBA,EAAEvD,KAAKqF,MAAMrF,KAAK,YACpBuD,EAAEvD,KAAKqF,MAAMrF,KAAK,WAAW,GAAOkE,IAAI,SAAU,WAAWoB,IAAI,cAIrEvD,QAAS,WACPzD,KAAKC,gBAAgB2F,IAAI,WAAY,UACrC5F,KAAKwB,OAAOiF,GAAG,uBAAwBzG,KAAKyF,iBAC5C9F,EAAEF,GAAUgH,GAAG,mBAAoB,CAAEM,MAAS/G,KAAKwB,QAAUxB,KAAK8G,gBAGpEvB,UAAW,WACTvF,KAAKC,gBAAgB2F,IAAI,WAAY,QACrC5F,KAAKwB,OAAOwF,IAAI,uBAAwBhH,KAAKyF,iBAC7C9F,EAAEF,GAAUuH,IAAI,mBAAoBhH,KAAK8G,gBAG3CG,iBAAkB,SAAUhC,GAC1B,IAAIiC,EAAKjC,EAAEvD,KAAKwF,GAChBjC,EAAEC,iBACF,IAAIiC,EAAY,GAA8B,EAAzBlC,EAAEmC,cAAcC,QAAc,GAAM,IACzDH,EAAGI,cAAcJ,EAAG1F,OAAQ2F,IAG9BI,iBAAkB,SAAUtC,GAC1B,GAAGA,EAAEU,SAAgC,IAArBV,EAAEU,QAAQ3D,OAAc,CACtC,IAAIkF,EAAKjC,EAAEvD,KAAKwF,GAChBA,EAAG1F,OAAOE,KAAK,YAAY,GAC3B,IAAI8F,EAAON,EAAGO,aAAaxC,GAC3BiC,EAAG1F,OAAOE,KAAK,iBAAkB8F,KAGrCE,eAAgB,SAAUzC,GACxB,IAAIiC,EAAKjC,EAAEvD,KAAKwF,GAChB,GAAGA,EAAG1F,OAAOE,KAAK,YAAa,CAC7B,IAAI8F,EAAON,EAAGO,aAAaxC,GAC3BiC,EAAG1F,OAAOE,KAAK,eAAgB8F,KAGnCG,eAAgB,SAAU1C,GACxB,IAAIiC,EAAKjC,EAAEvD,KAAKwF,GAChB,GAAGA,EAAG1F,OAAOE,KAAK,YAAa,CAC7BwF,EAAG1F,OAAOE,KAAK,YAAY,GAC3B,IAAIkG,EAAOV,EAAG1F,OAAOE,KAAK,gBAAkBwF,EAAG1F,OAAOE,KAAK,kBAChD,EAAPkG,EACFV,EAAGI,cAAcJ,EAAG1F,OAAQ,KACnBoG,EAAO,GAChBV,EAAGI,cAAcJ,EAAG1F,OAAQ,MAKlCkC,SAAU,WACR1D,KAAKC,gBAAgBwG,GAAG,QAAS,CAAES,GAAMlH,MAAQA,KAAKiH,kBACtDjH,KAAKC,gBAAgBwG,GAAG,aAAc,CAAES,GAAMlH,MAAQA,KAAKuH,kBAC3D5H,EAAEF,GAAUgH,GAAG,YAAa,CAAES,GAAMlH,MAAQA,KAAK0H,gBACjD/H,EAAEF,GAAUgH,GAAG,WAAY,CAAES,GAAMlH,MAAQA,KAAK2H,iBAElDnC,WAAY,WACVxF,KAAKC,gBAAgB+G,IAAI,QAAShH,KAAKiH,kBACvCjH,KAAKC,gBAAgB+G,IAAI,aAAchH,KAAKuH,kBAC5C5H,EAAEF,GAAUuH,IAAI,YAAahH,KAAK0H,gBAClC/H,EAAEF,GAAUuH,IAAI,WAAYhH,KAAK2H,iBAGnCF,aAAc,SAAUxC,GACtB,OAAO4C,KAAKC,MAAM7C,EAAEU,QAAQ,GAAGoC,QAAU9C,EAAEU,QAAQ,GAAGoC,UAAY9C,EAAEU,QAAQ,GAAGoC,QAAU9C,EAAEU,QAAQ,GAAGoC,UACrG9C,EAAEU,QAAQ,GAAGqC,QAAU/C,EAAEU,QAAQ,GAAGqC,UAAY/C,EAAEU,QAAQ,GAAGqC,QAAU/C,EAAEU,QAAQ,GAAGqC,WAGvFV,cAAe,SAAU9F,EAAQ2F,GAC/B,IAAIpH,EAAOyB,EAAOE,KAAK,WACnBqE,EAASvE,EAAOoE,IAAI,aACpBgB,EAAS,GACTqB,EAAc,EACH,SAAXlC,EACFvE,EAAOoE,IAAI,YAAa,SAAWuB,EAAW,IAAMA,EAAW,MAE/DP,EAASb,EAAOE,MAAM,MACQ,IAA1BF,EAAOG,QAAQ,OACjB+B,EAAcJ,KAAKK,IAAI1I,EAAO2I,WAAWvB,EAAO,IAAMO,IACpCpH,EAAKmB,cAAgB+G,EAAclI,EAAKkB,aACxDO,EAAOoE,IAAI,YAAaG,EAAS,UAAYoB,EAAW,IAAMA,EAAW,MAG3Ec,EAAcJ,KAAKK,IAAI1I,EAAO2I,WAAWvB,EAAO,IAAMO,IACpCpH,EAAKmB,cAAgB+G,EAAclI,EAAKkB,aACxDO,EAAOoE,IAAI,YAAaG,EAAS,YAAcoB,EAAW,IAAMA,EAAW,UAMnF1E,YAAa,SAAU2F,GACrB,IAAI/G,EAAOrB,KACPqI,EAAS,CACXC,KAAQF,EAAIG,WAAWC,GAAG,GAAGxD,OAAOyD,OACpCC,cAAiBN,EAAIO,SAASA,SAASC,GAAG,MAAQ,IAAK,MAAQR,EAAIS,SAAS,MAAM7G,OAAS,EAAG,IAAMoG,EAAI1F,SAAS,MAAMV,OAAS,EAAI,IAStI,OAPArC,EAAEmJ,KAAKV,EAAI1G,OAAQ,SAASqH,EAAKC,GAC9BX,EAAOU,GAAOC,IAEjBZ,EAAI1F,SAAS,MAAMA,WAAWoG,KAAK,WAC5BT,EAAO3F,WAAY2F,EAAO3F,SAAW,IAC1C2F,EAAO3F,SAASuG,KAAK5H,EAAKoB,YAAY9C,EAAEK,UAEnCqI,GAGTzF,UAAW,SAAUlB,EAAMwH,GACzB,IAAI7H,EAAOrB,KAOX,OANA0B,EAAKgH,aAAeQ,GAASxH,EAAKgB,UAAmC,EAAvBhB,EAAKgB,SAASV,OAAa,EAAI,GACzEN,EAAKgB,UACPhB,EAAKgB,SAASyG,QAAQ,SAASC,GAC7B/H,EAAKuB,UAAUwG,EAAM,KAA8B,EAAvB1H,EAAKgB,SAASV,OAAa,EAAI,MAGxDN,GAGT2H,UAAW,SAAU7H,EAAQ8H,GAC3BA,EAAuC,OAApBA,GAA4BA,IAAoB1J,GAAa0J,EAChF,IAAIjI,EAAOrB,KACPuJ,EAAQ/H,EAAOS,KAAK,eACpBoG,EAAS,CAAEmB,GAAMD,EAAM,GAAGC,IAU9B,OATIF,GACF3J,EAAEmJ,KAAKS,EAAM7H,KAAK,YAAa,SAAUqH,EAAKC,GAC5CX,EAAOU,GAAOC,IAGlBO,EAAMV,SAAS,UAAUnG,WAAWoG,KAAK,WAClCT,EAAO3F,WAAY2F,EAAO3F,SAAW,IAC1C2F,EAAO3F,SAASuG,KAAK5H,EAAKgI,UAAU1J,EAAEK,MAAOsJ,MAExCjB,GAGToB,aAAc,SAAUH,GAEtB,GADAA,EAAuC,OAApBA,GAA4BA,IAAoB1J,GAAa0J,OACrD,IAAhBtJ,KAAKwB,OACd,MAAO,iCAEP,IAAKxB,KAAKwB,OAAOS,KAAK,SAASD,OAC7B,MAAO,4BAEP,IAAI0H,GAAQ,EAOZ,OANA1J,KAAKwB,OAAOS,KAAK,SAAS6G,KAAK,WAC7B,IAAK9I,KAAKwJ,GAER,OADAE,GAAQ,IAIPA,EAKF1J,KAAKqJ,UAAUrJ,KAAKwB,OAAQ8H,GAJtB,4EAOfK,aAAc,SAAUJ,EAAOK,GAC7B,IAAIjF,EAAU,GACVkF,IAAmBN,EAAMxH,QAAQ,YAAYC,OAEjD,GAAiB,YADb4H,EAAWA,GAAY,SAazB,GAXIC,GACFlF,EAAU4E,EAAMxH,QAAQ,MAAM+H,QAAQ,OACzB9H,SACX2C,EAAU4E,EAAMxH,QAAQ,WACXC,SACX2C,EAAU4E,EAAMxH,QAAQ,aAAa8G,SAAS,WAIlDlE,EAAU4E,EAAMxH,QAAQ,UAAU8G,SAAS,SAEzClE,EAAQ3C,OACV,OAAI2C,EAAQiE,GAAG,aAAgBjE,EAAQiE,GAAG,YAAcjE,EAAQ5C,QAAQ,UAAU6G,GAAG,aAAiBjE,EAAQiE,GAAG,YAAcjE,EAAQ5C,QAAQ,aAAa6G,GAAG,WACtJ,CAAEmB,OAAS,EAAMC,SAAW,GAE9B,CAAED,OAAS,EAAMC,SAAW,QAEhC,GAAiB,aAAbJ,GAET,IADAjF,EAAUkF,EAAiBN,EAAMZ,SAASjG,SAAS,MAAQ6G,EAAMV,SAAS,WAC9D7G,OACV,OAAK2C,EAAQiE,GAAG,WAGT,CAAEmB,OAAS,EAAMC,SAAW,GAF1B,CAAED,OAAS,EAAMC,SAAW,QAIlC,GAAiB,aAAbJ,GAET,IADAjF,EAAUkF,EAAiBN,EAAMxH,QAAQ,MAAQwH,EAAMZ,SAASE,YACpD7G,UAAY6H,GAAkD,EAAhClF,EAAQjC,SAAS,MAAMV,QAC/D,OAAK2C,EAAQiE,GAAG,YAAejE,EAAQgE,SAASC,GAAG,YAAgBiB,GAAmBlF,EAAQ5C,QAAQ,aAAa6G,GAAG,WAG/G,CAAEmB,OAAS,EAAMC,SAAW,GAF1B,CAAED,OAAS,EAAMC,SAAW,QAMvC,IADArF,EAAU4E,GACEvH,OACV,OAAO2C,EAAQ5C,QAAQ,UAAUC,QAAU2C,EAAQ5C,QAAQ,UAAU6G,GAAG,YACrEjE,EAAQ5C,QAAQ,cAAcC,QAAU2C,EAAQ5C,QAAQ,cAAc6G,GAAG,YACzEjE,EAAQ5C,QAAQ,aAAaC,SAAW2C,EAAQ5C,QAAQ,UAAU6G,GAAG,YAAcjE,EAAQ5C,QAAQ,aAAa6G,GAAG,YAI/G,CAAEmB,OAAS,EAAMC,SAAW,GAF1B,CAAED,OAAS,EAAMC,SAAW,GAKzC,MAAO,CAAED,OAAS,EAAOC,SAAW,IAGtCC,gBAAiB,SAAUV,EAAOK,GAChC,OAAKL,GAAWA,aAAiB5J,GAAO4J,EAAMX,GAAG,SAGhC,WAAbgB,EACKL,EAAMxH,QAAQ,UAAU8G,SAAS,SAClB,aAAbe,EACFL,EAAMV,SAAS,UAAUnG,SAAS,cAAcT,KAAK,eACtC,aAAb2H,EACFL,EAAMxH,QAAQ,cAAc8G,WAAW5G,KAAK,eAE5CtC,IATAA,KAYXuK,cAAe,SAAUrI,GACvBlC,EAAEkC,EAAMC,QAAQI,YAAY,WAC5BL,EAAMH,KAAKiH,OAAOwB,SAAS,WAG7BC,WAAY,SAAUb,GACpB,IAAIc,EAAUd,EAAMxH,QAAQ,UAAU8G,SAAS,SAC3CwB,EAAQpI,KAAK,YAAYD,QAC3BuH,EAAMxH,QAAQ,aAAaL,KAAK,UAAU,GAGxC1B,KAAK2J,aAAaJ,EAAO,YAAYS,SACvChK,KAAKsK,aAAaf,GAGpBA,EAAMZ,SAASwB,SAAS,wBAEpBnK,KAAK2J,aAAaU,GAASL,SAC7BK,EAAQF,SAAS,sBAAsBI,IAAI,gBAAiB,CAAE5B,OAAU0B,GAAWrK,KAAKkK,eAGtFlK,KAAK2J,aAAaU,EAAS,UAAUL,SACvChK,KAAKoK,WAAWC,IAGpBG,cAAe,SAAU3I,GACvB,IAAI0H,EAAQ1H,EAAMH,KAAK+I,KACvB9K,EAAEkC,EAAMC,QAAQI,YAAY,WACxBlC,KAAK0K,WAAWnB,IAClBvJ,KAAK2K,oBAAoBpB,EAAM7G,SAAS,cAI5CkI,WAAY,SAAUrB,GAEpB,IAAIc,EAAUd,EAAMxH,QAAQ,UAAU8G,SAAS,SAAS3G,YAAY,UAEpEqH,EAAMxH,QAAQ,cAAcG,YAAY,wBAExClC,KAAK6K,QAAQR,EAAQ,IACrBA,EAAQF,SAAS,WAAWjI,YAAY,cAAcqI,IAAI,gBAAiB,CAAEE,KAAQlB,GAASvJ,KAAKwK,cAAcM,KAAK9K,QAExH+K,SAAU,SAAUC,GACdA,EAAW/I,KAAK,YAAYD,QAC9BgJ,EAAWjJ,QAAQ,aAAaL,KAAK,UAAU,IAGnDuJ,cAAe,SAAUC,EAAOpL,GAC9B,OAAOE,KAAK2J,aAAahK,EAAEG,IAAOkK,SAGpCmB,gBAAiB,SAAUtJ,GACzB,IAAI0H,EAAQ1H,EAAMH,KAAK+I,KACvB5I,EAAMH,KAAK0J,cAAclJ,YAAY,WACrCL,EAAMH,KAAK0J,cAAcrJ,QAAQ,UAAUoI,SAAS,UAChDnK,KAAK0K,WAAWnB,IAClBvJ,KAAK2K,oBAAoBpB,EAAM7G,SAAS,iBAI5C2I,aAAc,SAAU9B,GACtBA,EAAMxH,QAAQ,cAAcoI,SAAS,uBACrC,IAAImB,EAAc/B,EAAMV,SAAS,UACjC7I,KAAK+K,SAASO,GACd,IAAIC,EAAiBD,EAAYrJ,KAAK,SAASuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OACzDsL,EAAY1C,GAAG,cAElC2C,EAAexJ,QAAQ,cAAcoI,SAAS,0BAE5CmB,EAAY1C,GAAG,cAAgB0C,EAAYrJ,KAAK,aAAaD,SAC/DuJ,EAAetJ,KAAK,qBAAqBC,YAAY,oBAAoBiI,SAAS,mBAEpFnK,KAAK6K,QAAQU,EAAeE,IAAI,IAChCF,EAAepB,SAAS,oBAAoB3B,GAAG,GAAG+B,IAAI,gBAAiB,CAAEa,cAAiBG,EAAgBG,WAAcJ,EAAab,KAAQlB,GAASvJ,KAAKmL,gBAAgBL,KAAK9K,QAGlL2L,gBAAiB,SAAU9J,GACzB,IAAI0H,EAAQ1H,EAAMH,KAAK+I,KACvB5I,EAAMH,KAAK0J,cAAclJ,YAAY,WACjClC,KAAK0K,WAAWnB,IAClBvJ,KAAK2K,oBAAoBpB,EAAM7G,SAAS,iBAI5CkJ,aAAc,SAAUrC,GAEtBA,EAAMxH,QAAQ,cAAcG,YAAY,uBACxC,IAAI2J,EAAUtC,EAAMV,SAAS,UACzBiD,EAAiBD,EAAQjD,GAAG,aAC5B2C,EAAiBO,EACjBD,EAAQ3J,YAAY,UAAUD,KAAK,SAASuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OAC3E6L,EAAQ3J,YAAY,UAAUQ,SAAS,cAAcT,KAAK,eAAeuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OACvG8L,IACHP,EAAeC,OAAO,qBAAqBzJ,QAAQ,cAAcoI,SAAS,uBAC1EoB,EAAexJ,QAAQ,cAAcG,YAAY,0BAGnDlC,KAAK6K,QAAQU,EAAeE,IAAI,IAChCF,EAAepB,SAAS,WAAWjI,YAAY,YAAYsG,GAAG,GAAG+B,IAAI,gBAAiB,CAAEE,KAAQlB,EAAO6B,cAAiBG,GAAkBvL,KAAK2L,gBAAgBb,KAAK9K,QAGtK+L,gBAAiB,SAAUlK,GACzB,IAAI0H,EAAQ1H,EAAMH,KAAK+I,KACnBuB,EAAiBnK,EAAMH,KAAKuK,cAC5BnL,EAAYe,EAAMH,KAAKZ,UACvBoL,EAAYpL,EAA2B,SAAdA,EAAuBkL,EAAeG,QAAQ,iBAAmBH,EAAeI,QAAQ,iBAAoBJ,EAAenD,WACxJhH,EAAMH,KAAK0J,cAAclJ,YAAY,WACrCgK,EAAUjK,KAAK,eAAeuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OAC1DkC,YAAY,0BAA0BiI,SAAS,YAClD+B,EAAUjK,KAAK,qBAAqBkI,SAAS,UAC1CkC,MAAMlC,SAAS,UAEdnK,KAAK0K,WAAWnB,IAClBvJ,KAAKsM,sBAAsB/C,IAI/Be,aAAc,SAAUf,EAAOzI,GAC7B,IACIkL,EAAiBzC,EAAMxH,QAAQ,cAAcoI,SAAS,uBACtD6B,EAAenD,WAAW5G,KAAK,YAAYD,QAC7CuH,EAAMxH,QAAQ,aAAaL,KAAK,UAAU,GAExCZ,EACgB,SAAdA,EACFkL,EAAe7B,SAAS,aACrBgC,QAAQ,wBAAwBjK,YAAY,iCAAiCmK,MAC7EF,UAAUhC,SAAS,0CACnBlI,KAAK,SAASuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OAAOmK,SAAS,uBAEhE6B,EAAe7B,SAAS,cACrBiC,QAAQ,wBAAwBlK,YAAY,kCAAkCmK,MAC9ED,UAAUjC,SAAS,0CACnBlI,KAAK,SAASuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OAAOmK,SAAS,uBAGlE6B,EAAeG,UAAUlK,KAAK,SAASuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OAAOmK,SAAS,uBACtF6B,EAAeI,UAAUnK,KAAK,SAASuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OAAOmK,SAAS,sBACtF6B,EAAenD,WAAWsB,SAAS,2CAErC,IAAIoB,EAAiBS,EAAenD,WAAW5G,KAAK,YACpDsJ,EAAe/C,GAAG,GAAG+B,IAAI,gBAAiB,CAAEE,KAAQlB,EAAO0C,cAAiBD,EAAgBlL,UAAaA,EAAWsK,cAAiBG,GAAkBvL,KAAK+L,gBAAgBjB,KAAK9K,QAGnLuM,gBAAiB,SAAU1K,GACzB,IAAI0H,EAAQ1H,EAAMH,KAAK+I,KACvB5I,EAAMH,KAAK8K,aAAatK,YAAY,WAChClC,KAAK0K,WAAWnB,KAClBvJ,KAAKsM,sBAAsB/C,GAC3BA,EAAM7G,SAAS,YAAYR,YAAY,kBAAkBiI,SAAS,sBAItEsC,qBAAsB,SAAS5K,GAC7BlC,EAAEkC,EAAMC,QAAQI,YAAY,YAG9BwK,aAAc,SAAUnD,EAAOzI,GAC7B,IAEIoL,EAAYvM,IACZqM,EAAiBzC,EAAMxH,QAAQ,cAG/BmK,EAFApL,EACgB,SAAdA,EACUkL,EAAeG,UAAUjK,YAAY,UAErC8J,EAAeI,UAAUlK,YAAY,UAGvCqH,EAAMxH,QAAQ,cAAc8G,WAAW3G,YAAY,UAGjE,IAAIyK,EAAcpD,EAAMxH,QAAQ,UAAU8G,SAAS,SAC/C/H,GACFkL,EAAe9J,YAAYpB,EAAY,SAClCkL,EAAepD,GAAG,mBACrBoD,EAAe9J,YAAY,uBAE7BgK,EAAUhK,YAAY,sBAAwBpB,EAAY,WAE1DyI,EAAMxH,QAAQ,cAAcG,YAAY,uBACxCgK,EAAUhK,YAAY,uBAGnBlC,KAAK2J,aAAaJ,EAAO,UAAUS,UACtCT,EAAMxH,QAAQ,cAAcG,YAAY,wBACxCyK,EAAYzK,YAAY,UACxBlC,KAAK6K,QAAQ8B,EAAY,IACzBA,EAAYxC,SAAS,WAAWjI,YAAY,cAAcqI,IAAI,gBAAiBvK,KAAKyM,uBAGtF,IAAIG,EAAgBV,EAAUjK,KAAK,SAASuJ,OAAOxL,KAAKiL,cAAcH,KAAK9K,OAC3EA,KAAK6K,QAAQ+B,EAAcnB,IAAI,IAC/BmB,EAAczC,SAAS,WAAWjI,YAAY,0BAC9C0K,EAAcpE,GAAG,GAAG+B,IAAI,gBAAiB,CAAEE,KAAQlB,EAAOiD,aAAgBI,GAAiB5M,KAAKuM,gBAAgBzB,KAAK9K,QAGvH6M,aAAc,SAAUC,GACtB,IAAItL,EAASxB,KAAKwB,OAClB,YAAqC,IAA1BA,EAAOE,KAAK,YAAuD,IAA1BF,EAAOE,KAAK,aAIhEoL,EAAM3C,SAAS,UACf2C,EAAMnE,SAASrG,OAAO,2CACnBI,WAAWqK,IAAI,YAAYnH,IAAI,UAAW,IAC7CpE,EAAOE,KAAK,UAAU,GACtB/B,EAAE,kBAAkBqN,KAAK,YAAY,IAC9B,IAGTC,WAAY,SAAUH,GACpB,IAAIvD,EAAQuD,EAAMnE,SAClBmE,EAAM5K,YAAY,UAClBqH,EAAMtH,KAAK,YAAYR,SACvB8H,EAAM7G,WAAWwK,WAAW,SAC5BlN,KAAKwB,OAAOE,KAAK,UAAU,GAC3B/B,EAAE,kBAAkBqN,KAAK,YAAY,IAGvCtC,WAAY,SAAUnB,GACpB,OAAgE,EAAzDA,EAAM7G,SAAS,SAASyK,KAAK,SAASjH,QAAQ,SAGvDyE,oBAAqB,SAAUyC,GAC7BA,EAAOC,YAAY,kBAAkBA,YAAY,qBAGnDf,sBAAuB,SAAU/C,GAC/B,IAAIxJ,EAAOC,KAAKsB,QAChB,GAAIvB,EAAKM,0BAA+C,IAAjBN,EAAK4C,SAA2B4G,EAAMxH,QAAQ,UAAUL,KAAK,mBAAoB,CACtH,IAAI4L,EAAW/D,EAAMZ,SAAS4E,OAC1BD,EAAStL,SACPsL,EAAS1E,GAAG,WACdW,EAAM7G,SAAS,aAAayH,SAAS,oBAAoBjI,YAAY,qBAErEqH,EAAM7G,SAAS,aAAayH,SAAS,qBAAqBjI,YAAY,qBAG1E,IAAIsL,EAAWjE,EAAMZ,SAAS8E,OAC1BD,EAASxL,SACPwL,EAAS5E,GAAG,WACdW,EAAM7G,SAAS,cAAcyH,SAAS,qBAAqBjI,YAAY,oBAEvEqH,EAAM7G,SAAS,cAAcyH,SAAS,oBAAoBjI,YAAY,0BAGrE,CACL,IAAIwL,EAAQnE,EAAMZ,SAASE,WACvB8E,IAAcD,EAAM1L,SAAU0L,EAAM9E,GAAG,WAC3CW,EAAM7G,SAAS,aAAa2K,YAAY,oBAAqBM,GAAaN,YAAY,oBAAqBM,GAC3GpE,EAAM7G,SAAS,cAAc2K,YAAY,mBAAoBM,GAAaN,YAAY,qBAAsBM,KAIhH9C,QAAS,SAAUJ,GACbA,IACFA,EAAKmD,MAAMC,YAAcpD,EAAKoD,cAIlCC,sBAAuB,SAAUjM,GAC/B,IAAI0H,EAAQ5J,EAAEkC,EAAM6D,gBAChBqI,GAAO,EACX,GAAIxE,EAAMxH,QAAQ,mBAAmBC,OAAQ,CAC3C,IAAIgM,EAAazE,EAAM7G,SAAS,cACb,eAAfb,EAAMU,KACJgH,EAAM7G,SAAS,cAAcV,SAC/B+L,EAAO/N,KAAK2J,aAAaJ,EAAO,YAAYS,QAC5CgE,EAAWX,YAAY,mBAAoBU,GAAMV,YAAY,mBAAoBU,IAGnFC,EAAW9L,YAAY,wCAEpB,CACL,IAAI+L,EAAW1E,EAAM7G,SAAS,YAE1BwL,GADa3E,EAAM7G,SAAS,cACd6G,EAAM7G,SAAS,gBAC7ByL,EAAY5E,EAAM7G,SAAS,aACZ,eAAfb,EAAMU,MACJ0L,EAASjM,SACX+L,EAAO/N,KAAK2J,aAAaJ,EAAO,UAAUS,QAC1CiE,EAASZ,YAAY,kBAAmBU,GAAMV,YAAY,mBAAoBU,IAE5EG,EAAYlM,SACd+L,EAAO/N,KAAK2J,aAAaJ,EAAO,YAAYS,QAC5CkE,EAAYb,YAAY,oBAAqBU,GAAMV,YAAY,iBAAkBU,IAE/EI,EAAUnM,QACZhC,KAAKsM,sBAAsB/C,IAG7BA,EAAM7G,SAAS,SAASR,YAAY,wEAK1CkM,iBAAkB,SAAUvM,GAC1B7B,KAAKwB,OAAOS,KAAK,YAAYC,YAAY,WACzCvC,EAAEkC,EAAM6D,gBAAgByE,SAAS,YAGnCkE,UAAW,SAAUzJ,EAAK9B,EAAKgK,GAC7B,IAAIzL,EAAOrB,KACAA,KAAKsB,QAChB3B,EAAEkD,KAAK,CAAEC,IAAOA,EAAKC,SAAY,SAChCC,KAAK,SAAUtB,GACVL,EAAKG,OAAOE,KAAK,YACP,WAARkD,EACGjF,EAAE2O,cAAc5M,IACnBL,EAAKkN,UAAUzB,EAAMnE,SAAUjH,GAEhB,aAARkD,EACLlD,EAAKgB,SAASV,QAChBX,EAAKmN,YAAY1B,EAAMnE,SAAUjH,EAAKkD,IAGxCvD,EAAKoN,YAAY3B,EAAMnE,SAAUjH,EAAKmH,SAAWnH,EAAKmH,SAAWnH,GAEnEL,EAAKqD,iBAAiBoI,EAAMnE,SAAU/D,MAGzCzB,KAAK,WACJE,QAAQC,IAAI,iBAAmBsB,EAAM,WAEtCrB,OAAO,WACNlC,EAAK4L,WAAWH,MAIpB4B,mBAAoB,SAAU7M,GAC5B,IAAIoM,EAAWpM,EAAMH,KAAKiN,QACtBpF,EAAQ0E,EAAStF,SACjB3I,KAAK0K,WAAWnB,KAClBvJ,KAAK2K,oBAAoBsD,GACzBjO,KAAKsM,sBAAsB/C,KAI/BqF,oBAAqB,SAAU/M,GAC7BA,EAAMgN,kBACN,IACIZ,EAAWtO,EAAEkC,EAAMC,QACnByH,EAAQ5J,EAAEkC,EAAM6D,gBAChBoJ,EAAc9O,KAAK2J,aAAaJ,EAAO,UAC3C,GAAIuF,EAAY/E,MAAO,CACrB,IAAIM,EAAUd,EAAMxH,QAAQ,UAAU8G,SAAS,SAC/C,GAAIwB,EAAQzB,GAAG,YAAe,OAE1BkG,EAAY9E,SACdhK,KAAKoK,WAAWb,GAChBc,EAAQE,IAAI,gBAAiB,CAAEoE,QAAWV,GAAYjO,KAAK0O,mBAAmB5D,KAAK9K,OACnFA,KAAK8E,iBAAiByE,EAAO,YAE7BvJ,KAAK4K,WAAWrB,GAChBvJ,KAAK6E,iBAAiB0E,EAAO,gBAI/B,GAAIvJ,KAAK6M,aAAaoB,GAAW,CAC/B,IAAIlO,EAAOC,KAAKsB,QACZwB,EAAMnD,EAAEoP,WAAWhP,EAAK4C,QAAQgG,QAAU5I,EAAK4C,QAAQgG,OAAOY,EAAM7H,KAAK,aAAe3B,EAAK4C,QAAQgG,OAASY,EAAM,GAAGC,GAC3HxJ,KAAKqO,UAAU,SAAUvL,EAAKmL,KAKpCe,uBAAwB,SAAUnN,GAChCA,EAAMgN,kBACN,IAAIX,EAAcvO,EAAEkC,EAAMC,QACtByH,EAAQ5J,EAAEkC,EAAM6D,gBAChBuJ,EAAgBjP,KAAK2J,aAAaJ,EAAO,YAC7C,GAAI0F,EAAclF,MAAO,CAEvB,GADgBR,EAAMV,SAAS,UAAUnG,WAAWA,SAAS,SAC/CkG,GAAG,YAAe,OAE5BqG,EAAcjF,SAChBhK,KAAKqL,aAAa9B,GAClBvJ,KAAK8E,iBAAiByE,EAAO,cAE7BvJ,KAAK4L,aAAarC,GAClBvJ,KAAK6E,iBAAiB0E,EAAO,kBAG/B,GAAIvJ,KAAK6M,aAAaqB,GAAc,CAClC,IAAInO,EAAOC,KAAKsB,QACZwB,EAAMnD,EAAEoP,WAAWhP,EAAK4C,QAAQD,UAAY3C,EAAK4C,QAAQD,SAAS6G,EAAM7H,KAAK,aAAe3B,EAAK4C,QAAQD,SAAW6G,EAAM,GAAGC,GACjIxJ,KAAKqO,UAAU,WAAYvL,EAAKoL,KAKtCgB,kBAAmB,SAAUrN,GAC3BA,EAAMgN,kBACN,IAAIM,EAASxP,EAAEkC,EAAMC,QACjByH,EAAQ5J,EAAEkC,EAAM6D,gBAChB3F,EAAOC,KAAKsB,QACZ8N,EAAgBpP,KAAK2J,aAAaJ,EAAO,YAC7C,GAAI6F,EAAcrF,MAAO,CAEvB,GADgBR,EAAMxH,QAAQ,cAAc8G,WAC9B5G,KAAK,YAAYD,OAAU,OACzC,GAAIjC,EAAKM,mBAAoB,CAC3B,IAAIiN,EAAW/D,EAAMxH,QAAQ,cAAcwL,OACvCC,EAAWjE,EAAMxH,QAAQ,cAAc0L,OACvC0B,EAAOvG,GAAG,aACR0E,EAAS1E,GAAG,YACd5I,KAAK0M,aAAanD,EAAO,QACzBvJ,KAAK6E,iBAAiB0E,EAAM,cAE5BvJ,KAAKsK,aAAaf,EAAO,QACzBvJ,KAAK8E,iBAAiByE,EAAO,aAG3BiE,EAAS5E,GAAG,YACd5I,KAAK0M,aAAanD,EAAO,SACzBvJ,KAAK6E,iBAAiB0E,EAAM,cAE5BvJ,KAAKsK,aAAaf,EAAO,SACzBvJ,KAAK8E,iBAAiByE,EAAO,kBAI7B6F,EAAcpF,SAChBhK,KAAKsK,aAAaf,GAClBvJ,KAAK8E,iBAAiByE,EAAO,cAE7BvJ,KAAK0M,aAAanD,GAClBvJ,KAAK6E,iBAAiB0E,EAAO,kBAKjC,GAAIvJ,KAAK6M,aAAasC,GAAS,CAC7B,IAAI/O,EAASmJ,EAAM,GAAGC,GAClB1G,EAAO9C,KAAK2J,aAAaJ,EAAO,UAAe,MAChD5J,EAAEoP,WAAWhP,EAAK4C,QAAQkG,UAAY9I,EAAK4C,QAAQkG,SAASU,EAAM7H,KAAK,aAAe3B,EAAK4C,QAAQkG,SAAWzI,EAC9GT,EAAEoP,WAAWhP,EAAK4C,QAAQ0M,UAAYtP,EAAK4C,QAAQ0M,SAAS9F,EAAM7H,KAAK,aAAe3B,EAAK4C,QAAQ0M,SAAWjP,EACjHJ,KAAKqO,UAAU,WAAYvL,EAAKqM,KAKtCG,gBAAiB,SAAUzN,GACzBA,EAAMH,KAAK6N,OAAOrN,YAAY,YAGhCsN,kBAAmB,SAAU3N,GAC3BA,EAAMH,KAAK6N,OAAOrN,YAAY,WAAWH,QAAQ,MAAMoI,SAAS,WAGlEsF,aAAc,SAAU5N,GACtB,IAAImM,EAAarO,EAAEkC,EAAMC,QACrB4N,EAAe1B,EAAWrF,SAAS8E,OACnCkC,EAAeD,EAAazN,KAAK,SACjC2N,EAAYF,EAAahN,WAAWA,SAAS,SAC7CkN,EAAUhH,GAAG,cACjBoF,EAAWX,YAAY,oCACnBsC,EAAanH,GAAG,GAAGI,GAAG,cACxB8G,EAAaxN,YAAY,UACzBlC,KAAK6K,QAAQ+E,EAAUnE,IAAI,IAC3BmE,EAAUzF,SAAS,WAAWjI,YAAY,YAAYsG,GAAG,GAAG+B,IAAI,gBAAiB,CAAEgF,OAAUK,GAAa5P,KAAKsP,mBAE/GK,EAAaxF,SAAS,oBAAoB3B,GAAG,GAAG+B,IAAI,gBAAiB,CAAEgF,OAAUI,GAAgB3P,KAAKwP,mBACtGG,EAAa1N,KAAK,cAAcC,YAAY,oBAAoBiI,SAAS,sBAI7E0F,gBAAiB,SAAUhO,GACzB,IAIIiO,EAAWC,EAJXC,EAAWrQ,EAAEkC,EAAMC,QACnB/B,EAAOC,KAAKsB,QACZ2O,EAAYpO,EAAMuF,cAClB8I,EAAY,UAAUC,KAAK3Q,EAAO4Q,UAAUC,UAAUC,eAE1D,GAAK7Q,EAAS8Q,cAAc,eAQ1BT,EAAYE,EAASjO,QAAQ,aAAaW,SAAS,eAAe+I,IAAI,GACtEsE,EAAYpQ,EAAEmQ,GAAWpN,WAAW+I,IAAI,OATE,CAE1C,KADAqE,EAAYrQ,EAAS+Q,gBAAgB,6BAA8B,QACpDtM,UAAW,OAC1B4L,EAAU5L,UAAUuM,IAAI,cACxBV,EAAYtQ,EAAS+Q,gBAAgB,6BAA6B,QAClEV,EAAUY,YAAYX,GACtBC,EAASjO,QAAQ,aAAaO,OAAOwN,GAKvC,IAAIa,EAAcX,EAASjO,QAAQ,aAAa6D,IAAI,aAAaK,MAAM,KACnE2K,EAAkC,QAAnB7Q,EAAKe,WAA0C,QAAnBf,EAAKe,UAChD+P,EAAQhJ,KAAKK,IAAI1I,EAAO2I,WAAWyI,EAAeD,EAAY,GAAGG,MAAMH,EAAY,GAAGzK,QAAQ,KAAO,GAAKyK,EAAY,KAC1Hb,EAAUiB,aAAa,QAASH,EAAeZ,EAASgB,YAAW,GAAShB,EAASiB,aAAY,IACjGnB,EAAUiB,aAAa,SAAUH,EAAeZ,EAASiB,aAAY,GAASjB,EAASgB,YAAW,IAClGjB,EAAUgB,aAAa,IAAI,EAAIF,GAC/Bd,EAAUgB,aAAa,IAAI,EAAIF,GAC/Bd,EAAUgB,aAAa,QAAS,IAAMF,GACtCd,EAAUgB,aAAa,SAAU,GAAKF,GACtCd,EAAUgB,aAAa,KAAM,EAAIF,GACjCd,EAAUgB,aAAa,KAAM,EAAIF,GACjCd,EAAUgB,aAAa,eAAgB,EAAIF,GAC3C,IAAIK,EAAUjB,EAAUkB,QAAUN,EAC9BO,EAAUnB,EAAUoB,QAAUR,EAWlC,GAVuB,QAAnB9Q,EAAKe,WACPoQ,EAAUjB,EAAUoB,QAAUR,EAC9BO,EAAUnB,EAAUkB,QAAUN,GACF,QAAnB9Q,EAAKe,WACdoQ,EAAUlB,EAASgB,YAAW,GAASf,EAAUoB,QAAUR,EAC3DO,EAAUnB,EAAUkB,QAAUN,GACF,QAAnB9Q,EAAKe,YACdoQ,EAAUlB,EAASgB,YAAW,GAASf,EAAUkB,QAAUN,EAC3DO,EAAUpB,EAASiB,aAAY,GAAShB,EAAUoB,QAAUR,GAE1DX,EAAW,CACbH,EAAUgB,aAAa,OAAQ,sBAC/BhB,EAAUgB,aAAa,SAAU,kBACjC,IAAIO,EAAmB7R,EAAS8R,cAAc,OAC9CD,EAAiBE,IAAM,4BAA6B,IAAKC,eAAiBC,kBAAkB5B,GAC5FG,EAAU0B,aAAaC,aAAaN,EAAkBJ,EAASE,QAG3DnB,EAAU0B,aAAaC,cACzB3B,EAAU0B,aAAaC,aAAa9B,EAAWoB,EAASE,IAI9DS,uBAAwB,SAAUC,GAChC,IAAI/R,EAAOC,KAAKsB,QAEZyQ,EAAeD,EAAS/P,QAAQ,eAAeiQ,SAAS,QACxDC,EAAYH,EAAS/P,QAAQ,UAAU8G,SAAS,SAChDqJ,EAAYJ,EAAS/P,QAAQ,cAAcE,KAAK,SACpDjC,KAAKwB,OAAOE,KAAK,UAAWoQ,GACzB7P,KAAK,SAAS6G,KAAK,SAAUoC,EAAOT,GAC9BsH,IAA2C,IAA3BG,EAAUhH,MAAMT,KAC/B1K,EAAKoS,aACHpS,EAAKoS,aAAaL,EAAUG,EAAWtS,EAAE8K,KAC3C9K,EAAE8K,GAAMN,SAAS,eAGnBxK,EAAE8K,GAAMN,SAAS,mBAM3BiI,iBAAkB,SAAUvQ,GAC1BA,EAAMuF,cAAcuK,aAAaU,QAAQ,YAAa,oBAEjB,SAAjCrS,KAAKwB,OAAOoE,IAAI,cAClB5F,KAAK6P,gBAAgBhO,GAEvB7B,KAAK6R,uBAAuBlS,EAAEkC,EAAMC,UAGtCwQ,gBAAiB,SAAUzQ,GACpBlC,EAAEkC,EAAM6D,gBAAgBkD,GAAG,gBAK9B/G,EAAMqD,iBAJNrD,EAAMuF,cAAcuK,aAAaY,WAAa,QAQlDC,eAAgB,SAAU3Q,GACxB7B,KAAKwB,OAAOS,KAAK,gBAAgBC,YAAY,gBAG/CuQ,YAAa,SAAU5Q,GACrB,IAAI6Q,EAAY/S,EAAEkC,EAAM6D,gBACpBoM,EAAW9R,KAAKwB,OAAOE,KAAK,WAGhC,GAAKoQ,EAASE,SAAS,SAKvB,GAAKU,EAAUV,SAAS,eAAxB,CAMA,IAAIC,EAAYH,EAAS/P,QAAQ,UAAU8G,SAAS,SAChD8J,EAAYhT,EAAE2E,MAAM,qBAExB,GADAtE,KAAKwB,OAAO+C,QAAQoO,EAAW,CAAEC,YAAed,EAAUe,SAAYZ,EAAWa,SAAYJ,KACzFC,EAAUI,qBAAd,CAIA,GAAKL,EAAU7J,SAAS,UAAU7G,OAO3B,CACL,IAAIgR,EAAkB,oGACjBlB,EAAS7P,KAAK,mBAAmBD,QACpC8P,EAASxP,OAAO0Q,GAElBN,EAAU7J,SAAS,UAAUvG,OAAOwP,EAAS/P,QAAQ,eACrD,IAAIkR,EAAYnB,EAAS/P,QAAQ,cAAc8G,WAAW5G,KAAK,eACtC,IAArBgR,EAAUjR,QACZiR,EAAU3Q,OAAO0Q,QAdnBN,EAAUpQ,OAAO,oDACd8C,MAAM,2BACNyD,SAAS,UAAUvG,OAAOwP,EAAS7P,KAAK,mBAAmBR,SAAS4K,MAAMtK,QAAQ,eACjF2Q,EAAUhQ,SAAS,UAAUV,QAC/B0Q,EAAUhQ,SAAS,UAAUwQ,QAAQ,iBAAmBlT,KAAKwB,OAAOE,KAAK,WAAWd,iBAAmB,iBAcxC,IAA/DqR,EAAUpJ,SAAS,UAAUnG,SAAS,cAAcV,OACtDiQ,EAAUpJ,SAAS,UAAUnG,SAAS,cAAcT,KAAK,eACtDA,KAAK,mBAAmBR,SAC6C,IAA/DwQ,EAAUpJ,SAAS,UAAUnG,SAAS,cAAcV,QAC7DiQ,EAAUhQ,KAAK,wBAAwBR,SACpC4K,MAAMxD,SAAS,UAAUpH,gBAzC5BzB,KAAKwB,OAAO2R,eAAe,CAAE5Q,KAAQ,wBAAyB6Q,YAAetB,EAAUgB,SAAYJ,KA6CvGW,kBAAmB,SAAUxR,GACvB7B,KAAKsT,cAGLzR,EAAM8D,SAAkC,EAAvB9D,EAAM8D,QAAQ3D,SAGnChC,KAAKsT,cAAe,EACpBtT,KAAKuT,YAAa,EAClB1R,EAAMqD,mBAGRsO,iBAAkB,SAAU3R,GAC1B,GAAK7B,KAAKsT,gBAGNzR,EAAM8D,SAAkC,EAAvB9D,EAAM8D,QAAQ3D,QAAnC,CAGAH,EAAMqD,iBAEDlF,KAAKuT,aAERvT,KAAK6R,uBAAuBlS,EAAEkC,EAAM4R,gBAEpCzT,KAAK0T,eAAiB1T,KAAK2T,gBAAgB9R,EAAO7B,KAAKwB,OAAOE,KAAK,WAAW,KAEhF1B,KAAKuT,YAAa,EAGlBvT,KAAK4T,cAAc/R,EAAO7B,KAAK0T,gBAE/B,IACIG,EADYlU,EAAEF,EAASqU,iBAAiBjS,EAAM8D,QAAQ,GAAGoC,QAASlG,EAAM8D,QAAQ,GAAGqC,UACxDjG,QAAQ,YACvC,GAA4B,EAAxB8R,EAAe7R,OAAY,CAC7B,IAAI+R,EAAsBF,EAAe,GACrCA,EAAejL,GAAG,gBACpB5I,KAAKgU,gBAAkBD,EAGvB/T,KAAKgU,gBAAkB,UAIzBhU,KAAKgU,gBAAkB,OAI3BC,gBAAiB,SAAUpS,GACzB,GAAK7B,KAAKsT,aAAV,CAIA,GADAtT,KAAKkU,mBACDlU,KAAKuT,WAAY,CAEjB,GAAIvT,KAAKgU,gBAAiB,CACtB,IAAIG,EAA0B,CAAEzO,eAAgB1F,KAAKgU,iBACrDhU,KAAKyS,YAAY0B,GACjBnU,KAAKgU,gBAAkB,KAE3BhU,KAAKwS,eAAe3Q,OAEnB,CAED,IAAIuS,EAAavS,EAAMwS,eAAe,GAClCC,EAAsB7U,EAAS8U,YAAY,eAC/CD,EAAoBE,eAAe,SAAS,GAAM,EAAMhV,EAAQ,EAAG4U,EAAWK,QAASL,EAAWM,QAASN,EAAWrM,QAASqM,EAAWpM,QAASnG,EAAM8S,QAAS9S,EAAM+S,OAAQ/S,EAAMgT,SAAUhT,EAAMiT,QAAS,EAAG,MAClNjT,EAAMC,OAAOiT,cAAcT,GAE/BtU,KAAKsT,cAAe,IAGtBK,gBAAiB,SAAU9R,EAAOmT,GAChC,IAAIC,EAAYD,EAAOE,WAAU,GACjClV,KAAKmV,UAAUH,EAAQC,GACvBA,EAAUrH,MAAMwH,IAAMH,EAAUrH,MAAMyH,KAAO,UAC7C,IAAIC,EAAkBN,EAAOO,wBACzBC,EAAcxV,KAAKyV,cAAc5T,GAIrC,OAHA7B,KAAK0V,qBAAuB,CAAEC,EAAGH,EAAYG,EAAIL,EAAgBD,KAAMO,EAAGJ,EAAYI,EAAIN,EAAgBF,KAC1GH,EAAUrH,MAAMiI,QAAU,MAC1BpW,EAASqW,KAAKpF,YAAYuE,GACnBA,GAGTf,iBAAkB,WACZlU,KAAK0T,gBAAkB1T,KAAK0T,eAAeqC,eAC7C/V,KAAK0T,eAAeqC,cAAcC,YAAYhW,KAAK0T,gBACrD1T,KAAK0V,qBAAuB,KAC5B1V,KAAK0T,eAAiB,MAGxByB,UAAW,SAAU3D,EAAKyE,GAOxB,GALoB,CAAC,KAAM,QAAS,QAAS,aAC/B9M,QAAQ,SAAU+M,GAC5BD,EAAIE,gBAAgBD,KAGpB1E,aAAe4E,kBAAmB,CACpC,IAAIC,EAAO7E,EAAK8E,EAAOL,EACvBK,EAAKC,MAAQF,EAAKE,MAClBD,EAAKE,OAASH,EAAKG,OACnBF,EAAKG,WAAW,MAAMC,UAAUL,EAAM,EAAG,GAI3C,IADA,IAAIM,EAAKC,iBAAiBpF,GACjBzN,EAAI,EAAGA,EAAI4S,EAAG3U,OAAQ+B,IAAK,CAClC,IAAIgF,EAAM4N,EAAG5S,GACTgF,EAAI7C,QAAQ,cAAgB,IAC9B+P,EAAIrI,MAAM7E,GAAO4N,EAAG5N,IAGxBkN,EAAIrI,MAAMiJ,cAAgB,OAE1B,IAAS9S,EAAI,EAAGA,EAAIyN,EAAI9O,SAASV,OAAQ+B,IACvC/D,KAAKmV,UAAU3D,EAAI9O,SAASqB,GAAIkS,EAAIvT,SAASqB,KAIjD0R,cAAe,SAAU5T,GAIvB,OAHIA,GAASA,EAAM8D,UACjB9D,EAAQA,EAAM8D,QAAQ,IAEjB,CACLgQ,EAAG9T,EAAMkG,QACT6N,EAAG/T,EAAMmG,UAIb4L,cAAe,SAAU/R,EAAOiV,GAC9B,GAAKjV,GAAUiV,EAAf,CAEA,IAAIC,EAAiB/W,KACrBgX,sBAAsB,WACpB,IAAIC,EAAKF,EAAetB,cAAc5T,GAClCqV,EAAIJ,EAAMlJ,MACdsJ,EAAEC,SAAW,WACbD,EAAEL,cAAgB,OAClBK,EAAEE,OAAS,SACPL,EAAerB,uBACfwB,EAAE7B,KAAOxN,KAAKwP,MAAMJ,EAAGtB,EAAIoB,EAAerB,qBAAqBC,GAAK,KACpEuB,EAAE9B,IAAMvN,KAAKwP,MAAMJ,EAAGrB,EAAImB,EAAerB,qBAAqBE,GAAK,UAK3E0B,aAAc,SAAU/N,GACtBA,EAAM9C,GAAG,YAAazG,KAAKoS,iBAAiBtH,KAAK9K,OAC9CyG,GAAG,WAAYzG,KAAKsS,gBAAgBxH,KAAK9K,OACzCyG,GAAG,UAAWzG,KAAKwS,eAAe1H,KAAK9K,OACvCyG,GAAG,OAAQzG,KAAKyS,YAAY3H,KAAK9K,OACjCyG,GAAG,aAAczG,KAAKqT,kBAAkBvI,KAAK9K,OAC7CyG,GAAG,YAAazG,KAAKwT,iBAAiB1I,KAAK9K,OAC3CyG,GAAG,WAAYzG,KAAKiU,gBAAgBnJ,KAAK9K,QAG9CuX,WAAY,SAAU7V,GACpB,IACI3B,EAAOC,KAAKsB,QACZkW,EAAQ9V,EAAK8V,MACb9V,EAAKgB,UAAYhB,EAAK3B,EAAKK,SAC7BT,EAAEmJ,KAAKpH,EAAKgB,SAAU,SAAUwI,EAAOuM,GACrCA,EAAMC,SAAWhW,EAAK3B,EAAKK,UAI/B,IAAI4P,EAAWrQ,EAAE,QAAUI,EAAKc,UAAY,oBAAsB,KAAOa,EAAK3B,EAAKK,QAAU,QAAUsB,EAAK3B,EAAKK,QAAU,IAAM,KAAOsB,EAAKgW,SAAW,iBAAmBhW,EAAKgW,SAAW,IAAM,IAAM,KACpMvN,SAAS,SAAWzI,EAAKiW,WAAa,KAAQH,EAAQzX,EAAKO,aAAe,YAAc,KACvFP,EAAK6X,aACP5H,EAAS1N,OAAOvC,EAAK6X,aAAalW,IAElCsO,EAAS1N,OAAO,sBAAwBZ,EAAK3B,EAAKI,WAAa,UAC5DmC,YAAmC,IAArBvC,EAAK8X,YAA8B,yBAA2BnW,EAAK3B,EAAK8X,cAAgB,IAAM,SAAW,IAG5H,IAAIC,EAAWnY,EAAE4B,OAAO,GAAIG,UACrBoW,EAASpV,SAChBsN,EAAStO,KAAK,WAAYoW,GAE1B,IAAI5O,EAAQxH,EAAKgH,cAAgB,GAsCjC,OArCI3I,EAAKgY,eAAiBP,GAASzX,EAAKgY,cACjCP,EAAQ,EAAKzX,EAAKgY,eAAiBC,OAAO9O,EAAM+O,OAAO,EAAE,KAC5DjI,EAAS1N,OAAO,iCACbI,SAAS,UAAUwQ,QAAQ,iBAAkBnT,EAAKa,iBAAmB,kBAGtEoX,OAAO9O,EAAM+O,OAAO,EAAE,KACxBjI,EAAS1N,OAAO,iDAEf0V,OAAO9O,EAAM+O,OAAO,EAAE,KACvBjI,EAAS1N,OAAO,qGAGf0V,OAAO9O,EAAM+O,OAAO,EAAE,KACvBjI,EAAS1N,OAAO,oDACbI,SAAS,UAAUwQ,QAAQ,iBAAkBnT,EAAKa,iBAAmB,kBAI5EoP,EAASvJ,GAAG,wBAAyBzG,KAAK8N,sBAAsBhD,KAAK9K,OACrEgQ,EAASvJ,GAAG,QAASzG,KAAKoO,iBAAiBtD,KAAK9K,OAChDgQ,EAASvJ,GAAG,QAAS,WAAYzG,KAAK4O,oBAAoB9D,KAAK9K,OAC/DgQ,EAASvJ,GAAG,QAAS,cAAezG,KAAKgP,uBAAuBlE,KAAK9K,OACrEgQ,EAASvJ,GAAG,QAAS,wBAAyBzG,KAAKkP,kBAAkBpE,KAAK9K,OAC1EgQ,EAASvJ,GAAG,QAAS,aAAczG,KAAKyP,aAAa3E,KAAK9K,OAEtDD,EAAKc,YACPb,KAAKsX,aAAatH,GAClBhQ,KAAKsT,cAAe,EACpBtT,KAAKuT,YAAa,EAClBvT,KAAKgU,gBAAkB,MAGrBjU,EAAKwX,YACPxX,EAAKwX,WAAWvH,EAAUtO,GAGrBsO,GAGTxN,eAAgB,SAAU0V,EAAWxW,GACnC,IAAIL,EAAOrB,KACPD,EAAOC,KAAKsB,QACZkW,EAAQ,EAOZ,GALEA,EADE9V,EAAK8V,MACC9V,EAAK8V,MAEL9V,EAAK8V,MAAQU,EAAUC,aAAa,YAAa,UAAUnW,OAGtC,EAA3BoW,OAAOC,KAAK3W,GAAMM,OAAY,CAChC,IAAIgO,EAAWhQ,KAAKuX,WAAW7V,GAC3B3B,EAAKgY,eAA0BhY,EAAKgY,cACtCG,EAAU5V,OAAO0N,GAMrB,GAAItO,EAAKgB,UAAYhB,EAAKgB,SAASV,OAAQ,CACzC,IAEIsW,EAFAC,EAAWf,EAAQ,EAAIzX,EAAKO,cAAiBoB,EAAK8W,YAAc5Y,GAAa8B,EAAK8W,UAChEzY,EAAKgY,eAAkBP,EAAQ,GAAMzX,EAAKgY,eAG9DO,EAAc3Y,EAAE,sBACZ4Y,GAAYf,EAAQ,GAAKzX,EAAKgY,eAChCO,EAAYnO,SAAS,UAEnBqN,EAAQ,IAAMzX,EAAKgY,cACrBG,EAAU/N,SAAS,UAAU7H,OAAOgW,EAAYnO,SAAS,aAEzD+N,EAAU5V,OAAOgW,KAGnBA,EAAc3Y,EAAE,oBAAsB4Y,EAAW,UAAY,IAAM,MAClC,IAA7BH,OAAOC,KAAK3W,GAAMM,QAGhBuW,GACFL,EAAU/N,SAAS,uBAHrB+N,EAAU5V,OAAOgW,IASrB3Y,EAAEmJ,KAAKpH,EAAKgB,SAAU,WACpB,IAAI+V,EAAY9Y,EAAE,0BAClB2Y,EAAYhW,OAAOmW,GACnBzY,KAAKwX,MAAQA,EAAQ,EACrBnW,EAAKmB,eAAeiW,EAAWzY,UAKrC0Y,eAAgB,SAAUR,EAAWxW,GACnC1B,KAAKwC,eAAe0V,EAAW,CAAExV,SAAYhB,KAG/C8M,YAAa,SAAUjF,EAAO7H,GAC5B1B,KAAK0Y,eAAenP,EAAMxH,QAAQ,cAAeL,GAC5C6H,EAAMtH,KAAK,WAAWD,QACzBuH,EAAM7G,SAAS,UAAUwQ,QAAQ,iBAAkBlT,KAAKsB,QAAQV,iBAAmB,iBAEjF2I,EAAMxH,QAAQ,mBAAmBC,OAC9BuH,EAAM7G,SAAS,cAAcV,QAChCuH,EAAMjH,OAAO,iCAGViH,EAAM7G,SAAS,eAAeV,QACjCuH,EAAMjH,OAAO,oDAGbtC,KAAK0K,WAAWnB,IAClBvJ,KAAK2K,oBAAoBpB,EAAM7G,SAAS,iBAI5CiW,gBAAiB,SAAUC,EAAclX,GACvCA,EAAKgH,aAAehH,EAAKgH,cAAgB,MACzC,IAAImQ,EAAkBlZ,EAAE,sDACrBsC,KAAK,cAAcK,OAAOtC,KAAKuX,WAAW7V,IAAO2K,MACpDrM,KAAKwB,OAAO0R,QAAQ2F,GACjB5W,KAAK,oBAAoBK,OAAOsW,EAAa7W,QAAQ,MAAMoI,SAAS,WAGzEoE,UAAW,SAAUqK,EAAclX,GACjC1B,KAAK2Y,gBAAgBC,EAAclX,GAC9BkX,EAAalW,SAAS,YAAYV,QACrC4W,EAAalW,SAAS,UAAU0C,MAAM,iDAEpCpF,KAAK0K,WAAWkO,IAClB5Y,KAAK2K,oBAAoBiO,EAAalW,SAAS,cAInDoW,iBAAkB,SAAUC,EAAYrX,GACtC,IAAIsX,EAAkBrZ,EAAEsZ,QAAQvX,GAAQA,EAAKM,OAASN,EAAKgB,SAASV,OAChEkX,EAAsBH,EAAWpQ,SAASC,GAAG,UAAYmQ,EAAWlQ,WAAW7G,OAAS,EAAI,EAC5FmX,EAAeD,EAAsBF,EACrCI,EAAgC,EAAfD,EAAoBtR,KAAKwR,MAAMF,EAAa,EAAI,GAAK,EAE1E,GAAIJ,EAAWhX,QAAQ,UAAU4G,SAASC,GAAG,cAAe,CAC1D5I,KAAK0Y,eAAeK,EAAWpQ,SAAS5G,QAAQ,cAAeL,GAC/D,IAAIwK,EAAY6M,EAAWpQ,SAAS5G,QAAQ,cAAcW,SAAS,eAAeA,SAAS,cACjE,EAAtBwW,EACFhN,EAAU1D,GAAG,GAAG8Q,OAAOP,EAAWlQ,WAAW0Q,UAAUC,UAEvDtN,EAAU1D,GAAG4Q,GAAehU,MAAM2T,EAAWS,eAG/CxZ,KAAKwC,eAAeuW,EAAWpQ,SAASuK,QAAQvT,EAAE,2BAA2B+C,SAAS,oBAAqBhB,GAC3GqX,EAAW5M,QAAQ,cAAczJ,SAAS,UAAUA,WAAW8F,GAAG4Q,GAAehU,MAAM2T,IAI3FtK,YAAa,SAAUlF,EAAO7H,GAC5B1B,KAAK8Y,iBAAiBvP,EAAMxH,QAAQ,cAAeL,GACnD6H,EAAMxH,QAAQ,UAAUL,KAAK,kBAAkB,GAC1C6H,EAAM7G,SAAS,aAAaV,QAC/BuH,EAAM7G,SAAS,YAAY0C,MAAM,qGAE/BpF,KAAK0K,WAAWnB,KAClBvJ,KAAKsM,sBAAsB/C,GAC3BA,EAAM7G,SAAS,YAAYR,YAAY,kBAAkBiI,SAAS,sBAItEsP,YAAa,SAAUlQ,GACrB,IAAImQ,EAAWnQ,EAAMxH,QAAQ,cAAc4G,SACvC+Q,EAAS/Q,SAASC,GAAG,cACnB5I,KAAK2J,aAAaJ,EAAO,YAAYQ,OACvCR,EAAMxH,QAAQ,cAAcN,SACO,IAA/BiY,EAAShX,WAAWV,QACtB0X,EAASzX,KAAK,+BAA+BR,UAG/CiY,EAAS7Q,SAAS,SAAS5G,KAAK,eAAeR,SAC5C4K,MAAMA,MAAM5K,SAGjBiY,EAAS3X,QAAQ,aAAaN,UAIlCkY,cAAe,WAEK3Z,KACNwB,OAAOS,KAAK,gBACrBC,YAAY,gBAGjB0X,cAAe,SAAUC,GAGL7Z,KACNwB,OAAOS,KAAK,SACrB6G,KAAK,SAAUoC,EAAOT,GACrB9K,EAAE8K,GAAMN,SAAS,iBAHHnK,KAKNwB,OAAOE,KAAK,UAAW/B,EAAEka,KAGvCC,oBAAqB,SAAUhH,EAAU+G,GAEnCA,GACF7Z,KAAKwB,OAAOE,KAAK,UAAW/B,EAAEka,IAEZ/G,EAAS/Q,QAAQ,SAGvBoR,eAAe,CAAE5Q,KAAQ,UAGzCwX,UAAW,SAASC,EAAQtZ,GAC1B,IAAIuZ,EAAM,GACNC,EAAWrS,KAAKwR,MAAMW,EAAOzD,OAC7B4D,EAAYtS,KAAKwR,MAAMW,EAAOxD,QAC7BhX,EAAO4a,QACV5a,EAAO4a,MAAQ5a,EAAO6a,MAAMD,QAI5BH,EADaE,EAAXD,EACI,IAAIE,MAAM,CACdE,YAAa,YACbC,KAAM,KACNC,OAAQ,CAACN,EAAUC,KAGf,IAAIC,MAAM,CACdE,YAAa,WACbC,KAAM,KACNC,OAAQ,CAACL,EAAWD,MAGpBO,SAAST,EAAOU,YAAa,MAAO,EAAG,GAC3CT,EAAIU,KAAKja,EAAiB,SAG5Bka,UAAW,SAASZ,EAAQtZ,GAC1B,IACIma,EAAW,qBAAsBpb,EAASqb,gBAAgBlN,MAC1DmN,IAASvb,EAAOwb,QAChBC,EAA+B,gCAAtB7K,UAAU8K,SAAoE,aAAtB9K,UAAU8K,UAAkE,EAAxC9K,UAAU+K,WAAWjV,QAAQ,QAClIjG,EAAkBD,KAAKC,gBAE3B,IAAM4a,IAAaE,GAASE,EAC1Bzb,EAAO4Q,UAAUgL,WAAWpB,EAAOqB,WAAY3a,EAAiB,YAC3D,CACL,IAAI4a,EAAW,qBAAmD,KATzDtb,KASkCsB,QAAQf,WAAoB,IAT9DP,KASyEsB,QAAQf,WAAa,IAElGN,EAAgBgC,KAAKqZ,GAAUtZ,QAClC/B,EAAgBqC,OAAO,8BAA4D,KAZ5EtC,KAYqDsB,QAAQf,WAAoB,IAZjFP,KAY4FsB,QAAQf,WAAa,IAAM,eACrFG,EAAiB,cAG5DT,EAAgBgC,KAAKqZ,GAAUnO,KAAK,OAAQ6M,EAAOU,aAAa,GAAG9Y,UAIvEuD,OAAQ,SAAUzE,EAAgBC,GAChC,IAAIU,EAAOrB,KAGX,GAFAU,OAA4C,IAAnBA,EAAmCA,EAAiBV,KAAKsB,QAAQZ,eAC1FC,OAAsD,IAAxBA,EAAwCA,EAAsBX,KAAKsB,QAAQX,oBACrGhB,EAAEK,MAAM0C,SAAS,YAAYV,OAC/B,OAAO,EAET,IAAI/B,EAAkBD,KAAKC,gBACvBsb,EAAQtb,EAAgBgC,KAAK,SAC5BsZ,EAAMvZ,OAGTuZ,EAAMrZ,YAAY,UAFlBjC,EAAgBqC,OAAO,mEAIzB,IAAIkZ,EAAcvb,EAAgBkK,SAAS,mBAAmBlI,KAAK,4BAA4BwJ,IAAI,GAC/FsC,EAAkC,QAA3B1M,EAAKC,QAAQR,WAAkD,QAA3BO,EAAKC,QAAQR,UAC5D2a,YAAYD,EAAa,CACvBjF,MAASxI,EAAOyN,EAAYE,aAAeF,EAAYG,YACvDnF,OAAUzI,EAAOyN,EAAYG,YAAcH,EAAYE,aACvDE,QAAW,SAAUC,GACnBlc,EAAEkc,GAAU5Z,KAAK,oBAAoB2D,IAAI,WAAY,WAClD3D,KAAK,kCAAkC2D,IAAI,YAAa,OAG9DkW,KAAK,SAAU9B,GACd/Z,EAAgBgC,KAAK,SAASkI,SAAS,UAEG,QAAtCxJ,EAAoB2P,cACtBjP,EAAK0Y,UAAUC,EAAQtZ,GAEvBW,EAAKuZ,UAAUZ,EAAQtZ,GAGzBT,EAAgBiC,YAAY,oBAC3B,WACDjC,EAAgBiC,YAAY,uBAKlCvC,EAAEoc,GAAGC,SAAW,SAAUjc,GACxB,OAAO,IAAIF,EAASG,KAAMD,GAAMqB", "file": "jquery.orgchart.min.js", "sourcesContent": ["/*\n * jQ<PERSON>y <PERSON> Plugin\n * https://github.com/dabeng/OrgChart\n *\n * Copyright 2016, dabeng\n * https://github.com/dabeng\n *\n * Licensed under the MIT license:\n * http://www.opensource.org/licenses/MIT\n */\n'use strict';\n\n(function (factory) {\n  if (typeof module === 'object' && typeof module.exports === 'object') {\n    factory(require('jquery'), window, document);\n  } else {\n    factory(jQuery, window, document);\n  }\n}(function ($, window, document, undefined) {\n  var OrgChart = function (elem, opts) {\n    this.$chartContainer = $(elem);\n    this.opts = opts;\n    this.defaultOptions = {\n      'nodeTitle': 'name',\n      'nodeId': 'id',\n      'toggleSiblingsResp': false,\n      'visibleLevel': 999,\n      'chartClass': '',\n      'exportButton': false,\n      'exportButtonName': 'Export',\n      'exportFilename': 'OrgChart',\n      'exportFileextension': 'png',\n      'parentNodeSymbol': 'oci-leader',\n      'draggable': false,\n      'direction': 't2b',\n      'pan': false,\n      'zoom': false,\n      'zoominLimit': 7,\n      'zoomoutLimit': 0.5\n    };\n  };\n  //\n  OrgChart.prototype = {\n    //\n    init: function (opts) {\n      var that = this;\n      this.options = $.extend({}, this.defaultOptions, this.opts, opts);\n      // build the org-chart\n      var $chartContainer = this.$chartContainer;\n      if (this.$chart) {\n        this.$chart.remove();\n      }\n      var data = this.options.data;\n      var $chart = this.$chart = $('<div>', {\n        'data': { 'options': this.options },\n        'class': 'orgchart' + (this.options.chartClass !== '' ? ' ' + this.options.chartClass : '') + (this.options.direction !== 't2b' ? ' ' + this.options.direction : ''),\n        'click': function(event) {\n          if (!$(event.target).closest('.node').length) {\n            $chart.find('.node.focused').removeClass('focused');\n          }\n        }\n      });\n      if (typeof MutationObserver !== 'undefined') {\n        this.triggerInitEvent();\n      }\n      var $root = $chart.append($('<ul class=\"nodes\"><li class=\"hierarchy\"></li></ul>')).find('.hierarchy');\n      if ($.type(data) === 'object') {\n        if (data instanceof $) { // ul datasource\n          this.buildHierarchy($root, this.buildJsonDS(data.children()), 0, this.options);\n        } else { // local json datasource\n          this.buildHierarchy($root, this.options.ajaxURL ? data : this.attachRel(data, '00'));\n        }\n      } else {\n        $chart.append('<i class=\"oci oci-spinner spinner\"></i>');\n        $.ajax({\n          'url': data,\n          'dataType': 'json'\n        })\n        .done(function(data, textStatus, jqXHR) {\n          that.buildHierarchy($root, that.options.ajaxURL ? data : that.attachRel(data, '00'), 0, that.options);\n        })\n        .fail(function(jqXHR, textStatus, errorThrown) {\n          console.log(errorThrown);\n        })\n        .always(function() {\n          $chart.children('.spinner').remove();\n        });\n      }\n      $chartContainer.append($chart);\n\n      // append the export button\n      if (this.options.exportButton && !$('.oc-export-btn').length) {\n        this.attachExportButton();\n      }\n\n      if (this.options.pan) {\n        this.bindPan();\n      }\n\n      if (this.options.zoom) {\n        this.bindZoom();\n      }\n\n      return this;\n    },\n    //\n    triggerInitEvent: function () {\n      var that = this;\n      var mo = new MutationObserver(function (mutations) {\n        mo.disconnect();\n        initTime:\n        for (var i = 0; i < mutations.length; i++) {\n          for (var j = 0; j < mutations[i].addedNodes.length; j++) {\n            if (mutations[i].addedNodes[j].classList.contains('orgchart')) {\n              if (that.options.initCompleted && typeof that.options.initCompleted === 'function') {\n                that.options.initCompleted(that.$chart);\n              }\n              var initEvent = $.Event('init.orgchart');\n              that.$chart.trigger(initEvent);\n              break initTime;\n            }\n          }\n        }\n      });\n      mo.observe(this.$chartContainer[0], { childList: true });\n    },\n    triggerLoadEvent: function ($target, rel) {\n      var initEvent = $.Event('load-' + rel +'.orgchart');\n      $target.trigger(initEvent);\n    },\n    triggerShowEvent: function ($target, rel) {\n      var initEvent = $.Event('show-' + rel + '.orgchart');\n      $target.trigger(initEvent);\n    },\n    triggerHideEvent: function ($target, rel) {\n      var initEvent = $.Event('hide-' + rel + '.orgchart');\n      $target.trigger(initEvent);\n    },\n    // add export button for orgchart\n    attachExportButton: function () {\n      var that = this;\n      var $exportBtn = $('<button>', {\n        'class': 'oc-export-btn',\n        'text': this.options.exportButtonName,\n        'click': function(e) {\n          e.preventDefault();\n          that.export();\n        }\n      });\n      this.$chartContainer.after($exportBtn);\n    },\n    setOptions: function (opts, val) {\n      if (typeof opts === 'string') {\n        if (opts === 'pan') {\n          if (val) {\n            this.bindPan();\n          } else {\n            this.unbindPan();\n          }\n        }\n        if (opts === 'zoom') {\n          if (val) {\n            this.bindZoom();\n          } else {\n            this.unbindZoom();\n          }\n        }\n      }\n      if (typeof opts === 'object') {\n        if (opts.data) {\n          this.init(opts);\n        } else {\n          if (typeof opts.pan !== 'undefined') {\n            if (opts.pan) {\n              this.bindPan();\n            } else {\n              this.unbindPan();\n            }\n          }\n          if (typeof opts.zoom !== 'undefined') {\n            if (opts.zoom) {\n              this.bindZoom();\n            } else {\n              this.unbindZoom();\n            }\n          }\n        }\n      }\n\n      return this;\n    },\n    //\n    panStartHandler: function (e) {\n      var $chart = $(e.delegateTarget);\n      if ($(e.target).closest('.node').length || (e.touches && e.touches.length > 1)) {\n        $chart.data('panning', false);\n        return;\n      } else {\n        $chart.css('cursor', 'move').data('panning', true);\n      }\n      var lastX = 0;\n      var lastY = 0;\n      var lastTf = $chart.css('transform');\n      if (lastTf !== 'none') {\n        var temp = lastTf.split(',');\n        if (lastTf.indexOf('3d') === -1) {\n          lastX = parseInt(temp[4]);\n          lastY = parseInt(temp[5]);\n        } else {\n          lastX = parseInt(temp[12]);\n          lastY = parseInt(temp[13]);\n        }\n      }\n      var startX = 0;\n      var startY = 0;\n      if (!e.targetTouches) { // pand on desktop\n        startX = e.pageX - lastX;\n        startY = e.pageY - lastY;\n      } else if (e.targetTouches.length === 1) { // pan on mobile device\n        startX = e.targetTouches[0].pageX - lastX;\n        startY = e.targetTouches[0].pageY - lastY;\n      } else if (e.targetTouches.length > 1) {\n        return;\n      }\n      $chart.on('mousemove touchmove',function(e) {\n        if (!$chart.data('panning')) {\n          return;\n        }\n        var newX = 0;\n        var newY = 0;\n        if (!e.targetTouches) { // pand on desktop\n          newX = e.pageX - startX;\n          newY = e.pageY - startY;\n        } else if (e.targetTouches.length === 1) { // pan on mobile device\n          newX = e.targetTouches[0].pageX - startX;\n          newY = e.targetTouches[0].pageY - startY;\n        } else if (e.targetTouches.length > 1) {\n          return;\n        }\n        var lastTf = $chart.css('transform');\n        if (lastTf === 'none') {\n          if (lastTf.indexOf('3d') === -1) {\n            $chart.css('transform', 'matrix(1, 0, 0, 1, ' + newX + ', ' + newY + ')');\n          } else {\n            $chart.css('transform', 'matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, ' + newX + ', ' + newY + ', 0, 1)');\n          }\n        } else {\n          var matrix = lastTf.split(',');\n          if (lastTf.indexOf('3d') === -1) {\n            matrix[4] = ' ' + newX;\n            matrix[5] = ' ' + newY + ')';\n          } else {\n            matrix[12] = ' ' + newX;\n            matrix[13] = ' ' + newY;\n          }\n          $chart.css('transform', matrix.join(','));\n        }\n      });\n    },\n    //\n    panEndHandler: function (e) {\n      if (e.data.chart.data('panning')) {\n        e.data.chart.data('panning', false).css('cursor', 'default').off('mousemove');\n      }\n    },\n    //\n    bindPan: function () {\n      this.$chartContainer.css('overflow', 'hidden');\n      this.$chart.on('mousedown touchstart', this.panStartHandler);\n      $(document).on('mouseup touchend', { 'chart': this.$chart }, this.panEndHandler);\n    },\n    //\n    unbindPan: function () {\n      this.$chartContainer.css('overflow', 'auto');\n      this.$chart.off('mousedown touchstart', this.panStartHandler);\n      $(document).off('mouseup touchend', this.panEndHandler);\n    },\n    //\n    zoomWheelHandler: function (e) {\n      var oc = e.data.oc;\n      e.preventDefault();\n      var newScale  = 1 + (e.originalEvent.deltaY > 0 ? -0.2 : 0.2);\n      oc.setChartScale(oc.$chart, newScale);\n    },\n    //\n    zoomStartHandler: function (e) {\n      if(e.touches && e.touches.length === 2) {\n        var oc = e.data.oc;\n        oc.$chart.data('pinching', true);\n        var dist = oc.getPinchDist(e);\n        oc.$chart.data('pinchDistStart', dist);\n      }\n    },\n    zoomingHandler: function (e) {\n      var oc = e.data.oc;\n      if(oc.$chart.data('pinching')) {\n        var dist = oc.getPinchDist(e);\n        oc.$chart.data('pinchDistEnd', dist);\n      }\n    },\n    zoomEndHandler: function (e) {\n      var oc = e.data.oc;\n      if(oc.$chart.data('pinching')) {\n        oc.$chart.data('pinching', false);\n        var diff = oc.$chart.data('pinchDistEnd') - oc.$chart.data('pinchDistStart');\n        if (diff > 0) {\n          oc.setChartScale(oc.$chart, 1.2);\n        } else if (diff < 0) {\n          oc.setChartScale(oc.$chart, 0.8);\n        }\n      }\n    },\n    //\n    bindZoom: function () {\n      this.$chartContainer.on('wheel', { 'oc': this }, this.zoomWheelHandler);\n      this.$chartContainer.on('touchstart', { 'oc': this }, this.zoomStartHandler);\n      $(document).on('touchmove', { 'oc': this }, this.zoomingHandler);\n      $(document).on('touchend', { 'oc': this }, this.zoomEndHandler);\n    },\n    unbindZoom: function () {\n      this.$chartContainer.off('wheel', this.zoomWheelHandler);\n      this.$chartContainer.off('touchstart', this.zoomStartHandler);\n      $(document).off('touchmove', this.zoomingHandler);\n      $(document).off('touchend', this.zoomEndHandler);\n    },\n    //\n    getPinchDist: function (e) {\n      return Math.sqrt((e.touches[0].clientX - e.touches[1].clientX) * (e.touches[0].clientX - e.touches[1].clientX) +\n      (e.touches[0].clientY - e.touches[1].clientY) * (e.touches[0].clientY - e.touches[1].clientY));\n    },\n    //\n    setChartScale: function ($chart, newScale) {\n      var opts = $chart.data('options');\n      var lastTf = $chart.css('transform');\n      var matrix = '';\n      var targetScale = 1;\n      if (lastTf === 'none') {\n        $chart.css('transform', 'scale(' + newScale + ',' + newScale + ')');\n      } else {\n        matrix = lastTf.split(',');\n        if (lastTf.indexOf('3d') === -1) {\n          targetScale = Math.abs(window.parseFloat(matrix[3]) * newScale);\n          if (targetScale > opts.zoomoutLimit && targetScale < opts.zoominLimit) {\n            $chart.css('transform', lastTf + ' scale(' + newScale + ',' + newScale + ')');\n          }\n        } else {\n          targetScale = Math.abs(window.parseFloat(matrix[1]) * newScale);\n          if (targetScale > opts.zoomoutLimit && targetScale < opts.zoominLimit) {\n            $chart.css('transform', lastTf + ' scale3d(' + newScale + ',' + newScale + ', 1)');\n          }\n        }\n      }\n    },\n    //\n    buildJsonDS: function ($li) {\n      var that = this;\n      var subObj = {\n        'name': $li.contents().eq(0).text().trim(),\n        'relationship': ($li.parent().parent().is('li') ? '1': '0') + ($li.siblings('li').length ? 1: 0) + ($li.children('ul').length ? 1 : 0)\n      };\n      $.each($li.data(), function(key, value) {\n         subObj[key] = value;\n      });\n      $li.children('ul').children().each(function() {\n        if (!subObj.children) { subObj.children = []; }\n        subObj.children.push(that.buildJsonDS($(this)));\n      });\n      return subObj;\n    },\n    //\n    attachRel: function (data, flags) {\n      var that = this;\n      data.relationship = flags + (data.children && data.children.length > 0 ? 1 : 0);\n      if (data.children) {\n        data.children.forEach(function(item) {\n          that.attachRel(item, '1' + (data.children.length > 1 ? 1 : 0));\n        });\n      }\n      return data;\n    },\n    //\n    loopChart: function ($chart, includeNodeData) {\n      includeNodeData = (includeNodeData !== null && includeNodeData !== undefined) ? includeNodeData : false;\n      var that = this;\n      var $node = $chart.find('.node:first');\n      var subObj = { 'id': $node[0].id };\n      if (includeNodeData) {\n        $.each($node.data('nodeData'), function (key, value) {\n          subObj[key] = value;\n        });\n      }\n      $node.siblings('.nodes').children().each(function() {\n        if (!subObj.children) { subObj.children = []; }\n        subObj.children.push(that.loopChart($(this), includeNodeData));\n      });\n      return subObj;\n    },\n    //\n    getHierarchy: function (includeNodeData) {\n      includeNodeData = (includeNodeData !== null && includeNodeData !== undefined) ? includeNodeData : false;\n      if (typeof this.$chart === 'undefined') {\n        return 'Error: orgchart does not exist'\n      } else {\n        if (!this.$chart.find('.node').length) {\n          return 'Error: nodes do not exist'\n        } else {\n          var valid = true;\n          this.$chart.find('.node').each(function () {\n            if (!this.id) {\n              valid = false;\n              return false;\n            }\n          });\n          if (!valid) {\n            return 'Error: All nodes of orghcart to be exported must have data-id attribute!';\n          }\n        }\n      }\n      return this.loopChart(this.$chart, includeNodeData);\n    },\n    // detect the exist/display state of related node\n    getNodeState: function ($node, relation) {\n      var $target = {};\n      var isVerticalNode = !!$node.closest('vertical').length;\n      var relation = relation || 'self';\n      if (relation === 'parent') {\n        if (isVerticalNode) {\n          $target = $node.closest('ul').parents('ul');\n          if (!$target.length) {\n            $target = $node.closest('.nodes');\n            if (!$target.length) {\n              $target = $node.closest('.vertical').siblings(':first');\n            }\n          }\n        } else {\n          $target = $node.closest('.nodes').siblings('.node');\n        }\n        if ($target.length) {\n          if ($target.is('.hidden') || (!$target.is('.hidden') && $target.closest('.nodes').is('.hidden')) || (!$target.is('.hidden') && $target.closest('.vertical').is('.hidden'))) {\n            return { 'exist': true, 'visible': false };\n          }\n          return { 'exist': true, 'visible': true };\n        }\n      } else if (relation === 'children') {\n        $target = isVerticalNode ? $node.parent().children('ul') : $node.siblings('.nodes');\n        if ($target.length) {\n          if (!$target.is('.hidden')) {\n            return { 'exist': true, 'visible': true };\n          }\n          return { 'exist': true, 'visible': false };\n        }\n      } else if (relation === 'siblings') {\n        $target = isVerticalNode ? $node.closest('ul') : $node.parent().siblings();\n        if ($target.length && (!isVerticalNode || $target.children('li').length > 1)) {\n          if (!$target.is('.hidden') && !$target.parent().is('.hidden') && (!isVerticalNode || !$target.closest('.vertical').is('.hidden'))) {\n            return { 'exist': true, 'visible': true };\n          }\n          return { 'exist': true, 'visible': false };\n        }\n      } else {\n        $target = $node;\n        if ($target.length) {\n          if (!(($target.closest('.nodes').length && $target.closest('.nodes').is('.hidden')) ||\n            ($target.closest('.hierarchy').length && $target.closest('.hierarchy').is('.hidden')) ||\n            ($target.closest('.vertical').length && ($target.closest('.nodes').is('.hidden') || $target.closest('.vertical').is('.hidden')))\n          )) {\n            return { 'exist': true, 'visible': true };\n          }\n          return { 'exist': true, 'visible': false };\n        }\n      }\n      return { 'exist': false, 'visible': false };\n    },\n    // find the related nodes\n    getRelatedNodes: function ($node, relation) {\n      if (!$node || !($node instanceof $) || !$node.is('.node')) {\n        return $();\n      }\n      if (relation === 'parent') {\n        return $node.closest('.nodes').siblings('.node');\n      } else if (relation === 'children') {\n        return $node.siblings('.nodes').children('.hierarchy').find('.node:first');\n      } else if (relation === 'siblings') {\n        return $node.closest('.hierarchy').siblings().find('.node:first');\n      } else {\n        return $();\n      }\n    },\n    hideParentEnd: function (event) {\n      $(event.target).removeClass('sliding');\n      event.data.parent.addClass('hidden');\n    },\n    // recursively hide the ancestor node and sibling nodes of the specified node\n    hideParent: function ($node) {\n      var $parent = $node.closest('.nodes').siblings('.node');\n      if ($parent.find('.spinner').length) {\n        $node.closest('.orgchart').data('inAjax', false);\n      }\n      // hide the sibling nodes\n      if (this.getNodeState($node, 'siblings').visible) {\n        this.hideSiblings($node);\n      }\n      // hide the lines\n      $node.parent().addClass('isAncestorsCollapsed');\n      // hide the superior nodes with transition\n      if (this.getNodeState($parent).visible) {\n        $parent.addClass('sliding slide-down').one('transitionend', { 'parent': $parent }, this.hideParentEnd);\n      }\n      // if the current node has the parent node, hide it recursively\n      if (this.getNodeState($parent, 'parent').visible) {\n        this.hideParent($parent);\n      }\n    },\n    showParentEnd: function (event) {\n      var $node = event.data.node;\n      $(event.target).removeClass('sliding');\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.topEdge'));\n      }\n    },\n    // show the parent node of the specified node\n    showParent: function ($node) {\n      // just show only one superior level\n      var $parent = $node.closest('.nodes').siblings('.node').removeClass('hidden');\n      // just show only one line\n      $node.closest('.hierarchy').removeClass('isAncestorsCollapsed');\n      // show parent node with animation\n      this.repaint($parent[0]);\n      $parent.addClass('sliding').removeClass('slide-down').one('transitionend', { 'node': $node }, this.showParentEnd.bind(this));\n    },\n    stopAjax: function ($nodeLevel) {\n      if ($nodeLevel.find('.spinner').length) {\n        $nodeLevel.closest('.orgchart').data('inAjax', false);\n      }\n    },\n    isVisibleNode: function (index, elem) {\n      return this.getNodeState($(elem)).visible;\n    },\n    // do some necessary cleanup tasks when hide animation is finished\n    hideChildrenEnd: function (event) {\n      var $node = event.data.node;\n      event.data.animatedNodes.removeClass('sliding');\n      event.data.animatedNodes.closest('.nodes').addClass('hidden');\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.bottomEdge'));\n      }\n    },\n    // recursively hide the descendant nodes of the specified node\n    hideChildren: function ($node) {\n      $node.closest('.hierarchy').addClass('isChildrenCollapsed');\n      var $lowerLevel = $node.siblings('.nodes');\n      this.stopAjax($lowerLevel);\n      var $animatedNodes = $lowerLevel.find('.node').filter(this.isVisibleNode.bind(this));\n      var isVerticalDesc = $lowerLevel.is('.vertical');\n      if (!isVerticalDesc) {\n        $animatedNodes.closest('.hierarchy').addClass('isCollapsedDescendant');\n      }\n      if ($lowerLevel.is('.vertical') || $lowerLevel.find('.vertical').length) {\n        $animatedNodes.find('.oci-minus-square').removeClass('oci-minus-square').addClass('oci-plus-square');\n      }\n      this.repaint($animatedNodes.get(0));\n      $animatedNodes.addClass('sliding slide-up').eq(0).one('transitionend', { 'animatedNodes': $animatedNodes, 'lowerLevel': $lowerLevel, 'node': $node }, this.hideChildrenEnd.bind(this));\n    },\n    //\n    showChildrenEnd: function (event) {\n      var $node = event.data.node;\n      event.data.animatedNodes.removeClass('sliding');\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.bottomEdge'));\n      }\n    },\n    // show the children nodes of the specified node\n    showChildren: function ($node) {\n      var that = this;\n      $node.closest('.hierarchy').removeClass('isChildrenCollapsed');\n      var $levels = $node.siblings('.nodes');\n      var isVerticalDesc = $levels.is('.vertical');\n      var $animatedNodes = isVerticalDesc\n        ? $levels.removeClass('hidden').find('.node').filter(this.isVisibleNode.bind(this))\n        : $levels.removeClass('hidden').children('.hierarchy').find('.node:first').filter(this.isVisibleNode.bind(this));\n      if (!isVerticalDesc) {\n        $animatedNodes.filter(':not(:only-child)').closest('.hierarchy').addClass('isChildrenCollapsed');\n        $animatedNodes.closest('.hierarchy').removeClass('isCollapsedDescendant');\n      }\n      // the two following statements are used to enforce browser to repaint\n      this.repaint($animatedNodes.get(0));\n      $animatedNodes.addClass('sliding').removeClass('slide-up').eq(0).one('transitionend', { 'node': $node, 'animatedNodes': $animatedNodes }, this.showChildrenEnd.bind(this));\n    },\n    //\n    hideSiblingsEnd: function (event) {\n      var $node = event.data.node;\n      var $nodeContainer = event.data.nodeContainer;\n      var direction = event.data.direction;\n      var $siblings = direction ? (direction === 'left' ? $nodeContainer.prevAll(':not(.hidden)') : $nodeContainer.nextAll(':not(.hidden)')) : $nodeContainer.siblings();\n      event.data.animatedNodes.removeClass('sliding');\n      $siblings.find('.node:gt(0)').filter(this.isVisibleNode.bind(this))\n        .removeClass('slide-left slide-right').addClass('slide-up');\n      $siblings.find('.nodes, .vertical').addClass('hidden')\n        .end().addClass('hidden');\n\n      if (this.isInAction($node)) {\n        this.switchHorizontalArrow($node);\n      }\n    },\n    // hide the sibling nodes of the specified node\n    hideSiblings: function ($node, direction) {\n      var that = this;\n      var $nodeContainer = $node.closest('.hierarchy').addClass('isSiblingsCollapsed');\n      if ($nodeContainer.siblings().find('.spinner').length) {\n        $node.closest('.orgchart').data('inAjax', false);\n      }\n      if (direction) {\n        if (direction === 'left') {\n          $nodeContainer.addClass('left-sibs')\n            .prevAll('.isSiblingsCollapsed').removeClass('isSiblingsCollapsed left-sibs').end()\n            .prevAll().addClass('isCollapsedSibling isChildrenCollapsed')\n            .find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-right');\n        } else {\n          $nodeContainer.addClass('right-sibs')\n            .nextAll('.isSiblingsCollapsed').removeClass('isSiblingsCollapsed right-sibs').end()\n            .nextAll().addClass('isCollapsedSibling isChildrenCollapsed')\n            .find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-left');\n        }\n      } else {\n        $nodeContainer.prevAll().find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-right');\n        $nodeContainer.nextAll().find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-left');\n        $nodeContainer.siblings().addClass('isCollapsedSibling isChildrenCollapsed');\n      }\n      var $animatedNodes = $nodeContainer.siblings().find('.sliding');\n      $animatedNodes.eq(0).one('transitionend', { 'node': $node, 'nodeContainer': $nodeContainer, 'direction': direction, 'animatedNodes': $animatedNodes }, this.hideSiblingsEnd.bind(this));\n    },\n    //\n    showSiblingsEnd: function (event) {\n      var $node = event.data.node;\n      event.data.visibleNodes.removeClass('sliding');\n      if (this.isInAction($node)) {\n        this.switchHorizontalArrow($node);\n        $node.children('.topEdge').removeClass('oci-chevron-up').addClass('oci-chevron-down');\n      }\n    },\n    //\n    showRelatedParentEnd: function(event) {\n      $(event.target).removeClass('sliding');\n    },\n    // show the sibling nodes of the specified node\n    showSiblings: function ($node, direction) {\n      var that = this;\n      // firstly, show the sibling nodes\n      var $siblings = $();\n      var $nodeContainer = $node.closest('.hierarchy');\n      if (direction) {\n        if (direction === 'left') {\n          $siblings = $nodeContainer.prevAll().removeClass('hidden');\n        } else {\n          $siblings = $nodeContainer.nextAll().removeClass('hidden');\n        }\n      } else {\n        $siblings = $node.closest('.hierarchy').siblings().removeClass('hidden');\n      }\n      // secondly, show the lines\n      var $upperLevel = $node.closest('.nodes').siblings('.node');\n      if (direction) {\n        $nodeContainer.removeClass(direction + '-sibs');\n        if (!$nodeContainer.is('[class*=-sibs]')) {\n          $nodeContainer.removeClass('isSiblingsCollapsed');\n        }\n        $siblings.removeClass('isCollapsedSibling ' + direction + '-sibs');\n      } else {\n        $node.closest('.hierarchy').removeClass('isSiblingsCollapsed');\n        $siblings.removeClass('isCollapsedSibling');\n      }\n      // thirdly, show parent node if it is collapsed\n      if (!this.getNodeState($node, 'parent').visible) {\n        $node.closest('.hierarchy').removeClass('isAncestorsCollapsed');\n        $upperLevel.removeClass('hidden');\n        this.repaint($upperLevel[0]);\n        $upperLevel.addClass('sliding').removeClass('slide-down').one('transitionend', this.showRelatedParentEnd);\n      }\n      // lastly, show the sibling nodes with animation\n      var $visibleNodes = $siblings.find('.node').filter(this.isVisibleNode.bind(this));\n      this.repaint($visibleNodes.get(0));\n      $visibleNodes.addClass('sliding').removeClass('slide-left slide-right');\n      $visibleNodes.eq(0).one('transitionend', { 'node': $node, 'visibleNodes': $visibleNodes }, this.showSiblingsEnd.bind(this));\n    },\n    // start up loading status for requesting new nodes\n    startLoading: function ($edge) {\n      var $chart = this.$chart;\n      if (typeof $chart.data('inAjax') !== 'undefined' && $chart.data('inAjax') === true) {\n        return false;\n      }\n\n      $edge.addClass('hidden');\n      $edge.parent().append('<i class=\"oci oci-spinner spinner\"></i>')\n        .children().not('.spinner').css('opacity', 0.2);\n      $chart.data('inAjax', true);\n      $('.oc-export-btn').prop('disabled', true);\n      return true;\n    },\n    // terminate loading status for requesting new nodes\n    endLoading: function ($edge) {\n      var $node = $edge.parent();\n      $edge.removeClass('hidden');\n      $node.find('.spinner').remove();\n      $node.children().removeAttr('style');\n      this.$chart.data('inAjax', false);\n      $('.oc-export-btn').prop('disabled', false);\n    },\n    // whether the cursor is hovering over the node\n    isInAction: function ($node) {\n      return $node.children('.edge').attr('class').indexOf('oci-') > -1 ? true : false;\n    },\n    //\n    switchVerticalArrow: function ($arrow) {\n      $arrow.toggleClass('oci-chevron-up').toggleClass('oci-chevron-down');\n    },\n    //\n    switchHorizontalArrow: function ($node) {\n      var opts = this.options;\n      if (opts.toggleSiblingsResp && (typeof opts.ajaxURL === 'undefined' || $node.closest('.nodes').data('siblingsLoaded'))) {\n        var $prevSib = $node.parent().prev();\n        if ($prevSib.length) {\n          if ($prevSib.is('.hidden')) {\n            $node.children('.leftEdge').addClass('oci-chevron-left').removeClass('oci-chevron-right');\n          } else {\n            $node.children('.leftEdge').addClass('oci-chevron-right').removeClass('oci-chevron-left');\n          }\n        }\n        var $nextSib = $node.parent().next();\n        if ($nextSib.length) {\n          if ($nextSib.is('.hidden')) {\n            $node.children('.rightEdge').addClass('oci-chevron-right').removeClass('oci-chevron-left');\n          } else {\n            $node.children('.rightEdge').addClass('oci-chevron-left').removeClass('oci-chevron-right');\n          }\n        }\n      } else {\n        var $sibs = $node.parent().siblings();\n        var sibsVisible = $sibs.length ? !$sibs.is('.hidden') : false;\n        $node.children('.leftEdge').toggleClass('oci-chevron-right', sibsVisible).toggleClass('oci-chevron-left', !sibsVisible);\n        $node.children('.rightEdge').toggleClass('oci-chevron-left', sibsVisible).toggleClass('oci-chevron-right', !sibsVisible);\n      }\n    },\n    //\n    repaint: function (node) {\n      if (node) {\n        node.style.offsetWidth = node.offsetWidth;\n      }\n    },\n    // determines how to show arrow buttons \n    nodeEnterLeaveHandler: function (event) {\n      var $node = $(event.delegateTarget);\n      var flag = false;\n      if ($node.closest('.nodes.vertical').length) {\n        var $toggleBtn = $node.children('.toggleBtn');\n        if (event.type === 'mouseenter') {\n          if ($node.children('.toggleBtn').length) {\n            flag = this.getNodeState($node, 'children').visible;\n            $toggleBtn.toggleClass('oci-plus-square', !flag).toggleClass('oci-minus-square', flag);\n          }\n        } else {\n          $toggleBtn.removeClass('oci-plus-square oci-minus-square');\n        }\n      } else {\n        var $topEdge = $node.children('.topEdge');\n        var $rightEdge = $node.children('.rightEdge');\n        var $bottomEdge = $node.children('.bottomEdge');\n        var $leftEdge = $node.children('.leftEdge');\n        if (event.type === 'mouseenter') {\n          if ($topEdge.length) {\n            flag = this.getNodeState($node, 'parent').visible;\n            $topEdge.toggleClass('oci-chevron-up', !flag).toggleClass('oci-chevron-down', flag);\n          }\n          if ($bottomEdge.length) {\n            flag = this.getNodeState($node, 'children').visible;\n            $bottomEdge.toggleClass('oci-chevron-down', !flag).toggleClass('oci-chevron-up', flag);\n          }\n          if ($leftEdge.length) {\n            this.switchHorizontalArrow($node);\n          }\n        } else {\n          $node.children('.edge').removeClass('oci-chevron-up oci-chevron-down oci-chevron-right oci-chevron-left');\n        }\n      }\n    },\n    //\n    nodeClickHandler: function (event) {\n      this.$chart.find('.focused').removeClass('focused');\n      $(event.delegateTarget).addClass('focused');\n    },\n    // load new nodes by ajax\n    loadNodes: function (rel, url, $edge) {\n      var that = this;\n      var opts = this.options;\n      $.ajax({ 'url': url, 'dataType': 'json' })\n      .done(function (data) {\n        if (that.$chart.data('inAjax')) {\n          if (rel === 'parent') {\n            if (!$.isEmptyObject(data)) {\n              that.addParent($edge.parent(), data);\n            }\n          } else if (rel === 'children') {\n            if (data.children.length) {\n              that.addChildren($edge.parent(), data[rel]);\n            }\n          } else {\n            that.addSiblings($edge.parent(), data.siblings ? data.siblings : data);\n          }\n          that.triggerLoadEvent($edge.parent(), rel);\n        }\n      })\n      .fail(function () {\n        console.log('Failed to get ' + rel + ' data');\n      })\n      .always(function () {\n        that.endLoading($edge);\n      });\n    },\n    //\n    HideFirstParentEnd: function (event) {\n      var $topEdge = event.data.topEdge;\n      var $node = $topEdge.parent();\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($topEdge);\n        this.switchHorizontalArrow($node);\n      }\n    },\n    // actions on clinking top edge of a node\n    topEdgeClickHandler: function (event) {\n      event.stopPropagation();\n      var that = this;\n      var $topEdge = $(event.target);\n      var $node = $(event.delegateTarget);\n      var parentState = this.getNodeState($node, 'parent');\n      if (parentState.exist) {\n        var $parent = $node.closest('.nodes').siblings('.node');\n        if ($parent.is('.sliding')) { return; }\n        // hide the ancestor nodes and sibling nodes of the specified node\n        if (parentState.visible) {\n          this.hideParent($node);\n          $parent.one('transitionend', { 'topEdge': $topEdge }, this.HideFirstParentEnd.bind(this));\n          this.triggerHideEvent($node, 'parent');\n        } else { // show the ancestors and siblings\n          this.showParent($node);\n          this.triggerShowEvent($node, 'parent');\n        }\n      } else { // load the new parent node of the specified node by ajax request\n        // start up loading status\n        if (this.startLoading($topEdge)) {\n          var opts = this.options;\n          var url = $.isFunction(opts.ajaxURL.parent) ? opts.ajaxURL.parent($node.data('nodeData')) : opts.ajaxURL.parent + $node[0].id;\n          this.loadNodes('parent', url, $topEdge);\n        }\n      }\n    },\n    // actions on clinking bottom edge of a node\n    bottomEdgeClickHandler: function (event) {\n      event.stopPropagation();\n      var $bottomEdge = $(event.target);\n      var $node = $(event.delegateTarget);\n      var childrenState = this.getNodeState($node, 'children');\n      if (childrenState.exist) {\n        var $children = $node.siblings('.nodes').children().children('.node');\n        if ($children.is('.sliding')) { return; }\n        // hide the descendant nodes of the specified node\n        if (childrenState.visible) {\n          this.hideChildren($node);\n          this.triggerHideEvent($node, 'children');\n        } else { // show the descendants\n          this.showChildren($node);\n          this.triggerShowEvent($node, 'children');\n        }\n      } else { // load the new children nodes of the specified node by ajax request\n        if (this.startLoading($bottomEdge)) {\n          var opts = this.options;\n          var url = $.isFunction(opts.ajaxURL.children) ? opts.ajaxURL.children($node.data('nodeData')) : opts.ajaxURL.children + $node[0].id;\n          this.loadNodes('children', url, $bottomEdge);\n        }\n      }\n    },\n    // actions on clicking horizontal edges\n    hEdgeClickHandler: function (event) {\n      event.stopPropagation();\n      var $hEdge = $(event.target);\n      var $node = $(event.delegateTarget);\n      var opts = this.options;\n      var siblingsState = this.getNodeState($node, 'siblings');\n      if (siblingsState.exist) {\n        var $siblings = $node.closest('.hierarchy').siblings();\n        if ($siblings.find('.sliding').length) { return; }\n        if (opts.toggleSiblingsResp) {\n          var $prevSib = $node.closest('.hierarchy').prev();\n          var $nextSib = $node.closest('.hierarchy').next();\n          if ($hEdge.is('.leftEdge')) {\n            if ($prevSib.is('.hidden')) {\n              this.showSiblings($node, 'left');\n              this.triggerShowEvent($node,'siblings');\n            } else {\n              this.hideSiblings($node, 'left');\n              this.triggerHideEvent($node, 'siblings');\n            }\n          } else {\n            if ($nextSib.is('.hidden')) {\n              this.showSiblings($node, 'right');\n              this.triggerShowEvent($node,'siblings');\n            } else {\n              this.hideSiblings($node, 'right');\n              this.triggerHideEvent($node, 'siblings');\n            }\n          }\n        } else {\n          if (siblingsState.visible) {\n            this.hideSiblings($node);\n            this.triggerHideEvent($node, 'siblings');\n          } else {\n            this.showSiblings($node);\n            this.triggerShowEvent($node, 'siblings');\n          }\n        }\n      } else {\n        // load the new sibling nodes of the specified node by ajax request\n        if (this.startLoading($hEdge)) {\n          var nodeId = $node[0].id;\n          var url = (this.getNodeState($node, 'parent').exist) ?\n            ($.isFunction(opts.ajaxURL.siblings) ? opts.ajaxURL.siblings($node.data('nodeData')) : opts.ajaxURL.siblings + nodeId) :\n            ($.isFunction(opts.ajaxURL.families) ? opts.ajaxURL.families($node.data('nodeData')) : opts.ajaxURL.families + nodeId);\n          this.loadNodes('siblings', url, $hEdge);\n        }\n      }\n    },\n    //\n    expandVNodesEnd: function (event) {\n      event.data.vNodes.removeClass('sliding');\n    },\n    //\n    collapseVNodesEnd: function (event) {\n      event.data.vNodes.removeClass('sliding').closest('ul').addClass('hidden');\n    },\n    // event handler for toggle buttons in Hybrid(horizontal + vertical) OrgChart\n    toggleVNodes: function (event) {\n      var $toggleBtn = $(event.target);\n      var $descWrapper = $toggleBtn.parent().next();\n      var $descendants = $descWrapper.find('.node');\n      var $children = $descWrapper.children().children('.node');\n      if ($children.is('.sliding')) { return; }\n      $toggleBtn.toggleClass('oci-plus-square oci-minus-square');\n      if ($descendants.eq(0).is('.slide-up')) {\n        $descWrapper.removeClass('hidden');\n        this.repaint($children.get(0));\n        $children.addClass('sliding').removeClass('slide-up').eq(0).one('transitionend', { 'vNodes': $children }, this.expandVNodesEnd);\n      } else {\n        $descendants.addClass('sliding slide-up').eq(0).one('transitionend', { 'vNodes': $descendants }, this.collapseVNodesEnd);\n        $descendants.find('.toggleBtn').removeClass('oci-minus-square').addClass('oci-plus-square');\n      }\n    },\n    //\n    createGhostNode: function (event) {\n      var $nodeDiv = $(event.target);\n      var opts = this.options;\n      var origEvent = event.originalEvent;\n      var isFirefox = /firefox/.test(window.navigator.userAgent.toLowerCase());\n      var ghostNode, nodeCover;\n      if (!document.querySelector('.ghost-node')) {\n        ghostNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n        if (!ghostNode.classList) return;\n        ghostNode.classList.add('ghost-node');\n        nodeCover = document.createElementNS('http://www.w3.org/2000/svg','rect');\n        ghostNode.appendChild(nodeCover);\n        $nodeDiv.closest('.orgchart').append(ghostNode);\n      } else {\n        ghostNode = $nodeDiv.closest('.orgchart').children('.ghost-node').get(0);\n        nodeCover = $(ghostNode).children().get(0);\n      }\n      var transValues = $nodeDiv.closest('.orgchart').css('transform').split(',');\n      var isHorizontal = opts.direction === 't2b' || opts.direction === 'b2t';\n      var scale = Math.abs(window.parseFloat(isHorizontal ? transValues[0].slice(transValues[0].indexOf('(') + 1) : transValues[1]));\n      ghostNode.setAttribute('width', isHorizontal ? $nodeDiv.outerWidth(false) : $nodeDiv.outerHeight(false));\n      ghostNode.setAttribute('height', isHorizontal ? $nodeDiv.outerHeight(false) : $nodeDiv.outerWidth(false));\n      nodeCover.setAttribute('x',5 * scale);\n      nodeCover.setAttribute('y',5 * scale);\n      nodeCover.setAttribute('width', 120 * scale);\n      nodeCover.setAttribute('height', 40 * scale);\n      nodeCover.setAttribute('rx', 4 * scale);\n      nodeCover.setAttribute('ry', 4 * scale);\n      nodeCover.setAttribute('stroke-width', 1 * scale);\n      var xOffset = origEvent.offsetX * scale;\n      var yOffset = origEvent.offsetY * scale;\n      if (opts.direction === 'l2r') {\n        xOffset = origEvent.offsetY * scale;\n        yOffset = origEvent.offsetX * scale;\n      } else if (opts.direction === 'r2l') {\n        xOffset = $nodeDiv.outerWidth(false) - origEvent.offsetY * scale;\n        yOffset = origEvent.offsetX * scale;\n      } else if (opts.direction === 'b2t') {\n        xOffset = $nodeDiv.outerWidth(false) - origEvent.offsetX * scale;\n        yOffset = $nodeDiv.outerHeight(false) - origEvent.offsetY * scale;\n      }\n      if (isFirefox) { // hack for old version of Firefox(< 48.0)\n        nodeCover.setAttribute('fill', 'rgb(255, 255, 255)');\n        nodeCover.setAttribute('stroke', 'rgb(191, 0, 0)');\n        var ghostNodeWrapper = document.createElement('img');\n        ghostNodeWrapper.src = 'data:image/svg+xml;utf8,' + (new XMLSerializer()).serializeToString(ghostNode);\n        origEvent.dataTransfer.setDragImage(ghostNodeWrapper, xOffset, yOffset);\n      } else {\n        // IE/Edge do not support this, so only use it if we can\n        if (origEvent.dataTransfer.setDragImage)\n          origEvent.dataTransfer.setDragImage(ghostNode, xOffset, yOffset);\n      }\n    },\n    //\n    filterAllowedDropNodes: function ($dragged) {\n      var opts = this.options;\n      // what is being dragged?  a node, or something within a node?\n      var draggingNode = $dragged.closest('[draggable]').hasClass('node');\n      var $dragZone = $dragged.closest('.nodes').siblings('.node'); // parent node\n      var $dragHier = $dragged.closest('.hierarchy').find('.node'); // this node, and its children\n      this.$chart.data('dragged', $dragged)\n        .find('.node').each(function (index, node) {\n          if (!draggingNode || $dragHier.index(node) === -1) {\n            if (opts.dropCriteria) {\n              if (opts.dropCriteria($dragged, $dragZone, $(node))) {\n                $(node).addClass('allowedDrop');\n              }\n            } else {\n              $(node).addClass('allowedDrop');\n            }\n          }\n        });\n    },\n    //\n    dragstartHandler: function (event) {\n      event.originalEvent.dataTransfer.setData('text/html', 'hack for firefox');\n      // if users enable zoom or direction options\n      if (this.$chart.css('transform') !== 'none') {\n        this.createGhostNode(event);\n      }\n      this.filterAllowedDropNodes($(event.target));\n    },\n    //\n    dragoverHandler: function (event) {\n      if (!$(event.delegateTarget).is('.allowedDrop')) {\n        event.originalEvent.dataTransfer.dropEffect = 'none';\n      } else {\n        // default action for drag-and-drop of div is not to drop, so preventing default action for nodes which have allowedDrop class\n        //to fix drag and drop on IE and Edge\t\t\n        event.preventDefault();\n      }\n    },\n    //\n    dragendHandler: function (event) {\n      this.$chart.find('.allowedDrop').removeClass('allowedDrop');\n    },\n    // when user drops the node, it will be removed from original parent node and be added to new parent node\n    dropHandler: function (event) {\n      var $dropZone = $(event.delegateTarget);\n      var $dragged = this.$chart.data('dragged');\n\n      // Pass on drops which are not nodes (since they are not our doing)\n      if (!$dragged.hasClass('node')) {\n        this.$chart.triggerHandler({ 'type': 'otherdropped.orgchart', 'draggedItem': $dragged, 'dropZone': $dropZone });\n        return;\n      }\n      \n      if (!$dropZone.hasClass('allowedDrop')) {\n          // We are trying to drop a node into a node which isn't allowed\n          // IE/Edge have a habit of allowing this, so we need our own double-check\n          return;\n      }\n\n      var $dragZone = $dragged.closest('.nodes').siblings('.node');\n      var dropEvent = $.Event('nodedrop.orgchart');\n      this.$chart.trigger(dropEvent, { 'draggedNode': $dragged, 'dragZone': $dragZone, 'dropZone': $dropZone });\n      if (dropEvent.isDefaultPrevented()) {\n        return;\n      }\n      // firstly, deal with the hierarchy of drop zone\n      if (!$dropZone.siblings('.nodes').length) { // if the drop zone is a leaf node\n        $dropZone.append('<i class=\"edge verticalEdge bottomEdge oci\"></i>')\n          .after('<ul class=\"nodes\"></ul>')\n          .siblings('.nodes').append($dragged.find('.horizontalEdge').remove().end().closest('.hierarchy'));\n        if ($dropZone.children('.title').length) {\n          $dropZone.children('.title').prepend('<i class=\"oci '+  this.$chart.data('options').parentNodeSymbol + ' symbol\"></i>');\n        }\n      } else {\n        var horizontalEdges = '<i class=\"edge horizontalEdge rightEdge oci\"></i><i class=\"edge horizontalEdge leftEdge oci\"></i>';\n        if (!$dragged.find('.horizontalEdge').length) {\n          $dragged.append(horizontalEdges);\n        }\n        $dropZone.siblings('.nodes').append($dragged.closest('.hierarchy'));\n        var $dropSibs = $dragged.closest('.hierarchy').siblings().find('.node:first');\n        if ($dropSibs.length === 1) {\n          $dropSibs.append(horizontalEdges);\n        }\n      }\n      // secondly, deal with the hierarchy of dragged node\n      if ($dragZone.siblings('.nodes').children('.hierarchy').length === 1) { // if there is only one sibling node left\n        $dragZone.siblings('.nodes').children('.hierarchy').find('.node:first')\n          .find('.horizontalEdge').remove();\n      } else if ($dragZone.siblings('.nodes').children('.hierarchy').length === 0) {\n        $dragZone.find('.bottomEdge, .symbol').remove()\n          .end().siblings('.nodes').remove();\n      }\n    },\n    //\n    touchstartHandler: function (event) {\n      if (this.touchHandled)\n        return;\n\n      if (event.touches && event.touches.length > 1)\n        return;\n\n      this.touchHandled = true;\n      this.touchMoved = false; // this is so we can work out later if this was a 'press' or a 'drag' touch\n      event.preventDefault();\n    },\n    //\n    touchmoveHandler: function (event) {\n      if (!this.touchHandled)\n        return;\n\n      if (event.touches && event.touches.length > 1)\n        return;\n\n      event.preventDefault();\n\n      if (!this.touchMoved) {\n        // we do not bother with createGhostNode (dragstart does) since the touch event does not have a dataTransfer property\n        this.filterAllowedDropNodes($(event.currentTarget));  // will also set 'this.$chart.data('dragged')' for us\n        // create an image which can be used to illustrate the drag (our own createGhostNode)\n        this.touchDragImage = this.createDragImage(event, this.$chart.data('dragged')[0]);\n      }\n      this.touchMoved = true;\n\n      // move our dragimage so it follows our finger\n      this.moveDragImage(event, this.touchDragImage);\n\n      var $touching = $(document.elementFromPoint(event.touches[0].clientX, event.touches[0].clientY));\n      var $touchingNodes = $touching.closest('div.node');\n      if ($touchingNodes.length > 0) {\n        var touchingNodeElement = $touchingNodes[0];\n        if ($touchingNodes.is('.allowedDrop')) {\n          this.touchTargetNode = touchingNodeElement;\n        }\n        else {\n          this.touchTargetNode = null;\n        }\n      }\n      else {\n        this.touchTargetNode = null;\n      }\n    },\n    //\n    touchendHandler: function (event) {\n      if (!this.touchHandled) {\n          return;\n      }\n      this.destroyDragImage();\n      if (this.touchMoved) {\n          // we've had movement, so this was a 'drag' touch\n          if (this.touchTargetNode) {\n              var fakeEventForDropHandler = { delegateTarget: this.touchTargetNode };\n              this.dropHandler(fakeEventForDropHandler);\n              this.touchTargetNode = null;\n          }\n          this.dragendHandler(event);\n      }\n      else {\n          // we did not move, so this was a 'press' touch (fake a click)\n          var firstTouch = event.changedTouches[0];\n          var fakeMouseClickEvent = document.createEvent('MouseEvents');\n          fakeMouseClickEvent.initMouseEvent('click', true, true, window, 1, firstTouch.screenX, firstTouch.screenY, firstTouch.clientX, firstTouch.clientY, event.ctrlKey, event.altKey, event.shiftKey, event.metaKey, 0, null);\n          event.target.dispatchEvent(fakeMouseClickEvent);\n      }\n      this.touchHandled = false;\n    },\n    //\n    createDragImage: function (event, source) {\n      var dragImage = source.cloneNode(true);\n      this.copyStyle(source, dragImage);\n      dragImage.style.top = dragImage.style.left = '-9999px';\n      var sourceRectangle = source.getBoundingClientRect();\n      var sourcePoint = this.getTouchPoint(event);\n      this.touchDragImageOffset = { x: sourcePoint.x - sourceRectangle.left, y: sourcePoint.y - sourceRectangle.top };\n      dragImage.style.opacity = '0.5';\n      document.body.appendChild(dragImage);\n      return dragImage;\n    },\n    //\n    destroyDragImage: function () {\n      if (this.touchDragImage && this.touchDragImage.parentElement)\n        this.touchDragImage.parentElement.removeChild(this.touchDragImage);\n      this.touchDragImageOffset = null;\n      this.touchDragImage = null;\n    },\n    //\n    copyStyle: function (src, dst) {\n      // remove potentially troublesome attributes\n      var badAttributes = ['id', 'class', 'style', 'draggable'];\n      badAttributes.forEach(function (att) {\n          dst.removeAttribute(att);\n      });\n      // copy canvas content\n      if (src instanceof HTMLCanvasElement) {\n        var cSrc = src, cDst = dst;\n        cDst.width = cSrc.width;\n        cDst.height = cSrc.height;\n        cDst.getContext('2d').drawImage(cSrc, 0, 0);\n      }\n      // copy style (without transitions)\n      var cs = getComputedStyle(src);\n      for (var i = 0; i < cs.length; i++) {\n        var key = cs[i];\n        if (key.indexOf('transition') < 0) {\n          dst.style[key] = cs[key];\n        }\n      }\n      dst.style.pointerEvents = 'none';\n      // and repeat for all children\n      for (var i = 0; i < src.children.length; i++) {\n        this.copyStyle(src.children[i], dst.children[i]);\n      }\n    },\n    //\n    getTouchPoint: function (event) {\n      if (event && event.touches) {\n        event = event.touches[0];\n      }\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    },\n    //\n    moveDragImage: function (event, image) {\n      if (!event || !image)\n        return;\n      var orgChartMaster = this;\n      requestAnimationFrame(function () {\n        var pt = orgChartMaster.getTouchPoint(event);\n        var s = image.style;\n        s.position = 'absolute';\n        s.pointerEvents = 'none';\n        s.zIndex = '999999';\n        if (orgChartMaster.touchDragImageOffset) {\n            s.left = Math.round(pt.x - orgChartMaster.touchDragImageOffset.x) + 'px';\n            s.top = Math.round(pt.y - orgChartMaster.touchDragImageOffset.y) + 'px';\n        }\n      });\n    },\n    //\n    bindDragDrop: function ($node) {\n      $node.on('dragstart', this.dragstartHandler.bind(this))\n        .on('dragover', this.dragoverHandler.bind(this))\n        .on('dragend', this.dragendHandler.bind(this))\n        .on('drop', this.dropHandler.bind(this))\n        .on('touchstart', this.touchstartHandler.bind(this))\n        .on('touchmove', this.touchmoveHandler.bind(this))\n        .on('touchend', this.touchendHandler.bind(this));\n    },\n    // create node\n    createNode: function (data) {\n      var that = this;\n      var opts = this.options;\n      var level = data.level;\n      if (data.children && data[opts.nodeId]) {\n        $.each(data.children, function (index, child) {\n          child.parentId = data[opts.nodeId]\n        });\n      }\n      // construct the content of node\n      var $nodeDiv = $('<div' + (opts.draggable ? ' draggable=\"true\"' : '') + (data[opts.nodeId] ? ' id=\"' + data[opts.nodeId] + '\"' : '') + (data.parentId ? ' data-parent=\"' + data.parentId + '\"' : '') + '>')\n        .addClass('node ' + (data.className || '') +  (level > opts.visibleLevel ? ' slide-up' : ''));\n      if (opts.nodeTemplate) {\n        $nodeDiv.append(opts.nodeTemplate(data));\n      } else {\n        $nodeDiv.append('<div class=\"title\">' + data[opts.nodeTitle] + '</div>')\n          .append(typeof opts.nodeContent !== 'undefined' ? '<div class=\"content\">' + (data[opts.nodeContent] || '') + '</div>' : '');\n      }\n      //\n      var nodeData = $.extend({}, data);\n      delete nodeData.children;\n      $nodeDiv.data('nodeData', nodeData);\n      // append 4 direction arrows or expand/collapse buttons\n      var flags = data.relationship || '';\n      if (opts.verticalLevel && level >= opts.verticalLevel) {\n        if ((level + 1) > opts.verticalLevel && Number(flags.substr(2,1))) {\n          $nodeDiv.append('<i class=\"toggleBtn oci\"></i>')\n            .children('.title').prepend('<i class=\"oci '+ opts.parentNodeSymbol + ' symbol\"></i>');\n        }\n      } else {\n        if (Number(flags.substr(0,1))) {\n          $nodeDiv.append('<i class=\"edge verticalEdge topEdge oci\"></i>');\n        }\n        if(Number(flags.substr(1,1))) {\n          $nodeDiv.append('<i class=\"edge horizontalEdge rightEdge oci\"></i>' +\n            '<i class=\"edge horizontalEdge leftEdge oci\"></i>');\n        }\n        if(Number(flags.substr(2,1))) {\n          $nodeDiv.append('<i class=\"edge verticalEdge bottomEdge oci\"></i>')\n            .children('.title').prepend('<i class=\"oci '+ opts.parentNodeSymbol + ' symbol\"></i>');\n        }\n      }\n\n      $nodeDiv.on('mouseenter mouseleave', this.nodeEnterLeaveHandler.bind(this));\n      $nodeDiv.on('click', this.nodeClickHandler.bind(this));\n      $nodeDiv.on('click', '.topEdge', this.topEdgeClickHandler.bind(this));\n      $nodeDiv.on('click', '.bottomEdge', this.bottomEdgeClickHandler.bind(this));\n      $nodeDiv.on('click', '.leftEdge, .rightEdge', this.hEdgeClickHandler.bind(this));\n      $nodeDiv.on('click', '.toggleBtn', this.toggleVNodes.bind(this));\n\n      if (opts.draggable) {\n        this.bindDragDrop($nodeDiv);\n        this.touchHandled = false;\n        this.touchMoved = false;\n        this.touchTargetNode = null;\n      }\n      // allow user to append dom modification after finishing node create of orgchart\n      if (opts.createNode) {\n        opts.createNode($nodeDiv, data);\n      }\n\n      return $nodeDiv;\n    },\n    // recursively build the tree\n    buildHierarchy: function ($appendTo, data) {\n      var that = this;\n      var opts = this.options;\n      var level = 0;\n      if (data.level) {\n        level = data.level;\n      } else {\n        level = data.level = $appendTo.parentsUntil('.orgchart', '.nodes').length;\n      }\n      // Construct the node\n      if (Object.keys(data).length > 2) {\n        var $nodeDiv = this.createNode(data);\n        if (opts.verticalLevel && level >= opts.verticalLevel) {\n          $appendTo.append($nodeDiv);\n        } else {\n          $appendTo.append($nodeDiv);\n        }\n      }\n      // Construct the \"inferior nodes\"\n      if (data.children && data.children.length) {\n        var isHidden = level + 1 > opts.visibleLevel || (data.collapsed !== undefined && data.collapsed);\n        var isVerticalLayer = opts.verticalLevel && (level + 1) >= opts.verticalLevel;\n        var $nodesLayer;\n        if (isVerticalLayer) {\n          $nodesLayer = $('<ul class=\"nodes\">');\n          if (isHidden && level + 1 >= opts.verticalLevel) {\n            $nodesLayer.addClass('hidden');\n          }\n          if (level + 1 === opts.verticalLevel) {\n            $appendTo.addClass('hybrid').append($nodesLayer.addClass('vertical'));\n          } else {\n            $appendTo.append($nodesLayer);\n          }\n        } else {\n          $nodesLayer = $('<ul class=\"nodes' + (isHidden ? ' hidden' : '') + '\">');\n          if (Object.keys(data).length === 2) {\n            $appendTo.append($nodesLayer);\n          } else {\n            if (isHidden) {\n              $appendTo.addClass('isChildrenCollapsed');\n            }\n            $appendTo.append($nodesLayer);\n          }\n        }\n        // recurse through children nodes\n        $.each(data.children, function () {\n          var $nodeCell = $('<li class=\"hierarchy\">');\n          $nodesLayer.append($nodeCell);\n          this.level = level + 1;\n          that.buildHierarchy($nodeCell, this);\n        });\n      }\n    },\n    // build the child nodes of specific node\n    buildChildNode: function ($appendTo, data) {\n      this.buildHierarchy($appendTo, { 'children': data });\n    },\n    // exposed method\n    addChildren: function ($node, data) {\n      this.buildChildNode($node.closest('.hierarchy'), data);\n      if (!$node.find('.symbol').length) {\n        $node.children('.title').prepend('<i class=\"oci '+ this.options.parentNodeSymbol + ' symbol\"></i>');\n      }\n      if ($node.closest('.nodes.vertical').length) {\n        if (!$node.children('.toggleBtn').length) {\n          $node.append('<i class=\"toggleBtn oci\"></i>');\n        }\n      } else {\n        if (!$node.children('.bottomEdge').length) {\n          $node.append('<i class=\"edge verticalEdge bottomEdge oci\"></i>');\n        }\n      }\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.bottomEdge'));\n      }\n    },\n    // build the parent node of specific node\n    buildParentNode: function ($currentRoot, data) {\n      data.relationship = data.relationship || '001';\n      var $newRootWrapper = $('<ul class=\"nodes\"><li class=\"hierarchy\"></li></ul>')\n        .find('.hierarchy').append(this.createNode(data)).end();\n      this.$chart.prepend($newRootWrapper)\n        .find('.hierarchy:first').append($currentRoot.closest('ul').addClass('nodes'));\n    },\n    // exposed method\n    addParent: function ($currentRoot, data) {\n      this.buildParentNode($currentRoot, data);\n      if (!$currentRoot.children('.topEdge').length) {\n        $currentRoot.children('.title').after('<i class=\"edge verticalEdge topEdge oci\"></i>');\n      }\n      if (this.isInAction($currentRoot)) {\n        this.switchVerticalArrow($currentRoot.children('.topEdge'));\n      }\n    },\n    // build the sibling nodes of specific node\n    buildSiblingNode: function ($nodeChart, data) {\n      var newSiblingCount = $.isArray(data) ? data.length : data.children.length;\n      var existingSibligCount = $nodeChart.parent().is('.nodes') ? $nodeChart.siblings().length + 1 : 1;\n      var siblingCount = existingSibligCount + newSiblingCount;\n      var insertPostion = (siblingCount > 1) ? Math.floor(siblingCount/2 - 1) : 0;\n      // just build the sibling nodes for the specific node\n      if ($nodeChart.closest('.nodes').parent().is('.hierarchy')) {\n        this.buildChildNode($nodeChart.parent().closest('.hierarchy'), data);\n        var $siblings = $nodeChart.parent().closest('.hierarchy').children('.nodes:last').children('.hierarchy');\n        if (existingSibligCount > 1) {\n          $siblings.eq(0).before($nodeChart.siblings().addBack().unwrap());\n        } else {\n          $siblings.eq(insertPostion).after($nodeChart.unwrap());\n        }\n      } else { // build the sibling nodes and parent node for the specific ndoe\n        this.buildHierarchy($nodeChart.parent().prepend($('<li class=\"hierarchy\">')).children('.hierarchy:first'), data);\n        $nodeChart.prevAll('.hierarchy').children('.nodes').children().eq(insertPostion).after($nodeChart);\n      }\n    },\n    //\n    addSiblings: function ($node, data) {\n      this.buildSiblingNode($node.closest('.hierarchy'), data);\n      $node.closest('.nodes').data('siblingsLoaded', true);\n      if (!$node.children('.leftEdge').length) {\n        $node.children('.topEdge').after('<i class=\"edge horizontalEdge rightEdge oci\"></i><i class=\"edge horizontalEdge leftEdge oci\"></i>');\n      }\n      if (this.isInAction($node)) {\n        this.switchHorizontalArrow($node);\n        $node.children('.topEdge').removeClass('oci-chevron-up').addClass('oci-chevron-down');\n      }\n    },\n    // remove node and its descendent nodes\n    removeNodes: function ($node) {\n      var $wrapper = $node.closest('.hierarchy').parent();\n      if ($wrapper.parent().is('.hierarchy')) {\n        if (this.getNodeState($node, 'siblings').exist) {\n          $node.closest('.hierarchy').remove();\n          if ($wrapper.children().length === 1) {\n            $wrapper.find('.node:first .horizontalEdge').remove();\n          }\n        } else {\n          $wrapper.siblings('.node').find('.bottomEdge').remove()\n            .end().end().remove();\n        }\n      } else { // if $node is root node\n        $wrapper.closest('.orgchart').remove();\n      }\n    },\n    //\n    hideDropZones: function () {\n      // Remove all the 'this is a drop zone' indicators\n      var orgChartObj = this;\n      orgChartObj.$chart.find('.allowedDrop')\n        .removeClass('allowedDrop');\n    },\n    //\n    showDropZones: function (dragged) {\n      // Highlight all the 'drop zones', and set dragged, so that the drop/enter can work out what happens later\n      // TODO: This assumes all nodes are droppable: it doesn't run the custom isDroppable function - it should!\n      var orgChartObj = this;\n      orgChartObj.$chart.find('.node')\n        .each(function (index, node) {\n          $(node).addClass('allowedDrop');\n        });\n      orgChartObj.$chart.data('dragged', $(dragged));\n    },\n    //\n    processExternalDrop: function (dropZone, dragged) {\n      // Allow an external drop event to be handled by one of our nodes\n      if (dragged) {\n        this.$chart.data('dragged', $(dragged));\n      }\n      var droppedOnNode = dropZone.closest('.node');\n      // would like to just call 'dropZoneHandler', but I can't reach it from here\n      // instead raise a drop event on the node element\n      droppedOnNode.triggerHandler({ 'type': 'drop' });\n    },\n    //\n    exportPDF: function(canvas, exportFilename){\n      var doc = {};\n      var docWidth = Math.floor(canvas.width);\n      var docHeight = Math.floor(canvas.height);\n      if (!window.jsPDF) {\n        window.jsPDF = window.jspdf.jsPDF;\n      }\n\n      if (docWidth > docHeight) {\n        doc = new jsPDF({\n          orientation: 'landscape',\n          unit: 'px',\n          format: [docWidth, docHeight]\n        });\n      } else {\n        doc = new jsPDF({\n          orientation: 'portrait',\n          unit: 'px',\n          format: [docHeight, docWidth]\n        });\n      }\n      doc.addImage(canvas.toDataURL(), 'png', 0, 0);\n      doc.save(exportFilename + '.pdf');\n    },\n    //\n    exportPNG: function(canvas, exportFilename){\n      var that = this;\n      var isWebkit = 'WebkitAppearance' in document.documentElement.style;\n      var isFf = !!window.sidebar;\n      var isEdge = navigator.appName === 'Microsoft Internet Explorer' || (navigator.appName === \"Netscape\" && navigator.appVersion.indexOf('Edge') > -1);\n      var $chartContainer = this.$chartContainer;\n\n      if ((!isWebkit && !isFf) || isEdge) {\n        window.navigator.msSaveBlob(canvas.msToBlob(), exportFilename + '.png');\n      } else {\n        var selector = '.oci-download-btn' + (that.options.chartClass !== '' ? '.' + that.options.chartClass : '');\n\n        if (!$chartContainer.find(selector).length) {\n          $chartContainer.append('<a class=\"oci-download-btn' + (that.options.chartClass !== '' ? ' ' + that.options.chartClass : '') + '\"'\n                                 + ' download=\"' + exportFilename + '.png\"></a>');\n        }\n\n        $chartContainer.find(selector).attr('href', canvas.toDataURL())[0].click();\n      }\n    },\n    //\n    export: function (exportFilename, exportFileextension) {\n      var that = this;\n      exportFilename = (typeof exportFilename !== 'undefined') ?  exportFilename : this.options.exportFilename;\n      exportFileextension = (typeof exportFileextension !== 'undefined') ?  exportFileextension : this.options.exportFileextension;\n      if ($(this).children('.spinner').length) {\n        return false;\n      }\n      var $chartContainer = this.$chartContainer;\n      var $mask = $chartContainer.find('.mask');\n      if (!$mask.length) {\n        $chartContainer.append('<div class=\"mask\"><i class=\"oci oci-spinner spinner\"></i></div>');\n      } else {\n        $mask.removeClass('hidden');\n      }\n      var sourceChart = $chartContainer.addClass('canvasContainer').find('.orgchart:not(\".hidden\")').get(0);\n      var flag = that.options.direction === 'l2r' || that.options.direction === 'r2l';\n      html2canvas(sourceChart, {\n        'width': flag ? sourceChart.clientHeight : sourceChart.clientWidth,\n        'height': flag ? sourceChart.clientWidth : sourceChart.clientHeight,\n        'onclone': function (cloneDoc) {\n          $(cloneDoc).find('.canvasContainer').css('overflow', 'visible')\n            .find('.orgchart:not(\".hidden\"):first').css('transform', '');\n        }\n      })\n      .then(function (canvas) {\n        $chartContainer.find('.mask').addClass('hidden');\n\n        if (exportFileextension.toLowerCase() === 'pdf') {\n          that.exportPDF(canvas, exportFilename);\n        } else {\n          that.exportPNG(canvas, exportFilename);\n        }\n\n        $chartContainer.removeClass('canvasContainer');\n      }, function () {\n        $chartContainer.removeClass('canvasContainer');\n      });\n    }\n  };\n\n  $.fn.orgchart = function (opts) {\n    return new OrgChart(this, opts).init();\n  };\n\n}));\n"]}