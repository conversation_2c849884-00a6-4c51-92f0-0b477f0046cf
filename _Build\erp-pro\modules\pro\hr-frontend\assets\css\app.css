/**
 * Default
 */
/**
 * B<PERSON>ma
 */
/**
 * Bootstrap
 */
.vue-switcher {
  position: relative;
  display: inline-block;
}
.vue-switcher__label {
  display: block;
  font-size: 10px;
  margin-bottom: 5px;
}
.vue-switcher input {
  opacity: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  cursor: pointer;
}
.vue-switcher div {
  height: 15px;
  width: 36px;
  position: relative;
  border-radius: 30px;
  display: -webkit-flex;
  display: -ms-flex;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  transition: linear .2s, background-color linear .2s;
}
.vue-switcher div:after {
  content: '';
  height: 20px;
  width: 20px;
  border-radius: 100px;
  display: block;
  transition: linear .15s, background-color linear .15s;
  position: absolute;
  left: 100%;
  margin-left: -18px;
  cursor: pointer;
  top: -3px;
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);
}
.vue-switcher--unchecked div {
  justify-content: flex-end;
}
.vue-switcher--unchecked div:after {
  left: 15px;
}
.vue-switcher--disabled div {
  opacity: .3;
}
.vue-switcher--disabled input {
  cursor: not-allowed;
}
.vue-switcher--bold div {
  top: -8px;
  height: 26px;
  width: 51px;
}
.vue-switcher--bold div:after {
  margin-left: -24px;
  top: 3px;
}
.vue-switcher--bold--unchecked div:after {
  left: 28px;
}
.vue-switcher--bold .vue-switcher__label span {
  padding-bottom: 7px;
  display: inline-block;
}
.vue-switcher-theme--default.vue-switcher-color--default div {
  background-color: #b7b7b7;
}
.vue-switcher-theme--default.vue-switcher-color--default div:after {
  background-color: #9d9d9d;
}
.vue-switcher-theme--default.vue-switcher-color--default.vue-switcher--unchecked div {
  background-color: #aaa;
}
.vue-switcher-theme--default.vue-switcher-color--default.vue-switcher--unchecked div:after {
  background-color: #c4c4c4;
}
.vue-switcher-theme--default.vue-switcher-color--blue div {
  background-color: #77b0c8;
}
.vue-switcher-theme--default.vue-switcher-color--blue div:after {
  background-color: #539bb9;
}
.vue-switcher-theme--default.vue-switcher-color--blue.vue-switcher--unchecked div {
  background-color: #c0dae5;
}
.vue-switcher-theme--default.vue-switcher-color--blue.vue-switcher--unchecked div:after {
  background-color: #77b0c8;
}
.vue-switcher-theme--default.vue-switcher-color--red div {
  background-color: #c87777;
}
.vue-switcher-theme--default.vue-switcher-color--red div:after {
  background-color: #b95353;
}
.vue-switcher-theme--default.vue-switcher-color--red.vue-switcher--unchecked div {
  background-color: #e5c0c0;
}
.vue-switcher-theme--default.vue-switcher-color--red.vue-switcher--unchecked div:after {
  background-color: #c87777;
}
.vue-switcher-theme--default.vue-switcher-color--yellow div {
  background-color: #c9c377;
}
.vue-switcher-theme--default.vue-switcher-color--yellow div:after {
  background-color: #bab353;
}
.vue-switcher-theme--default.vue-switcher-color--yellow.vue-switcher--unchecked div {
  background-color: #e6e3c0;
}
.vue-switcher-theme--default.vue-switcher-color--yellow.vue-switcher--unchecked div:after {
  background-color: #c9c377;
}
.vue-switcher-theme--default.vue-switcher-color--orange div {
  background-color: #c89577;
}
.vue-switcher-theme--default.vue-switcher-color--orange div:after {
  background-color: #b97953;
}
.vue-switcher-theme--default.vue-switcher-color--orange.vue-switcher--unchecked div {
  background-color: #e5cec0;
}
.vue-switcher-theme--default.vue-switcher-color--orange.vue-switcher--unchecked div:after {
  background-color: #c89577;
}
.vue-switcher-theme--default.vue-switcher-color--green div {
  background-color: #77c88d;
}
.vue-switcher-theme--default.vue-switcher-color--green div:after {
  background-color: #53b96e;
}
.vue-switcher-theme--default.vue-switcher-color--green.vue-switcher--unchecked div {
  background-color: #c0e5ca;
}
.vue-switcher-theme--default.vue-switcher-color--green.vue-switcher--unchecked div:after {
  background-color: #77c88d;
}
.vue-switcher-theme--bulma.vue-switcher-color--default div {
  background-color: gainsboro;
}
.vue-switcher-theme--bulma.vue-switcher-color--default div:after {
  background-color: #f5f5f5;
}
.vue-switcher-theme--bulma.vue-switcher-color--default.vue-switcher--unchecked div {
  background-color: #e8e8e8;
}
.vue-switcher-theme--bulma.vue-switcher-color--default.vue-switcher--unchecked div:after {
  background-color: #f5f5f5;
}
.vue-switcher-theme--bulma.vue-switcher-color--primary div {
  background-color: #05ffda;
}
.vue-switcher-theme--bulma.vue-switcher-color--primary div:after {
  background-color: #00d1b2;
}
.vue-switcher-theme--bulma.vue-switcher-color--primary.vue-switcher--unchecked div {
  background-color: #6bffe9;
}
.vue-switcher-theme--bulma.vue-switcher-color--primary.vue-switcher--unchecked div:after {
  background-color: #05ffda;
}
.vue-switcher-theme--bulma.vue-switcher-color--blue div {
  background-color: #5e91e3;
}
.vue-switcher-theme--bulma.vue-switcher-color--blue div:after {
  background-color: #3273dc;
}
.vue-switcher-theme--bulma.vue-switcher-color--blue.vue-switcher--unchecked div {
  background-color: #b5ccf2;
}
.vue-switcher-theme--bulma.vue-switcher-color--blue.vue-switcher--unchecked div:after {
  background-color: #5e91e3;
}
.vue-switcher-theme--bulma.vue-switcher-color--red div {
  background-color: #ff6b89;
}
.vue-switcher-theme--bulma.vue-switcher-color--red div:after {
  background-color: #ff3860;
}
.vue-switcher-theme--bulma.vue-switcher-color--red.vue-switcher--unchecked div {
  background-color: #ffd1da;
}
.vue-switcher-theme--bulma.vue-switcher-color--red.vue-switcher--unchecked div:after {
  background-color: #ff6b89;
}
.vue-switcher-theme--bulma.vue-switcher-color--yellow div {
  background-color: #ffe78a;
}
.vue-switcher-theme--bulma.vue-switcher-color--yellow div:after {
  background-color: #ffdd57;
}
.vue-switcher-theme--bulma.vue-switcher-color--yellow.vue-switcher--unchecked div {
  background-color: #fffcf0;
}
.vue-switcher-theme--bulma.vue-switcher-color--yellow.vue-switcher--unchecked div:after {
  background-color: #ffe78a;
}
.vue-switcher-theme--bulma.vue-switcher-color--green div {
  background-color: #3dde75;
}
.vue-switcher-theme--bulma.vue-switcher-color--green div:after {
  background-color: #22c65b;
}
.vue-switcher-theme--bulma.vue-switcher-color--green.vue-switcher--unchecked div {
  background-color: #94edb3;
}
.vue-switcher-theme--bulma.vue-switcher-color--green.vue-switcher--unchecked div:after {
  background-color: #3dde75;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--default div {
  background-color: #e6e6e6;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--default div:after {
  background-color: #f0f0f0;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--default.vue-switcher--unchecked div {
  background-color: whitesmoke;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--default.vue-switcher--unchecked div:after {
  background-color: #f0f0f0;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--primary div {
  background-color: #4f93ce;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--primary div:after {
  background-color: #337ab7;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--primary.vue-switcher--unchecked div {
  background-color: #9fc4e4;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--primary.vue-switcher--unchecked div:after {
  background-color: #4f93ce;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--success div {
  background-color: #80c780;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--success div:after {
  background-color: #5cb85c;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--success.vue-switcher--unchecked div {
  background-color: #c7e6c7;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--success.vue-switcher--unchecked div:after {
  background-color: #80c780;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--info div {
  background-color: #85d0e7;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--info div:after {
  background-color: #5bc0de;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--info.vue-switcher--unchecked div {
  background-color: #daf1f8;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--info.vue-switcher--unchecked div:after {
  background-color: #85d0e7;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--warning div {
  background-color: #f4c37d;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--warning div:after {
  background-color: #f0ad4e;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--warning.vue-switcher--unchecked div {
  background-color: #fceedb;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--warning.vue-switcher--unchecked div:after {
  background-color: #f4c37d;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--danger div {
  background-color: #d9534f;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--danger div:after {
  background-color: #c9302c;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--danger.vue-switcher--unchecked div {
  background-color: #eba5a3;
}
.vue-switcher-theme--bootstrap.vue-switcher-color--danger.vue-switcher--unchecked div:after {
  background-color: #d9534f;
}

.announce-details[data-v-47aab902] {
  width: 450px;
  left: -450px;
  padding: 30px;
  background: #fff;
  border: 1px solid #E0E3EC;
  box-shadow: inset 1px 1px 18px 1px #F5F6F8;
  border-radius: 4px;
  height: 100vh;
}
.announce-details .icon[data-v-47aab902] {
  top: 10px;
  right: 10px;
  color: #D9DCE7;
  font-size: 16px;
  cursor: pointer;
}
.details-header[data-v-47aab902] {
  font-size: 15px;
  font-weight: bold;
  color: #1158C6;
}
.details-content p[data-v-47aab902] {
  padding-top: 22px;
  text-align: justify;
  font-size: 13px;
  color: #AFBACF;
  line-height: 22px;
}
.details-footer[data-v-47aab902] {
  box-shadow: 1px 1px 18px 1px #DBE7F7;
  padding: 15px;
  margin-top: 25px;
  border-radius: 50px;
  display: flex;
  justify-content: space-around;
}
.details-footer .icon-date[data-v-47aab902] {
  color: #AFBACF;
}
.details-footer .icon-heart i[data-v-47aab902] {
  color: #E74C3C;
}
.details-footer .icon-comment i[data-v-47aab902] {
  color: #6C75FF;
}
.details-footer .icon-date i[data-v-47aab902] {
  color: #9956B5;
}
.details-footer .icon-switch[data-v-47aab902] {
  display: flex;
  align-items: center;
}
.details-footer .vue-switcher[data-v-47aab902] {
  margin-left: 5px;
}

@media only screen and (max-width: 643px) {
.announce-details[data-v-47aab902] {
    width: auto;
    left: auto;
    position: unset;
    margin-top: 20px;
}
.announce-details .icon[data-v-47aab902] {
    top: 20px;
}
}

.icon-announce[data-v-0de25e11] {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -66px 0;
}
.is-active .icon-announce[data-v-0de25e11] {
  background-position-x: -98px;
}
.load-few li[data-v-0de25e11]:nth-child(n+6) {
  display: none;
}
.latest-announce-box .navbar-item[data-v-0de25e11] {
  height: 100%;
}
.latest-announce-box a.navbar-item[data-v-0de25e11]:hover,
.latest-announce-box a.navbar-item.is-active[data-v-0de25e11],
.latest-announce-box .navbar-link[data-v-0de25e11]:hover,
.latest-announce-box .navbar-link.is-active[data-v-0de25e11] {
  background: none;
}

/** Announcement list */
.dropdown-menu[data-v-0de25e11] {
  right: -170px;
  left: auto;
  top: 48px;
  z-index: 21;
}
.dropdown-menu .dropdown-content > .icon[data-v-0de25e11] {
  top: 5px;
  right: 5px;
  font-size: 20px;
  color: #2762CA;
  cursor: pointer;
}
.dropdown-menu .box-heading[data-v-0de25e11] {
  font-size: 15px;
}
.dropdown-menu li[data-v-0de25e11] {
  padding: 15px;
  border: 2px solid #F0F2F5;
  border-radius: 4px;
  margin: 15px 0;
  font-weight: 600;
  color: #AAB3B9;
}
.dropdown-menu button[data-v-0de25e11] {
  font-size: 13px;
  padding: 0 25px;
  margin: 0 auto;
  display: block;
}
.dropdown-menu .box-content[data-v-0de25e11] {
  width: 350px;
  font-size: 15px;
}
.dropdown-menu li[data-v-0de25e11] {
  background: #fff;
  cursor: pointer;
  font-size: 14px;
}
.dropdown-menu li.is-reading[data-v-0de25e11] {
  background: #F5F7FC;
}
.dropdown-menu li.is-active[data-v-0de25e11] {
  background: #2B62C9;
}
.dropdown-menu li.is-active .announce-title[data-v-0de25e11] {
  color: #fff;
}
.dropdown-menu li.is-unread[data-v-0de25e11] {
  color: #69738A;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dropdown-menu li .announce-title[data-v-0de25e11] {
  font-weight: bold;
}
.announce-container li.is-reading .announce-title[data-v-0de25e11] {
  color: #7496DA;
}
.announce-container li.is-unread p[data-v-0de25e11],
.announce-container li.is-unread .announce-footer[data-v-0de25e11] {
  display: none;
}
.announce-container p[data-v-0de25e11] {
  padding: 10px 0;
  font-weight: normal;
  color: #9FAEC5;
  font-size: 13px;
}
.announce-footer span[data-v-0de25e11] {
  color: #9FAEC5;
  font-weight: normal;
  margin-right: 20px;
  font-size: 13px;
}
.announce-footer span:first-child i[data-v-0de25e11] {
  color: #1ABC9C;
}
.announce-footer span:last-child i[data-v-0de25e11] {
  color: #B991CD;
}
.announce-container i[data-v-0de25e11] {
  padding-right: 5px;
}
.announce-container .unread-mark[data-v-0de25e11] {
  background: #F4447C;
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 100%;
  border: 3px solid #fff;
  box-shadow: 1px 1px 18px 1px rgba(244, 68, 124, 0.2);
}

.my-profile {
  color: #13BD9D;
  font-size: 16px;
  margin-right: 10px;
}
.sign-out {
  color: #9B59B6;
  font-size: 16px;
  margin-right: 10px;
}

.icon-toggler {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat 0 -192px;
}
a.navbar-item:hover,
a.navbar-item.is-active,
.navbar-link:hover,
.navbar-link.is-active {
  background-color: transparent;
}
.toggler:hover .icon-toggler,
.toggler.is-active .icon-toggler {
  background-position-x: -32px;
}
.navbar-item.has-dropdown:hover .navbar-link,
.navbar-item.has-dropdown.is-active .navbar-link,
.navbar-item:hover,
.navbar-item.is-active,
.navbar-link:hover,
.navbar-link.is-active {
  background: none;
}
.top-nav-bar .navbar-link:hover::after {
  border-color: #799BDE;
}
.navbar-dropdown {
  box-shadow: 0px 30px 70px 0px rgba(187, 194, 211, 0.8);
}

.sign-out {
  color: #9B59B6;
}
.icon-overview,
.icon-employees,
.icon-departments,
.icon-designations,
.icon-announcements,
.icon-reporting {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat 0 0;
}
.erp-side-menu a:hover .icon-overview,
.active.router-link-active .icon-overview {
  background-position-x: -32px;
}
.icon-employees {
  background-position-y: -32px;
}
.erp-side-menu a:hover .icon-employees,
.router-link-active .icon-employees {
  background-position-x: -32px;
}
.icon-departments {
  background-position-y: -64px;
}
.erp-side-menu a:hover .icon-departments,
.router-link-active .icon-departments {
  background-position-x: -32px;
}
.icon-designations {
  background-position-y: -97px;
}
.erp-side-menu a:hover .icon-designations,
.router-link-active .icon-designations {
  background-position-x: -32px;
}
.icon-announcements {
  background-position-y: -128px;
}
.erp-side-menu a:hover .icon-announcements,
.router-link-active .icon-announcements {
  background-position-x: -32px;
}
.icon-reporting {
  background-position-y: -160px;
}
.erp-side-menu a:hover .icon-reporting,
.router-link-active .icon-reporting {
  background-position-x: -32px;
}
.icon-logout {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -160px -33px;
}
@media only screen and (max-width: 768px) {
.erp-side-menu li span {
    display: inline-block;
}
.top-nav-bar {
    display: none;
}
}

.menu-bar {
  min-width: 224px;
}
.menu-bar,
.content-section {
  transition: .11s ease;
}
.slide .site-logo {
  display: none;
}
.slide .space {
  height: 86px;
}
.slide.menu-bar {
  width: 62px;
  min-width: 62px;
}
.slide.menu-bar + .content-section {
  padding-right: 62px !important;
  width: 100%;
}
.slide .menu-name {
  display: none;
}
.slide .erp-side-menu li a,
.slide .erp-sidebar-title {
  padding: 0;
  text-align: center;
}
.slide .erp-sidebar-title {
  padding-bottom: 23px;
}

.icon-over-counter {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat 0 0;
}
.overview-employees .icon-over-counter {
  background-position: -66px -66px;
}
.overview-departments .icon-over-counter {
  background-position: -98px -65px;
}
.overview-designations .icon-over-counter {
  background-position: -65px -98px;
}

.erp-birthday-box .box-heading {
  display: flex;
  align-items: center;
}
.icon-birthday {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -169px 0;
}
.load-few li:nth-child(n+6) {
  display: none;
}

.erp-calendar-box .box-heading {
  display: flex;
  align-items: center;
}
.icon-calendar {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -135px -32px;
}
.comp-full-calendar {
  padding: 0;
}
.erp-calendar-box .take-leave-button {
  background: #2762CA;
  color: #fff;
  text-shadow: none;
  border: 0;
  width: 120px;
  height: 38px;
  box-shadow: 0 7px 20px 0 rgba(24, 117, 240, 0.23);
  border-radius: 3px;
  cursor: pointer;
  font-size: 13px;
}
.erp-calendar-box .prev-month,
.erp-calendar-box .next-month {
  width: 33px;
  height: 33px;
  font-size: 0;
  background: url(../images/icons-overview.png) no-repeat -128px -63px;
}
.erp-calendar-box .next-month {
  background-position: -160px -96px;
}
.erp-calendar-box .prev-month:hover {
  background-position-x: -160px;
}
.erp-calendar-box .next-month:hover {
  background-position-x: -128px;
}
.erp-calendar-box .next-month {
  color: #2762CA;
}
.erp-calendar-box .header-center {
  padding-top: 61px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.erp-calendar-box .header-center .title {
  font-size: 20px;
  font-weight: bold;
  padding: 0 15px;
}
.erp-calendar-box .full-calendar-body {
  background: #FAFBFD;
}
.erp-calendar-box .comp-full-calendar ul,
.erp-calendar-box .comp-full-calendar p {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: #75848b;
}
.erp-calendar-box .dates .dates-events .events-week .events-day .event-box .event-item {
  background-color: #f3ddde !important;
  color: #db4232 !important;
  padding: 10px !important;
  border: 0;
  height: 33px;
  border-radius: 3px;
}
.erp-calendar-box .fc-event {
  background-color: #ffebee;
  color: #ef5350 !important;
  padding: 10px !important;
  border: 0;
  margin: 10px 4px 1px !important;
}
.erp-calendar-box .fc-event span {
  font-size: 13px;
}
.erp-calendar-box thead tr th {
  padding: 10px !important;
  border-width: 2px !important;
}
.erp-calendar-box .weeks,
.erp-calendar-box .weeks .week,
.erp-calendar-box .dates .week-row .day-cell {
  border-width: 2px !important;
  border-color: #EFF0F4;
}
.erp-calendar-box .weeks .week {
  padding: 5px 0;
  background: #fff;
  font-weight: normal;
  color: #18222c;
  font-size: 13px;
}
.erp-calendar-box .take-leave .icon-leave {
  background: #FE7C5F;
  color: #fff;
  border-radius: 100%;
  display: inline-flex;
  margin-right: 10px;
}
.take-leave .box-heading {
  font-size: 20px;
  margin-bottom: 40px;
}
.take-leave .modal-card-body {
  padding: 24px 30px;
}
.take-leave .icon-cross {
  position: absolute;
  right: 10px;
  font-size: 18px;
  color: #f44336;
  cursor: pointer;
}
.take-leave .modal-card {
  max-width: 430px;
  border-radius: 3px;
}
.take-leave .field-horizontal label {
  font-size: 15px;
  font-weight: normal;
}
.take-leave .field-horizontal .icon-calendar-from {
  color: #6C75FF;
}
.take-leave .field-horizontal .icon-calendar-to {
  color: #1ABC9C;
}
.send-request {
  padding: 20px 30px;
  margin: 15px 0;
  font-size: 15px;
  box-shadow: 0 7px 20px 0 rgba(24, 117, 240, 0.23);
}
.send-request .icon-send {
  margin-right: 2px !important;
}
#leave_document {
  width: 100%;
}

.erp-who-out-box .box-heading {
  display: flex;
  align-items: center;
}
.icon-whoout {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -169px -33px;
}
.load-few li:nth-child(n+6) {
  display: none;
}

.erp-attendance-service-box .box-heading[data-v-5fd2c080] {
  display: flex;
  align-items: center;
}
.icon-check[data-v-5fd2c080] {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -71px -160px;
}
.attendance-box .box-content[data-v-5fd2c080] {
  margin-bottom: 25px;
}
.icon-check[data-v-5fd2c080] {
  color: #1abc9c;
}
.field-horizontal .select[data-v-5fd2c080],
.field-horizontal select[data-v-5fd2c080] {
  width: 120px;
}
.clock[data-v-5fd2c080] {
  font-size: 32px;
  font-weight: 600;
  text-align: center;
  color: #9B59B6;
  padding-top: 20px;
}
.total-time[data-v-5fd2c080] {
  color: #AEB5B9;
  font-size: 13px;
  text-align: center;
}
.attendance-box .buttons[data-v-5fd2c080] {
  display: flex;
  justify-content: space-between;
  padding-top: 25px;
}
.attendance-box .buttons .button[data-v-5fd2c080] {
  font-size: 13px;
  height: 31px;
  padding: 0 15px;
}
.in-out[data-v-5fd2c080] {
  display: flex;
  justify-content: space-between;
}
.in-out p:first-child .time-label[data-v-5fd2c080] {
  color: #58B99D;
}
.in-out p:last-child .time-label[data-v-5fd2c080] {
  color: #DA5867;
}
.button.is-success[data-v-5fd2c080] {
  background: #58B99D;
}
.button.is-danger[data-v-5fd2c080] {
  background: #DA5867;
}
.erp-attendance-status-box[data-v-5fd2c080] {
  display: none;
}

@media only screen and (max-width: 1120px) {
.attendance-box .buttons .button {
    font-size: 11px;
    height: 25px;
    padding: 0 8px;
}
}

.erp-attendance-service-box .box-heading[data-v-70262a3b] {
  display: flex;
  align-items: center;
}
.icon-check[data-v-70262a3b] {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -71px -160px;
}
.attendance-box .box-content[data-v-70262a3b] {
  margin-bottom: 25px;
}
.icon-check[data-v-70262a3b] {
  color: #1abc9c;
}
.field-horizontal .select[data-v-70262a3b],
.field-horizontal select[data-v-70262a3b] {
  width: 120px;
}
.clock[data-v-70262a3b] {
  font-size: 32px;
  font-weight: 600;
  text-align: center;
  color: #9B59B6;
  padding-top: 20px;
}
.total-time[data-v-70262a3b] {
  color: #AEB5B9;
  font-size: 13px;
  text-align: center;
}
.attendance-box .buttons[data-v-70262a3b] {
  display: flex;
  justify-content: space-between;
  padding-top: 25px;
}
.attendance-box .buttons .button[data-v-70262a3b] {
  font-size: 13px;
  height: 31px;
  padding: 0 15px;
}
.in-out[data-v-70262a3b] {
  display: flex;
  justify-content: space-between;
}
.in-out p:first-child .time-label[data-v-70262a3b] {
  color: #58B99D;
}
.in-out p:last-child .time-label[data-v-70262a3b] {
  color: #DA5867;
}
.button.is-success[data-v-70262a3b] {
  background: #58B99D;
}
.button.is-danger[data-v-70262a3b] {
  background: #DA5867;
}
.erp-attendance-status-box[data-v-70262a3b] {
  display: none;
}

@media only screen and (max-width: 1120px) {
.attendance-box .buttons .button {
    font-size: 11px;
    height: 25px;
    padding: 0 8px;
}
}

.noty_bar {
  box-shadow: 1px 1px 18px 1px #DBE7F7;
}
.noty_buttons button {
  padding: 0 25px;
  margin-right: 8px;
}
.same-height .column {
  display: flex;
  flex-direction: row;
}
.same-height .box-content {
  width: 100%;
}
@media only screen and (max-width: 1060px) {
.same-height {
    display: block !important;
}
}

.filter-buttons a {
  border-width: 2px !important;
  border-color: #DEE2EA !important;
}
.filter-status-button a {
  border-right-width: 1px !important;
}
.filter-status-button .control:last-child a {
  border-right: 2px solid #DEE2EA !important;
}
.filter-status-buttons .is-active.button {
  border: 2px solid #DEE2EA;
}

@media only screen and (max-width: 1255px) {
.filter-status-buttons {
    display: block !important;
}
.filter-status-buttons .is-active.button {
    border: 0;
}
.filter-status-buttons .control {
    width: 130px;
    border: 1px solid #dbdbdb;
    border-bottom: 0;
}
.filter-status-buttons .control:last-child {
    border-bottom: 1px solid #dbdbdb;
}
.filter-status-buttons .control a {
    width: 128px;
    border: 0;
}
}

/** Filter buttons */
.filter-buttons a {
  background: transparent;
  color: #9E9FA3;
  padding: 18px 22px;
  font-size: 14px;
  border-width: 2px;
}
.filter-buttons img {
  padding-top: 4px;
}
.filter-buttons.is-pulled-right a {
  padding: 18px 10px;
  color: #D8E5EB;
  font-size: 18px;
}
.filter-buttons .is-active {
  background: #fff;
  border-color: #ebeef5;
  color: #27313a;
}

/** Dropdown filters */
.filter-by .dropdown-trigger {
  line-height: 40px;
}
.filter-by-txt {
  color: #9E9FA3;
}
.filter-by {
  margin-right: 30px;
}
.filter-by a {
  color: #27313a;
}
.filter-by i {
  color: #9E9FA3;
}
.arrow-up {
  top: -10px;
  border-bottom-color: #fff;
}
.filter-by li {
  margin-bottom: 15px;
}
.filter-by li:last-child {
  margin: 0;
}
.filter-by .select,
.filter-by select {
  height: 40px;
}
.filter-by select {
  width: 200px;
  padding-left: 10px;
  color: #9E9FA3;
  border: 2px solid #F0F2F5;
}
.filter-by .select select:focus,
.filter-by .select select.is-focused,
.filter-by .select select:active,
.filter-by .select select.is-active {
  border-color: transparent;
}
.filter-by .select:not(.is-multiple)::after {
  border-color: #9E9FA3;
}
.filter-by li .input {
  color: #9E9FA3;
}

@media only screen and (max-width: 768px) {
.filter-by.is-pulled-right[data-v-4c5a175e] {
    float: none !important;
}
}

.view-switcher .is-active.button {
  box-shadow: 1px 2px 8px 0 rgba(202, 210, 240, 0.4);
  border: 0;
}
.view-switcher .filter-buttons a {
  padding: 0 14px;
}

@media screen and (max-width: 1024px) {
.view-switcher[data-v-e088b24c] {
    display: none;
}
}

.move-trash i {
  color: #f44336;
}

.no-content-container {
  padding: 10px;
  text-align: center;
  font-size: 18px;
  color: #e57373;
  font-weight: bold;
}

/** List box headers */
.list-header {
  display: table;
  width: 100%;
}
.list-header li {
  display: table-cell;
  color: #9E9FA3;
}

/** List and Grid section */
.dash-list .list-body,
.dash-list .grid-body {
  background: #fff;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  position: relative;
}
.dash-list .columns {
  flex-wrap: wrap;
}
.dash-list .has-checkbox {
  padding-left: 20px;
}
.dash-list .is-checkbox + label {
  padding: 0;
}
.dash-list .thumbnail {
  width: 40px;
  height: 40px;
  margin-right: 15px;
  border-radius: 100%;
}
.dash-list .box-btn span {
  padding: 5px;
  font-size: 14px;
  border-radius: 3px;
  text-transform: capitalize;
  width: 90px;
  display: block;
  text-align: center;
}
.grid-body .box-btn span {
  margin: 0 auto;
  width: 74px;
  padding: 4px 0;
}
.list-table {
  display: table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 1em;
}
.list-table .list-table-body {
  display: table-row-group;
}
.list-table .list-table-row {
  display: table-row;
  height: 80px;
  box-shadow: 0 2px 10px 0 rgba(202, 210, 240, 0.2);
}
.list-table .row-selected {
  background: #D6E8FA;
}
.list-table .list-table-cell {
  display: table-cell;
  vertical-align: middle;
  color: #98A0B4;
}
.list-body .align-part {
  display: flex;
  align-items: center;
}

/*start width*/
.list-header li:first-child,
.list-body .has-checkbox {
  width: 70px;
}
.list-header li:nth-child(2),
.list-body .img-and-name {
  width: 270px;
}
.list-header li:nth-child(3),
.list-body .box-designtion {
  width: 170px;
}
.list-header li:nth-child(4),
.list-body .box-department {
  width: 170px;
}
.list-header li:nth-child(5),
.list-body .box-type {
  width: 140px;
}
.list-header li:nth-child(6),
.list-body .box-date {
  width: 120px;
}
.list-header li:nth-child(7),
.list-body .box-btn {
  width: 120px;
}

/*end width*/
.list-body .box-actions {
  width: 30px;
  font-size: 20px;
}
.list-body .img-and-name span {
  padding-right: 10px;
}
.list-body .img-and-name span a {
  color: #27313a;
  font-weight: 600;
}
.dash-list .grid-body {
  text-align: center;
  padding: 20px 0 28px;
  border-radius: 3px;
  box-shadow: 0 2px 10px 0 rgba(202, 210, 240, 0.2);
}
.dash-list .grid-body .img-and-name a {
  font-size: 15px;
  font-weight: 600;
  color: #18222C;
}
.dash-list .grid-body .thumbnail {
  margin: 10px auto 20px;
  display: block;
}
.dash-list .grid-body .box-department,
.dash-list .grid-body .has-checkbox,
.dash-list .grid-body .box-date,
.dash-list .grid-body .box-type {
  display: none;
}
.dash-list .grid-body .box-designtion {
  font-size: 13px;
  padding: 5px 0 15px 0;
  color: #9AA1B4;
}
.grid-body .action-dropdown i {
  color: #DEE2EA;
  font-size: 20px;
}
.dash-list .grid-body .box-actions {
  position: absolute;
  top: 10px;
  right: 10px;
}

/** List item edit/trash options */
.action-dropdown .dropdown-trigger {
  width: 10px;
  padding-left: 3px;
  cursor: pointer;
}
.action-dropdown .dropdown-content {
  display: flex;
  justify-content: space-evenly;
  width: 100px;
  color: #BACED8;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  font-size: 16px;
  left: -100px;
}
.action-dropdown .dropdown-content i {
  cursor: pointer;
}
.status-active {
  background-color: #23d160;
  border-color: transparent;
  color: #fff;
}
.status-terminated {
  background-color: #ff3860;
  border-color: transparent;
  color: #fff;
}
.status-resigned {
  background-color: #ffdd57;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.7);
}
.status-deceased {
  background-color: #363636;
  border-color: transparent;
  color: whitesmoke;
}

/* Role based design */
.role-normal-user .list-table .img-and-name {
  padding-left: 25px;
}
.role-normal-user .list-header li:nth-child(2) {
  width: 220px;
}
.role-normal-user .list-header li:nth-child(3),
.role-normal-user .list-header li:nth-child(4) {
  width: 170px;
}
.role-normal-user .list-header li:nth-child(5) {
  width: 170px;
}
.is-radio[type="radio"] + label::before,
.is-checkbox[type="checkbox"] + label::before {
  border-color: #DEE2EA;
}

@media screen and (min-width: 769) and (max-width: 1024px) {
.column[data-v-b305e9d0] {
    width: 33.33%;
}
}
@media only screen and (max-width: 768px) {
.dash-list .column[data-v-b305e9d0] {
    width: 100% !important;
}
}
@media only screen and (max-width: 560px) {
.employee-right.is-pulled-right[data-v-b305e9d0] {
    float: none !important;
}
.employee-right .letter-icons[data-v-b305e9d0] {
    display: none;
}
.employee-right .add-new-employee[data-v-b305e9d0] {
    margin-left: 0;
}
}

/** Employee index top section */
.employee-right,
.letter-icons {
  display: flex;
}

/** Employee name first letter icon */
.letter-icons li {
  width: 42px;
  height: 42px;
  border: 4px solid #fff;
  border-radius: 100%;
  text-align: center;
  line-height: 32px;
  font-weight: bold;
  font-size: 14px;
  margin-left: -12px;
}
.letter-icons li:first-child {
  background: #F5D3A0;
  color: #F6A200;
}
.letter-icons li:nth-child(2) {
  background: #DBDDFB;
  color: #979DFD;
}
.letter-icons li:nth-child(3) {
  background: #CAEBE8;
  color: #90D9CC;
}
.letter-icons li:nth-child(4) {
  background: #ECDFF0;
  color: #BA8DCC;
}

/** Add employee button */
.add-new-employee {
  margin-left: 15px;
}
.add-new-employee a {
  border-color: rgba(229, 233, 242, 0.6);
  border-width: 2px;
  height: 40px;
  border-radius: 4px;
  font-size: 13px;
  color: #18222C;
}
.add-new-employee .icon {
  margin-right: 0;
}
.add-new-employee i {
  color: #2762CA;
}
.add-new-employee .btn-text {
  font-size: 14px;
}

form[data-v-e150d99e] {
  overflow-y: scroll;
}

.profile-image {
  position: relative;
}
.hoverlay-image {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: #c0392b;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  opacity: 0;
}
.profile-image:hover .hoverlay-image {
  opacity: 1;
  cursor: pointer;
}
.overlay-top {
  background: #677FEF;
  background: -webkit-linear-gradient(to right, #677FEF, #A863E0);
  background: linear-gradient(to right, #677FEF, #A863E0);
  height: 270px;
  margin-left: -30px;
  margin-top: -30px;
  width: 106%;
  padding: 30px;
}
.top-left .thumb {
  width: 90px;
  height: 90px;
  border-radius: 100%;
  border: 10px solid #fff;
  box-shadow: 1px 1px 18px 1px rgba(38, 50, 56, 0.2);
}
.top-left .inactive {
  left: 2px;
  top: 20px;
  width: 100px;
}
.top-left .img-upload-placeholder {
  width: 126px;
  height: 126px;
  border-radius: 100%;
  border: 10px solid #fff;
  margin-left: 10px;
  background: #f0f4f7;
}
.top-left .img-upload-placeholder .hoverlay {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  background: rgba(255, 116, 174, 0.1);
  background: -webkit-linear-gradient(to right, rgba(255, 116, 174, 0.1), rgba(129, 138, 255, 0.1));
  background: linear-gradient(to right, rgba(255, 116, 174, 0.1), rgba(129, 138, 255, 0.1));
}
.top-left .img-upload-placeholder .hoverlay i {
  font-size: 40px;
  cursor: pointer;
}
.top-left .img-upload-placeholder img {
  margin: 10px auto 0;
  display: block;
}
.top-left .img-upload-placeholder:hover {
  box-shadow: 1px 1px 18px 1px rgba(6, 42, 146, 0.5);
}
.top-left .img-upload-placeholder:hover .hoverlay {
  opacity: 1;
}

.single-employee-page .erp-page {
  padding-right: 0;
}
.single-employee-page .info-boxes {
  padding-right: 30px;
}
.overlay-top {
  background: #677FEF;
  background: -webkit-linear-gradient(to right, #677FEF, #A863E0);
  background: linear-gradient(to right, #677FEF, #A863E0);
  height: 250px;
  margin-left: -30px;
  margin-top: -30px;
  padding: 30px;
}
.info-boxes {
  top: -60px;
}
.flex4 {
  flex: 4;
}

/** Top `left` and `right` section */
.top-left,
.top-right {
  display: flex;
  align-items: center;
  height: 100%;
  color: #fff;
}

/** Top left section */
.top-left .thumb {
  width: 90px;
  height: 90px;
  border-radius: 100%;
  border: 10px solid #fff;
  box-shadow: 1px 1px 18px 1px rgba(38, 50, 56, 0.2);
}
.top-left .inactive {
  left: -5px;
  top: 60px;
  width: 100px;
}
.short-info {
  padding-left: 30px;
}
.short-info h2 {
  font-weight: bold;
  font-size: 20px;
}
.short-info i {
  padding-right: 5px;
}
.short-info .info-email {
  padding-left: 15px;
}

/** Top right section */
.top-right {
  justify-content: flex-end;
}
.top-right a {
  color: #fff;
  border: 0;
  margin: 0 5px;
}
.top-edit-btn {
  background: #00AFFF;
}
.top-terminate-btn {
  background: #F1C506;
  font-size: 12px;
  padding: 0 15px;
}
.icon-print {
  font-size: 20px;
  color: #fff;
  margin-left: 15px;
  cursor: pointer;
}

/** Info left side list menu */
.info-list-menu li {
  padding: 5px 0;
}
.info-list-menu .info-list-name .active {
  color: #2762CA;
  font-weight: bold;
}
.info-list-menu .info-list-name {
  font-size: 15px;
  padding-left: 10px;
}
.info-list-menu .info-list-name a {
  color: inherit;
}
.info-list-menu .icon {
  font-size: 18px;
}
.info-list-menu li:first-child .icon {
  color: #13BD9D;
}
.info-list-menu li:nth-child(2) .icon {
  color: #3199DC;
}
.info-list-menu li:nth-child(3) .icon {
  color: #9C58B7;
}
.info-list-menu li:nth-child(4) .icon {
  color: #A5B8CB;
}
.info-list-menu li:nth-child(5) .icon {
  color: #F39D0A;
}
.info-list-menu li:nth-child(6) .icon {
  color: #D45300;
}
.info-list-menu li:nth-child(7) .icon {
  color: #6C76FF;
}

/** Info boxes right side */
.info-boxes .info-details .box-content {
  margin-bottom: 20px;
}
.info-boxes .info-details hr {
  margin: 0;
}
.info-top,
.title-with-icon,
.field-horizontal {
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.field-horizontal label {
  padding-right: 15px;
  font-weight: 600;
  width: 170px;
}
.field-horizontal .required {
  color: #f44336;
}
.field-horizontal .input,
.field-horizontal .textarea,
.field-horizontal .select,
.field-horizontal select {
  width: 220px;
}
.info-top {
  justify-content: space-between;
  height: 60px;
  margin: 0;
  padding: 0;
}
.title-with-icon {
  font-size: 20px;
  color: #ABB1C1;
}
.title-with-icon i {
  font-size: 24px;
  padding-left: 30px;
  padding-right: 15px;
}
.action-edit i {
  padding-right: 25px;
  font-size: 14px;
  color: #ABB1C1;
  cursor: pointer;
}
.editing .title-with-icon h2 {
  color: #63696F;
}
.editing .action-edit {
  margin-right: 15px;
}
.editing .action-edit i {
  color: #2762CA;
  background: #EFF3FB;
  width: 35px;
  height: 35px;
  line-height: 35px;
  border-radius: 100%;
  padding-left: 10px;
}
.info-bottom > ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 30px;
}
.info-bottom > ul > li {
  width: 50%;
  padding: 10px 0;
  display: flex;
}
.info-bottom > ul > li span:first-child,
.info-bottom > ul > li span:nth-child(2) {
  min-width: 115px;
  display: inline-block;
  color: #B6BCCA;
  font-size: 14px;
}
.info-bottom > ul > li span:nth-child(2) {
  min-width: 25px;
}
.info-bottom > ul > li span:nth-child(3) {
  text-transform: capitalize;
  font-size: 14px;
}

/** Edit form */
.edit-form {
  padding: 25px;
}
.edit-form .field {
  width: 266px;
}
.edit-form .field.is-grouped.is-grouped-right {
  width: 100%;
  margin-top: 20px;
}
.edit-form label {
  font-size: 14px;
  font-weight: 600;
  color: #585858;
}
.edit-form a {
  width: 100px;
  font-size: 14px;
  height: 40px;
}
.edit-form .save-btn {
  color: #fff;
  background: #6dd30a;
  background: -webkit-linear-gradient(to right, #6dd30a, #00C08E);
  background: linear-gradient(to right, #6dd30a, #00C08E);
}

/* Headline icons color */
.work-info .title-with-icon i {
  color: #569FFE;
}
.personal-info .title-with-icon i {
  color: #7800EF;
}

/* Input and select box basic style */
.info-bottom select,
.info-bottom .work-info input,
.control.has-date {
  width: 250px;
  border-color: #EEF1F6;
  color: #9BA3B6;
}
.info-bottom .select:not(.is-multiple)::after {
  border-color: #9BA3B6;
}

/* Table style */
.table-info .title-with-icon i {
  color: #63E37B;
}
.table-info .info-bottom {
  padding: 30px;
}
.table-info thead td {
  color: #8992A9;
  background: #fff;
  font-size: 14px;
}
.table-info table td {
  border: 2px solid #EFF2F7;
  padding: 10px;
}
.table-info tbody td {
  background-color: #fafafa;
}
.table-info tbody .icon-save {
  color: #63E37B;
}
.table-info tr td:last-child {
  width: 70px;
}
.table-info .table-editing td {
  padding: 0;
}
.table-info .table-editing td:last-child {
  padding: 8px;
}
.table-info tbody td:last-child {
  color: #8992A9;
  border: 1px solid #EEF1F6;
}
@media print {
.menu-bar,
  .top-nav-bar,
  .top-right,
  .info-boxes > .columns > .is-3,
  .action-edit,
  .button.is-link {
    display: none !important;
}
.info-details,
  .content-section {
    width: 100% !important;
    margin-bottom: 30px !important;
}
.erp-page {
    padding: 0;
    border: 0 !important;
}
.title-with-icon i {
    color: #ABB1C1;
}
}

@media only screen and (max-width: 1155px) {
.info-boxes .columns[data-v-adffd684] {
    display: block;
}
.info-boxes .column[data-v-adffd684] {
    width: 100%;
}
.info-list-menu[data-v-adffd684] {
    display: flex;
    justify-content: space-between;
}
.info-list-menu .info-list-name[data-v-adffd684] {
    padding-left: 0;
}
}
@media only screen and (max-width: 1000px) {
.info-list-menu[data-v-adffd684] {
    display: block;
}
}
@media only screen and (max-width: 768px) {
.info-bottom > ul > li[data-v-adffd684] {
    width: 100%;
    display: block;
}
}

.email.non-capitalize span:nth-child(3) {
  text-transform: lowercase;
}

fieldset[disabled] .multiselect {
  pointer-events: none;
}
.multiselect__spinner {
  position: absolute;
  right: 1px;
  top: 1px;
  width: 48px;
  height: 35px;
  background: #fff;
  display: block;
}
.multiselect__spinner:after, .multiselect__spinner:before {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  margin: -8px 0 0 -8px;
  width: 16px;
  height: 16px;
  border-radius: 100%;
  border: 2px solid transparent;
  border-top-color: #41b883;
  box-shadow: 0 0 0 1px transparent;
}
.multiselect__spinner:before {
  animation: spinning 2.4s cubic-bezier(0.41, 0.26, 0.2, 0.62);
  animation-iteration-count: infinite;
}
.multiselect__spinner:after {
  animation: spinning 2.4s cubic-bezier(0.51, 0.09, 0.21, 0.8);
  animation-iteration-count: infinite;
}
.multiselect__loading-enter-active, .multiselect__loading-leave-active {
  transition: opacity .4s ease-in-out;
  opacity: 1;
}
.multiselect__loading-enter, .multiselect__loading-leave-active {
  opacity: 0;
}
.multiselect, .multiselect__input, .multiselect__single {
  font-family: inherit;
  font-size: 16px;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}
.multiselect {
  box-sizing: content-box;
  display: block;
  position: relative;
  width: 100%;
  min-height: 40px;
  text-align: left;
  color: #35495e;
}
.multiselect * {
  box-sizing: border-box;
}
.multiselect:focus {
  outline: none;
}
.multiselect--disabled {
  background: #ededed;
  pointer-events: none;
  opacity: .6;
}
.multiselect--active {
  z-index: 50;
}
.multiselect--active:not(.multiselect--above) .multiselect__current, .multiselect--active:not(.multiselect--above) .multiselect__input, .multiselect--active:not(.multiselect--above) .multiselect__tags {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.multiselect--active .multiselect__select {
  transform: rotate(180deg);
}
.multiselect--above.multiselect--active .multiselect__current, .multiselect--above.multiselect--active .multiselect__input, .multiselect--above.multiselect--active .multiselect__tags {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.multiselect__input, .multiselect__single {
  position: relative;
  display: inline-block;
  min-height: 20px;
  line-height: 20px;
  border: none;
  border-radius: 5px;
  background: #fff;
  padding: 0 0 0 5px;
  width: 100%;
  transition: border .1s ease;
  box-sizing: border-box;
  margin-bottom: 8px;
  vertical-align: top;
}
.multiselect__input:-ms-input-placeholder {
  color: #35495e;
}
.multiselect__input::placeholder {
  color: #35495e;
}
.multiselect__tag ~ .multiselect__input, .multiselect__tag ~ .multiselect__single {
  width: auto;
}
.multiselect__input:hover, .multiselect__single:hover {
  border-color: #cfcfcf;
}
.multiselect__input:focus, .multiselect__single:focus {
  border-color: #a8a8a8;
  outline: none;
}
.multiselect__single {
  padding-left: 5px;
  margin-bottom: 8px;
}
.multiselect__tags-wrap {
  display: inline;
}
.multiselect__tags {
  min-height: 40px;
  display: block;
  padding: 8px 40px 0 8px;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  background: #fff;
  font-size: 14px;
}
.multiselect__tag {
  position: relative;
  display: inline-block;
  padding: 4px 26px 4px 10px;
  border-radius: 5px;
  margin-right: 10px;
  color: #fff;
  line-height: 1;
  background: #41b883;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
}
.multiselect__tag-icon {
  cursor: pointer;
  margin-left: 7px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  font-weight: 700;
  font-style: normal;
  width: 22px;
  text-align: center;
  line-height: 22px;
  transition: all .2s ease;
  border-radius: 5px;
}
.multiselect__tag-icon:after {
  content: "\D7";
  color: #266d4d;
  font-size: 14px;
}
.multiselect__tag-icon:focus, .multiselect__tag-icon:hover {
  background: #369a6e;
}
.multiselect__tag-icon:focus:after, .multiselect__tag-icon:hover:after {
  color: #fff;
}
.multiselect__current {
  min-height: 40px;
  overflow: hidden;
  padding: 8px 30px 0 12px;
  white-space: nowrap;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
}
.multiselect__current, .multiselect__select {
  line-height: 16px;
  box-sizing: border-box;
  display: block;
  margin: 0;
  text-decoration: none;
  cursor: pointer;
}
.multiselect__select {
  position: absolute;
  width: 40px;
  height: 38px;
  right: 1px;
  top: 1px;
  padding: 4px 8px;
  text-align: center;
  transition: transform .2s ease;
}
.multiselect__select:before {
  position: relative;
  right: 0;
  top: 65%;
  color: #999;
  margin-top: 4px;
  border-color: #999 transparent transparent;
  border-style: solid;
  border-width: 5px 5px 0;
  content: "";
}
.multiselect__placeholder {
  color: #adadad;
  display: inline-block;
  margin-bottom: 10px;
  padding-top: 2px;
}
.multiselect--active .multiselect__placeholder {
  display: none;
}
.multiselect__content-wrapper {
  position: absolute;
  display: block;
  background: #fff;
  width: 100%;
  max-height: 240px;
  overflow: auto;
  border: 1px solid #e8e8e8;
  border-top: none;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  z-index: 50;
  -webkit-overflow-scrolling: touch;
}
.multiselect__content {
  list-style: none;
  display: inline-block;
  padding: 0;
  margin: 0;
  min-width: 100%;
  vertical-align: top;
}
.multiselect--above .multiselect__content-wrapper {
  bottom: 100%;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-bottom: none;
  border-top: 1px solid #e8e8e8;
}
.multiselect__content::webkit-scrollbar {
  display: none;
}
.multiselect__element {
  display: block;
}
.multiselect__option {
  display: block;
  padding: 12px;
  min-height: 40px;
  line-height: 16px;
  text-decoration: none;
  text-transform: none;
  vertical-align: middle;
  position: relative;
  cursor: pointer;
  white-space: nowrap;
}
.multiselect__option:after {
  top: 0;
  right: 0;
  position: absolute;
  line-height: 40px;
  padding-right: 12px;
  padding-left: 20px;
  font-size: 13px;
}
.multiselect__option--highlight {
  background: #41b883;
  outline: none;
  color: #fff;
}
.multiselect__option--highlight:after {
  content: attr(data-select);
  background: #41b883;
  color: #fff;
}
.multiselect__option--selected {
  background: #f3f3f3;
  color: #35495e;
  font-weight: 700;
}
.multiselect__option--selected:after {
  content: attr(data-selected);
  color: silver;
}
.multiselect__option--selected.multiselect__option--highlight {
  background: #ff6a6a;
  color: #fff;
}
.multiselect__option--selected.multiselect__option--highlight:after {
  background: #ff6a6a;
  content: attr(data-deselect);
  color: #fff;
}
.multiselect--disabled .multiselect__current, .multiselect--disabled .multiselect__select {
  background: #ededed;
  color: #a6a6a6;
}
.multiselect__option--disabled {
  background: #ededed !important;
  color: #a6a6a6 !important;
  cursor: text;
  pointer-events: none;
}
.multiselect__option--group {
  background: #ededed;
  color: #35495e;
}
.multiselect__option--group.multiselect__option--highlight {
  background: #35495e;
  color: #fff;
}
.multiselect__option--group.multiselect__option--highlight:after {
  background: #35495e;
}
.multiselect__option--disabled.multiselect__option--highlight {
  background: #dedede;
}
.multiselect__option--group-selected.multiselect__option--highlight {
  background: #ff6a6a;
  color: #fff;
}
.multiselect__option--group-selected.multiselect__option--highlight:after {
  background: #ff6a6a;
  content: attr(data-deselect);
  color: #fff;
}
.multiselect-enter-active, .multiselect-leave-active {
  transition: all .15s ease;
}
.multiselect-enter, .multiselect-leave-active {
  opacity: 0;
}
.multiselect__strong {
  margin-bottom: 8px;
  line-height: 20px;
  display: inline-block;
  vertical-align: top;
}
[dir=rtl] .multiselect {
  text-align: right;
}
[dir=rtl] .multiselect__select {
  right: auto;
  left: 1px;
}
[dir=rtl] .multiselect__tags {
  padding: 8px 8px 0 40px;
}
[dir=rtl] .multiselect__content {
  text-align: right;
}
[dir=rtl] .multiselect__option:after {
  right: auto;
  left: 0;
}
[dir=rtl] .multiselect__clear {
  right: auto;
  left: 12px;
}
[dir=rtl] .multiselect__spinner {
  right: auto;
  left: 1px;
}
@keyframes spinning {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(2turn);
}
}

.experience-table tr td:nth-child(3),
.experience-table tr td:nth-child(4) {
  width: 90px;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td[data-v-6438f19a]:nth-of-type(1):before {
    content: "Company";
}
.res-table td[data-v-6438f19a]:nth-of-type(2):before {
    content: "Job Title";
}
.res-table td[data-v-6438f19a]:nth-of-type(3):before {
    content: "From";
}
.res-table td[data-v-6438f19a]:nth-of-type(4):before {
    content: "To";
}
.res-table td[data-v-6438f19a]:nth-of-type(5):before {
    content: "Job Description";
}
.res-table td[data-v-6438f19a]:nth-of-type(6):before {
    content: "Actions";
}
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td[data-v-215349e1]:nth-of-type(1):before {
    content: __("School Name", "erp-pro");
}
.res-table td[data-v-215349e1]:nth-of-type(2):before {
    content: __("Degree", "erp-pro");
}
.res-table td[data-v-215349e1]:nth-of-type(3):before {
    content: __("Field(s) of Study", "erp-pro");
}
.res-table td[data-v-215349e1]:nth-of-type(4):before {
    content: __("Year of Completion", "erp-pro");
}
.res-table td[data-v-215349e1]:nth-of-type(5):before {
    content: __("Additional Notes", "erp-pro");
}
.res-table td[data-v-215349e1]:nth-of-type(6):before {
    content: __("Interests", "erp-pro");
}
.res-table td[data-v-215349e1]:nth-of-type(7):before {
    content: __("Actions", "erp-pro");
}
}

.dependents-table tbody td:nth-child(3) {
  width: 106px;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td[data-v-eae0b62c]:nth-of-type(1):before {
    content: __("Name", "erp-pro");
}
.res-table td[data-v-eae0b62c]:nth-of-type(2):before {
    content: __("Relationship", "erp-pro");
}
.res-table td[data-v-eae0b62c]:nth-of-type(3):before {
    content: __("Date of Birth", "erp-pro");
}
.res-table td[data-v-eae0b62c]:nth-of-type(4):before {
    content: __("Actions", "erp-pro");
}
}

.basic-info .title-with-icon i {
  color: #FF5863;
}
td.actions > div {
  float: left;
  width: 11px;
  margin: 0 6px;
}
td.actions i {
  padding: 0;
}
td.actions i:hover {
  color: #2762CA;
  cursor: pointer;
}
td.actions .action-delete i:hover {
  color: #f44336;
}

.action-delete {
  color: #f44336;
}
.employment-table tbody td:first-child {
  width: 100px;
}
.employment-table tbody td:nth-child(2) {
  width: 150px;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td[data-v-0faef469]:nth-of-type(1):before {
    content: __("Date", "erp-pro");
}
.res-table td[data-v-0faef469]:nth-of-type(2):before {
    content: __("Employment Status", "erp-pro");
}
.res-table td[data-v-0faef469]:nth-of-type(3):before {
    content: __("Comment", "erp-pro");
}
.res-table td[data-v-0faef469]:nth-of-type(4):before {
    content: __("Actions", "erp-pro");
}
}

.compensation-table tbody td:first-child {
  width: 100px;
}
.compensation-table tbody td:nth-child(2) {
  width: 100px;
}
.compensation-table tbody td:nth-child(3) {
  width: 80px;
}
.compensation-table tbody td:nth-child(4) {
  width: 130px;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td[data-v-95fa2576]:nth-of-type(1):before {
    content: __("Date", "erp-pro");
}
.res-table td[data-v-95fa2576]:nth-of-type(2):before {
    content: __("Pay Rate", "erp-pro");
}
.res-table td[data-v-95fa2576]:nth-of-type(3):before {
    content: __("Pay Type", "erp-pro");
}
.res-table td[data-v-95fa2576]:nth-of-type(4):before {
    content: __("Change Reason", "erp-pro");
}
.res-table td[data-v-95fa2576]:nth-of-type(5):before {
    content: __("Comment", "erp-pro");
}
.res-table td[data-v-95fa2576]:nth-of-type(6):before {
    content: __("Actions", "erp-pro");
}
}

.zindex {
  z-index: 19;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td[data-v-22372fde]:nth-of-type(1):before {
    content: __("Date", "erp-pro");
}
.res-table td[data-v-22372fde]:nth-of-type(2):before {
    content: __("Location", "erp-pro");
}
.res-table td[data-v-22372fde]:nth-of-type(3):before {
    content: __("Department", "erp-pro");
}
.res-table td[data-v-22372fde]:nth-of-type(4):before {
    content: __("Job Title", "erp-pro");
}
.res-table td[data-v-22372fde]:nth-of-type(5):before {
    content: __("Reports To", "erp-pro");
}
.res-table td[data-v-22372fde]:nth-of-type(6):before {
    content: __("Actions", "erp-pro");
}
}

td.actions > div {
  float: left;
  width: 11px;
  margin: 0 6px;
}
td.actions i {
  padding: 0;
}
td.actions i:hover {
  color: #2762CA;
  cursor: pointer;
}
td.actions .action-delete i:hover {
  color: #f44336;
}

.info-bottom {
  padding: 25px;
}
.balances-table {
  margin: 0;
}
.table-info tr td:last-child {
  width: auto;
}
.filters {
  padding-bottom: 25px;
}
.filters .select {
  width: 200px;
}
.filters .button {
  height: 29px;
  line-height: 28px;
  padding: 0 20px;
  color: #fff;
  background: #6dd30a;
  background: -webkit-linear-gradient(to right, #6dd30a, #00C08E);
  background: linear-gradient(to right, #6dd30a, #00C08E);
}

.notes-info .action-edit i {
  line-height: 36px;
  padding-left: 11px;
}
.notes ul {
  padding: 0 20px 20px;
}
.notes li {
  width: 100%;
  border-bottom: 1px solid #EEF1F5;
  padding: 30px 0;
  display: flex;
}
.notes li:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}
.note-content .name {
  color: inherit;
  font-weight: bold;
  min-width: auto;
  padding-right: 15px;
}
.note-content .time {
  color: #999;
}
.note-content .body {
  padding-top: 8px;
  line-height: 23px;
}
.note-avatar {
  min-width: 100px;
}
.note-avatar img {
  width: 48px;
}
.delete-note {
  cursor: pointer;
  background: #f44336;
  color: #fff;
  padding: 3px 10px;
  margin-top: 10px;
  border-radius: 4px;
  display: inline-block;
}
@media print {
.delete-note {
    display: none;
}
}

.action-delete {
  cursor: pointer;
  text-align: center;
}
.action-delete:hover i {
  color: #f44336;
}
@media screen, print {
.reviews-table tr td:first-child,
  .comments-table tr td:first-child,
  .goals-table tr td:first-child {
    width: 100px !important;
}
}
@media print {
.table-info .info-bottom {
    padding: 0 !important;
}
}

@media print {
.save-btn[data-v-2243ef6c] {
    display: none;
}
}

.info-bottom hr {
  margin: 0 25px;
}
.title-with-icon {
  color: inherit;
}
.checkboxes {
  padding: 30px;
}
.checkboxes li {
  width: 100%;
  display: flex;
  margin: 15px 0;
}
.checkboxes li label {
  font-size: 14px;
}
.checkboxes li label:first-child {
  width: 170px;
  font-weight: bold;
}
.checkboxes li label:last-child {
  padding-top: 0;
  font-size: 15px;
}
.edit-form {
  padding: 0 25px 25px;
}

.overlay-top[data-v-362fe61c] {
  background: #677FEF;
  background: -webkit-linear-gradient(to right, #677FEF, #A863E0);
  background: linear-gradient(to right, #677FEF, #A863E0);
  height: 270px;
  margin-left: -30px;
  margin-top: -30px;
  width: 106%;
  padding: 30px;
}
.info-boxes[data-v-362fe61c] {
  top: -60px;
}

/** Top `left` and `right` section */
.top-left[data-v-362fe61c],
.top-right[data-v-362fe61c] {
  display: flex;
  align-items: center;
  height: 100%;
  color: #fff;
}

/** Top left section */
.top-left .img-upload-placeholder[data-v-362fe61c] {
  width: 126px;
  height: 126px;
  border-radius: 100%;
  border: 10px solid #fff;
  margin-left: 10px;
  background: #f0f4f7;
}
.top-left .img-upload-placeholder .hoverlay[data-v-362fe61c] {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  background: rgba(255, 116, 174, 0.1);
  background: -webkit-linear-gradient(to right, rgba(255, 116, 174, 0.1), rgba(129, 138, 255, 0.1));
  background: linear-gradient(to right, rgba(255, 116, 174, 0.1), rgba(129, 138, 255, 0.1));
}
.top-left .img-upload-placeholder .hoverlay i[data-v-362fe61c] {
  font-size: 40px;
  cursor: pointer;
}
.top-left .img-upload-placeholder img[data-v-362fe61c] {
  margin: 10px auto 0;
  display: block;
}
.top-left .img-upload-placeholder[data-v-362fe61c]:hover {
  box-shadow: 1px 1px 18px 1px rgba(6, 42, 146, 0.5);
}
.top-left .img-upload-placeholder:hover .hoverlay[data-v-362fe61c] {
  opacity: 1;
}

/** Top right section */
.top-right[data-v-362fe61c] {
  justify-content: flex-end;
}
.top-right a[data-v-362fe61c] {
  color: #fff;
  border: 0;
  margin: 0 5px;
}
.top-edit-btn[data-v-362fe61c] {
  background: #00AFFF;
}
.top-terminate-btn[data-v-362fe61c] {
  background: #F1C506;
  font-size: 12px;
  padding: 0 15px;
}
.icon-print[data-v-362fe61c] {
  font-size: 20px;
  color: #fff;
  margin-left: 15px;
}

/** Info left side list menu */
.info-list-menu .info-list-name[data-v-362fe61c] {
  font-size: 15px;
  padding-left: 10px;
  cursor: pointer;
}
.info-list-menu li[data-v-362fe61c] {
  padding: 5px 0;
  color: #B2BAD0;
}
.info-list-menu li.is-active .info-list-name[data-v-362fe61c] {
  color: #27313a;
  font-weight: bold;
}
.info-list-menu .icon i[data-v-362fe61c] {
  font-size: 22px;
}
.info-list-menu .step1 .icon i[data-v-362fe61c] {
  color: #ff5964;
}
.info-list-menu .step2.is-active .icon i[data-v-362fe61c] {
  color: #8f91fe;
}
.info-list-menu .step3.is-active .icon i[data-v-362fe61c] {
  color: #7D03EF;
}

/** Info boxes right side */
.info-boxes .info-details .box-content[data-v-362fe61c] {
  padding-bottom: 85px !important;
}
.info-top[data-v-362fe61c],
.title-with-icon[data-v-362fe61c] {
  display: flex;
  align-items: center;
}
.info-top[data-v-362fe61c] {
  justify-content: space-between;
  height: 60px;
  margin: 0;
  padding: 0;
}
.title-with-icon[data-v-362fe61c] {
  font-size: 20px;
  color: #ABB1C1;
}
.title-with-icon i[data-v-362fe61c] {
  font-size: 24px;
  padding-left: 30px;
  padding-right: 15px;
}
.basic-info .info-top i[data-v-362fe61c] {
  color: #FF5964;
}
.work-info .info-top i[data-v-362fe61c] {
  color: #03a9f4;
}
.personal-info .info-top i[data-v-362fe61c] {
  color: #9c27b0;
}
.info-top h2[data-v-362fe61c] {
  font-size: 16px;
  font-weight: 600;
}
.info-bottom > ul[data-v-362fe61c] {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 30px;
}
.info-bottom > ul > li[data-v-362fe61c] {
  width: 50%;
  padding: 10px 0;
}
.info-bottom[data-v-362fe61c] {
  padding: 25px;
}
.info-bottom .field label[data-v-362fe61c],
.notify-box label[data-v-362fe61c] {
  font-size: 13px;
  font-weight: 600;
}
.info-bottom > ul > li span[data-v-362fe61c]:first-child,
.info-bottom > ul > li span[data-v-362fe61c]:nth-child(2) {
  min-width: 100px;
  display: inline-block;
  color: #B6BCCA;
  font-size: 14px;
}
.info-bottom > ul > li span[data-v-362fe61c]:nth-child(2) {
  min-width: 20px;
}
.info-bottom select[data-v-362fe61c],
.info-bottom input[data-v-362fe61c],
.info-bottom .v-select[data-v-362fe61c],
.control.has-date[data-v-362fe61c] {
  width: 250px;
  border-color: #EEF1F6;
  color: #9BA3B6;
}
.info-bottom .select[data-v-362fe61c]:not(.is-multiple)::after {
  border-color: #9BA3B6;
}
.columns.form-padding[data-v-362fe61c] {
  margin-bottom: 0;
}
.columns.form-padding .column[data-v-362fe61c] {
  padding-bottom: 0;
}
.notify-box .field[data-v-362fe61c] {
  display: flex;
  align-items: center;
}
.notify-box .field > label[data-v-362fe61c] {
  margin: 0;
}
.notify-box .control[data-v-362fe61c] {
  padding-left: 25px;
  color: #B2BAD0;
}
.is-grouped[data-v-362fe61c] {
  margin: 20px 20px 0;
}
.is-grouped .button[data-v-362fe61c] {
  font-size: 13px;
  padding: 18px 30px;
}
.is-grouped .has-gradient[data-v-362fe61c] {
  background: #6DD213;
  background: -webkit-linear-gradient(to right, #6DD213, #00BF8D);
  background: linear-gradient(to right, #6DD213, #00BF8D);
}

/** Letter icons */
.letter-icons {
  display: flex;
}
.letter-icons li {
  width: 42px;
  height: 42px;
  border: 4px solid #fff;
  border-radius: 100%;
  text-align: center;
  line-height: 32px;
  font-weight: bold;
  font-size: 14px;
  margin-left: -12px;
  box-shadow: 1px 1px 18px 1px #DBE7F7;
}
.letter-icons li:first-child {
  background: #F5D3A0;
  color: #F6A200;
}
.letter-icons li:nth-child(2) {
  background: #DBDDFB;
  color: #979DFD;
}
.letter-icons li:nth-child(3) {
  background: #CAEBE8;
  color: #90D9CC;
}
.letter-icons li:nth-child(4) {
  background: #ECDFF0;
  color: #BA8DCC;
}

/* List specefic letter icons */
.list-body .employees-no .letter-icons li {
  width: 38px;
  height: 38px;
}
.list-body .employees-no .letter-icons li:last-child {
  border: 0;
  background: none;
  box-shadow: none;
  padding-top: 5px;
  padding-left: 25px;
  font-weight: normal;
  color: #363636;
  letter-spacing: .02em;
}

/* Grid specefic letter icons */
.grid-body .employees-no .letter-icons li {
  width: 30px;
  height: 30px;
  border-width: 3px;
  line-height: 24px;
  font-weight: 600;
  font-size: 11px;
  margin-left: -8px;
}
.grid-body .employees-no .letter-icons li:nth-child(5) {
  background: #ECDFF0;
  color: #BA8DCC;
}
.grid-body .employees-no .letter-icons li:nth-child(3),
.grid-body .employees-no .letter-icons li:nth-child(4) {
  display: none;
}
.grid-body .img-and-name .letter-icons {
  padding-top: 20px;
}
.grid-body .img-and-name .letter-icons li {
  margin: 15px auto;
  font-size: 16px;
  width: 48px;
  height: 48px;
  line-height: 42px;
}

/** Add department button */
.add-new-department a {
  border-color: rgba(229, 233, 242, 0.6);
  border-width: 2px;
  color: #363636;
  height: 40px;
  border-radius: 4px;
}
.add-new-department i {
  color: #2762CA;
}
.add-new-department .btn-text {
  font-size: 14px;
}

/** Filter buttons (Grid/List view) */
.filter-buttons a {
  background: transparent;
  color: #9E9FA3;
  padding: 18px 22px;
  font-size: 14px;
  border-width: 2px;
}
.filter-buttons.is-pulled-right a {
  padding: 18px 10px;
  color: #D8E5EB;
  font-size: 18px;
}
.filter-buttons .is-active {
  background: #fff;
  border-color: #ebeef5;
  color: #27313a;
}
.view-switcher .is-active.button {
  box-shadow: 1px 2px 8px 0 rgba(202, 210, 240, 0.4);
  border: 0;
}

/** List box headers */
.list-header li {
  display: inline-block;
  color: #9E9FA3;
}

/** List and Grid section */
.dash-list .list-body,
.dash-list .grid-body {
  background: #fff;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  position: relative;
}
.dash-list .columns {
  flex-wrap: wrap;
}

/* Checkbox */
.dash-list .has-checkbox {
  padding-left: 20px;
}
.dash-list .is-checkbox + label {
  padding: 0;
}

/* Thumbnail */
.dash-list .thumbnail {
  width: 36px;
  height: 36px;
  border-radius: 100%;
}
.dash-list .grid-body .thumbnail {
  position: absolute;
  bottom: 0;
  right: 25px;
  margin: 10px auto 17px;
  display: block;
}

/* List (div)table */
.list-table {
  display: table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 1em;
}
.list-table .list-table-body {
  display: table-row-group;
}
.list-table .list-table-row {
  display: table-row;
  height: 80px;
  box-shadow: 0 2px 10px 0 rgba(202, 210, 240, 0.2);
}
.list-table .list-table-cell {
  display: table-cell;
  vertical-align: middle;
  color: #98A0B4;
}
.list-body .align-part {
  display: flex;
  align-items: center;
}
.list-body .has-checkbox {
  width: 80px;
}
.list-header li:first-child {
  width: 65px;
}
.list-header li:nth-child(2) {
  width: 310px;
}
.list-header li:nth-child(3) {
  width: 335px;
}
.role-normal-user .list-header li:first-child,
.role-normal-user .list-table .img-and-name {
  padding-left: 40px;
}
.role-normal-user .list-header li:first-child {
  width: 400px;
}
.role-normal-user .list-header li:nth-child(2) {
  width: 350px;
}
.role-normal-user .list-header li:nth-child(3) {
  width: 220px;
}

/* List styles */
.list-body .box-actions {
  width: 30px;
  font-size: 20px;
}
.list-body .img-and-name span {
  padding-left: 15px;
}
.list-body .img-and-name span a {
  color: #27313a;
  font-weight: 600;
}
.action-edit i {
  color: #673ab7;
}
.action-delete i {
  color: #f44336;
}

/* Grid styles */
.grid-body {
  text-align: center;
}
.grid-body .img-and-name.type-1 span {
  padding: 20px 30px;
  display: block;
}
.grid-body .has-checkbox,
.grid-body .img-and-name.type-2 span {
  display: none;
}
.grid-body .action-dropdown i {
  color: #DEE2EA;
  font-size: 20px;
}
.grid-body .box-actions {
  position: absolute;
  top: 15px;
  right: 15px;
}
.grid-body .employees-no {
  background: #FAFBFD;
  padding: 20px 30px;
  border-top: 1px solid #EFF2F7;
}

/** List item edit/trash options */
.action-dropdown .dropdown-trigger {
  width: 10px;
  padding-left: 3px;
  cursor: pointer;
}
.action-dropdown .dropdown-content {
  display: flex;
  justify-content: space-evenly;
  width: 100px;
  color: #BACED8;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  font-size: 16px;
  left: -100px;
}
.action-dropdown .dropdown-content i {
  cursor: pointer;
}

/* Status buttons */
.status-active {
  background-color: #23d160;
  border-color: transparent;
  color: #fff;
}
.status-terminated {
  background-color: #ff3860;
  border-color: transparent;
  color: #fff;
}
.status-resigned {
  background-color: #ffdd57;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.7);
}
.status-deceased {
  background-color: #363636;
  border-color: transparent;
  color: whitesmoke;
}
.department-index .list-header li:nth-child(4) {
  width: 110px;
}
.department-index .grid-body {
  padding: 0;
}

@media only screen and (max-width: 1350px) {
.dash-list .list-header[data-v-34cca569] {
    display: none;
}
}
@media screen and (max-width: 1024px) {
.view-switcher[data-v-34cca569] {
    display: none;
}
}
@media screen and (max-width: 768px) {
.modal-card[data-v-34cca569] {
    margin: 0;
}
}

/** Letter icons */
.letter-icons {
  display: flex;
}
.letter-icons li {
  width: 42px;
  height: 42px;
  border: 4px solid #fff;
  border-radius: 100%;
  text-align: center;
  line-height: 32px;
  font-weight: bold;
  font-size: 14px;
  margin-left: -12px;
  box-shadow: 1px 1px 18px 1px #DBE7F7;
}
.letter-icons li:first-child {
  background: #F5D3A0;
  color: #F6A200;
}
.letter-icons li:nth-child(2) {
  background: #DBDDFB;
  color: #979DFD;
}
.letter-icons li:nth-child(3) {
  background: #CAEBE8;
  color: #90D9CC;
}
.letter-icons li:nth-child(4) {
  background: #ECDFF0;
  color: #BA8DCC;
}

/* List specefic letter icons */
.list-body .employees-no .letter-icons li:last-child {
  border: 0;
  background: none;
  box-shadow: none;
  padding-top: 5px;
  padding-left: 25px;
  font-weight: normal;
  color: #363636;
  letter-spacing: .02em;
}
.list-body .employees-no .letter-icons li {
  width: 38px;
  height: 38px;
}

/* Grid specefic letter icons */
.grid-body .employees-no .letter-icons li {
  width: 30px;
  height: 30px;
  border-width: 3px;
  line-height: 24px;
  font-weight: 600;
  font-size: 11px;
  margin-left: -8px;
}
.grid-body .employees-no .letter-icons li:nth-child(5) {
  background: #ECDFF0;
  color: #BA8DCC;
}
.grid-body .employees-no .letter-icons li:nth-child(3),
.grid-body .employees-no .letter-icons li:nth-child(4) {
  display: none;
}
.grid-body .img-and-name .letter-icons {
  padding-top: 20px;
}
.grid-body .img-and-name .letter-icons li {
  margin: 15px auto;
  font-size: 16px;
  width: 48px;
  height: 48px;
  line-height: 42px;
}

/** Add department button */
.add-new-department a {
  border-color: rgba(229, 233, 242, 0.6);
  border-width: 2px;
  color: #363636;
  height: 40px;
  border-radius: 4px;
}
.add-new-department i {
  color: #2762CA;
}
.add-new-department .btn-text {
  font-size: 14px;
}

/** Filter buttons (Grid/List view) */
.filter-buttons a {
  background: transparent;
  color: #9E9FA3;
  padding: 18px 22px;
  font-size: 14px;
  border-width: 2px;
}
.filter-buttons.is-pulled-right a {
  padding: 18px 10px;
  color: #D8E5EB;
  font-size: 18px;
}
.filter-buttons .is-active {
  background: #fff;
  border-color: #ebeef5;
  color: #27313a;
}
.view-switcher .is-active.button {
  box-shadow: 1px 2px 8px 0 rgba(202, 210, 240, 0.4);
  border: 0;
}

/** List box headers */
.list-header li {
  display: inline-block;
  color: #9E9FA3;
}

/** List and Grid section */
.dash-list .list-body,
.dash-list .grid-body {
  background: #fff;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  position: relative;
}
.dash-list .columns {
  flex-wrap: wrap;
}

/* Checkbox */
.dash-list .has-checkbox {
  padding-left: 20px;
}
.dash-list .is-checkbox + label {
  padding: 0;
}

/* Thumbnail */
.dash-list .thumbnail {
  width: 36px;
  height: 36px;
  border-radius: 100%;
}
.dash-list .grid-body .thumbnail {
  position: absolute;
  bottom: 0;
  right: 25px;
  margin: 10px auto 17px;
  display: block;
}

/* List (div)table */
.list-table {
  display: table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 1em;
}
.list-table .list-table-body {
  display: table-row-group;
}
.list-table .list-table-row {
  display: table-row;
  height: 80px;
  box-shadow: 0 2px 10px 0 rgba(202, 210, 240, 0.2);
}
.list-table .list-table-cell {
  display: table-cell;
  vertical-align: middle;
  color: #98A0B4;
}
.list-body .align-part {
  display: flex;
  align-items: center;
}
.list-body .has-checkbox {
  width: 80px;
}
.list-header li:first-child {
  width: 65px;
}
.list-header li:nth-child(2),
.list-header li:nth-child(3) {
  width: 270px;
}
.role-normal-user .list-header li:first-child,
.role-normal-user .list-table .img-and-name {
  padding-left: 40px;
}
.role-normal-user .list-header li:first-child {
  width: 400px;
}
.role-normal-user .list-header li:nth-child(2) {
  width: 350px;
}
.role-normal-user .list-header li:nth-child(3) {
  width: 220px;
}
.role-normal-user .list-body .img-and-name {
  width: 330px;
}
.role-normal-user .list-body .description {
  width: 390px;
  padding-right: 25px;
}

/* List styles */
.list-body .box-actions {
  width: 30px;
  font-size: 20px;
}
.list-body .img-and-name span {
  padding-left: 15px;
}
.list-body .img-and-name {
  width: 280px;
}
.list-body .description {
  width: 250px;
}
.list-body .img-and-name span a {
  color: #27313a;
  font-weight: 600;
}

/* Grid styles */
.grid-body {
  text-align: center;
}
.grid-body .img-and-name.type-1 span {
  padding: 20px 30px;
  display: block;
}
.grid-body .has-checkbox,
.grid-body .img-and-name.type-2 span,
.grid-body .description {
  display: none;
}
.grid-body .action-dropdown i {
  color: #DEE2EA;
  font-size: 20px;
}
.grid-body .box-actions {
  position: absolute;
  top: 15px;
  right: 15px;
}
.grid-body .employees-no {
  background: #FAFBFD;
  padding: 20px 30px;
  border-top: 1px solid #EFF2F7;
}

/** List item edit/trash options */
.action-dropdown .dropdown-trigger {
  width: 10px;
  padding-left: 3px;
  cursor: pointer;
}
.action-dropdown .dropdown-content {
  display: flex;
  justify-content: space-evenly;
  width: 100px;
  color: #BACED8;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  font-size: 16px;
  left: -100px;
}
.action-dropdown .dropdown-content i {
  cursor: pointer;
}

/* Status buttons */
.status-active {
  background-color: #23d160;
  border-color: transparent;
  color: #fff;
}
.status-terminated {
  background-color: #ff3860;
  border-color: transparent;
  color: #fff;
}
.status-resigned {
  background-color: #ffdd57;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.7);
}
.status-deceased {
  background-color: #363636;
  border-color: transparent;
  color: whitesmoke;
}
.action-delete i:hover {
  color: #f44336;
}

@media only screen and (max-width: 1350px) {
.dash-list .list-header[data-v-576a43f8] {
    display: none;
}
}
@media screen and (max-width: 1024px) {
.view-switcher[data-v-576a43f8] {
    display: none;
}
}
@media screen and (max-width: 768px) {
.modal-card[data-v-576a43f8] {
    margin: 0;
}
}

.btn-remove {
  border-color: #DFE3EB;
  border-width: 2px;
  height: 40px;
  border-radius: 4px;
}
.btn-remove i {
  color: #f44336;
}

/** List box headers */
.list-header li {
  display: inline-block;
  color: #9E9FA3;
}

/** List and Grid section */
.dash-list .list-body,
.dash-list .grid-body {
  background: #fff;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  position: relative;
}
.dash-list .columns {
  flex-wrap: wrap;
}
.dash-list .has-checkbox {
  padding-left: 20px;
}
.dash-list .is-checkbox + label {
  padding: 0;
}
.is-checkbox[type="checkbox"] + label::before {
  width: 1.2rem;
  height: 1.2rem;
}
.list-table {
  display: table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 1em;
}
.table {
  background: none;
  border-collapse: separate;
  border-spacing: 0 1em;
}
.table tr {
  background-color: #fff;
}
.table td,
.table th {
  padding: 1.8em 1em;
  border-bottom: 0;
}
.table .text {
  width: 600px;
}
.table td > div:last-child {
  padding-top: 6px;
  color: #C0C6D2;
}
.has-min-height {
  min-height: 18px;
}
.is-unread,
.actions {
  vertical-align: middle !important;
}
.text strong {
  cursor: pointer;
  color: #667188;
}
.cal-icon {
  display: flex;
  align-items: center;
}

/** Unread point mark */
.is-unread .unread-mark {
  display: flex;
}
.unread-mark {
  background: #F4447C;
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 100%;
  border: 3px solid #fff;
  box-shadow: 1px 1px 18px 1px rgba(244, 68, 124, 0.2);
}

/** List item edit/trash options */
.action-dropdown .dropdown-trigger {
  width: 10px;
  padding-left: 3px;
  font-size: 20px;
  cursor: pointer;
}
.action-dropdown .dropdown-content {
  display: flex;
  justify-content: space-evenly;
  width: 100px;
  color: #BACED8;
  box-shadow: 3px 2px 12px 7px #f4f5fc;
  font-size: 16px;
  left: -100px;
}
.action-dropdown .dropdown-content i {
  cursor: pointer;
}
.is-checkbox[type="checkbox"] + label::after {
  top: 0.20rem;
  left: 0.45rem;
  border-color: #EFF2F6;
}
.announce-list .modal-content,
.announce-list .modal-card {
  width: 700px !important;
}
.announce-list .table tr {
  box-shadow: 0 2px 10px 0 rgba(202, 210, 240, 0.2);
}
.role-normal-user .announce-author {
  width: 200px;
  padding-left: 50px;
}
.action-edit i {
  color: #3f51b5;
}
.action-delete:hover i {
  color: #f44336;
}
.icon-user {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -33px -33px;
  padding-top: 26px !important;
}
.icon-calendar {
  width: 33px;
  height: 33px;
  background: url(../images/icons-overview.png) no-repeat -132px -33px;
}

@media only screen and (max-width: 990px) {
.table .text[data-v-5ca79eb1] {
    width: 300px;
}
}
@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td:first-child .has-text-centered[data-v-5ca79eb1] {
    text-align: left !important;
}
.res-table td[data-v-5ca79eb1]:nth-of-type(1):before {
    content: "Announcement";
}
.res-table td[data-v-5ca79eb1]:nth-of-type(2):before {
    content: "Description";
}
.res-table td[data-v-5ca79eb1]:nth-of-type(3):before {
    content: "Author";
}
.res-table td[data-v-5ca79eb1]:nth-of-type(4):before {
    content: "Date";
}
.res-table td[data-v-5ca79eb1]:nth-of-type(5):before {
    content: "Actions";
}
.action-dropdown .dropdown-content[data-v-5ca79eb1] {
    left: -110px;
    top: -20px;
}
.has-min-height[data-v-5ca79eb1] {
    display: none;
}
.table td > div[data-v-5ca79eb1]:last-child {
    padding-top: 0;
}
}

.announce-create {
  width: 625px;
  left: -625px;
  padding: 30px;
  background: #fff;
  box-shadow: 1px 1px 18px 1px #DBE7F7;
}
.create-header {
  display: flex;
  align-items: center;
}
.announce-create .icon-header,
.announce-create .icon-close {
  font-size: 24px;
  color: #2B62C9;
}
.announce-create .icon-close {
  right: 10px;
  position: absolute;
  top: 10px;
}
.announce-create .icon-close i {
  cursor: pointer;
}
.announce-create .add-title {
  font-size: 15px;
  font-weight: 600;
}
.create-content {
  padding-top: 20px;
}
.announce-create .input {
  height: 50px;
  border-color: #F4F5F7;
  box-shadow: none;
  border-width: 2px;
}
.ck-editor__top .ck-sticky-panel .ck-toolbar {
  background: #FAFBFD;
}
.ck-editor-toolbar .ck-button.ck-disabled,
.ck-editor-toolbar .ck-button:not(:hover):not(:focus):not(.ck-on) {
  background: none;
}
.ck-editor-toolbar .ck-button.ck-dropdown__button:not(:hover):not(:focus):not(.ck-on) {
  color: #C9D3E4;
  border-color: #C9D3E4;
}
.announce-create svg.ck-icon * {
  fill: #C9D3E4;
}
.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners,
.ck-rounded-corners .ck-editor__top .ck-sticky-panel .ck-toolbar {
  border-color: #F4F5F7;
  border-width: 2px;
}
.ck-editor__editable {
  padding: 10px;
  min-height: 115px;
  border-color: #F4F5F7;
  border-width: 2px;
  border-top: 0;
}
.create-footer {
  padding-top: 15px;
}
.announce-create .message-header {
  border: 2px solid #F4F5F7;
  background: #FAFBFD;
  border-bottom: 0;
}
.announce-create .message-header i {
  color: #C5D7E0;
  margin-right: 10px;
}
.announce-create .message-header h4 {
  color: #95989B;
}
.announce-create .message-body {
  background: #fff;
  border: 2px solid #F4F5F7;
}
.announce-create .message-body h5 {
  font-size: 13px;
  padding-bottom: 8px;
}
.announce-create select {
  border: 2px solid #F4F5F7;
}
.announce-create .select,
.announce-create select,
.announce-create .datepicker {
  width: 100%;
  font-size: 15px;
  color: #D1D4DC;
  height: 35px;
}
.announce-create .select:not(.is-multiple)::after {
  border-color: #D1D4DC;
}
.announce-create .is-checkbox[type="checkbox"] + label {
  font-size: 13px;
  color: #C5D7E0;
}
.announce-create .is-checkbox[type="checkbox"] + label::before {
  width: 1.5rem;
  height: 1.5rem;
}
.announce-btn {
  padding: 20px 0;
}
.announce-btn .button {
  font-size: 13px;
  padding: 5px 20px;
}
.announce-create .multiselect__tags {
  min-height: 36px;
  padding-top: 6px;
  border: 2px solid #F4F5F7;
}

@media only screen and (max-width: 643px) {
.announce-create[data-v-4c0a7d4f] {
    width: auto !important;
    left: auto !important;
    position: unset !important;
}
}

.details-section {
  z-index: 22;
  right: -30px;
  top: -20px;
}
.announce-right {
  display: flex;
}
.add-new-announce a {
  border-color: rgba(229, 233, 242, 0.6);
  border-width: 2px;
  height: 40px;
  margin-left: 10px;
  border-radius: 4px;
}
.add-new-announce i {
  color: #2762CA;
}
.add-new-announce .btn-text {
  font-size: 14px;
}

@media only screen and (max-width: 760px) {
.announce-right[data-v-676a5094] {
    float: none !important;
}
.announce-right .button[data-v-676a5094] {
    margin-left: 0;
}
.details-section[data-v-676a5094] {
    left: 0;
    right: 0;
    position: fixed;
}
}

.report-desc {
  margin-bottom: 10px;
}
.report-table {
  border-collapse: separate;
}
.report-table th {
  padding: 20px 0;
}
.report-table td {
  text-align: center;
  padding: 15px 0;
  color: #979FB3;
  vertical-align: middle;
  border-bottom: 1px solid #EBEFF3;
}
.report-table thead th {
  border-bottom: 0;
  text-align: center;
  border: 2px solid #EEF1F6;
  border-left: 0;
  border-right: 0;
}
.report-table th:first-child {
  border-left: 2px solid #EEF1F6;
  border-radius: 5px 0 0 5px;
}
.report-table th:last-child {
  border-right: 2px solid #EEF1F6;
  border-radius: 0 5px 5px 0;
}
.report-table .column-6 th {
  width: 16.4%;
}
.report-table thead tr {
  background: #FAFBFD;
}
.report-table .name {
  font-weight: 600;
  color: #18222C;
  min-width: 200px;
  display: flex;
  align-items: center;
}
.report-table .name img {
  width: 42px;
  border-radius: 100%;
  margin-right: 8px;
}
.report-table tbody .button {
  font-size: 13px;
  min-width: 120px;
  padding: 0 15px;
}
.report-table th:nth-child(2),
.report-table th:nth-child(3),
.report-table th:nth-child(6) {
  color: #64AEE0;
}
.report-table th:nth-child(4) {
  color: #DB7545;
}
.report-table th:nth-child(5) {
  color: #9F62B9;
}
.report-table th:nth-child(7),
.report-table th:nth-child(8) {
  color: #7FD5C2;
}

.reports-tab .filter-buttons .button {
  border: 2px solid rgba(216, 222, 236, 0.6) !important;
}
.filter-buttons a {
  background: transparent;
  color: #9E9FA3;
  padding: 18px 22px;
  font-size: 14px;
  border-width: 2px;
}
.filter-buttons .is-active {
  background: #fff;
  border-color: #ebeef5;
  color: #27313a;
}
.current-date {
  color: #828bff;
  padding-bottom: 5px;
}

@media only screen and (max-width: 868px) {
.reports-tab[data-v-6ddc2002] {
    display: flex !important;
}
.filter-buttons[data-v-6ddc2002] {
    display: block !important;
}
.filter-buttons .control a[data-v-6ddc2002] {
    width: 160px;
}
.reports-tab .is-active.button[data-v-6ddc2002] {
    border: 2px solid #DEE2EA;
}
}

.mr-top-30[data-v-3e189cb4] {
  margin-top: 30px;
}
.filter-box[data-v-3e189cb4] {
  padding: 20px;
  background: #fff;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.filter-box select[data-v-3e189cb4] {
  border: 0 none;
  padding: 10px;
}
.filter-by-txt[data-v-3e189cb4] {
  color: #929397;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table td[data-v-3e189cb4]:nth-of-type(1):before {
    content: "Department";
}
.res-table td[data-v-3e189cb4]:nth-of-type(2):before {
    content: "Under 18 year";
}
.res-table td[data-v-3e189cb4]:nth-of-type(3):before {
    content: "18 to 25 year";
}
.res-table td[data-v-3e189cb4]:nth-of-type(4):before {
    content: "26 to 35 year";
}
.res-table td[data-v-3e189cb4]:nth-of-type(5):before {
    content: "36 to 45 year";
}
.res-table td[data-v-3e189cb4]:nth-of-type(6):before {
    content: "46 to 55 year";
}
.res-table td[data-v-3e189cb4]:nth-of-type(7):before {
    content: "56 to 65 year";
}
.res-table td[data-v-3e189cb4]:nth-of-type(8):before {
    content: "65+ year";
}
}

.mr-top-30 {
  margin-top: 30px;
}

.mr-top-30 {
  margin-top: 30px;
}
.filter-box {
  padding: 20px;
  background: #fff;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  display: flex;
  justify-content: flex-end;
}
.filter-box p {
  margin-left: 40px;
  display: flex;
  align-items: center;
}
.filter-box select {
  border: 0 none;
}
.filter-by-txt {
  color: #929397;
}
.status-Active {
  background-color: #23d160;
  border-color: transparent;
  color: #fff;
}
.status-Terminated {
  background-color: #ff3860;
  border-color: transparent;
  color: #fff;
}
.status-Resigned {
  background-color: #ffdd57;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.7);
}
.status-Deceased {
  background-color: #363636;
  border-color: transparent;
  color: whitesmoke;
}
.box-btn span {
  padding: 5px;
  font-size: 14px;
  border-radius: 3px;
  text-transform: capitalize;
  width: 90px;
  display: inline-block;
  text-align: center;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table .name[data-v-b56aa57a] {
    display: flex;
    justify-content: center;
    color: #979FB3;
    font-weight: normal;
}
.res-table td[data-v-b56aa57a]:nth-of-type(1):before {
    content: "Employee Name";
}
.res-table td[data-v-b56aa57a]:nth-of-type(2):before {
    content: "Hire Date";
}
.res-table td[data-v-b56aa57a]:nth-of-type(3):before {
    content: "Job Title";
}
.res-table td[data-v-b56aa57a]:nth-of-type(4):before {
    content: "Department";
}
.res-table td[data-v-b56aa57a]:nth-of-type(5):before {
    content: "Location";
}
.res-table td[data-v-b56aa57a]:nth-of-type(6):before {
    content: "Status";
}
}

.box-content {
  padding-bottom: 0;
}

@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
.res-table .name[data-v-2425d61e] {
    display: flex;
    justify-content: center;
    color: #979FB3;
    font-weight: normal;
}
.res-table td[data-v-2425d61e]:nth-of-type(1):before {
    content: "Employee";
}
.res-table td[data-v-2425d61e]:nth-of-type(2):before {
    content: "Date";
}
.res-table td[data-v-2425d61e]:nth-of-type(3):before {
    content: "Pay Rate";
}
.res-table td[data-v-2425d61e]:nth-of-type(4):before {
    content: "Pay Type";
}
.res-table td[data-v-2425d61e]:nth-of-type(5):before {
    content: "Employee ID";
}
}

@keyframes spinAround {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(359deg);
}
}
.timeline {
  display: flex;
  flex-direction: column;
}
.timeline .timeline-header {
  width: 4em;
  min-width: 4em;
  max-width: 8em;
  word-wrap: normal;
  text-align: center;
  display: flex;
  justify-content: center;
}
.timeline .timeline-item {
  display: flex;
  display: -ms-flexbox;
  display: -webkit-flex;
  position: relative;
  margin-left: 2em;
  padding-bottom: 2em;
}
.timeline .timeline-item::before {
  content: "";
  background-color: #dbdbdb;
  display: block;
  width: 0.1em;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.timeline .timeline-item .timeline-marker {
  position: absolute;
  background: #dbdbdb;
  border: 0.1em solid #dbdbdb;
  border-radius: 100%;
  content: "";
  display: block;
  height: 0.8em;
  left: -0.35em;
  top: 1.2rem;
  width: 0.8em;
  display: flex;
  align-items: center;
  justify-content: center;
}
.timeline .timeline-item .timeline-marker.is-image {
  background: #dbdbdb;
  border: 0.1em solid #dbdbdb;
  border-radius: 100%;
  display: block;
  overflow: hidden;
}
.timeline .timeline-item .timeline-marker.is-image.is-16x16 {
  height: 16px;
  width: 16px;
  left: -8px;
}
.timeline .timeline-item .timeline-marker.is-image.is-24x24 {
  height: 24px;
  width: 24px;
  left: -12px;
}
.timeline .timeline-item .timeline-marker.is-image.is-32x32 {
  height: 32px;
  width: 32px;
  left: -16px;
}
.timeline .timeline-item .timeline-marker.is-image.is-48x48 {
  height: 48px;
  width: 48px;
  left: -24px;
}
.timeline .timeline-item .timeline-marker.is-image.is-64x64 {
  height: 64px;
  width: 64px;
  left: -32px;
}
.timeline .timeline-item .timeline-marker.is-image.is-96x96 {
  height: 96px;
  width: 96px;
  left: -48px;
}
.timeline .timeline-item .timeline-marker.is-image.is-128x128 {
  height: 128px;
  width: 128px;
  left: -64px;
}
.timeline .timeline-item .timeline-marker.is-icon {
  height: 1.5em;
  width: 1.5em;
  left: -0.7em;
  line-height: .75rem;
  padding: 0.25rem;
  background: #dbdbdb;
  border: 0.1em solid #dbdbdb;
  border-radius: 100%;
}
.timeline .timeline-item .timeline-marker.is-icon > i {
  color: white;
  font-size: 0.75rem !important;
}
.timeline .timeline-item .timeline-marker.is-outlined .image {
  background: white;
}
.timeline .timeline-item .timeline-marker.is-outlined.is-icon {
  background: white;
}
.timeline .timeline-item .timeline-marker.is-outlined.is-icon > i {
  color: #dbdbdb;
}
.timeline .timeline-item .timeline-marker.is-white {
  background-color: white !important;
  border-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-white .image {
  border-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-white.is-icon {
  background-color: white !important;
  border-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-white.is-icon > i {
  color: #0a0a0a !important;
}
.timeline .timeline-item .timeline-marker.is-white.is-outlined {
  background-color: white !important;
  border-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-white.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-white.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-white.is-outlined.is-icon > i {
  color: white !important;
}
.timeline .timeline-item .timeline-marker.is-black {
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
}
.timeline .timeline-item .timeline-marker.is-black .image {
  border-color: #0a0a0a !important;
}
.timeline .timeline-item .timeline-marker.is-black.is-icon {
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
}
.timeline .timeline-item .timeline-marker.is-black.is-icon > i {
  color: white !important;
}
.timeline .timeline-item .timeline-marker.is-black.is-outlined {
  background-color: white !important;
  border-color: #0a0a0a !important;
}
.timeline .timeline-item .timeline-marker.is-black.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-black.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-black.is-outlined.is-icon > i {
  color: #0a0a0a !important;
}
.timeline .timeline-item .timeline-marker.is-light {
  background-color: whitesmoke !important;
  border-color: whitesmoke !important;
}
.timeline .timeline-item .timeline-marker.is-light .image {
  border-color: whitesmoke !important;
}
.timeline .timeline-item .timeline-marker.is-light.is-icon {
  background-color: whitesmoke !important;
  border-color: whitesmoke !important;
}
.timeline .timeline-item .timeline-marker.is-light.is-icon > i {
  color: #363636 !important;
}
.timeline .timeline-item .timeline-marker.is-light.is-outlined {
  background-color: white !important;
  border-color: whitesmoke !important;
}
.timeline .timeline-item .timeline-marker.is-light.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-light.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-light.is-outlined.is-icon > i {
  color: whitesmoke !important;
}
.timeline .timeline-item .timeline-marker.is-dark {
  background-color: #363636 !important;
  border-color: #363636 !important;
}
.timeline .timeline-item .timeline-marker.is-dark .image {
  border-color: #363636 !important;
}
.timeline .timeline-item .timeline-marker.is-dark.is-icon {
  background-color: #363636 !important;
  border-color: #363636 !important;
}
.timeline .timeline-item .timeline-marker.is-dark.is-icon > i {
  color: whitesmoke !important;
}
.timeline .timeline-item .timeline-marker.is-dark.is-outlined {
  background-color: white !important;
  border-color: #363636 !important;
}
.timeline .timeline-item .timeline-marker.is-dark.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-dark.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-dark.is-outlined.is-icon > i {
  color: #363636 !important;
}
.timeline .timeline-item .timeline-marker.is-primary {
  background-color: #00d1b2 !important;
  border-color: #00d1b2 !important;
}
.timeline .timeline-item .timeline-marker.is-primary .image {
  border-color: #00d1b2 !important;
}
.timeline .timeline-item .timeline-marker.is-primary.is-icon {
  background-color: #00d1b2 !important;
  border-color: #00d1b2 !important;
}
.timeline .timeline-item .timeline-marker.is-primary.is-icon > i {
  color: #fff !important;
}
.timeline .timeline-item .timeline-marker.is-primary.is-outlined {
  background-color: white !important;
  border-color: #00d1b2 !important;
}
.timeline .timeline-item .timeline-marker.is-primary.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-primary.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-primary.is-outlined.is-icon > i {
  color: #00d1b2 !important;
}
.timeline .timeline-item .timeline-marker.is-link {
  background-color: #3273dc !important;
  border-color: #3273dc !important;
}
.timeline .timeline-item .timeline-marker.is-link .image {
  border-color: #3273dc !important;
}
.timeline .timeline-item .timeline-marker.is-link.is-icon {
  background-color: #3273dc !important;
  border-color: #3273dc !important;
}
.timeline .timeline-item .timeline-marker.is-link.is-icon > i {
  color: #fff !important;
}
.timeline .timeline-item .timeline-marker.is-link.is-outlined {
  background-color: white !important;
  border-color: #3273dc !important;
}
.timeline .timeline-item .timeline-marker.is-link.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-link.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-link.is-outlined.is-icon > i {
  color: #3273dc !important;
}
.timeline .timeline-item .timeline-marker.is-info {
  background-color: #209cee !important;
  border-color: #209cee !important;
}
.timeline .timeline-item .timeline-marker.is-info .image {
  border-color: #209cee !important;
}
.timeline .timeline-item .timeline-marker.is-info.is-icon {
  background-color: #209cee !important;
  border-color: #209cee !important;
}
.timeline .timeline-item .timeline-marker.is-info.is-icon > i {
  color: #fff !important;
}
.timeline .timeline-item .timeline-marker.is-info.is-outlined {
  background-color: white !important;
  border-color: #209cee !important;
}
.timeline .timeline-item .timeline-marker.is-info.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-info.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-info.is-outlined.is-icon > i {
  color: #209cee !important;
}
.timeline .timeline-item .timeline-marker.is-success {
  background-color: #23d160 !important;
  border-color: #23d160 !important;
}
.timeline .timeline-item .timeline-marker.is-success .image {
  border-color: #23d160 !important;
}
.timeline .timeline-item .timeline-marker.is-success.is-icon {
  background-color: #23d160 !important;
  border-color: #23d160 !important;
}
.timeline .timeline-item .timeline-marker.is-success.is-icon > i {
  color: #fff !important;
}
.timeline .timeline-item .timeline-marker.is-success.is-outlined {
  background-color: white !important;
  border-color: #23d160 !important;
}
.timeline .timeline-item .timeline-marker.is-success.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-success.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-success.is-outlined.is-icon > i {
  color: #23d160 !important;
}
.timeline .timeline-item .timeline-marker.is-warning {
  background-color: #ffdd57 !important;
  border-color: #ffdd57 !important;
}
.timeline .timeline-item .timeline-marker.is-warning .image {
  border-color: #ffdd57 !important;
}
.timeline .timeline-item .timeline-marker.is-warning.is-icon {
  background-color: #ffdd57 !important;
  border-color: #ffdd57 !important;
}
.timeline .timeline-item .timeline-marker.is-warning.is-icon > i {
  color: rgba(0, 0, 0, 0.7) !important;
}
.timeline .timeline-item .timeline-marker.is-warning.is-outlined {
  background-color: white !important;
  border-color: #ffdd57 !important;
}
.timeline .timeline-item .timeline-marker.is-warning.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-warning.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-warning.is-outlined.is-icon > i {
  color: #ffdd57 !important;
}
.timeline .timeline-item .timeline-marker.is-danger {
  background-color: #ff3860 !important;
  border-color: #ff3860 !important;
}
.timeline .timeline-item .timeline-marker.is-danger .image {
  border-color: #ff3860 !important;
}
.timeline .timeline-item .timeline-marker.is-danger.is-icon {
  background-color: #ff3860 !important;
  border-color: #ff3860 !important;
}
.timeline .timeline-item .timeline-marker.is-danger.is-icon > i {
  color: #fff !important;
}
.timeline .timeline-item .timeline-marker.is-danger.is-outlined {
  background-color: white !important;
  border-color: #ff3860 !important;
}
.timeline .timeline-item .timeline-marker.is-danger.is-outlined .image {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-danger.is-outlined.is-icon {
  background-color: white !important;
}
.timeline .timeline-item .timeline-marker.is-danger.is-outlined.is-icon > i {
  color: #ff3860 !important;
}
.timeline .timeline-item .timeline-content {
  padding: 1em 0 0 .5em;
  padding: 1em 0 0 2em;
}
.timeline .timeline-item .timeline-content .heading {
  font-weight: 600;
}
.timeline .timeline-item.is-white::before {
  background-color: white;
}
.timeline .timeline-item.is-black::before {
  background-color: #0a0a0a;
}
.timeline .timeline-item.is-light::before {
  background-color: whitesmoke;
}
.timeline .timeline-item.is-dark::before {
  background-color: #363636;
}
.timeline .timeline-item.is-primary::before {
  background-color: #00d1b2;
}
.timeline .timeline-item.is-link::before {
  background-color: #3273dc;
}
.timeline .timeline-item.is-info::before {
  background-color: #209cee;
}
.timeline .timeline-item.is-success::before {
  background-color: #23d160;
}
.timeline .timeline-item.is-warning::before {
  background-color: #ffdd57;
}
.timeline .timeline-item.is-danger::before {
  background-color: #ff3860;
}
.timeline.is-centered .timeline-header {
  display: flex;
  width: 100%;
  align-self: center;
}
.timeline.is-centered .timeline-item {
  width: 50%;
  align-self: flex-end;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) {
  align-self: flex-start;
  margin-left: 0;
  margin-right: 2em;
}
.timeline.is-centered .timeline-item:nth-of-type(2n)::before {
  right: -0.1em;
  left: auto;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker {
  left: auto;
  right: -0.45em;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-image.is-16x16 {
  left: auto;
  right: -8px;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-image.is-24x24 {
  left: auto;
  right: -12px;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-image.is-32x32 {
  left: auto;
  right: -16px;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-image.is-48x48 {
  left: auto;
  right: -24px;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-image.is-64x64 {
  left: auto;
  right: -32px;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-image.is-96x96 {
  left: auto;
  right: -48px;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-image.is-128x128 {
  left: auto;
  right: -64px;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-marker.is-icon {
  left: auto;
  right: -0.8em;
}
.timeline.is-centered .timeline-item:nth-of-type(2n) .timeline-content {
  padding: 1em 2em 0 0;
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex-basis: 100%;
}
.timeline.is-centered .timeline-item:nth-of-type(2n+1)::before {
  content: "";
  background-color: #dbdbdb;
  display: block;
  width: 0.1em;
  height: 100%;
  position: absolute;
  top: 0;
}
.timeline.is-rtl {
  justify-content: flex-end;
  align-items: flex-end;
}
.timeline.is-rtl .timeline-item {
  justify-content: flex-end;
  border-left: none;
  margin-left: 0;
  margin-right: 2em;
}
.timeline.is-rtl .timeline-item::before {
  right: 0;
  left: auto;
}
.timeline.is-rtl .timeline-item .timeline-marker {
  left: auto;
  right: -0.35em;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-image.is-16x16 {
  left: auto;
  right: -8px;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-image.is-24x24 {
  left: auto;
  right: -12px;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-image.is-32x32 {
  left: auto;
  right: -16px;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-image.is-48x48 {
  left: auto;
  right: -24px;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-image.is-64x64 {
  left: auto;
  right: -32px;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-image.is-96x96 {
  left: auto;
  right: -48px;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-image.is-128x128 {
  left: auto;
  right: -64px;
}
.timeline.is-rtl .timeline-item .timeline-marker.is-icon {
  left: auto;
  right: -0.7em;
}
.timeline.is-rtl .timeline-item .timeline-content {
  padding: 1em 2em 0 0;
  text-align: right;
}

.icon[data-v-3458dd0a] {
  border-radius: 100%;
  font-size: 18px;
  color: #fff;
}
.age-profile .icon[data-v-3458dd0a],
.age-profile .button[data-v-3458dd0a] {
  background: #6C76FF;
}
.head-count .icon[data-v-3458dd0a],
.head-count .button[data-v-3458dd0a] {
  background: #13BD9D;
}
.gender-profile .icon[data-v-3458dd0a],
.gender-profile .button[data-v-3458dd0a] {
  background: #00AFFF;
}
.year-service .icon[data-v-3458dd0a],
.year-service .button[data-v-3458dd0a] {
  background: #9C58B7;
}
.salary-history .icon[data-v-3458dd0a],
.salary-history .button[data-v-3458dd0a] {
  background: #E84B39;
}
.reports-list > .column[data-v-3458dd0a] {
  display: flex;
}
.box-content h3[data-v-3458dd0a] {
  padding: 25px 0;
  font-size: 15px !important;
}
.report-desc[data-v-3458dd0a] {
  line-height: 20px;
  color: #94A4C0;
  margin: 0 auto;
  margin-bottom: 25px;
}
.box-content .button i[data-v-3458dd0a] {
  margin-right: 10px;
}
.button[data-v-3458dd0a] {
  color: #fff;
  font-size: 14px;
  padding: 18px;
}
.age-profile .button[data-v-3458dd0a] {
  box-shadow: 0 7px 20px 0 rgba(24, 117, 240, 0.23);
}
.head-count .button[data-v-3458dd0a] {
  box-shadow: 0 7px 20px 0 rgba(22, 145, 19, 0.23);
}
.gender-profile .button[data-v-3458dd0a] {
  box-shadow: 0 7px 20px 0 rgba(0, 186, 255, 0.23);
}
.year-service .button[data-v-3458dd0a] {
  box-shadow: 0 7px 20px 0 rgba(155, 43, 201, 0.23);
}
.salary-history .button[data-v-3458dd0a] {
  box-shadow: 0 7px 20px 0 rgba(240, 24, 34, 0.23);
}

@media only screen and (max-width: 768px) {
.reports-list > .column[data-v-3458dd0a] {
    display: block;
}
}

