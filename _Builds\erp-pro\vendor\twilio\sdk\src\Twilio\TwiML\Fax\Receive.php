<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\TwiML\Fax;

use <PERSON><PERSON><PERSON>\TwiML\TwiML;

class Receive extends TwiML {
    /**
     * Receive constructor.
     *
     * @param array $attributes Optional attributes
     */
    public function __construct($attributes = array()) {
        parent::__construct('Receive', null, $attributes);
    }

    /**
     * Add Action attribute.
     *
     * @param string $action Receive action URL
     * @return static $this.
     */
    public function setAction($action) {
        return $this->setAttribute('action', $action);
    }

    /**
     * Add Method attribute.
     *
     * @param string $method Receive action URL method
     * @return static $this.
     */
    public function setMethod($method) {
        return $this->setAttribute('method', $method);
    }

    /**
     * Add MediaType attribute.
     *
     * @param string $mediaType The media type used to store media in the fax media
     *                          store
     * @return static $this.
     */
    public function setMediaType($mediaType) {
        return $this->setAttribute('mediaType', $mediaType);
    }

    /**
     * Add PageSize attribute.
     *
     * @param string $pageSize What size to interpret received pages as
     * @return static $this.
     */
    public function setPageSize($pageSize) {
        return $this->setAttribute('pageSize', $pageSize);
    }

    /**
     * Add StoreMedia attribute.
     *
     * @param bool $storeMedia Whether or not to store received media in the fax
     *                         media store
     * @return static $this.
     */
    public function setStoreMedia($storeMedia) {
        return $this->setAttribute('storeMedia', $storeMedia);
    }
}
