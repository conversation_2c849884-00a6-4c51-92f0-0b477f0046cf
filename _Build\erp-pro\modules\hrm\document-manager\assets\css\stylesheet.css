/*file system style*/
.file-primary{
    width: 98%;
}
.fileUpload {
    position: relative;
    overflow: hidden;
    margin: 10px;
}
.fileUpload input.upload {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    padding: 0;
    font-size: 20px;
    cursor: pointer;
    opacity: 0;
    filter: alpha(opacity=0);
    background-color: #337ab7;
    border-color: #2e6da4;
    color: #fff;
}
.fileUpload .fu_text{
    font-family: "Open Sans", sans-serif;
}
.create_folder{
    margin-right: 10px !important;
}
.file-search-box{
    margin-bottom: 15px !important;
}
#browse-header{
    background: #fff none repeat scroll 0 0;
    min-height: 98px;
    position: fixed;
    top: 50px;
    width: 800px;
    z-index: 4;
}
#file_folder_options{
    float: left;
    width: 80%;
}
#file_folder_wrapper{
    /*float: left;*/
    /*width: 90%;*/
}
#file_folder_list{
    float: left;
    margin: 0;
    width: 100%;
    background-color: #fafafa;
    list-style-type: none;
}
#file_folder_wrapper ul#file_folder_list li,
#company_file_folder_wrapper ul#file_folder_list li{
    border-bottom: 1px solid #e5e5e5;
    float: left;
    margin-bottom: 0;
    padding: 7px 0;
    width: 100%;
}
#file_folder_wrapper ul#file_folder_list li:hover,
#company_file_folder_wrapper ul#file_folder_list li:hover{
    background-color: #f5fafe;
}
.dir_icon{
    width: 32px;
    float: left;
}
#file_folder_wrapper ul#file_folder_list li .filename-col,
#company_file_folder_wrapper ul#file_folder_list li .filename-col{
    float: left;
    height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 35%;
    padding-left: 7px;
}
#file_folder_wrapper ul#file_folder_list li .modified,
#company_file_folder_wrapper ul#file_folder_list li .modified{
    float: left;
    height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 14.5%;
    margin-right: 10px;
}
#file_folder_wrapper ul#file_folder_list li .file_dir_link,
#company_file_folder_wrapper ul#file_folder_list li .file_dir_link{
    color: #3d464d;
    font-size: 13px;
    line-height: 32px;
    margin-left: 10px;
    text-decoration: none;
}
#file_folder_wrapper ul#file_folder_list li a.userlink,
#company_file_folder_wrapper ul#file_folder_list li a.userlink{
    text-decoration: none;
}
#file_folder_wrapper ul#file_folder_list li .file_dir_link:hover,
#company_file_folder_wrapper ul#file_folder_list li .file_dir_link:hover{
    text-decoration: underline;
}
#file_folder_wrapper ul#file_folder_list li .modified-time,
#file_folder_wrapper ul#file_folder_list li .fsizek,
#company_file_folder_wrapper ul#file_folder_list li .modified-time,
#company_file_folder_wrapper ul#file_folder_list li .fsizek{
    line-height: 30px;
}

#file_browser_breadcrumb_list {
    list-style-type: none;
}

#file_browser_breadcrumb_list li{
    float: left;
    margin-right: 5px;
}
#file_browser_breadcrumb_list li a{
    text-decoration: none;
}
#file_browser_breadcrumb_list li:after{
    font-family: dashicons;
    content: "\f345";
    color: #999;
    vertical-align: middle;
}
#file_browser_breadcrumb_list li:last-child:after{
    content: '';
}
/* always present */
.fade-transition {
    transition: all 1s ease;
}
/* .expand-enter defines the starting state for entering */
/* .expand-leave defines the ending state for leaving */
.fade-enter, .fade-leave {
    opacity: 0;
}
.dir_file_chkbox{
    float: left;
    margin-top: 10px !important;
}
#checkbox_all{
    border-bottom: 1px solid #e5e5e5;
    float: left;
    width: 100%;
    padding-bottom: 8px;
    background-color: #fff;
    padding-top: 8px;
    margin-top: 5px;
}
#dir-file-operation-control{
    float: left;
    width: 100%;
    margin-top: 5px;
}
#search-feature{
    float: left;
    width: 100%;
    margin-bottom: 8px;
}
#search-feature #search_input{
    float: right;
}
#browse-location{
    float: left;
    width: 53%;
}
.header_caption{
    float: left;
}
.header_caption_checkall{
    width: 35%;
    margin-left: 9px;
}
.header_caption_mat{
    width: 15%;
}
.filelink img{
    width: 32px;
}
.dir_file_icon_wrapper{
    float: left;
}
.image_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 192px 0;
}
.audio_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 160px 0;
}
.video_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 160px -32px;
}
.zip_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 64px 64px;
}
.text_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 0px 64px;
}
.pdf_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') -32px -32px;
}
.office_word_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 0px -64px;
}
.office_excel_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 64px -64px;
}
.office_powerpoint_file_icon{
    width: 32px;
    height: 32px;
    background: url('../images/sprite.png') 96px -64px;
}
.no_file_folder_wrapper{
    float: left;
    margin-top: 50px;
    text-align: center;
    width: 100%;
    color: #999;
}
.no_file_folder_wrapper h4 {
    font-weight: normal;
}
.no_file_folder_wrapper .dashicons-portfolio{
    font-size: 32px;
}
.doc-upload-filelist{
    position: absolute;
    margin-left: 90px;
    background-color: #E8E8E8;
    /*width: 213px;*/
}
.upload-item{
    padding-left: 5px;
    padding-right: 5px;
    padding-bottom: 5px;
}
.doc-attachment-area .progress{
    background: #ffffff -moz-linear-gradient(center bottom , #ffffff 0%, #f7f7f7 100%) repeat scroll 0 0;
    border: 1px solid #d1d1d1;
    border-radius: 3px;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.1) inset;
    float: left;
    height: 22px;
    line-height: 2em;
    margin: 0;
    overflow: hidden;
    padding: 0;
    width: 200px;
}
.doc-attachment-area .percent{
    color: rgba(0, 0, 0, 0.6);
    padding: 0 8px;
    position: relative;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.4);
    width: 200px;
    z-index: 10;
}
.doc-attachment-area .bar{
    background-color: #83b4d8;
    background-image: -moz-linear-gradient(center bottom , #72a7cf 0%, #90c5ee 100%);
    border-radius: 3px;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
    height: 100%;
    margin-top: -26px;
    width: 0;
    z-index: 9;
}
.error_notice{
    float: left;
    width: 99%;
    padding: 11px 15px;
    /*border: 1px solid #ff0d2d;*/
    border-left: 3px solid #ff0000;
    /*background-color: #fadce0;*/
    background-color: #fff;
    /*border-radius: 2px;*/
    /*text-align: center;*/
    text-align: left;
    font-size: 14px;
}
.success_notice{
    float: left;
    width: 99%;
    padding: 11px 15px;
    /*border: 1px solid #1c9e08;*/
    border-left: 3px solid #7ad03a;
    /*background-color: #e0fadc;*/
    background-color: #fff;
    /*border-radius: 2px;*/
    /*text-align: center;*/
    text-align: left;
    font-size: 14px;
    margin-bottom: 5px;
}
#loader_wrapper{
    /*margin: 0 auto;*/
    /*width: 32px;*/
    margin-top: 10px !important;
    position: absolute;
    width: 40% !important;
}
#loader_gif{
    background-image: url('../images/loader.gif');
    background-repeat: no-repeat;
    width: 32px;
    height: 32px;
}
.not-loaded{
    display: none;
}
#operational-control{
    float: right;
}
#file_browser_breadcrumb_list{
    margin: 8px 0 0;
}
/*body.js .no-js-display{*/
    /*display: block;*/
/*}*/
/*css for uploader box*/
.upload-item-head{
    background-color: #323232;
    color: #fff;
    padding: 5px;
    margin-bottom: 10px;
}
.doc-upload-filelist{
    background-color: #e8e8e8;
    bottom: 10px;
    /*margin-left: -115px;*/
    padding: 5px;
    position: fixed;
    width: 205px;
    right: 12px;
    z-index: 1;
    display: none;
}
/*tree view css*/
.css-treeview ul,
.css-treeview li
{
    padding: 0;
    margin: 0;
    list-style: none;
}

.css-treeview li{
    margin-top: 5px;
}

.css-treeview input
{
    position: absolute;
    opacity: 0;
}
.css-treeview input.itid{
    margin-top: 3px;
}
.css-treeview
{
    user-select: none;
}

.css-treeview a
{
    color: #35d;
    text-decoration: none;
}

.css-treeview a:hover
{
    text-decoration: underline;
}

.css-treeview input + label + ul
{
    margin: 0 0 0 22px;
}

.css-treeview input ~ ul
{
    display: none;
}

.css-treeview label,
.css-treeview label::before
{
    cursor: pointer;
}

.css-treeview input:disabled + label
{
    cursor: default;
    opacity: .6;
}

.css-treeview input:checked:not(:disabled) ~ ul
{
    display: block;
}

.css-treeview label,
.css-treeview label::before
{
    background: url("../images/icons.png") no-repeat;
}

.css-treeview label,
.css-treeview a,
.css-treeview label::before
{
    display: inline-block;
    height: 16px;
    line-height: 16px;
    vertical-align: middle;
}

.css-treeview label
{
    background-position: 18px 0;
    font-size: 15px;
    line-height: 7px;
}

.css-treeview label:hover{
    background-color: #EBF6FA;
}

.css-treeview label::before
{
    content: "";
    width: 16px;
    margin: 0 22px 0 0;
    vertical-align: middle;
    background-position: 0 -32px;
}

.css-treeview input:checked + label::before
{
    background-position: 0 -16px;
}

@media screen and (-webkit-min-device-pixel-ratio:0)
{
    .css-treeview
    {
        -webkit-animation: webkit-adjacent-element-selector-bugfix infinite 1s;
    }

    @-webkit-keyframes webkit-adjacent-element-selector-bugfix
    {
        from
        {
            padding: 0;
        }
        to
        {
            padding: 0;
        }
    }
}

.share_template_wrap .select2.select2-container, .share_by {
    width: 50% !important;
}

.share_template_wrap .row.no-show {
    display: none;
}