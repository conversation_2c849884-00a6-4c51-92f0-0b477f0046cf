# Copyright (C) 2020 weDevs
# This file is distributed under the GPL2.
msgid ""
msgstr ""
"Project-Id-Version: WP ERP - Workflow 1.2.2\n"
"Report-Msgid-Bugs-To: https://wperp.com/support/\n"
"POT-Creation-Date: 2020-05-29 14:59:46+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2020-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <EMAIL@ADDRESS>\n"
"X-Generator: grunt-wp-i18n 0.5.4\n"

#: i18n/localize-strings.php:8
msgid "No action added"
msgstr ""

#: i18n/localize-strings.php:9
msgid "Choose an action"
msgstr ""

#: i18n/localize-strings.php:10
msgid "Action Name"
msgstr ""

#: i18n/localize-strings.php:11
msgid "Role"
msgstr ""

#: i18n/localize-strings.php:12
msgid "Users"
msgstr ""

#: i18n/localize-strings.php:13
msgid "Employees"
msgstr ""

#: i18n/localize-strings.php:14 includes/functions.php:522
msgid "Task Title"
msgstr ""

#: i18n/localize-strings.php:15 includes/functions.php:415
msgid "Description"
msgstr ""

#: i18n/localize-strings.php:16
msgid "Select Condition"
msgstr ""

#: i18n/localize-strings.php:17
msgid "Hook Name"
msgstr ""

#: i18n/localize-strings.php:18 includes/functions.php:705
msgid "Subject"
msgstr ""

#: i18n/localize-strings.php:19
msgid "To"
msgstr ""

#: i18n/localize-strings.php:20
msgid "The Contact Itself"
msgstr ""

#: i18n/localize-strings.php:21
msgid "The User Itself"
msgstr ""

#: i18n/localize-strings.php:22
msgid "The Customer Itself"
msgstr ""

#: i18n/localize-strings.php:23
msgid "The Employee Itself"
msgstr ""

#: i18n/localize-strings.php:24 includes/functions.php:518
#: includes/functions.php:523 includes/functions.php:530
msgid "Message"
msgstr ""

#: i18n/localize-strings.php:25
msgid "Log Type"
msgstr ""

#: i18n/localize-strings.php:26 includes/class-workflows-list-table.php:78
msgid "Date"
msgstr ""

#: i18n/localize-strings.php:27
msgid "Time"
msgstr ""

#: i18n/localize-strings.php:28
msgid "Invite CRM User"
msgstr ""

#: i18n/localize-strings.php:29
msgid "Add this action"
msgstr ""

#: i18n/localize-strings.php:30
msgid "Update this action"
msgstr ""

#: i18n/localize-strings.php:31
msgid "Choose a contact"
msgstr ""

#: i18n/localize-strings.php:32 includes/functions.php:528
msgid "Schedule Title"
msgstr ""

#: i18n/localize-strings.php:33 includes/functions.php:524
#: includes/functions.php:531 includes/functions.php:545
#: includes/functions.php:552
msgid "Start Date"
msgstr ""

#: i18n/localize-strings.php:34 includes/functions.php:546
#: includes/functions.php:553
msgid "End Date"
msgstr ""

#: i18n/localize-strings.php:35
msgid "All Day"
msgstr ""

#: i18n/localize-strings.php:36 includes/functions.php:529
msgid "Schedule Type"
msgstr ""

#: i18n/localize-strings.php:37
msgid "Allow Notification"
msgstr ""

#: i18n/localize-strings.php:38
msgid "Notify Via"
msgstr ""

#: i18n/localize-strings.php:39
msgid "Notify Before"
msgstr ""

#: i18n/localize-strings.php:40
msgid "Field Name"
msgstr ""

#: i18n/localize-strings.php:41
msgid "Field Value"
msgstr ""

#: i18n/localize-strings.php:42
msgid "Notification"
msgstr ""

#: i18n/localize-strings.php:43
msgid "Allow"
msgstr ""

#: i18n/localize-strings.php:44
msgid "Text Variables"
msgstr ""

#: i18n/localize-strings.php:45
msgid "Insert"
msgstr ""

#: i18n/localize-strings.php:46
msgid "Enter value"
msgstr ""

#: i18n/localize-strings.php:47
msgid ""
"The text variables are coming from the current event. <br/>Select any of "
"the following text variable and click on the insert button."
msgstr ""

#: i18n/localize-strings.php:48 includes/views/workflow-new.php:20
#: includes/views/workflow-new.php:59
msgid "This field is required"
msgstr ""

#: i18n/localize-strings.php:49
msgid "Select Contact(s)"
msgstr ""

#: i18n/localize-strings.php:50
msgid "Select Employee(s)"
msgstr ""

#: i18n/localize-strings.php:51
msgid "Select CRM User(s)"
msgstr ""

#: i18n/localize-strings.php:52
msgid "OR"
msgstr ""

#: i18n/localize-strings.php:53
msgid "You should add at least an action!"
msgstr ""

#: i18n/localize-strings.php:54
msgid "Successfully saved!"
msgstr ""

#: includes/class-admin-menu.php:77 includes/class-admin-menu.php:83
msgid "Workflow"
msgstr ""

#: includes/class-admin-menu.php:79
msgid "All Workflows"
msgstr ""

#: includes/class-admin-menu.php:81 includes/class-admin-menu.php:84
msgid "Add New"
msgstr ""

#: includes/class-admin-menu.php:372
msgid "Number of items per page:"
msgstr ""

#: includes/class-workflows-list-table.php:10
msgid "workflow"
msgstr ""

#: includes/class-workflows-list-table.php:11
msgid "workflows"
msgstr ""

#: includes/class-workflows-list-table.php:44
#: includes/class-workflows-list-table.php:171
msgid "Restore"
msgstr ""

#: includes/class-workflows-list-table.php:45
msgid "Parmanent Delete"
msgstr ""

#: includes/class-workflows-list-table.php:53
msgid "Activate"
msgstr ""

#: includes/class-workflows-list-table.php:53
msgid "Pause"
msgstr ""

#: includes/class-workflows-list-table.php:57
msgid "Edit"
msgstr ""

#: includes/class-workflows-list-table.php:72
msgid "Name"
msgstr ""

#: includes/class-workflows-list-table.php:73 includes/functions.php:403
msgid "Status"
msgstr ""

#: includes/class-workflows-list-table.php:74
#: includes/views/workflow-new.php:49
msgid "Module"
msgstr ""

#: includes/class-workflows-list-table.php:75
#: includes/views/workflow-new.php:55
msgid "Event"
msgstr ""

#: includes/class-workflows-list-table.php:76
msgid "Total Runs"
msgstr ""

#: includes/class-workflows-list-table.php:77
msgid "Created By"
msgstr ""

#: includes/class-workflows-list-table.php:115
msgid "All"
msgstr ""

#: includes/class-workflows-list-table.php:116
msgid "Active"
msgstr ""

#: includes/class-workflows-list-table.php:117
msgid "Paused"
msgstr ""

#: includes/class-workflows-list-table.php:118
msgid "Trash"
msgstr ""

#: includes/class-workflows-list-table.php:156
msgid "No workflows found."
msgstr ""

#: includes/class-workflows-list-table.php:166
msgid "Delete"
msgstr ""

#: includes/class-workflows-list-table.php:172
msgid "Permanent Delete"
msgstr ""

#: includes/functions.php:73
msgid "No field provided"
msgstr ""

#: includes/functions.php:77
msgid "No value provided"
msgstr ""

#: includes/functions.php:353 includes/functions.php:390
#: includes/functions.php:504 includes/functions.php:560
#: includes/functions.php:579 includes/functions.php:598
#: includes/functions.php:617
msgid "First Name"
msgstr ""

#: includes/functions.php:354 includes/functions.php:392
#: includes/functions.php:505 includes/functions.php:561
#: includes/functions.php:580 includes/functions.php:599
#: includes/functions.php:618
msgid "Last Name"
msgstr ""

#: includes/functions.php:355 includes/functions.php:373
#: includes/functions.php:502 includes/functions.php:562
#: includes/functions.php:581 includes/functions.php:600
#: includes/functions.php:619
msgid "Email"
msgstr ""

#: includes/functions.php:356 includes/functions.php:374
#: includes/functions.php:405 includes/functions.php:564
#: includes/functions.php:583 includes/functions.php:602
#: includes/functions.php:621
msgid "Phone"
msgstr ""

#: includes/functions.php:357 includes/functions.php:375
#: includes/functions.php:407 includes/functions.php:565
#: includes/functions.php:584 includes/functions.php:603
#: includes/functions.php:622
msgid "Mobile"
msgstr ""

#: includes/functions.php:358 includes/functions.php:376
#: includes/functions.php:566 includes/functions.php:585
#: includes/functions.php:604 includes/functions.php:623
msgid "Website"
msgstr ""

#: includes/functions.php:359 includes/functions.php:377
#: includes/functions.php:567 includes/functions.php:586
#: includes/functions.php:605 includes/functions.php:624
msgid "Fax"
msgstr ""

#: includes/functions.php:360 includes/functions.php:378
#: includes/functions.php:568 includes/functions.php:587
#: includes/functions.php:606 includes/functions.php:625
msgid "Notes"
msgstr ""

#: includes/functions.php:361 includes/functions.php:379
#: includes/functions.php:416 includes/functions.php:569
#: includes/functions.php:588 includes/functions.php:607
#: includes/functions.php:626
msgid "Street 1"
msgstr ""

#: includes/functions.php:362 includes/functions.php:380
#: includes/functions.php:417 includes/functions.php:570
#: includes/functions.php:589 includes/functions.php:608
#: includes/functions.php:627
msgid "Street 2"
msgstr ""

#: includes/functions.php:363 includes/functions.php:381
#: includes/functions.php:418 includes/functions.php:571
#: includes/functions.php:590 includes/functions.php:609
#: includes/functions.php:628
msgid "City"
msgstr ""

#: includes/functions.php:364 includes/functions.php:382
#: includes/functions.php:420 includes/functions.php:572
#: includes/functions.php:591 includes/functions.php:610
#: includes/functions.php:629
msgid "State"
msgstr ""

#: includes/functions.php:365 includes/functions.php:383
#: includes/functions.php:421 includes/functions.php:573
#: includes/functions.php:592 includes/functions.php:611
#: includes/functions.php:630
msgid "Postal Code"
msgstr ""

#: includes/functions.php:366 includes/functions.php:384
#: includes/functions.php:419 includes/functions.php:574
#: includes/functions.php:593 includes/functions.php:612
#: includes/functions.php:631
msgid "Country"
msgstr ""

#: includes/functions.php:367 includes/functions.php:385
#: includes/functions.php:575 includes/functions.php:594
#: includes/functions.php:613 includes/functions.php:632
msgid "Currency"
msgstr ""

#: includes/functions.php:368 includes/functions.php:386
msgid "Life Stage"
msgstr ""

#: includes/functions.php:372 includes/functions.php:563
#: includes/functions.php:582 includes/functions.php:601
#: includes/functions.php:620
msgid "Company"
msgstr ""

#: includes/functions.php:391
msgid "Middle Name"
msgstr ""

#: includes/functions.php:393
msgid "User Email"
msgstr ""

#: includes/functions.php:394
msgid "Designation"
msgstr ""

#: includes/functions.php:395
msgid "Department"
msgstr ""

#: includes/functions.php:396
msgid "Location"
msgstr ""

#: includes/functions.php:397
msgid "Hiring Source"
msgstr ""

#: includes/functions.php:398
msgid "Hiring Date"
msgstr ""

#: includes/functions.php:399
msgid "Date of Birth"
msgstr ""

#: includes/functions.php:400
msgid "Pay Rate"
msgstr ""

#: includes/functions.php:401
msgid "Pay Type"
msgstr ""

#: includes/functions.php:402
msgid "Type"
msgstr ""

#: includes/functions.php:404
msgid "Other Email"
msgstr ""

#: includes/functions.php:406
msgid "Work Phone"
msgstr ""

#: includes/functions.php:408
msgid "Address"
msgstr ""

#: includes/functions.php:409
msgid "Gender"
msgstr ""

#: includes/functions.php:410
msgid "Marital Status"
msgstr ""

#: includes/functions.php:411
msgid "Nationality"
msgstr ""

#: includes/functions.php:412
msgid "Driving License"
msgstr ""

#: includes/functions.php:413
msgid "Hobbies"
msgstr ""

#: includes/functions.php:414
msgid "User Url"
msgstr ""

#: includes/functions.php:434
msgid "Created User"
msgstr ""

#: includes/functions.php:439
msgid "Created Contact"
msgstr ""

#: includes/functions.php:440
msgid "Deleted Contact"
msgstr ""

#: includes/functions.php:441
msgid "Subscribed Contact"
msgstr ""

#: includes/functions.php:442
msgid "Unsubscribed Contact"
msgstr ""

#: includes/functions.php:443
msgid "Created Note"
msgstr ""

#: includes/functions.php:444
msgid "Created Task"
msgstr ""

#: includes/functions.php:445
msgid "Scheduled Meeting"
msgstr ""

#: includes/functions.php:449
msgid "Inbound Email"
msgstr ""

#: includes/functions.php:455
msgid "Created Employee"
msgstr ""

#: includes/functions.php:456
msgid "Deleted Employee"
msgstr ""

#: includes/functions.php:457
msgid "Published Announcement"
msgstr ""

#: includes/functions.php:458
msgid "Requested Leave"
msgstr ""

#: includes/functions.php:459
msgid "Confirmed Leave Request"
msgstr ""

#: includes/functions.php:460
msgid "Happened Birthday Today"
msgstr ""

#: includes/functions.php:466
msgid "Created Customer"
msgstr ""

#: includes/functions.php:467
msgid "Deleted Customer"
msgstr ""

#: includes/functions.php:468
msgid "Created Vendor"
msgstr ""

#: includes/functions.php:469
msgid "Deleted Vendor"
msgstr ""

#: includes/functions.php:470
msgid "Added Sale"
msgstr ""

#: includes/functions.php:471
msgid "Added Expense"
msgstr ""

#: includes/functions.php:475
msgid "Added Check"
msgstr ""

#: includes/functions.php:476
msgid "Added Bill"
msgstr ""

#: includes/functions.php:477
msgid "Added Purchase Order"
msgstr ""

#: includes/functions.php:478
msgid "Added purchase"
msgstr ""

#: includes/functions.php:503
msgid "Roles"
msgstr ""

#: includes/functions.php:506
msgid "Full Name"
msgstr ""

#: includes/functions.php:539
msgid "Title"
msgstr ""

#: includes/functions.php:540
msgid "Content"
msgstr ""

#: includes/functions.php:544 includes/functions.php:551
msgid "Reason"
msgstr ""

#: includes/functions.php:547 includes/functions.php:554
msgid "Policy"
msgstr ""

#: includes/functions.php:636 includes/functions.php:689
#: includes/functions.php:697
msgid "Issue Date"
msgstr ""

#: includes/functions.php:637 includes/functions.php:646
#: includes/functions.php:667 includes/functions.php:675
#: includes/functions.php:682 includes/functions.php:690
#: includes/functions.php:698
msgid "Due Date"
msgstr ""

#: includes/functions.php:638 includes/functions.php:648
#: includes/functions.php:655 includes/functions.php:661
#: includes/functions.php:668 includes/functions.php:676
#: includes/functions.php:683 includes/functions.php:692
#: includes/functions.php:699
msgid "Total Amount"
msgstr ""

#: includes/functions.php:639 includes/functions.php:649
#: includes/functions.php:669 includes/functions.php:693
#: includes/functions.php:700
msgid "Due Amount"
msgstr ""

#: includes/functions.php:645 includes/functions.php:654
#: includes/functions.php:660 includes/functions.php:666
#: includes/functions.php:674 includes/functions.php:681
msgid "Transaction Date"
msgstr ""

#: includes/functions.php:647
msgid "Voucher Number"
msgstr ""

#: includes/functions.php:691
msgid "Invoice Number"
msgstr ""

#: includes/functions.php:706
msgid "From"
msgstr ""

#: includes/functions.php:707
msgid "Body"
msgstr ""

#: includes/functions.php:916
msgid "Add User Role"
msgstr ""

#: includes/functions.php:917
msgid "Send Email"
msgstr ""

#: includes/functions.php:918
msgid "Assign Task"
msgstr ""

#: includes/functions.php:919
msgid "Trigger Action Hook"
msgstr ""

#: includes/functions.php:920
msgid "Update Field"
msgstr ""

#: includes/functions.php:921
msgid "Add Activity"
msgstr ""

#: includes/functions.php:922
msgid "Schedule Meeting"
msgstr ""

#: includes/functions.php:925
msgid "Send Invoice"
msgstr ""

#: includes/views/workflow-new.php:4
msgid "Edit Workflow"
msgstr ""

#: includes/views/workflow-new.php:6
msgid "Create Workflow"
msgstr ""

#: includes/views/workflow-new.php:16
msgid "Workflow Name"
msgstr ""

#: includes/views/workflow-new.php:19
msgid "Enter Workflow Name"
msgstr ""

#: includes/views/workflow-new.php:25
msgid "Delay"
msgstr ""

#: includes/views/workflow-new.php:44
msgid "Trigger"
msgstr ""

#: includes/views/workflow-new.php:52
msgid "Select Module"
msgstr ""

#: includes/views/workflow-new.php:58
msgid "Select Event"
msgstr ""

#: includes/views/workflow-new.php:68
msgid "Conditions"
msgstr ""

#: includes/views/workflow-new.php:76
msgid "of the following conditions"
msgstr ""

#: includes/views/workflow-new.php:85
msgid "+ Add Condition"
msgstr ""

#: includes/views/workflow-new.php:98
msgid "Actions"
msgstr ""

#: includes/views/workflow-new.php:122
msgid "Update"
msgstr ""

#: includes/views/workflow-new.php:128
msgid "Save &amp; Activate"
msgstr ""

#: includes/views/workflow-new.php:129
msgid "Save Only"
msgstr ""

#: includes/views/workflows.php:2
msgid "Workflows"
msgstr ""

#: includes/views/workflows.php:12
msgid "Search Workflow"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "WP ERP - Workflow"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://wperp.com/downloads/workflow/"
msgstr ""

#. Description of the plugin/theme
msgid "Workflow Automation System"
msgstr ""

#. Author of the plugin/theme
msgid "weDevs"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://wedevs.com"
msgstr ""