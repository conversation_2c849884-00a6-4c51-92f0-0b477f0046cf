{"name": "phpcompatibility/phpcompatibility-wp", "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "type": "phpcodesniffer-standard", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "homepage": "http://phpcompatibility.com/", "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "prefer-stable": true}