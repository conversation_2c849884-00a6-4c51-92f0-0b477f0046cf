{"name": "infobip/infobip-api-php-client", "description": "Infobip SMS library for PHP", "version": "1.1.0", "keywords": ["infobip", "sms", "hlr", "msisdn", "api"], "homepage": "http://dev.infobip.com", "authors": [{"name": "", "email": "<EMAIL>"}], "support": {"email": "<EMAIL>"}, "license": "Apache-2.0", "minimum-stability": "dev", "require": {"netresearch/jsonmapper": "0.9.0"}, "autoload": {"psr-4": {"infobip\\api\\": "infobip/api/", "infobip\\api\\client\\": "infobip/api/client/", "infobip\\api\\configuration\\": "infobip/api/configuration/", "infobip\\api\\model\\": "infobip/api/model/", "infobip\\api\\model\\sms\\": "infobip/api/model/sms/", "infobip\\api\\model\\sms\\mo\\": "infobip/api/model/sms/mo/", "infobip\\api\\model\\sms\\mo\\logs\\": "infobip/api/model/sms/mo/logs/", "infobip\\api\\model\\sms\\mo\\reports\\": "infobip/api/model/sms/mo/reports/", "infobip\\api\\model\\sms\\mt\\": "infobip/api/model/sms/mt/", "infobip\\api\\model\\sms\\mt\\logs\\": "infobip/api/model/sms/mt/logs/", "infobip\\api\\model\\sms\\mt\\reports\\": "infobip/api/model/sms/mt/reports/", "infobip\\api\\model\\sms\\mt\\send\\": "infobip/api/model/sms/mt/send/", "infobip\\api\\model\\sms\\mt\\send\\binary\\": "infobip/api/model/sms/mt/send/binary/", "infobip\\api\\model\\sms\\mt\\send\\textual\\": "infobip/api/model/sms/mt/send/textual/", "infobip\\api\\model\\nc\\": "infobip/api/model/nc/", "infobip\\api\\model\\nc\\logs\\": "infobip/api/model/nc/logs/", "infobip\\api\\model\\nc\\notify\\": "infobip/api/model/nc/notify/", "infobip\\api\\model\\nc\\query\\": "infobip/api/model/nc/query/", "infobip\\api\\model\\account\\": "infobip/api/model/account/"}}}