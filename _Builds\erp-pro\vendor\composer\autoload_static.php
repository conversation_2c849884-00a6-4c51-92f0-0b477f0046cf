<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitb060a29ae342cc8ca6e6289c6155a89e
{
    public static $prefixLengthsPsr4 = array (
        'i' => 
        array (
            'infobip\\api\\model\\sms\\mt\\send\\textual\\' => 38,
            'infobip\\api\\model\\sms\\mt\\send\\binary\\' => 37,
            'infobip\\api\\model\\sms\\mt\\send\\' => 30,
            'infobip\\api\\model\\sms\\mt\\reports\\' => 33,
            'infobip\\api\\model\\sms\\mt\\logs\\' => 30,
            'infobip\\api\\model\\sms\\mt\\' => 25,
            'infobip\\api\\model\\sms\\mo\\reports\\' => 33,
            'infobip\\api\\model\\sms\\mo\\logs\\' => 30,
            'infobip\\api\\model\\sms\\mo\\' => 25,
            'infobip\\api\\model\\sms\\' => 22,
            'infobip\\api\\model\\nc\\query\\' => 27,
            'infobip\\api\\model\\nc\\notify\\' => 28,
            'infobip\\api\\model\\nc\\logs\\' => 26,
            'infobip\\api\\model\\nc\\' => 21,
            'infobip\\api\\model\\account\\' => 26,
            'infobip\\api\\model\\' => 18,
            'infobip\\api\\configuration\\' => 26,
            'infobip\\api\\client\\' => 19,
            'infobip\\api\\' => 12,
        ),
        'W' => 
        array (
            'Wikimedia\\Composer\\Merge\\V2\\' => 28,
            'WeDevs\\Reimbursement\\' => 21,
            'WeDevs\\Recruitment\\' => 19,
            'WeDevs\\Payroll\\' => 15,
            'WeDevs\\PaymentGateway\\' => 22,
            'WeDevs\\HrTraining\\' => 18,
            'WeDevs\\HrFrontend\\' => 18,
            'WeDevs\\HelpScout\\' => 17,
            'WeDevs\\Erp_Inventory\\' => 21,
            'WeDevs\\WSL_ERP\\PRO\\' => 19,
            'WeDevs\\WSL_ERP\\HRM\\' => 19,
            'WeDevs\\WSL_ERP\\CRM\\' => 19,
            'WeDevs\\WSL_ERP\\Accounting\\' => 26,
            'WeDevs\\WSL_ERP\\' => 15,
            'WeDevs\\ERP\\Zendesk\\' => 19,
            'WeDevs\\ERP\\Workflow\\' => 20,
            'WeDevs\\ERP\\Salesforce\\' => 22,
            'WeDevs\\ERP\\SMS\\' => 15,
            'WeDevs\\ERP\\Mailchimp\\' => 21,
            'WeDevs\\ERP\\Hubspot\\' => 19,
            'WeDevs\\ERP\\Accounting\\Reimbursement\\' => 36,
            'WeDevs\\DocumentManager\\' => 23,
            'WeDevs\\Deals\\' => 13,
            'WeDevs\\CustomFieldBuilder\\' => 26,
            'WeDevs\\AwesomeSupport\\' => 22,
            'WeDevs\\Attendance\\' => 18,
            'WeDevs\\AssetManagement\\' => 23,
            'WeDevs\\AdvancedLeave\\' => 21,
        ),
        'T' => 
        array (
            'Twilio\\' => 7,
        ),
        'S' => 
        array (
            'Stripe\\' => 7,
        ),
        'D' => 
        array (
            'Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\' => 55,
        ),
        'C' => 
        array (
            'Clickatell\\' => 11,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'infobip\\api\\model\\sms\\mt\\send\\textual\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual',
        ),
        'infobip\\api\\model\\sms\\mt\\send\\binary\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary',
        ),
        'infobip\\api\\model\\sms\\mt\\send\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send',
        ),
        'infobip\\api\\model\\sms\\mt\\reports\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports',
        ),
        'infobip\\api\\model\\sms\\mt\\logs\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs',
        ),
        'infobip\\api\\model\\sms\\mt\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt',
        ),
        'infobip\\api\\model\\sms\\mo\\reports\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports',
        ),
        'infobip\\api\\model\\sms\\mo\\logs\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs',
        ),
        'infobip\\api\\model\\sms\\mo\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo',
        ),
        'infobip\\api\\model\\sms\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms',
        ),
        'infobip\\api\\model\\nc\\query\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/query',
        ),
        'infobip\\api\\model\\nc\\notify\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify',
        ),
        'infobip\\api\\model\\nc\\logs\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs',
        ),
        'infobip\\api\\model\\nc\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc',
        ),
        'infobip\\api\\model\\account\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/account',
        ),
        'infobip\\api\\model\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model',
        ),
        'infobip\\api\\configuration\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/configuration',
        ),
        'infobip\\api\\client\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client',
        ),
        'infobip\\api\\' => 
        array (
            0 => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api',
        ),
        'Wikimedia\\Composer\\Merge\\V2\\' => 
        array (
            0 => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src',
        ),
        'WeDevs\\Reimbursement\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes',
        ),
        'WeDevs\\Recruitment\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes',
        ),
        'WeDevs\\Payroll\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/payroll/includes',
        ),
        'WeDevs\\PaymentGateway\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/accounting/payment-gateway/includes',
        ),
        'WeDevs\\HrTraining\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/hr-training/includes',
        ),
        'WeDevs\\HrFrontend\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/hr-frontend/includes',
        ),
        'WeDevs\\HelpScout\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/help-scout/includes',
        ),
        'WeDevs\\Erp_Inventory\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/accounting/inventory/includes',
        ),
        'WeDevs\\WSL_ERP\\PRO\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro',
        ),
        'WeDevs\\WSL_ERP\\HRM\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm',
        ),
        'WeDevs\\WSL_ERP\\CRM\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/crm',
        ),
        'WeDevs\\WSL_ERP\\Accounting\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/accounting',
        ),
        'WeDevs\\WSL_ERP\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
        'WeDevs\\ERP\\Zendesk\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/zendesk/includes',
        ),
        'WeDevs\\ERP\\Workflow\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/workflow/includes',
        ),
        'WeDevs\\ERP\\Salesforce\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/salesforce/includes',
        ),
        'WeDevs\\ERP\\SMS\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes',
        ),
        'WeDevs\\ERP\\Mailchimp\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes',
        ),
        'WeDevs\\ERP\\Hubspot\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/hubspot/includes',
        ),
        'WeDevs\\ERP\\Accounting\\Reimbursement\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/reimbursement/deprecated/includes',
        ),
        'WeDevs\\DocumentManager\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes',
        ),
        'WeDevs\\Deals\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/crm/deals/includes',
        ),
        'WeDevs\\CustomFieldBuilder\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/custom-field-builder/includes',
        ),
        'WeDevs\\AwesomeSupport\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/awesome-support/includes',
        ),
        'WeDevs\\Attendance\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/attendance/includes',
        ),
        'WeDevs\\AssetManagement\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes',
        ),
        'WeDevs\\AdvancedLeave\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes',
        ),
        'Twilio\\' => 
        array (
            0 => __DIR__ . '/..' . '/twilio/sdk/src/Twilio',
        ),
        'Stripe\\' => 
        array (
            0 => __DIR__ . '/..' . '/stripe/stripe-php/lib',
        ),
        'Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\' => 
        array (
            0 => __DIR__ . '/..' . '/dealerdirect/phpcodesniffer-composer-installer/src',
        ),
        'Clickatell\\' => 
        array (
            0 => __DIR__ . '/..' . '/arcturial/clickatell/src',
            1 => __DIR__ . '/..' . '/arcturial/clickatell/test',
        ),
    );

    public static $prefixesPsr0 = array (
        'h' => 
        array (
            'hoiio' => 
            array (
                0 => __DIR__ . '/..' . '/hoiio/hoiio-php/Services',
            ),
        ),
    );

    public static $fallbackDirsPsr0 = array (
        0 => __DIR__ . '/..' . '/netresearch/jsonmapper/src',
    );

    public static $classMap = array (
        'Clickatell\\Api\\ClickatellHttp' => __DIR__ . '/..' . '/arcturial/clickatell/src/Api/ClickatellHttp.php',
        'Clickatell\\Api\\ClickatellHttpTest' => __DIR__ . '/..' . '/arcturial/clickatell/test/Api/ClickatellHttpTest.php',
        'Clickatell\\Api\\ClickatellRest' => __DIR__ . '/..' . '/arcturial/clickatell/src/Api/ClickatellRest.php',
        'Clickatell\\Callback' => __DIR__ . '/..' . '/arcturial/clickatell/src/Callback.php',
        'Clickatell\\CallbackTest' => __DIR__ . '/..' . '/arcturial/clickatell/test/CallbackTest.php',
        'Clickatell\\Clickatell' => __DIR__ . '/..' . '/arcturial/clickatell/src/Clickatell.php',
        'Clickatell\\ClickatellEvent' => __DIR__ . '/..' . '/arcturial/clickatell/src/ClickatellEvent.php',
        'Clickatell\\ClickatellOtpTest' => __DIR__ . '/..' . '/arcturial/clickatell/test/Otp/ClickatellOtpTest.php',
        'Clickatell\\ClickatellTest' => __DIR__ . '/..' . '/arcturial/clickatell/test/ClickatellTest.php',
        'Clickatell\\Decoder' => __DIR__ . '/..' . '/arcturial/clickatell/src/Decoder.php',
        'Clickatell\\DecoderTest' => __DIR__ . '/..' . '/arcturial/clickatell/test/DecoderTest.php',
        'Clickatell\\Diagnostic' => __DIR__ . '/..' . '/arcturial/clickatell/src/Diagnostic.php',
        'Clickatell\\DiagnosticTest' => __DIR__ . '/..' . '/arcturial/clickatell/test/DiagnosticTest.php',
        'Clickatell\\Event' => __DIR__ . '/..' . '/arcturial/clickatell/src/Event.php',
        'Clickatell\\Otp\\ClickatellOtp' => __DIR__ . '/..' . '/arcturial/clickatell/src/Otp/ClickatellOtp.php',
        'Clickatell\\Otp\\SessionStorage' => __DIR__ . '/..' . '/arcturial/clickatell/src/Otp/SessionStorage.php',
        'Clickatell\\Otp\\StorageInterface' => __DIR__ . '/..' . '/arcturial/clickatell/src/Otp/StorageInterface.php',
        'Clickatell\\Symfony\\ClickatellBundle' => __DIR__ . '/..' . '/arcturial/clickatell/src/Symfony/ClickatellBundle.php',
        'Clickatell\\Symfony\\DependencyInjection\\ClickatellExtension' => __DIR__ . '/..' . '/arcturial/clickatell/src/Symfony/DependencyInjection/ClickatellExtension.php',
        'Clickatell\\Symfony\\DependencyInjection\\Configuration' => __DIR__ . '/..' . '/arcturial/clickatell/src/Symfony/DependencyInjection/Configuration.php',
        'Clickatell\\TransportInterface' => __DIR__ . '/..' . '/arcturial/clickatell/src/TransportInterface.php',
        'Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin' => __DIR__ . '/..' . '/dealerdirect/phpcodesniffer-composer-installer/src/Plugin.php',
        'JsonMapper' => __DIR__ . '/..' . '/netresearch/jsonmapper/src/JsonMapper.php',
        'JsonMapper_Exception' => __DIR__ . '/..' . '/netresearch/jsonmapper/src/JsonMapper/Exception.php',
        'Stripe\\Account' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Account.php',
        'Stripe\\AccountLink' => __DIR__ . '/..' . '/stripe/stripe-php/lib/AccountLink.php',
        'Stripe\\AlipayAccount' => __DIR__ . '/..' . '/stripe/stripe-php/lib/AlipayAccount.php',
        'Stripe\\ApiOperations\\All' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/All.php',
        'Stripe\\ApiOperations\\Create' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/Create.php',
        'Stripe\\ApiOperations\\Delete' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/Delete.php',
        'Stripe\\ApiOperations\\NestedResource' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/NestedResource.php',
        'Stripe\\ApiOperations\\Request' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/Request.php',
        'Stripe\\ApiOperations\\Retrieve' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/Retrieve.php',
        'Stripe\\ApiOperations\\Search' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/Search.php',
        'Stripe\\ApiOperations\\Update' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiOperations/Update.php',
        'Stripe\\ApiRequestor' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiRequestor.php',
        'Stripe\\ApiResource' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiResource.php',
        'Stripe\\ApiResponse' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApiResponse.php',
        'Stripe\\ApplePayDomain' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApplePayDomain.php',
        'Stripe\\ApplicationFee' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApplicationFee.php',
        'Stripe\\ApplicationFeeRefund' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ApplicationFeeRefund.php',
        'Stripe\\Balance' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Balance.php',
        'Stripe\\BalanceTransaction' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BalanceTransaction.php',
        'Stripe\\BankAccount' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BankAccount.php',
        'Stripe\\BaseStripeClient' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BaseStripeClient.php',
        'Stripe\\BaseStripeClientInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BaseStripeClientInterface.php',
        'Stripe\\BillingPortal\\Configuration' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BillingPortal/Configuration.php',
        'Stripe\\BillingPortal\\Session' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BillingPortal/Session.php',
        'Stripe\\BitcoinReceiver' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BitcoinReceiver.php',
        'Stripe\\BitcoinTransaction' => __DIR__ . '/..' . '/stripe/stripe-php/lib/BitcoinTransaction.php',
        'Stripe\\Capability' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Capability.php',
        'Stripe\\Card' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Card.php',
        'Stripe\\CashBalance' => __DIR__ . '/..' . '/stripe/stripe-php/lib/CashBalance.php',
        'Stripe\\Charge' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Charge.php',
        'Stripe\\Checkout\\Session' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Checkout/Session.php',
        'Stripe\\Collection' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Collection.php',
        'Stripe\\CountrySpec' => __DIR__ . '/..' . '/stripe/stripe-php/lib/CountrySpec.php',
        'Stripe\\Coupon' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Coupon.php',
        'Stripe\\CreditNote' => __DIR__ . '/..' . '/stripe/stripe-php/lib/CreditNote.php',
        'Stripe\\CreditNoteLineItem' => __DIR__ . '/..' . '/stripe/stripe-php/lib/CreditNoteLineItem.php',
        'Stripe\\Customer' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Customer.php',
        'Stripe\\CustomerBalanceTransaction' => __DIR__ . '/..' . '/stripe/stripe-php/lib/CustomerBalanceTransaction.php',
        'Stripe\\Discount' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Discount.php',
        'Stripe\\Dispute' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Dispute.php',
        'Stripe\\EphemeralKey' => __DIR__ . '/..' . '/stripe/stripe-php/lib/EphemeralKey.php',
        'Stripe\\ErrorObject' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ErrorObject.php',
        'Stripe\\Event' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Event.php',
        'Stripe\\Exception\\ApiConnectionException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/ApiConnectionException.php',
        'Stripe\\Exception\\ApiErrorException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/ApiErrorException.php',
        'Stripe\\Exception\\AuthenticationException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/AuthenticationException.php',
        'Stripe\\Exception\\BadMethodCallException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/BadMethodCallException.php',
        'Stripe\\Exception\\CardException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/CardException.php',
        'Stripe\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/ExceptionInterface.php',
        'Stripe\\Exception\\IdempotencyException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/IdempotencyException.php',
        'Stripe\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/InvalidArgumentException.php',
        'Stripe\\Exception\\InvalidRequestException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/InvalidRequestException.php',
        'Stripe\\Exception\\OAuth\\ExceptionInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/ExceptionInterface.php',
        'Stripe\\Exception\\OAuth\\InvalidClientException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/InvalidClientException.php',
        'Stripe\\Exception\\OAuth\\InvalidGrantException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/InvalidGrantException.php',
        'Stripe\\Exception\\OAuth\\InvalidRequestException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/InvalidRequestException.php',
        'Stripe\\Exception\\OAuth\\InvalidScopeException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/InvalidScopeException.php',
        'Stripe\\Exception\\OAuth\\OAuthErrorException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/OAuthErrorException.php',
        'Stripe\\Exception\\OAuth\\UnknownOAuthErrorException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/UnknownOAuthErrorException.php',
        'Stripe\\Exception\\OAuth\\UnsupportedGrantTypeException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/UnsupportedGrantTypeException.php',
        'Stripe\\Exception\\OAuth\\UnsupportedResponseTypeException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/OAuth/UnsupportedResponseTypeException.php',
        'Stripe\\Exception\\PermissionException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/PermissionException.php',
        'Stripe\\Exception\\RateLimitException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/RateLimitException.php',
        'Stripe\\Exception\\SignatureVerificationException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/SignatureVerificationException.php',
        'Stripe\\Exception\\UnexpectedValueException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/UnexpectedValueException.php',
        'Stripe\\Exception\\UnknownApiErrorException' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Exception/UnknownApiErrorException.php',
        'Stripe\\ExchangeRate' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ExchangeRate.php',
        'Stripe\\File' => __DIR__ . '/..' . '/stripe/stripe-php/lib/File.php',
        'Stripe\\FileLink' => __DIR__ . '/..' . '/stripe/stripe-php/lib/FileLink.php',
        'Stripe\\FinancialConnections\\Account' => __DIR__ . '/..' . '/stripe/stripe-php/lib/FinancialConnections/Account.php',
        'Stripe\\FinancialConnections\\AccountOwner' => __DIR__ . '/..' . '/stripe/stripe-php/lib/FinancialConnections/AccountOwner.php',
        'Stripe\\FinancialConnections\\AccountOwnership' => __DIR__ . '/..' . '/stripe/stripe-php/lib/FinancialConnections/AccountOwnership.php',
        'Stripe\\FinancialConnections\\Session' => __DIR__ . '/..' . '/stripe/stripe-php/lib/FinancialConnections/Session.php',
        'Stripe\\FundingInstructions' => __DIR__ . '/..' . '/stripe/stripe-php/lib/FundingInstructions.php',
        'Stripe\\HttpClient\\ClientInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/HttpClient/ClientInterface.php',
        'Stripe\\HttpClient\\CurlClient' => __DIR__ . '/..' . '/stripe/stripe-php/lib/HttpClient/CurlClient.php',
        'Stripe\\HttpClient\\StreamingClientInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/HttpClient/StreamingClientInterface.php',
        'Stripe\\Identity\\VerificationReport' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Identity/VerificationReport.php',
        'Stripe\\Identity\\VerificationSession' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Identity/VerificationSession.php',
        'Stripe\\Invoice' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Invoice.php',
        'Stripe\\InvoiceItem' => __DIR__ . '/..' . '/stripe/stripe-php/lib/InvoiceItem.php',
        'Stripe\\InvoiceLineItem' => __DIR__ . '/..' . '/stripe/stripe-php/lib/InvoiceLineItem.php',
        'Stripe\\Issuing\\Authorization' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Issuing/Authorization.php',
        'Stripe\\Issuing\\Card' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Issuing/Card.php',
        'Stripe\\Issuing\\CardDetails' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Issuing/CardDetails.php',
        'Stripe\\Issuing\\Cardholder' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Issuing/Cardholder.php',
        'Stripe\\Issuing\\Dispute' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Issuing/Dispute.php',
        'Stripe\\Issuing\\Transaction' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Issuing/Transaction.php',
        'Stripe\\LineItem' => __DIR__ . '/..' . '/stripe/stripe-php/lib/LineItem.php',
        'Stripe\\LoginLink' => __DIR__ . '/..' . '/stripe/stripe-php/lib/LoginLink.php',
        'Stripe\\Mandate' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Mandate.php',
        'Stripe\\OAuth' => __DIR__ . '/..' . '/stripe/stripe-php/lib/OAuth.php',
        'Stripe\\OAuthErrorObject' => __DIR__ . '/..' . '/stripe/stripe-php/lib/OAuthErrorObject.php',
        'Stripe\\Order' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Order.php',
        'Stripe\\OrderItem' => __DIR__ . '/..' . '/stripe/stripe-php/lib/OrderItem.php',
        'Stripe\\OrderReturn' => __DIR__ . '/..' . '/stripe/stripe-php/lib/OrderReturn.php',
        'Stripe\\PaymentIntent' => __DIR__ . '/..' . '/stripe/stripe-php/lib/PaymentIntent.php',
        'Stripe\\PaymentLink' => __DIR__ . '/..' . '/stripe/stripe-php/lib/PaymentLink.php',
        'Stripe\\PaymentMethod' => __DIR__ . '/..' . '/stripe/stripe-php/lib/PaymentMethod.php',
        'Stripe\\Payout' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Payout.php',
        'Stripe\\Person' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Person.php',
        'Stripe\\Plan' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Plan.php',
        'Stripe\\Price' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Price.php',
        'Stripe\\Product' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Product.php',
        'Stripe\\PromotionCode' => __DIR__ . '/..' . '/stripe/stripe-php/lib/PromotionCode.php',
        'Stripe\\Quote' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Quote.php',
        'Stripe\\Radar\\EarlyFraudWarning' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Radar/EarlyFraudWarning.php',
        'Stripe\\Radar\\ValueList' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Radar/ValueList.php',
        'Stripe\\Radar\\ValueListItem' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Radar/ValueListItem.php',
        'Stripe\\Recipient' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Recipient.php',
        'Stripe\\RecipientTransfer' => __DIR__ . '/..' . '/stripe/stripe-php/lib/RecipientTransfer.php',
        'Stripe\\Refund' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Refund.php',
        'Stripe\\Reporting\\ReportRun' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Reporting/ReportRun.php',
        'Stripe\\Reporting\\ReportType' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Reporting/ReportType.php',
        'Stripe\\RequestTelemetry' => __DIR__ . '/..' . '/stripe/stripe-php/lib/RequestTelemetry.php',
        'Stripe\\Review' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Review.php',
        'Stripe\\SKU' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SKU.php',
        'Stripe\\SearchResult' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SearchResult.php',
        'Stripe\\Service\\AbstractService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/AbstractService.php',
        'Stripe\\Service\\AbstractServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/AbstractServiceFactory.php',
        'Stripe\\Service\\AccountLinkService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/AccountLinkService.php',
        'Stripe\\Service\\AccountService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/AccountService.php',
        'Stripe\\Service\\ApplePayDomainService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/ApplePayDomainService.php',
        'Stripe\\Service\\ApplicationFeeService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/ApplicationFeeService.php',
        'Stripe\\Service\\BalanceService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/BalanceService.php',
        'Stripe\\Service\\BalanceTransactionService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/BalanceTransactionService.php',
        'Stripe\\Service\\BillingPortal\\BillingPortalServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/BillingPortal/BillingPortalServiceFactory.php',
        'Stripe\\Service\\BillingPortal\\ConfigurationService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/BillingPortal/ConfigurationService.php',
        'Stripe\\Service\\BillingPortal\\SessionService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/BillingPortal/SessionService.php',
        'Stripe\\Service\\ChargeService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/ChargeService.php',
        'Stripe\\Service\\Checkout\\CheckoutServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Checkout/CheckoutServiceFactory.php',
        'Stripe\\Service\\Checkout\\SessionService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Checkout/SessionService.php',
        'Stripe\\Service\\CoreServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/CoreServiceFactory.php',
        'Stripe\\Service\\CountrySpecService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/CountrySpecService.php',
        'Stripe\\Service\\CouponService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/CouponService.php',
        'Stripe\\Service\\CreditNoteService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/CreditNoteService.php',
        'Stripe\\Service\\CustomerService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/CustomerService.php',
        'Stripe\\Service\\DisputeService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/DisputeService.php',
        'Stripe\\Service\\EphemeralKeyService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/EphemeralKeyService.php',
        'Stripe\\Service\\EventService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/EventService.php',
        'Stripe\\Service\\ExchangeRateService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/ExchangeRateService.php',
        'Stripe\\Service\\FileLinkService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/FileLinkService.php',
        'Stripe\\Service\\FileService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/FileService.php',
        'Stripe\\Service\\FinancialConnections\\AccountService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/FinancialConnections/AccountService.php',
        'Stripe\\Service\\FinancialConnections\\FinancialConnectionsServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/FinancialConnections/FinancialConnectionsServiceFactory.php',
        'Stripe\\Service\\FinancialConnections\\SessionService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/FinancialConnections/SessionService.php',
        'Stripe\\Service\\Identity\\IdentityServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Identity/IdentityServiceFactory.php',
        'Stripe\\Service\\Identity\\VerificationReportService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Identity/VerificationReportService.php',
        'Stripe\\Service\\Identity\\VerificationSessionService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Identity/VerificationSessionService.php',
        'Stripe\\Service\\InvoiceItemService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/InvoiceItemService.php',
        'Stripe\\Service\\InvoiceService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/InvoiceService.php',
        'Stripe\\Service\\Issuing\\AuthorizationService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Issuing/AuthorizationService.php',
        'Stripe\\Service\\Issuing\\CardService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Issuing/CardService.php',
        'Stripe\\Service\\Issuing\\CardholderService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Issuing/CardholderService.php',
        'Stripe\\Service\\Issuing\\DisputeService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Issuing/DisputeService.php',
        'Stripe\\Service\\Issuing\\IssuingServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Issuing/IssuingServiceFactory.php',
        'Stripe\\Service\\Issuing\\TransactionService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Issuing/TransactionService.php',
        'Stripe\\Service\\MandateService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/MandateService.php',
        'Stripe\\Service\\OAuthService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/OAuthService.php',
        'Stripe\\Service\\OrderReturnService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/OrderReturnService.php',
        'Stripe\\Service\\OrderService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/OrderService.php',
        'Stripe\\Service\\PaymentIntentService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/PaymentIntentService.php',
        'Stripe\\Service\\PaymentLinkService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/PaymentLinkService.php',
        'Stripe\\Service\\PaymentMethodService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/PaymentMethodService.php',
        'Stripe\\Service\\PayoutService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/PayoutService.php',
        'Stripe\\Service\\PlanService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/PlanService.php',
        'Stripe\\Service\\PriceService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/PriceService.php',
        'Stripe\\Service\\ProductService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/ProductService.php',
        'Stripe\\Service\\PromotionCodeService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/PromotionCodeService.php',
        'Stripe\\Service\\QuoteService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/QuoteService.php',
        'Stripe\\Service\\Radar\\EarlyFraudWarningService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Radar/EarlyFraudWarningService.php',
        'Stripe\\Service\\Radar\\RadarServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Radar/RadarServiceFactory.php',
        'Stripe\\Service\\Radar\\ValueListItemService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Radar/ValueListItemService.php',
        'Stripe\\Service\\Radar\\ValueListService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Radar/ValueListService.php',
        'Stripe\\Service\\RefundService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/RefundService.php',
        'Stripe\\Service\\Reporting\\ReportRunService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Reporting/ReportRunService.php',
        'Stripe\\Service\\Reporting\\ReportTypeService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Reporting/ReportTypeService.php',
        'Stripe\\Service\\Reporting\\ReportingServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Reporting/ReportingServiceFactory.php',
        'Stripe\\Service\\ReviewService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/ReviewService.php',
        'Stripe\\Service\\SetupAttemptService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/SetupAttemptService.php',
        'Stripe\\Service\\SetupIntentService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/SetupIntentService.php',
        'Stripe\\Service\\ShippingRateService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/ShippingRateService.php',
        'Stripe\\Service\\Sigma\\ScheduledQueryRunService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Sigma/ScheduledQueryRunService.php',
        'Stripe\\Service\\Sigma\\SigmaServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Sigma/SigmaServiceFactory.php',
        'Stripe\\Service\\SkuService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/SkuService.php',
        'Stripe\\Service\\SourceService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/SourceService.php',
        'Stripe\\Service\\SubscriptionItemService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/SubscriptionItemService.php',
        'Stripe\\Service\\SubscriptionScheduleService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/SubscriptionScheduleService.php',
        'Stripe\\Service\\SubscriptionService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/SubscriptionService.php',
        'Stripe\\Service\\TaxCodeService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TaxCodeService.php',
        'Stripe\\Service\\TaxRateService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TaxRateService.php',
        'Stripe\\Service\\Terminal\\ConfigurationService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Terminal/ConfigurationService.php',
        'Stripe\\Service\\Terminal\\ConnectionTokenService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Terminal/ConnectionTokenService.php',
        'Stripe\\Service\\Terminal\\LocationService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Terminal/LocationService.php',
        'Stripe\\Service\\Terminal\\ReaderService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Terminal/ReaderService.php',
        'Stripe\\Service\\Terminal\\TerminalServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/Terminal/TerminalServiceFactory.php',
        'Stripe\\Service\\TestHelpers\\RefundService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TestHelpers/RefundService.php',
        'Stripe\\Service\\TestHelpers\\Terminal\\ReaderService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TestHelpers/Terminal/ReaderService.php',
        'Stripe\\Service\\TestHelpers\\Terminal\\TerminalServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TestHelpers/Terminal/TerminalServiceFactory.php',
        'Stripe\\Service\\TestHelpers\\TestClockService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TestHelpers/TestClockService.php',
        'Stripe\\Service\\TestHelpers\\TestHelpersServiceFactory' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TestHelpers/TestHelpersServiceFactory.php',
        'Stripe\\Service\\TokenService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TokenService.php',
        'Stripe\\Service\\TopupService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TopupService.php',
        'Stripe\\Service\\TransferService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/TransferService.php',
        'Stripe\\Service\\WebhookEndpointService' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Service/WebhookEndpointService.php',
        'Stripe\\SetupAttempt' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SetupAttempt.php',
        'Stripe\\SetupIntent' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SetupIntent.php',
        'Stripe\\ShippingRate' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ShippingRate.php',
        'Stripe\\Sigma\\ScheduledQueryRun' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Sigma/ScheduledQueryRun.php',
        'Stripe\\SingletonApiResource' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SingletonApiResource.php',
        'Stripe\\Source' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Source.php',
        'Stripe\\SourceTransaction' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SourceTransaction.php',
        'Stripe\\Stripe' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Stripe.php',
        'Stripe\\StripeClient' => __DIR__ . '/..' . '/stripe/stripe-php/lib/StripeClient.php',
        'Stripe\\StripeClientInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/StripeClientInterface.php',
        'Stripe\\StripeObject' => __DIR__ . '/..' . '/stripe/stripe-php/lib/StripeObject.php',
        'Stripe\\StripeStreamingClientInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/StripeStreamingClientInterface.php',
        'Stripe\\Subscription' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Subscription.php',
        'Stripe\\SubscriptionItem' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SubscriptionItem.php',
        'Stripe\\SubscriptionSchedule' => __DIR__ . '/..' . '/stripe/stripe-php/lib/SubscriptionSchedule.php',
        'Stripe\\TaxCode' => __DIR__ . '/..' . '/stripe/stripe-php/lib/TaxCode.php',
        'Stripe\\TaxId' => __DIR__ . '/..' . '/stripe/stripe-php/lib/TaxId.php',
        'Stripe\\TaxRate' => __DIR__ . '/..' . '/stripe/stripe-php/lib/TaxRate.php',
        'Stripe\\Terminal\\Configuration' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Terminal/Configuration.php',
        'Stripe\\Terminal\\ConnectionToken' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Terminal/ConnectionToken.php',
        'Stripe\\Terminal\\Location' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Terminal/Location.php',
        'Stripe\\Terminal\\Reader' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Terminal/Reader.php',
        'Stripe\\TestHelpers\\TestClock' => __DIR__ . '/..' . '/stripe/stripe-php/lib/TestHelpers/TestClock.php',
        'Stripe\\ThreeDSecure' => __DIR__ . '/..' . '/stripe/stripe-php/lib/ThreeDSecure.php',
        'Stripe\\Token' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Token.php',
        'Stripe\\Topup' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Topup.php',
        'Stripe\\Transfer' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Transfer.php',
        'Stripe\\TransferReversal' => __DIR__ . '/..' . '/stripe/stripe-php/lib/TransferReversal.php',
        'Stripe\\UsageRecord' => __DIR__ . '/..' . '/stripe/stripe-php/lib/UsageRecord.php',
        'Stripe\\UsageRecordSummary' => __DIR__ . '/..' . '/stripe/stripe-php/lib/UsageRecordSummary.php',
        'Stripe\\Util\\CaseInsensitiveArray' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/CaseInsensitiveArray.php',
        'Stripe\\Util\\DefaultLogger' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/DefaultLogger.php',
        'Stripe\\Util\\LoggerInterface' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/LoggerInterface.php',
        'Stripe\\Util\\ObjectTypes' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/ObjectTypes.php',
        'Stripe\\Util\\RandomGenerator' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/RandomGenerator.php',
        'Stripe\\Util\\RequestOptions' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/RequestOptions.php',
        'Stripe\\Util\\Set' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/Set.php',
        'Stripe\\Util\\Util' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Util/Util.php',
        'Stripe\\Webhook' => __DIR__ . '/..' . '/stripe/stripe-php/lib/Webhook.php',
        'Stripe\\WebhookEndpoint' => __DIR__ . '/..' . '/stripe/stripe-php/lib/WebhookEndpoint.php',
        'Stripe\\WebhookSignature' => __DIR__ . '/..' . '/stripe/stripe-php/lib/WebhookSignature.php',
        'Twilio\\Deserialize' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Deserialize.php',
        'Twilio\\Domain' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Domain.php',
        'Twilio\\Exceptions\\ConfigurationException' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Exceptions/ConfigurationException.php',
        'Twilio\\Exceptions\\DeserializeException' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Exceptions/DeserializeException.php',
        'Twilio\\Exceptions\\EnvironmentException' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Exceptions/EnvironmentException.php',
        'Twilio\\Exceptions\\HttpException' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Exceptions/HttpException.php',
        'Twilio\\Exceptions\\RestException' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Exceptions/RestException.php',
        'Twilio\\Exceptions\\TwilioException' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Exceptions/TwilioException.php',
        'Twilio\\Exceptions\\TwimlException' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Exceptions/TwimlException.php',
        'Twilio\\Http\\Client' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Http/Client.php',
        'Twilio\\Http\\CurlClient' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Http/CurlClient.php',
        'Twilio\\Http\\GuzzleClient' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Http/GuzzleClient.php',
        'Twilio\\Http\\Response' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Http/Response.php',
        'Twilio\\InstanceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/InstanceContext.php',
        'Twilio\\InstanceResource' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/InstanceResource.php',
        'Twilio\\Jwt\\AccessToken' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/AccessToken.php',
        'Twilio\\Jwt\\ClientToken' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/ClientToken.php',
        'Twilio\\Jwt\\Client\\ScopeURI' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/Client/ScopeURI.php',
        'Twilio\\Jwt\\Grants\\ChatGrant' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/Grants/ChatGrant.php',
        'Twilio\\Jwt\\Grants\\Grant' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/Grants/Grant.php',
        'Twilio\\Jwt\\Grants\\SyncGrant' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/Grants/SyncGrant.php',
        'Twilio\\Jwt\\Grants\\TaskRouterGrant' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/Grants/TaskRouterGrant.php',
        'Twilio\\Jwt\\Grants\\VideoGrant' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/Grants/VideoGrant.php',
        'Twilio\\Jwt\\Grants\\VoiceGrant' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/Grants/VoiceGrant.php',
        'Twilio\\Jwt\\JWT' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/JWT.php',
        'Twilio\\Jwt\\TaskRouter\\CapabilityToken' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/CapabilityToken.php',
        'Twilio\\Jwt\\TaskRouter\\Policy' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/Policy.php',
        'Twilio\\Jwt\\TaskRouter\\TaskQueueCapability' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/TaskQueueCapability.php',
        'Twilio\\Jwt\\TaskRouter\\WorkerCapability' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/WorkerCapability.php',
        'Twilio\\Jwt\\TaskRouter\\WorkspaceCapability' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/WorkspaceCapability.php',
        'Twilio\\ListResource' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/ListResource.php',
        'Twilio\\Options' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Options.php',
        'Twilio\\Page' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Page.php',
        'Twilio\\Rest\\Accounts' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts.php',
        'Twilio\\Rest\\Accounts\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1.php',
        'Twilio\\Rest\\Accounts\\V1\\CredentialInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/CredentialInstance.php',
        'Twilio\\Rest\\Accounts\\V1\\CredentialList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/CredentialList.php',
        'Twilio\\Rest\\Accounts\\V1\\CredentialPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/CredentialPage.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsContext.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsInstance.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsList.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsOptions.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsPage.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\CreateAwsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsOptions.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\CreatePublicKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyOptions.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyContext.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyInstance.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyList.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyOptions.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyPage.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\UpdateAwsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsOptions.php',
        'Twilio\\Rest\\Accounts\\V1\\Credential\\UpdatePublicKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyOptions.php',
        'Twilio\\Rest\\Api' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api.php',
        'Twilio\\Rest\\Api\\V2010' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010.php',
        'Twilio\\Rest\\Api\\V2010\\AccountContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountContext.php',
        'Twilio\\Rest\\Api\\V2010\\AccountInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountInstance.php',
        'Twilio\\Rest\\Api\\V2010\\AccountList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountList.php',
        'Twilio\\Rest\\Api\\V2010\\AccountOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
        'Twilio\\Rest\\Api\\V2010\\AccountPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AddressContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AddressInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AddressList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AddressOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AddressPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Address\\DependentPhoneNumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Address/DependentPhoneNumberInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Address\\DependentPhoneNumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Address/DependentPhoneNumberList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Address\\DependentPhoneNumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Address/DependentPhoneNumberPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachineInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachineList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachineOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachinePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachinePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobileInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobileList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobileOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobilePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobilePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadLocalOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadMachineToMachineOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadMobileOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadNationalOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadSharedCostOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadTollFreeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadVoipOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\BalanceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/BalanceInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\BalanceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/BalanceList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\BalancePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/BalancePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CallContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CallInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CallList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CallPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreateFeedbackOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreateFeedbackSummaryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreatePaymentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreateRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\ReadNotificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\ReadRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\UpdateFeedbackOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\UpdatePaymentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Call\\UpdateRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConferencePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferencePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\CreateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ReadParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ReadRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\UpdateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\UpdateRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateAddressOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateApplicationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateCallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateIncomingPhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateNewKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateNewSigningKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateQueueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateTokenOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\CreateValidationRequestOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\CreateLocalOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\CreateMobileOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\CreateTollFreeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobileInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobileList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobileOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobilePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobilePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\ReadLocalOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\ReadMobileOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\ReadTollFreeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\KeyContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\KeyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\KeyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\KeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\KeyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\MessageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\MessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\MessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\MessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\MessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessagePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\CreateFeedbackOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Message\\ReadMediaOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NotificationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NotificationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NotificationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NotificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\NotificationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\QueueContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\QueueInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\QueueList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\QueueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\QueuePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueuePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\UpdateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadAddressOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadApplicationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadCallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadConferenceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadIncomingPhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadNotificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadOutgoingCallerIdOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ReadShortCodeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\RecordingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\RecordingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\RecordingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\RecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\RecordingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SipInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SipInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SipList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SipList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\SipPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SipPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CreateDomainOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\UpdateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypesInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypesInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypesList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypesList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypesPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypesPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCallsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCallsInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCallsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCallsList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCallsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCallsPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrationsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrationsInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrationsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrationsList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrationsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrationsPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\CreateIpAddressOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\UpdateIpAddressOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\UpdateDomainOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TokenInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TokenList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TokenOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TokenPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateAddressOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateApplicationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateCallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateConferenceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateConnectAppOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateIncomingPhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateOutgoingCallerIdOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateQueueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateShortCodeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UpdateSigningKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UsageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/UsageInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UsageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/UsageList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\UsagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/UsagePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\CreateTriggerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\ReadRecordOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\ReadTriggerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimePage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadAllTimeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadDailyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadLastMonthOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadMonthlyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadThisMonthOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadTodayOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadYearlyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadYesterdayOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerContext.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerPage.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\UpdateTriggerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestInstance.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestList.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestOptions.php',
        'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestPage.php',
        'Twilio\\Rest\\Api\\V2010\\CreateAccountOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
        'Twilio\\Rest\\Api\\V2010\\ReadAccountOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
        'Twilio\\Rest\\Api\\V2010\\UpdateAccountOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
        'Twilio\\Rest\\Authy' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy.php',
        'Twilio\\Rest\\Authy\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1.php',
        'Twilio\\Rest\\Authy\\V1\\FormContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormContext.php',
        'Twilio\\Rest\\Authy\\V1\\FormInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormInstance.php',
        'Twilio\\Rest\\Authy\\V1\\FormList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormList.php',
        'Twilio\\Rest\\Authy\\V1\\FormPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormPage.php',
        'Twilio\\Rest\\Authy\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceContext.php',
        'Twilio\\Rest\\Authy\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceInstance.php',
        'Twilio\\Rest\\Authy\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceList.php',
        'Twilio\\Rest\\Authy\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceOptions.php',
        'Twilio\\Rest\\Authy\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServicePage.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\EntityContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityContext.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\EntityInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityInstance.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\EntityList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityList.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\EntityPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityPage.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorContext.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorInstance.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorList.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorOptions.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorPage.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeContext.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeInstance.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeList.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeOptions.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengePage.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\CreateChallengeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeOptions.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\UpdateChallengeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeOptions.php',
        'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\UpdateFactorOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorOptions.php',
        'Twilio\\Rest\\Authy\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceOptions.php',
        'Twilio\\Rest\\Autopilot' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot.php',
        'Twilio\\Rest\\Autopilot\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1.php',
        'Twilio\\Rest\\Autopilot\\V1\\AssistantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\AssistantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\AssistantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantList.php',
        'Twilio\\Rest\\Autopilot\\V1\\AssistantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\AssistantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateFieldTypeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateModelBuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateQueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateTaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialogueContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialogueContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialogueInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialogueInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialogueList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialogueList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialoguePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialoguePage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypePage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\CreateFieldValueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValuePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValuePage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\ReadFieldValueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ReadQueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\CreateSampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\ReadSampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SamplePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SamplePage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\UpdateSampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\UpdateTaskActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateDefaultsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateFieldTypeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateModelBuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateQueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateStyleSheetOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateTaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookContext.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookInstance.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookList.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookPage.php',
        'Twilio\\Rest\\Autopilot\\V1\\CreateAssistantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantOptions.php',
        'Twilio\\Rest\\Autopilot\\V1\\UpdateAssistantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantOptions.php',
        'Twilio\\Rest\\Chat' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat.php',
        'Twilio\\Rest\\Chat\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1.php',
        'Twilio\\Rest\\Chat\\V1\\CreateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialOptions.php',
        'Twilio\\Rest\\Chat\\V1\\CredentialContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialContext.php',
        'Twilio\\Rest\\Chat\\V1\\CredentialInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialInstance.php',
        'Twilio\\Rest\\Chat\\V1\\CredentialList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialList.php',
        'Twilio\\Rest\\Chat\\V1\\CredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialOptions.php',
        'Twilio\\Rest\\Chat\\V1\\CredentialPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialPage.php',
        'Twilio\\Rest\\Chat\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceContext.php',
        'Twilio\\Rest\\Chat\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceInstance.php',
        'Twilio\\Rest\\Chat\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceList.php',
        'Twilio\\Rest\\Chat\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceOptions.php',
        'Twilio\\Rest\\Chat\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServicePage.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\ChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelContext.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\ChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelInstance.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\ChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelList.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\ChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\ChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelPage.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\CreateInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\CreateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\CreateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteContext.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteInstance.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteList.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InvitePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InvitePage.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberContext.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberInstance.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberList.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberPage.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageContext.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageInstance.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageList.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessagePage.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\ReadInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\ReadMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\ReadMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\UpdateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\UpdateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\CreateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\CreateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\ReadChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\RoleContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RoleContext.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\RoleInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RoleInstance.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\RoleList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RoleList.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\RolePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RolePage.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\UpdateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\UpdateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\UserContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserContext.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\UserInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserInstance.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\UserList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserList.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\UserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserOptions.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\UserPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserPage.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\User\\UserChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/User/UserChannelInstance.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\User\\UserChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/User/UserChannelList.php',
        'Twilio\\Rest\\Chat\\V1\\Service\\User\\UserChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/User/UserChannelPage.php',
        'Twilio\\Rest\\Chat\\V1\\UpdateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialOptions.php',
        'Twilio\\Rest\\Chat\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceOptions.php',
        'Twilio\\Rest\\Chat\\V2' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2.php',
        'Twilio\\Rest\\Chat\\V2\\CreateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialOptions.php',
        'Twilio\\Rest\\Chat\\V2\\CredentialContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialContext.php',
        'Twilio\\Rest\\Chat\\V2\\CredentialInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialInstance.php',
        'Twilio\\Rest\\Chat\\V2\\CredentialList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialList.php',
        'Twilio\\Rest\\Chat\\V2\\CredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialOptions.php',
        'Twilio\\Rest\\Chat\\V2\\CredentialPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialPage.php',
        'Twilio\\Rest\\Chat\\V2\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceContext.php',
        'Twilio\\Rest\\Chat\\V2\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceInstance.php',
        'Twilio\\Rest\\Chat\\V2\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceList.php',
        'Twilio\\Rest\\Chat\\V2\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceOptions.php',
        'Twilio\\Rest\\Chat\\V2\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServicePage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\BindingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\BindingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\BindingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\BindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\BindingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingPage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\ChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\ChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\ChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\ChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\ChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelPage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InvitePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InvitePage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberPage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessagePage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\ReadInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\ReadMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\ReadMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\UpdateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\UpdateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\UpdateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookPage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\CreateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\CreateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\ReadBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\ReadChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\RoleContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RoleContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\RoleInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RoleInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\RoleList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RoleList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\RolePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RolePage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\UpdateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\UpdateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\UserContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\UserInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\UserList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\UserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\UserPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserPage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\ReadUserBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UpdateUserChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingPage.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelContext.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelInstance.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelList.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelOptions.php',
        'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelPage.php',
        'Twilio\\Rest\\Chat\\V2\\UpdateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialOptions.php',
        'Twilio\\Rest\\Chat\\V2\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceOptions.php',
        'Twilio\\Rest\\Client' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Client.php',
        'Twilio\\Rest\\Conversations' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations.php',
        'Twilio\\Rest\\Conversations\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1.php',
        'Twilio\\Rest\\Conversations\\V1\\ConversationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationContext.php',
        'Twilio\\Rest\\Conversations\\V1\\ConversationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationInstance.php',
        'Twilio\\Rest\\Conversations\\V1\\ConversationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationList.php',
        'Twilio\\Rest\\Conversations\\V1\\ConversationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\ConversationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationPage.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\CreateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\CreateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\CreateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageContext.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageInstance.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageList.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessagePage.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantContext.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantInstance.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantList.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantPage.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\UpdateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\UpdateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\UpdateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookContext.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookInstance.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookList.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookPage.php',
        'Twilio\\Rest\\Conversations\\V1\\CreateConversationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\UpdateConversationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\UpdateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\WebhookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookContext.php',
        'Twilio\\Rest\\Conversations\\V1\\WebhookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookInstance.php',
        'Twilio\\Rest\\Conversations\\V1\\WebhookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookList.php',
        'Twilio\\Rest\\Conversations\\V1\\WebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookOptions.php',
        'Twilio\\Rest\\Conversations\\V1\\WebhookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookPage.php',
        'Twilio\\Rest\\Fax' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax.php',
        'Twilio\\Rest\\Fax\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1.php',
        'Twilio\\Rest\\Fax\\V1\\CreateFaxOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
        'Twilio\\Rest\\Fax\\V1\\FaxContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxContext.php',
        'Twilio\\Rest\\Fax\\V1\\FaxInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxInstance.php',
        'Twilio\\Rest\\Fax\\V1\\FaxList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxList.php',
        'Twilio\\Rest\\Fax\\V1\\FaxOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
        'Twilio\\Rest\\Fax\\V1\\FaxPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxPage.php',
        'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaContext.php',
        'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaInstance.php',
        'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaList.php',
        'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaPage.php',
        'Twilio\\Rest\\Fax\\V1\\ReadFaxOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
        'Twilio\\Rest\\Fax\\V1\\UpdateFaxOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
        'Twilio\\Rest\\FlexApi' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi.php',
        'Twilio\\Rest\\FlexApi\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1.php',
        'Twilio\\Rest\\FlexApi\\V1\\ChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelContext.php',
        'Twilio\\Rest\\FlexApi\\V1\\ChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelInstance.php',
        'Twilio\\Rest\\FlexApi\\V1\\ChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelList.php',
        'Twilio\\Rest\\FlexApi\\V1\\ChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\ChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelPage.php',
        'Twilio\\Rest\\FlexApi\\V1\\ConfigurationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationContext.php',
        'Twilio\\Rest\\FlexApi\\V1\\ConfigurationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationInstance.php',
        'Twilio\\Rest\\FlexApi\\V1\\ConfigurationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationList.php',
        'Twilio\\Rest\\FlexApi\\V1\\ConfigurationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\ConfigurationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationPage.php',
        'Twilio\\Rest\\FlexApi\\V1\\CreateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\CreateFlexFlowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\CreateWebChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\FetchConfigurationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\FlexFlowContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowContext.php',
        'Twilio\\Rest\\FlexApi\\V1\\FlexFlowInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowInstance.php',
        'Twilio\\Rest\\FlexApi\\V1\\FlexFlowList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowList.php',
        'Twilio\\Rest\\FlexApi\\V1\\FlexFlowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\FlexFlowPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowPage.php',
        'Twilio\\Rest\\FlexApi\\V1\\ReadFlexFlowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\UpdateFlexFlowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\UpdateWebChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\WebChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelContext.php',
        'Twilio\\Rest\\FlexApi\\V1\\WebChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelInstance.php',
        'Twilio\\Rest\\FlexApi\\V1\\WebChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelList.php',
        'Twilio\\Rest\\FlexApi\\V1\\WebChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelOptions.php',
        'Twilio\\Rest\\FlexApi\\V1\\WebChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelPage.php',
        'Twilio\\Rest\\Insights' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights.php',
        'Twilio\\Rest\\Insights\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1.php',
        'Twilio\\Rest\\Insights\\V1\\CallContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallContext.php',
        'Twilio\\Rest\\Insights\\V1\\CallInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallInstance.php',
        'Twilio\\Rest\\Insights\\V1\\CallList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallList.php',
        'Twilio\\Rest\\Insights\\V1\\CallPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallPage.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryContext.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryInstance.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryList.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryOptions.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryPage.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\EventInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventInstance.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\EventList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventList.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\EventOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventOptions.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\EventPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventPage.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\FetchCallSummaryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryOptions.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\MetricInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricInstance.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\MetricList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricList.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\MetricOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricOptions.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\MetricPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricPage.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\ReadEventOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventOptions.php',
        'Twilio\\Rest\\Insights\\V1\\Call\\ReadMetricOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricOptions.php',
        'Twilio\\Rest\\IpMessaging' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging.php',
        'Twilio\\Rest\\IpMessaging\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1.php',
        'Twilio\\Rest\\IpMessaging\\V1\\CreateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\CredentialContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\CredentialInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\CredentialList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\CredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\CredentialPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialPage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServicePage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelPage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\CreateInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\CreateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\CreateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InvitePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InvitePage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberPage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessagePage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\ReadInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\ReadMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\ReadMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\UpdateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\UpdateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\CreateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\CreateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\ReadChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\RoleContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RoleContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\RoleInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RoleInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\RoleList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RoleList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\RolePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RolePage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\UpdateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\UpdateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserContext.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserPage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\User\\UserChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/User/UserChannelInstance.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\User\\UserChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/User/UserChannelList.php',
        'Twilio\\Rest\\IpMessaging\\V1\\Service\\User\\UserChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/User/UserChannelPage.php',
        'Twilio\\Rest\\IpMessaging\\V1\\UpdateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialOptions.php',
        'Twilio\\Rest\\IpMessaging\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2.php',
        'Twilio\\Rest\\IpMessaging\\V2\\CreateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\CredentialContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\CredentialInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\CredentialList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\CredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\CredentialPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServicePage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InvitePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InvitePage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessagePage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\ReadInviteOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\ReadMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\ReadMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\UpdateMemberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\UpdateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\UpdateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\CreateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\CreateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\ReadBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\ReadChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\RoleContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RoleContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\RoleInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RoleInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\RoleList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RoleList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\RolePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RolePage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\UpdateChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\UpdateUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\ReadUserBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UpdateUserChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelContext.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelInstance.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelList.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelPage.php',
        'Twilio\\Rest\\IpMessaging\\V2\\UpdateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialOptions.php',
        'Twilio\\Rest\\IpMessaging\\V2\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceOptions.php',
        'Twilio\\Rest\\Lookups' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups.php',
        'Twilio\\Rest\\Lookups\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups/V1.php',
        'Twilio\\Rest\\Lookups\\V1\\FetchPhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberOptions.php',
        'Twilio\\Rest\\Lookups\\V1\\PhoneNumberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberContext.php',
        'Twilio\\Rest\\Lookups\\V1\\PhoneNumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberInstance.php',
        'Twilio\\Rest\\Lookups\\V1\\PhoneNumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberList.php',
        'Twilio\\Rest\\Lookups\\V1\\PhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberOptions.php',
        'Twilio\\Rest\\Lookups\\V1\\PhoneNumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberPage.php',
        'Twilio\\Rest\\Messaging' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging.php',
        'Twilio\\Rest\\Messaging\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1.php',
        'Twilio\\Rest\\Messaging\\V1\\CreateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\CreateSessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceContext.php',
        'Twilio\\Rest\\Messaging\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceList.php',
        'Twilio\\Rest\\Messaging\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServicePage.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderContext.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderList.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderPage.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberContext.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberList.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberPage.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodeContext.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodeInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodeList.php',
        'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodePage.php',
        'Twilio\\Rest\\Messaging\\V1\\SessionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionContext.php',
        'Twilio\\Rest\\Messaging\\V1\\SessionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\SessionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionList.php',
        'Twilio\\Rest\\Messaging\\V1\\SessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\SessionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionPage.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\CreateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\CreateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\CreateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\MessageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageContext.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\MessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\MessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageList.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\MessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\MessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessagePage.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantContext.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantList.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantPage.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\UpdateMessageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\UpdateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\UpdateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookContext.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookList.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookPage.php',
        'Twilio\\Rest\\Messaging\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\UpdateSessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\UpdateWebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\WebhookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookContext.php',
        'Twilio\\Rest\\Messaging\\V1\\WebhookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookInstance.php',
        'Twilio\\Rest\\Messaging\\V1\\WebhookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookList.php',
        'Twilio\\Rest\\Messaging\\V1\\WebhookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookOptions.php',
        'Twilio\\Rest\\Messaging\\V1\\WebhookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookPage.php',
        'Twilio\\Rest\\Monitor' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor.php',
        'Twilio\\Rest\\Monitor\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1.php',
        'Twilio\\Rest\\Monitor\\V1\\AlertContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertContext.php',
        'Twilio\\Rest\\Monitor\\V1\\AlertInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertInstance.php',
        'Twilio\\Rest\\Monitor\\V1\\AlertList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertList.php',
        'Twilio\\Rest\\Monitor\\V1\\AlertOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertOptions.php',
        'Twilio\\Rest\\Monitor\\V1\\AlertPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertPage.php',
        'Twilio\\Rest\\Monitor\\V1\\EventContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventContext.php',
        'Twilio\\Rest\\Monitor\\V1\\EventInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventInstance.php',
        'Twilio\\Rest\\Monitor\\V1\\EventList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventList.php',
        'Twilio\\Rest\\Monitor\\V1\\EventOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventOptions.php',
        'Twilio\\Rest\\Monitor\\V1\\EventPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventPage.php',
        'Twilio\\Rest\\Monitor\\V1\\ReadAlertOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertOptions.php',
        'Twilio\\Rest\\Monitor\\V1\\ReadEventOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventOptions.php',
        'Twilio\\Rest\\Notify' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify.php',
        'Twilio\\Rest\\Notify\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1.php',
        'Twilio\\Rest\\Notify\\V1\\CreateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialOptions.php',
        'Twilio\\Rest\\Notify\\V1\\CreateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
        'Twilio\\Rest\\Notify\\V1\\CredentialContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialContext.php',
        'Twilio\\Rest\\Notify\\V1\\CredentialInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialInstance.php',
        'Twilio\\Rest\\Notify\\V1\\CredentialList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialList.php',
        'Twilio\\Rest\\Notify\\V1\\CredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialOptions.php',
        'Twilio\\Rest\\Notify\\V1\\CredentialPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialPage.php',
        'Twilio\\Rest\\Notify\\V1\\ReadServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
        'Twilio\\Rest\\Notify\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceContext.php',
        'Twilio\\Rest\\Notify\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceInstance.php',
        'Twilio\\Rest\\Notify\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceList.php',
        'Twilio\\Rest\\Notify\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
        'Twilio\\Rest\\Notify\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServicePage.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\BindingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingContext.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\BindingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingInstance.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\BindingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingList.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\BindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingOptions.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\BindingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingPage.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\CreateBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingOptions.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\CreateNotificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationOptions.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\NotificationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationInstance.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\NotificationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationList.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\NotificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationOptions.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\NotificationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationPage.php',
        'Twilio\\Rest\\Notify\\V1\\Service\\ReadBindingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingOptions.php',
        'Twilio\\Rest\\Notify\\V1\\UpdateCredentialOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialOptions.php',
        'Twilio\\Rest\\Notify\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
        'Twilio\\Rest\\Numbers' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers.php',
        'Twilio\\Rest\\Numbers\\V2' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryComplianceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryComplianceInstance.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryComplianceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryComplianceList.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliancePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliancePage.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleContext.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleInstance.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleList.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundlePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundlePage.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentContext.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentInstance.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentList.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentPage.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\CreateBundleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\CreateEndUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\CreateSupportingDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserContext.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserInstance.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserList.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserPage.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypeContext.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypeInstance.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypeList.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypePage.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\ReadBundleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentContext.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentInstance.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentList.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentPage.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypeContext.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypeInstance.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypeList.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypePage.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\UpdateBundleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\UpdateEndUserOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserOptions.php',
        'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\UpdateSupportingDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentOptions.php',
        'Twilio\\Rest\\Preview' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview.php',
        'Twilio\\Rest\\Preview\\BulkExports' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationContext.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationInstance.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationList.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationOptions.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationPage.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportContext.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportInstance.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportList.php',
        'Twilio\\Rest\\Preview\\BulkExports\\ExportPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportPage.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\CreateExportCustomJobOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobOptions.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\DayInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/DayInstance.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\DayList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/DayList.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\DayPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/DayPage.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobInstance.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobList.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobOptions.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobPage.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobContext.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobInstance.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobList.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobPage.php',
        'Twilio\\Rest\\Preview\\BulkExports\\Export\\ReadExportCustomJobOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobOptions.php',
        'Twilio\\Rest\\Preview\\BulkExports\\UpdateExportConfigurationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\CreateFleetOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\FleetContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetContext.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\FleetInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetInstance.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\FleetList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetList.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\FleetOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\FleetPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetPage.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateContext.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateInstance.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateList.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificatePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificatePage.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateCertificateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateDeploymentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateDeviceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentContext.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentInstance.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentList.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentPage.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceContext.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceInstance.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceList.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DevicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DevicePage.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyContext.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyInstance.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyList.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyPage.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\ReadCertificateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\ReadDeviceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\ReadKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateCertificateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateDeploymentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateDeviceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateKeyOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
        'Twilio\\Rest\\Preview\\DeployedDevices\\UpdateFleetOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentContext.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentInstance.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentList.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentPage.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderInstance.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderList.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderPage.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\ReadDependentHostedNumberOrderOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\CreateAuthorizationDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\CreateHostedNumberOrderOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderContext.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderInstance.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderList.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderPage.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\ReadAuthorizationDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\ReadHostedNumberOrderOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\UpdateAuthorizationDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
        'Twilio\\Rest\\Preview\\HostedNumbers\\UpdateHostedNumberOrderOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
        'Twilio\\Rest\\Preview\\Marketplace' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnContext.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnInstance.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnList.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnPage.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionContext.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionInstance.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionList.php',
        'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionPage.php',
        'Twilio\\Rest\\Preview\\Marketplace\\CreateInstalledAddOnOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnOptions.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnContext.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnInstance.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnList.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnOptions.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnPage.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionContext.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionInstance.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionList.php',
        'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionPage.php',
        'Twilio\\Rest\\Preview\\Marketplace\\UpdateInstalledAddOnOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnOptions.php',
        'Twilio\\Rest\\Preview\\Sync' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync.php',
        'Twilio\\Rest\\Preview\\Sync\\CreateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceContext.php',
        'Twilio\\Rest\\Preview\\Sync\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceList.php',
        'Twilio\\Rest\\Preview\\Sync\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServicePage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\CreateDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\CreateSyncListOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\CreateSyncMapOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentPage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionPage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListPage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\ReadSyncListItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemPage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionPage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapPage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\ReadSyncMapItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemOptions.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemPage.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionContext.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionInstance.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionList.php',
        'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionPage.php',
        'Twilio\\Rest\\Preview\\Sync\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceOptions.php',
        'Twilio\\Rest\\Preview\\TrustedComms' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallOptions.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallPage.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BusinessContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessContext.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BusinessInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BusinessList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\BusinessPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessPage.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\InsightsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/InsightsInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\InsightsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/InsightsList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\InsightsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/InsightsPage.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\FetchSuccessRateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateOptions.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateContext.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateOptions.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRatePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRatePage.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CpsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsContext.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CpsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CpsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CpsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsPage.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CreateBrandedCallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallOptions.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CreatePhoneCallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallOptions.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallContext.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallPage.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\DeviceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/DeviceInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\DeviceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/DeviceList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\DevicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/DevicePage.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallInstance.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallList.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallOptions.php',
        'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallPage.php',
        'Twilio\\Rest\\Preview\\Understand' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand.php',
        'Twilio\\Rest\\Preview\\Understand\\AssistantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantContext.php',
        'Twilio\\Rest\\Preview\\Understand\\AssistantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\AssistantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantList.php',
        'Twilio\\Rest\\Preview\\Understand\\AssistantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\AssistantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateFieldTypeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateModelBuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateQueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateTaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialogueContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialogueContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialogueInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialogueInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialogueList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialogueList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialoguePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialoguePage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypePage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\CreateFieldValueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValuePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValuePage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\ReadFieldValueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\ReadQueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\CreateSampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\ReadSampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SamplePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SamplePage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsContext.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsInstance.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsList.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsPage.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\UpdateSampleOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\UpdateTaskActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateAssistantFallbackActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateAssistantInitiationActionsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateFieldTypeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateModelBuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateQueryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateStyleSheetOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateTaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\CreateAssistantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantOptions.php',
        'Twilio\\Rest\\Preview\\Understand\\UpdateAssistantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantOptions.php',
        'Twilio\\Rest\\Preview\\Wireless' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless.php',
        'Twilio\\Rest\\Preview\\Wireless\\CommandContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandContext.php',
        'Twilio\\Rest\\Preview\\Wireless\\CommandInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandInstance.php',
        'Twilio\\Rest\\Preview\\Wireless\\CommandList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandList.php',
        'Twilio\\Rest\\Preview\\Wireless\\CommandOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\CommandPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandPage.php',
        'Twilio\\Rest\\Preview\\Wireless\\CreateCommandOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\CreateRatePlanOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\RatePlanContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanContext.php',
        'Twilio\\Rest\\Preview\\Wireless\\RatePlanInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanInstance.php',
        'Twilio\\Rest\\Preview\\Wireless\\RatePlanList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanList.php',
        'Twilio\\Rest\\Preview\\Wireless\\RatePlanOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\RatePlanPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanPage.php',
        'Twilio\\Rest\\Preview\\Wireless\\ReadCommandOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\ReadSimOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\SimContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimContext.php',
        'Twilio\\Rest\\Preview\\Wireless\\SimInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimInstance.php',
        'Twilio\\Rest\\Preview\\Wireless\\SimList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimList.php',
        'Twilio\\Rest\\Preview\\Wireless\\SimOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\SimPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimPage.php',
        'Twilio\\Rest\\Preview\\Wireless\\Sim\\FetchUsageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageContext.php',
        'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageInstance.php',
        'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageList.php',
        'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsagePage.php',
        'Twilio\\Rest\\Preview\\Wireless\\UpdateRatePlanOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanOptions.php',
        'Twilio\\Rest\\Preview\\Wireless\\UpdateSimOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimOptions.php',
        'Twilio\\Rest\\Pricing' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing.php',
        'Twilio\\Rest\\Pricing\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1.php',
        'Twilio\\Rest\\Pricing\\V1\\MessagingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/MessagingInstance.php',
        'Twilio\\Rest\\Pricing\\V1\\MessagingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/MessagingList.php',
        'Twilio\\Rest\\Pricing\\V1\\MessagingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/MessagingPage.php',
        'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryContext.php',
        'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryInstance.php',
        'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryList.php',
        'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryPage.php',
        'Twilio\\Rest\\Pricing\\V1\\PhoneNumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumberInstance.php',
        'Twilio\\Rest\\Pricing\\V1\\PhoneNumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumberList.php',
        'Twilio\\Rest\\Pricing\\V1\\PhoneNumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumberPage.php',
        'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryContext.php',
        'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryInstance.php',
        'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryList.php',
        'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryPage.php',
        'Twilio\\Rest\\Pricing\\V1\\VoiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/VoiceInstance.php',
        'Twilio\\Rest\\Pricing\\V1\\VoiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/VoiceList.php',
        'Twilio\\Rest\\Pricing\\V1\\VoicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/VoicePage.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryContext.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryInstance.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryList.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryPage.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberContext.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberInstance.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberList.php',
        'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberPage.php',
        'Twilio\\Rest\\Pricing\\V2' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2.php',
        'Twilio\\Rest\\Pricing\\V2\\VoiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/VoiceInstance.php',
        'Twilio\\Rest\\Pricing\\V2\\VoiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/VoiceList.php',
        'Twilio\\Rest\\Pricing\\V2\\VoicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/VoicePage.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryContext.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryInstance.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryList.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryPage.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\FetchNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberOptions.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberContext.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberInstance.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberList.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberOptions.php',
        'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberPage.php',
        'Twilio\\Rest\\Proxy' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy.php',
        'Twilio\\Rest\\Proxy\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1.php',
        'Twilio\\Rest\\Proxy\\V1\\CreateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceContext.php',
        'Twilio\\Rest\\Proxy\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceInstance.php',
        'Twilio\\Rest\\Proxy\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceList.php',
        'Twilio\\Rest\\Proxy\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServicePage.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\CreatePhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\CreateSessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberContext.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberInstance.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberList.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberPage.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\SessionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionContext.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\SessionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionInstance.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\SessionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionList.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\SessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\SessionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionPage.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\CreateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionContext.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionInstance.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionList.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionPage.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantContext.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantInstance.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantList.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantPage.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\CreateMessageInteractionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionContext.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionInstance.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionList.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionPage.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeContext.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeInstance.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeList.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodePage.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\UpdatePhoneNumberOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\UpdateSessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\Service\\UpdateShortCodeOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeOptions.php',
        'Twilio\\Rest\\Proxy\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceOptions.php',
        'Twilio\\Rest\\Serverless' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless.php',
        'Twilio\\Rest\\Serverless\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1.php',
        'Twilio\\Rest\\Serverless\\V1\\CreateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceContext.php',
        'Twilio\\Rest\\Serverless\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceList.php',
        'Twilio\\Rest\\Serverless\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServicePage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\AssetContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\AssetInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\AssetList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\AssetPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetPage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionPage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\BuildContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\BuildInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\BuildList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\BuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\BuildPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildPage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\CreateBuildOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\CreateEnvironmentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentPage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentPage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogPage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\ReadLogOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\UpdateVariableOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableOptions.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariablePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariablePage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionPage.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionContext.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionInstance.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionList.php',
        'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionPage.php',
        'Twilio\\Rest\\Serverless\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceOptions.php',
        'Twilio\\Rest\\Studio' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio.php',
        'Twilio\\Rest\\Studio\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1.php',
        'Twilio\\Rest\\Studio\\V1\\FlowContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowContext.php',
        'Twilio\\Rest\\Studio\\V1\\FlowInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowInstance.php',
        'Twilio\\Rest\\Studio\\V1\\FlowList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowList.php',
        'Twilio\\Rest\\Studio\\V1\\FlowPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\CreateEngagementOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementOptions.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\CreateExecutionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionOptions.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementOptions.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionOptions.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextContext.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextInstance.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextList.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextPage.php',
        'Twilio\\Rest\\Studio\\V1\\Flow\\ReadExecutionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionOptions.php',
        'Twilio\\Rest\\Studio\\V2' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2.php',
        'Twilio\\Rest\\Studio\\V2\\CreateFlowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowOptions.php',
        'Twilio\\Rest\\Studio\\V2\\FlowContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowContext.php',
        'Twilio\\Rest\\Studio\\V2\\FlowInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowInstance.php',
        'Twilio\\Rest\\Studio\\V2\\FlowList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowList.php',
        'Twilio\\Rest\\Studio\\V2\\FlowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowOptions.php',
        'Twilio\\Rest\\Studio\\V2\\FlowPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowPage.php',
        'Twilio\\Rest\\Studio\\V2\\FlowValidateInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateInstance.php',
        'Twilio\\Rest\\Studio\\V2\\FlowValidateList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateList.php',
        'Twilio\\Rest\\Studio\\V2\\FlowValidateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateOptions.php',
        'Twilio\\Rest\\Studio\\V2\\FlowValidatePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidatePage.php',
        'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionContext.php',
        'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionInstance.php',
        'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionList.php',
        'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionPage.php',
        'Twilio\\Rest\\Studio\\V2\\UpdateFlowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowOptions.php',
        'Twilio\\Rest\\Studio\\V2\\UpdateFlowValidateOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateOptions.php',
        'Twilio\\Rest\\Sync' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync.php',
        'Twilio\\Rest\\Sync\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1.php',
        'Twilio\\Rest\\Sync\\V1\\CreateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceOptions.php',
        'Twilio\\Rest\\Sync\\V1\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceContext.php',
        'Twilio\\Rest\\Sync\\V1\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceInstance.php',
        'Twilio\\Rest\\Sync\\V1\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceList.php',
        'Twilio\\Rest\\Sync\\V1\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceOptions.php',
        'Twilio\\Rest\\Sync\\V1\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServicePage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\CreateDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\CreateSyncListOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\CreateSyncMapOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\CreateSyncStreamOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\DocumentContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\DocumentInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\DocumentList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\DocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\DocumentPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncListContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncListInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncListList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncListOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncListPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\CreateSyncListItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\ReadSyncListItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\UpdateSyncListItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\CreateSyncMapItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\ReadSyncMapItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\UpdateSyncMapItemOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamContext.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamPage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStream\\StreamMessageInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStream/StreamMessageInstance.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStream\\StreamMessageList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStream/StreamMessageList.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\SyncStream\\StreamMessagePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStream/StreamMessagePage.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\UpdateDocumentOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\UpdateSyncListOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\UpdateSyncMapOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapOptions.php',
        'Twilio\\Rest\\Sync\\V1\\Service\\UpdateSyncStreamOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamOptions.php',
        'Twilio\\Rest\\Sync\\V1\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceOptions.php',
        'Twilio\\Rest\\Taskrouter' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter.php',
        'Twilio\\Rest\\Taskrouter\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1.php',
        'Twilio\\Rest\\Taskrouter\\V1\\CreateWorkspaceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\ReadWorkspaceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\UpdateWorkspaceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\WorkspacePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspacePage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateActivityOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateTaskChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateTaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateTaskQueueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateWorkerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateWorkflowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\FetchWorkspaceCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\FetchWorkspaceRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\FetchWorkspaceStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadActivityOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadEventOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadTaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadTaskQueueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadWorkerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadWorkflowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueuePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueuePage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\FetchTaskQueueCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\FetchTaskQueueRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\FetchTaskQueueStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\ReadTaskQueuesStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReadReservationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\UpdateReservationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateActivityOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateTaskChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateTaskOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateTaskQueueOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateWorkerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateWorkflowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkerStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkersCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkersRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkersStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReadReservationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\UpdateReservationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\UpdateWorkerChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\FetchWorkflowCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\FetchWorkflowRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\FetchWorkflowStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsPage.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsContext.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsInstance.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsList.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsOptions.php',
        'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsPage.php',
        'Twilio\\Rest\\Trunking' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking.php',
        'Twilio\\Rest\\Trunking\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1.php',
        'Twilio\\Rest\\Trunking\\V1\\CreateTrunkOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkOptions.php',
        'Twilio\\Rest\\Trunking\\V1\\TrunkContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkContext.php',
        'Twilio\\Rest\\Trunking\\V1\\TrunkInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkInstance.php',
        'Twilio\\Rest\\Trunking\\V1\\TrunkList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkList.php',
        'Twilio\\Rest\\Trunking\\V1\\TrunkOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkOptions.php',
        'Twilio\\Rest\\Trunking\\V1\\TrunkPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkPage.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListContext.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListInstance.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListList.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListPage.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListContext.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListInstance.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListList.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListPage.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlContext.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlInstance.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlList.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlOptions.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlPage.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberContext.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberInstance.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberList.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberPage.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainContext.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainInstance.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainList.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainPage.php',
        'Twilio\\Rest\\Trunking\\V1\\Trunk\\UpdateOriginationUrlOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlOptions.php',
        'Twilio\\Rest\\Trunking\\V1\\UpdateTrunkOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkOptions.php',
        'Twilio\\Rest\\Verify' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify.php',
        'Twilio\\Rest\\Verify\\V2' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2.php',
        'Twilio\\Rest\\Verify\\V2\\CreateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceOptions.php',
        'Twilio\\Rest\\Verify\\V2\\ServiceContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceContext.php',
        'Twilio\\Rest\\Verify\\V2\\ServiceInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceInstance.php',
        'Twilio\\Rest\\Verify\\V2\\ServiceList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceList.php',
        'Twilio\\Rest\\Verify\\V2\\ServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceOptions.php',
        'Twilio\\Rest\\Verify\\V2\\ServicePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServicePage.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\CreateRateLimitOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\CreateVerificationCheckOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\CreateVerificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationContext.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationInstance.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationList.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationPage.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitContext.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitInstance.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitList.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitPage.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketContext.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketInstance.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketList.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketPage.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\UpdateBucketOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\UpdateRateLimitOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckInstance.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckList.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckPage.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationContext.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationInstance.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationList.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationOptions.php',
        'Twilio\\Rest\\Verify\\V2\\Service\\VerificationPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationPage.php',
        'Twilio\\Rest\\Verify\\V2\\UpdateServiceOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceOptions.php',
        'Twilio\\Rest\\Video' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video.php',
        'Twilio\\Rest\\Video\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1.php',
        'Twilio\\Rest\\Video\\V1\\CompositionContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionContext.php',
        'Twilio\\Rest\\Video\\V1\\CompositionHookContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookContext.php',
        'Twilio\\Rest\\Video\\V1\\CompositionHookInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookInstance.php',
        'Twilio\\Rest\\Video\\V1\\CompositionHookList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookList.php',
        'Twilio\\Rest\\Video\\V1\\CompositionHookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
        'Twilio\\Rest\\Video\\V1\\CompositionHookPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookPage.php',
        'Twilio\\Rest\\Video\\V1\\CompositionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionInstance.php',
        'Twilio\\Rest\\Video\\V1\\CompositionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionList.php',
        'Twilio\\Rest\\Video\\V1\\CompositionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionOptions.php',
        'Twilio\\Rest\\Video\\V1\\CompositionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionPage.php',
        'Twilio\\Rest\\Video\\V1\\CompositionSettingsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsContext.php',
        'Twilio\\Rest\\Video\\V1\\CompositionSettingsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsInstance.php',
        'Twilio\\Rest\\Video\\V1\\CompositionSettingsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsList.php',
        'Twilio\\Rest\\Video\\V1\\CompositionSettingsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsOptions.php',
        'Twilio\\Rest\\Video\\V1\\CompositionSettingsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsPage.php',
        'Twilio\\Rest\\Video\\V1\\CreateCompositionHookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
        'Twilio\\Rest\\Video\\V1\\CreateCompositionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionOptions.php',
        'Twilio\\Rest\\Video\\V1\\CreateCompositionSettingsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsOptions.php',
        'Twilio\\Rest\\Video\\V1\\CreateRecordingSettingsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsOptions.php',
        'Twilio\\Rest\\Video\\V1\\CreateRoomOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomOptions.php',
        'Twilio\\Rest\\Video\\V1\\ReadCompositionHookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
        'Twilio\\Rest\\Video\\V1\\ReadCompositionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionOptions.php',
        'Twilio\\Rest\\Video\\V1\\ReadRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingOptions.php',
        'Twilio\\Rest\\Video\\V1\\ReadRoomOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomOptions.php',
        'Twilio\\Rest\\Video\\V1\\RecordingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingContext.php',
        'Twilio\\Rest\\Video\\V1\\RecordingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingInstance.php',
        'Twilio\\Rest\\Video\\V1\\RecordingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingList.php',
        'Twilio\\Rest\\Video\\V1\\RecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingOptions.php',
        'Twilio\\Rest\\Video\\V1\\RecordingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingPage.php',
        'Twilio\\Rest\\Video\\V1\\RecordingSettingsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsContext.php',
        'Twilio\\Rest\\Video\\V1\\RecordingSettingsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsInstance.php',
        'Twilio\\Rest\\Video\\V1\\RecordingSettingsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsList.php',
        'Twilio\\Rest\\Video\\V1\\RecordingSettingsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsOptions.php',
        'Twilio\\Rest\\Video\\V1\\RecordingSettingsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsPage.php',
        'Twilio\\Rest\\Video\\V1\\RoomContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomContext.php',
        'Twilio\\Rest\\Video\\V1\\RoomInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomInstance.php',
        'Twilio\\Rest\\Video\\V1\\RoomList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomList.php',
        'Twilio\\Rest\\Video\\V1\\RoomOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomOptions.php',
        'Twilio\\Rest\\Video\\V1\\RoomPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomPage.php',
        'Twilio\\Rest\\Video\\V1\\Room\\ParticipantContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantContext.php',
        'Twilio\\Rest\\Video\\V1\\Room\\ParticipantInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantInstance.php',
        'Twilio\\Rest\\Video\\V1\\Room\\ParticipantList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantList.php',
        'Twilio\\Rest\\Video\\V1\\Room\\ParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantOptions.php',
        'Twilio\\Rest\\Video\\V1\\Room\\ParticipantPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantPage.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackContext.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackInstance.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackList.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackPage.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesInstance.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesList.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesOptions.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesPage.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackContext.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackInstance.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackList.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackPage.php',
        'Twilio\\Rest\\Video\\V1\\Room\\Participant\\UpdateSubscribeRulesOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesOptions.php',
        'Twilio\\Rest\\Video\\V1\\Room\\ReadParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantOptions.php',
        'Twilio\\Rest\\Video\\V1\\Room\\ReadRoomRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingOptions.php',
        'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingContext.php',
        'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingInstance.php',
        'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingList.php',
        'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingOptions.php',
        'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingPage.php',
        'Twilio\\Rest\\Video\\V1\\Room\\UpdateParticipantOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantOptions.php',
        'Twilio\\Rest\\Video\\V1\\UpdateCompositionHookOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
        'Twilio\\Rest\\Voice' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice.php',
        'Twilio\\Rest\\Voice\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissionsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissionsInstance.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissionsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissionsList.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissionsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissionsPage.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\BulkCountryUpdateInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/BulkCountryUpdateInstance.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\BulkCountryUpdateList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/BulkCountryUpdateList.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\BulkCountryUpdatePage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/BulkCountryUpdatePage.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryContext.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryInstance.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryList.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryOptions.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryPage.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\Country\\HighriskSpecialPrefixInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/Country/HighriskSpecialPrefixInstance.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\Country\\HighriskSpecialPrefixList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/Country/HighriskSpecialPrefixList.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\Country\\HighriskSpecialPrefixPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/Country/HighriskSpecialPrefixPage.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\ReadCountryOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryOptions.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsContext.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsInstance.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsList.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsOptions.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsPage.php',
        'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\UpdateSettingsOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsOptions.php',
        'Twilio\\Rest\\Wireless' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless.php',
        'Twilio\\Rest\\Wireless\\V1' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1.php',
        'Twilio\\Rest\\Wireless\\V1\\CommandContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandContext.php',
        'Twilio\\Rest\\Wireless\\V1\\CommandInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandInstance.php',
        'Twilio\\Rest\\Wireless\\V1\\CommandList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandList.php',
        'Twilio\\Rest\\Wireless\\V1\\CommandOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\CommandPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandPage.php',
        'Twilio\\Rest\\Wireless\\V1\\CreateCommandOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\CreateRatePlanOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\RatePlanContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanContext.php',
        'Twilio\\Rest\\Wireless\\V1\\RatePlanInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanInstance.php',
        'Twilio\\Rest\\Wireless\\V1\\RatePlanList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanList.php',
        'Twilio\\Rest\\Wireless\\V1\\RatePlanOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\RatePlanPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanPage.php',
        'Twilio\\Rest\\Wireless\\V1\\ReadCommandOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\ReadSimOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\ReadUsageRecordOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\SimContext' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimContext.php',
        'Twilio\\Rest\\Wireless\\V1\\SimInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimInstance.php',
        'Twilio\\Rest\\Wireless\\V1\\SimList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimList.php',
        'Twilio\\Rest\\Wireless\\V1\\SimOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\SimPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimPage.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionInstance.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionList.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionPage.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\ReadDataSessionOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\ReadUsageRecordOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordInstance.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordList.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordPage.php',
        'Twilio\\Rest\\Wireless\\V1\\UpdateRatePlanOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\UpdateSimOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\UsageRecordInstance' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordInstance.php',
        'Twilio\\Rest\\Wireless\\V1\\UsageRecordList' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordList.php',
        'Twilio\\Rest\\Wireless\\V1\\UsageRecordOptions' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordOptions.php',
        'Twilio\\Rest\\Wireless\\V1\\UsageRecordPage' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordPage.php',
        'Twilio\\Security\\RequestValidator' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Security/RequestValidator.php',
        'Twilio\\Serialize' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Serialize.php',
        'Twilio\\Stream' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Stream.php',
        'Twilio\\TaskRouter\\WorkflowConfiguration' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TaskRouter/WorkflowConfiguration.php',
        'Twilio\\TaskRouter\\WorkflowRule' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TaskRouter/WorkflowRule.php',
        'Twilio\\TaskRouter\\WorkflowRuleTarget' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TaskRouter/WorkflowRuleTarget.php',
        'Twilio\\TwiML\\FaxResponse' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/FaxResponse.php',
        'Twilio\\TwiML\\Fax\\Receive' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Fax/Receive.php',
        'Twilio\\TwiML\\GenericNode' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/GenericNode.php',
        'Twilio\\TwiML\\MessagingResponse' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/MessagingResponse.php',
        'Twilio\\TwiML\\Messaging\\Body' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Messaging/Body.php',
        'Twilio\\TwiML\\Messaging\\Media' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Messaging/Media.php',
        'Twilio\\TwiML\\Messaging\\Message' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Messaging/Message.php',
        'Twilio\\TwiML\\Messaging\\Redirect' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Messaging/Redirect.php',
        'Twilio\\TwiML\\TwiML' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/TwiML.php',
        'Twilio\\TwiML\\Video\\Room' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Video/Room.php',
        'Twilio\\TwiML\\VoiceResponse' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/VoiceResponse.php',
        'Twilio\\TwiML\\Voice\\Autopilot' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Autopilot.php',
        'Twilio\\TwiML\\Voice\\Client' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Client.php',
        'Twilio\\TwiML\\Voice\\Conference' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Conference.php',
        'Twilio\\TwiML\\Voice\\Connect' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Connect.php',
        'Twilio\\TwiML\\Voice\\Dial' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Dial.php',
        'Twilio\\TwiML\\Voice\\Echo_' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Echo.php',
        'Twilio\\TwiML\\Voice\\Enqueue' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Enqueue.php',
        'Twilio\\TwiML\\Voice\\Gather' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Gather.php',
        'Twilio\\TwiML\\Voice\\Hangup' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Hangup.php',
        'Twilio\\TwiML\\Voice\\Identity' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Identity.php',
        'Twilio\\TwiML\\Voice\\Leave' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Leave.php',
        'Twilio\\TwiML\\Voice\\Number' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Number.php',
        'Twilio\\TwiML\\Voice\\Parameter' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Parameter.php',
        'Twilio\\TwiML\\Voice\\Pause' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Pause.php',
        'Twilio\\TwiML\\Voice\\Pay' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Pay.php',
        'Twilio\\TwiML\\Voice\\Play' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Play.php',
        'Twilio\\TwiML\\Voice\\Prompt' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Prompt.php',
        'Twilio\\TwiML\\Voice\\Queue' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Queue.php',
        'Twilio\\TwiML\\Voice\\Record' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Record.php',
        'Twilio\\TwiML\\Voice\\Redirect' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Redirect.php',
        'Twilio\\TwiML\\Voice\\Refer' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Refer.php',
        'Twilio\\TwiML\\Voice\\ReferSip' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/ReferSip.php',
        'Twilio\\TwiML\\Voice\\Reject' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Reject.php',
        'Twilio\\TwiML\\Voice\\Room' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Room.php',
        'Twilio\\TwiML\\Voice\\Say' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Say.php',
        'Twilio\\TwiML\\Voice\\Sim' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Sim.php',
        'Twilio\\TwiML\\Voice\\Sip' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Sip.php',
        'Twilio\\TwiML\\Voice\\Siprec' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Siprec.php',
        'Twilio\\TwiML\\Voice\\Sms' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Sms.php',
        'Twilio\\TwiML\\Voice\\SsmlBreak' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlBreak.php',
        'Twilio\\TwiML\\Voice\\SsmlEmphasis' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlEmphasis.php',
        'Twilio\\TwiML\\Voice\\SsmlLang' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlLang.php',
        'Twilio\\TwiML\\Voice\\SsmlP' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlP.php',
        'Twilio\\TwiML\\Voice\\SsmlPhoneme' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlPhoneme.php',
        'Twilio\\TwiML\\Voice\\SsmlProsody' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlProsody.php',
        'Twilio\\TwiML\\Voice\\SsmlS' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlS.php',
        'Twilio\\TwiML\\Voice\\SsmlSayAs' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlSayAs.php',
        'Twilio\\TwiML\\Voice\\SsmlSub' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlSub.php',
        'Twilio\\TwiML\\Voice\\SsmlW' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlW.php',
        'Twilio\\TwiML\\Voice\\Start' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Start.php',
        'Twilio\\TwiML\\Voice\\Stop' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Stop.php',
        'Twilio\\TwiML\\Voice\\Stream' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Stream.php',
        'Twilio\\TwiML\\Voice\\Task' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/TwiML/Voice/Task.php',
        'Twilio\\Values' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Values.php',
        'Twilio\\Version' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/Version.php',
        'Twilio\\VersionInfo' => __DIR__ . '/..' . '/twilio/sdk/src/Twilio/VersionInfo.php',
        'WeDevs\\AdvancedLeave\\Accrual\\Accrual' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Accrual/Accrual.php',
        'WeDevs\\AdvancedLeave\\Accrual\\AccrualBgProcess' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Accrual/AccrualBgProcess.php',
        'WeDevs\\AdvancedLeave\\Forward\\Forward' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Forward/Forward.php',
        'WeDevs\\AdvancedLeave\\Forward\\LeaveCarryForwardBgProcess' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Forward/LeaveCarryForwardBgProcess.php',
        'WeDevs\\AdvancedLeave\\Forward\\LeaveForwardListTable' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Forward/LeaveForwardListTable.php',
        'WeDevs\\AdvancedLeave\\Halfday\\Halfday' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Halfday/Halfday.php',
        'WeDevs\\AdvancedLeave\\Multilevel\\ForwardedLeaveRequest' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Multilevel/ForwardedLeaveRequest.php',
        'WeDevs\\AdvancedLeave\\Multilevel\\Multilevel' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Multilevel/Multilevel.php',
        'WeDevs\\AdvancedLeave\\Segregation\\Segregation' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Segregation/Segregation.php',
        'WeDevs\\AdvancedLeave\\Unpaid\\LeaveUnpaidListTable' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Unpaid/LeaveUnpaidListTable.php',
        'WeDevs\\AdvancedLeave\\Unpaid\\Unpaid' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/includes/Unpaid/Unpaid.php',
        'WeDevs\\AssetManagement\\AdminMenu' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/AdminMenu.php',
        'WeDevs\\AssetManagement\\AjaxHandler' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/AjaxHandler.php',
        'WeDevs\\AssetManagement\\AllottmentListTable' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/AllottmentListTable.php',
        'WeDevs\\AssetManagement\\AssetsListTable' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/AssetsListTable.php',
        'WeDevs\\AssetManagement\\EmployeeAssetApprove' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/EmployeeAssetApprove.php',
        'WeDevs\\AssetManagement\\EmployeeAssetOverdue' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/EmployeeAssetOverdue.php',
        'WeDevs\\AssetManagement\\EmployeeAssetRequest' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/EmployeeAssetRequest.php',
        'WeDevs\\AssetManagement\\EmployeeAssetRequestReject' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/EmployeeAssetRequestReject.php',
        'WeDevs\\AssetManagement\\FormHandler' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/FormHandler.php',
        'WeDevs\\AssetManagement\\Log' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/Log.php',
        'WeDevs\\AssetManagement\\RequestListTable' => __DIR__ . '/../..' . '/modules/hrm/asset-management/includes/RequestListTable.php',
        'WeDevs\\Attendance\\ActionFilter' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/ActionFilter.php',
        'WeDevs\\Attendance\\Admin' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Admin.php',
        'WeDevs\\Attendance\\Ajax' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Ajax.php',
        'WeDevs\\Attendance\\Api\\AttendanceController' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Api/AttendanceController.php',
        'WeDevs\\Attendance\\Assets' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Assets.php',
        'WeDevs\\Attendance\\AssignedShiftEmployees' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/AssignedShiftEmployees.php',
        'WeDevs\\Attendance\\AttendanceListTable' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/AttendanceListTable.php',
        'WeDevs\\Attendance\\AttendanceReportEmployeeBased' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/AttendanceReportEmployeeBased.php',
        'WeDevs\\Attendance\\AttendanceShifts' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/AttendanceShifts.php',
        'WeDevs\\Attendance\\AttendanceSingleListTable' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/AttendanceSingleListTable.php',
        'WeDevs\\Attendance\\Attendance_Controller' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/api/prev-class-attendance-controller.php',
        'WeDevs\\Attendance\\Emails\\AttendanceReminder' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Emails/AttendanceReminder.php',
        'WeDevs\\Attendance\\Emails\\Emailer' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Emails/Emailer.php',
        'WeDevs\\Attendance\\FormHandler' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/FormHandler.php',
        'WeDevs\\Attendance\\Frontend' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Frontend.php',
        'WeDevs\\Attendance\\Install' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Install.php',
        'WeDevs\\Attendance\\IpUtils' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/IpUtils.php',
        'WeDevs\\Attendance\\Log' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Log.php',
        'WeDevs\\Attendance\\Notification' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Notification.php',
        'WeDevs\\Attendance\\Queries\\AttendanceByDate' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Queries/AttendanceByDate.php',
        'WeDevs\\Attendance\\REST_API' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/REST_API.php',
        'WeDevs\\Attendance\\Updates' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Updates.php',
        'WeDevs\\Attendance\\Updates\\BP\\ERP_Att_Migrate_Attendance' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Updates/BP/ERP_Att_Migrate_Attendance.php',
        'WeDevs\\Attendance\\Widgets' => __DIR__ . '/../..' . '/modules/hrm/attendance/includes/Widgets.php',
        'WeDevs\\AwesomeSupport\\Import' => __DIR__ . '/../..' . '/modules/pro/awesome-support/includes/Import.php',
        'WeDevs\\AwesomeSupport\\Settings' => __DIR__ . '/../..' . '/modules/pro/awesome-support/includes/Settings.php',
        'WeDevs\\AwesomeSupport\\Widget' => __DIR__ . '/../..' . '/modules/pro/awesome-support/includes/Widget.php',
        'WeDevs\\CustomFieldBuilder\\API\\CustomFieldBuilderController' => __DIR__ . '/../..' . '/modules/hrm/custom-field-builder/includes/API/CustomFieldBuilderController.php',
        'WeDevs\\CustomFieldBuilder\\API\\REST_API' => __DIR__ . '/../..' . '/modules/hrm/custom-field-builder/includes/API/REST_API.php',
        'WeDevs\\CustomFieldBuilder\\Classes\\Admin' => __DIR__ . '/../..' . '/modules/hrm/custom-field-builder/includes/Classes/Admin.php',
        'WeDevs\\CustomFieldBuilder\\Classes\\Assets' => __DIR__ . '/../..' . '/modules/hrm/custom-field-builder/includes/Classes/Assets.php',
        'WeDevs\\Deals\\Admin' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Admin.php',
        'WeDevs\\Deals\\Deal_Ajax' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Deal_Ajax.php',
        'WeDevs\\Deals\\Deals' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Deals.php',
        'WeDevs\\Deals\\Frontend' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Frontend.php',
        'WeDevs\\Deals\\Helpers' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Helpers.php',
        'WeDevs\\Deals\\Hooks' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Hooks.php',
        'WeDevs\\Deals\\Log' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Log.php',
        'WeDevs\\Deals\\Models\\Activity' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Activity.php',
        'WeDevs\\Deals\\Models\\ActivityType' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/ActivityType.php',
        'WeDevs\\Deals\\Models\\Agent' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Agent.php',
        'WeDevs\\Deals\\Models\\Attachment' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Attachment.php',
        'WeDevs\\Deals\\Models\\Competitor' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Competitor.php',
        'WeDevs\\Deals\\Models\\Deal' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Deal.php',
        'WeDevs\\Deals\\Models\\Email' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Email.php',
        'WeDevs\\Deals\\Models\\LostReason' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/LostReason.php',
        'WeDevs\\Deals\\Models\\Note' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Note.php',
        'WeDevs\\Deals\\Models\\Participant' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Participant.php',
        'WeDevs\\Deals\\Models\\Pipeline' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/Pipeline.php',
        'WeDevs\\Deals\\Models\\PipelineStage' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/PipelineStage.php',
        'WeDevs\\Deals\\Models\\StageHistory' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Models/StageHistory.php',
        'WeDevs\\Deals\\Shortcodes' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Shortcodes.php',
        'WeDevs\\Deals\\Statistics' => __DIR__ . '/../..' . '/modules/crm/deals/includes/Statistics.php',
        'WeDevs\\DocumentManager\\API\\DocumentController' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/API/DocumentController.php',
        'WeDevs\\DocumentManager\\AjaxHandler' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/AjaxHandler.php',
        'WeDevs\\DocumentManager\\AjaxHandlerBack' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/class-doc-ajax_back.php',
        'WeDevs\\DocumentManager\\DocLog' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/DocLog.php',
        'WeDevs\\DocumentManager\\DocumentInstaller' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/DocumentInstaller.php',
        'WeDevs\\DocumentManager\\Emails\\DmFileShareNotification' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/Emails/DmFileShareNotification.php',
        'WeDevs\\DocumentManager\\Emails\\Emailer' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/Emails/Emailer.php',
        'WeDevs\\DocumentManager\\FormHandler' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/FormHandler.php',
        'WeDevs\\DocumentManager\\Settings' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/class-settings.php',
        'WeDevs\\DocumentManager\\Updates' => __DIR__ . '/../..' . '/modules/hrm/document-manager/includes/Updates.php',
        'WeDevs\\ERP\\Accounting\\Reimbursement\\Admin' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/deprecated/includes/Admin.php',
        'WeDevs\\ERP\\Accounting\\Reimbursement\\ReimbursementTransactionListTable' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/deprecated/includes/ReimbursementTransactionListTable.php',
        'WeDevs\\ERP\\Hubspot\\AdminMenu' => __DIR__ . '/../..' . '/modules/pro/hubspot/includes/AdminMenu.php',
        'WeDevs\\ERP\\Hubspot\\AjaxHandler' => __DIR__ . '/../..' . '/modules/pro/hubspot/includes/AjaxHandler.php',
        'WeDevs\\ERP\\Hubspot\\Http_Client' => __DIR__ . '/../..' . '/modules/pro/hubspot/includes/Http_Client.php',
        'WeDevs\\ERP\\Hubspot\\Hubspot' => __DIR__ . '/../..' . '/modules/pro/hubspot/includes/Hubspot.php',
        'WeDevs\\ERP\\Hubspot\\Hubspot_Integration' => __DIR__ . '/../..' . '/modules/pro/hubspot/includes/Hubspot_Integration.php',
        'WeDevs\\ERP\\Mailchimp\\AdminMenu' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/AdminMenu.php',
        'WeDevs\\ERP\\Mailchimp\\AjaxHandler' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/AjaxHandler.php',
        'WeDevs\\ERP\\Mailchimp\\Http_Client' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/Http_Client.php',
        'WeDevs\\ERP\\Mailchimp\\Mailchimp' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/Mailchimp.php',
        'WeDevs\\ERP\\Mailchimp\\Mailchimp_Integration' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/Mailchimp_Integration.php',
        'WeDevs\\ERP\\Mailchimp\\Sync_Handler' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/Sync_Handler.php',
        'WeDevs\\ERP\\Mailchimp\\Webhook_Controller' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/Webhook_Controller.php',
        'WeDevs\\ERP\\Mailchimp\\Webhook_Manager' => __DIR__ . '/../..' . '/modules/pro/mailchimp/includes/Webhook_Manager.php',
        'WeDevs\\ERP\\SMS\\Clickatell' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/Clickatell.php',
        'WeDevs\\ERP\\SMS\\GatewayHandler' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/GatewayHandler.php',
        'WeDevs\\ERP\\SMS\\GatewayInterface' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/GatewayInterface.php',
        'WeDevs\\ERP\\SMS\\Hoiio' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/Hoiio.php',
        'WeDevs\\ERP\\SMS\\Infobip' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/Infobip.php',
        'WeDevs\\ERP\\SMS\\Intellisms' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/Intellisms.php',
        'WeDevs\\ERP\\SMS\\Nexmo' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/Nexmo.php',
        'WeDevs\\ERP\\SMS\\SmsSettings' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/SmsSettings.php',
        'WeDevs\\ERP\\SMS\\Smsglobal' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/Smsglobal.php',
        'WeDevs\\ERP\\SMS\\Twilio' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/includes/Twilio.php',
        'WeDevs\\ERP\\Salesforce\\AdminMenu' => __DIR__ . '/../..' . '/modules/pro/salesforce/includes/AdminMenu.php',
        'WeDevs\\ERP\\Salesforce\\AjaxHandler' => __DIR__ . '/../..' . '/modules/pro/salesforce/includes/AjaxHandler.php',
        'WeDevs\\ERP\\Salesforce\\HttpClient' => __DIR__ . '/../..' . '/modules/pro/salesforce/includes/HttpClient.php',
        'WeDevs\\ERP\\Salesforce\\Salesforce' => __DIR__ . '/../..' . '/modules/pro/salesforce/includes/Salesforce.php',
        'WeDevs\\ERP\\Salesforce\\SalesforceIntegration' => __DIR__ . '/../..' . '/modules/pro/salesforce/includes/SalesforceIntegration.php',
        'WeDevs\\ERP\\Workflow\\AdminMenu' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/AdminMenu.php',
        'WeDevs\\ERP\\Workflow\\AjaxHandler' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/AjaxHandler.php',
        'WeDevs\\ERP\\Workflow\\Models\\Action' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/Models/Action.php',
        'WeDevs\\ERP\\Workflow\\Models\\Condition' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/Models/Condition.php',
        'WeDevs\\ERP\\Workflow\\Models\\Email' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/Models/Email.php',
        'WeDevs\\ERP\\Workflow\\Models\\Log' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/Models/Log.php',
        'WeDevs\\ERP\\Workflow\\Models\\Workflow' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/Models/Workflow.php',
        'WeDevs\\ERP\\Workflow\\Workflows' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/Workflows.php',
        'WeDevs\\ERP\\Workflow\\WorkflowsListTable' => __DIR__ . '/../..' . '/modules/hrm/workflow/includes/WorkflowsListTable.php',
        'WeDevs\\ERP\\Zendesk\\Ajax' => __DIR__ . '/../..' . '/modules/pro/zendesk/includes/Ajax.php',
        'WeDevs\\ERP\\Zendesk\\Settings' => __DIR__ . '/../..' . '/modules/pro/zendesk/includes/Settings.php',
        'WeDevs\\ERP\\Zendesk\\User' => __DIR__ . '/../..' . '/modules/pro/zendesk/includes/User.php',
        'WeDevs\\WSL_ERP\\Accounting\\Inventory\\Module' => __DIR__ . '/../..' . '/modules/accounting/inventory/Module.php',
        'WeDevs\\WSL_ERP\\Accounting\\woocommerce\\Module' => __DIR__ . '/../..' . '/modules/accounting/woocommerce/Module.php',
        'WeDevs\\WSL_ERP\\Admin\\Admin' => __DIR__ . '/../..' . '/includes/Admin/Admin.php',
        'WeDevs\\WSL_ERP\\Admin\\Ajax' => __DIR__ . '/../..' . '/includes/Admin/Ajax.php',
        'WeDevs\\WSL_ERP\\Admin\\ComposerUpgradeNotice' => __DIR__ . '/../..' . '/includes/Admin/ComposerUpgradeNotice.php',
        'WeDevs\\WSL_ERP\\Admin\\Menu\\Extensions' => __DIR__ . '/../..' . '/includes/Admin/Menu/Extensions.php',
        'WeDevs\\WSL_ERP\\Admin\\Update' => __DIR__ . '/../..' . '/includes/Admin/Update.php',
        'WeDevs\\WSL_ERP\\CRM\\Deals\\Module' => __DIR__ . '/../..' . '/modules/crm/deals/Module.php',
        'WeDevs\\WSL_ERP\\Feature\\Accounting\\Api\\PurchaseReturnController' => __DIR__ . '/../..' . '/includes/Feature/Accounting/Api/PurchaseReturnController.php',
        'WeDevs\\WSL_ERP\\Feature\\Accounting\\Api\\ReportsController' => __DIR__ . '/../..' . '/includes/Feature/Accounting/Api/ReportsController.php',
        'WeDevs\\WSL_ERP\\Feature\\Accounting\\Api\\Rest_Controller' => __DIR__ . '/../..' . '/includes/Feature/Accounting/Api/Rest_Controller.php',
        'WeDevs\\WSL_ERP\\Feature\\Accounting\\Api\\SalesReturnController' => __DIR__ . '/../..' . '/includes/Feature/Accounting/Api/SalesReturnController.php',
        'WeDevs\\WSL_ERP\\Feature\\Accounting\\Base' => __DIR__ . '/../..' . '/includes/Feature/Accounting/Base.php',
        'WeDevs\\WSL_ERP\\Feature\\Accounting\\Core\\Assets' => __DIR__ . '/../..' . '/includes/Feature/Accounting/Core/Assets.php',
        'WeDevs\\WSL_ERP\\Feature\\Accounting\\Core\\Core' => __DIR__ . '/../..' . '/includes/Feature/Accounting/Core/Core.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Base' => __DIR__ . '/../..' . '/includes/Feature/CRM/Base.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Core\\Core' => __DIR__ . '/../..' . '/includes/Feature/CRM/Core/Core.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Life_Stages\\Ajax' => __DIR__ . '/../..' . '/includes/Feature/CRM/Life_Stages/Ajax.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Life_Stages\\Helpers' => __DIR__ . '/../..' . '/includes/Feature/CRM/Life_Stages/Helpers.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Life_Stages\\Hooks' => __DIR__ . '/../..' . '/includes/Feature/CRM/Life_Stages/Hooks.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Life_Stages\\Main' => __DIR__ . '/../..' . '/includes/Feature/CRM/Life_Stages/Main.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Tasks\\Admin' => __DIR__ . '/../..' . '/includes/Feature/CRM/Tasks/Admin.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Tasks\\Ajax' => __DIR__ . '/../..' . '/includes/Feature/CRM/Tasks/Ajax.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Tasks\\Helpers' => __DIR__ . '/../..' . '/includes/Feature/CRM/Tasks/Helpers.php',
        'WeDevs\\WSL_ERP\\Feature\\CRM\\Tasks\\Main' => __DIR__ . '/../..' . '/includes/Feature/CRM/Tasks/Main.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Base' => __DIR__ . '/../..' . '/includes/Feature/HRM/Base.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Core\\Assets' => __DIR__ . '/../..' . '/includes/Feature/HRM/Core/Assets.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Core\\Core' => __DIR__ . '/../..' . '/includes/Feature/HRM/Core/Core.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Digest_Email\\Main' => __DIR__ . '/../..' . '/includes/Feature/HRM/Digest_Email/Main.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Org_Chart\\Ajax' => __DIR__ . '/../..' . '/includes/Feature/HRM/Org_Chart/Ajax.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Org_Chart\\Helpers' => __DIR__ . '/../..' . '/includes/Feature/HRM/Org_Chart/Helpers.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Org_Chart\\Org_Chart' => __DIR__ . '/../..' . '/includes/Feature/HRM/Org_Chart/Org_Chart.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Remote_Work\\Ajax' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Remote_Work/Ajax.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Remote_Work\\Hooks' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Remote_Work/Hooks.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Remote_Work\\Main' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Remote_Work/Main.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Remote_Work\\Settings' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Remote_Work/Settings.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Requests' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Requests.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Resignation\\Ajax' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Resignation/Ajax.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Resignation\\Email' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Resignation/Email.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Resignation\\Hooks' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Resignation/Hooks.php',
        'WeDevs\\WSL_ERP\\Feature\\HRM\\Requests\\Resignation\\Main' => __DIR__ . '/../..' . '/includes/Feature/HRM/Requests/Resignation/Main.php',
        'WeDevs\\WSL_ERP\\HRM\\AssetManagement\\Module' => __DIR__ . '/../..' . '/modules/hrm/asset-management/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\Attendance\\Module' => __DIR__ . '/../..' . '/modules/hrm/attendance/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\CustomFieldBuilder\\Module' => __DIR__ . '/../..' . '/modules/hrm/custom-field-builder/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\DocumentManager\\Module' => __DIR__ . '/../..' . '/modules/hrm/document-manager/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\HrTraining\\Module' => __DIR__ . '/../..' . '/modules/hrm/hr-training/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\Payroll\\Module' => __DIR__ . '/../..' . '/modules/hrm/payroll/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\Recruitment\\Module' => __DIR__ . '/../..' . '/modules/hrm/recruitment/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\Reimbursement\\Module' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\SmsNotification\\Module' => __DIR__ . '/../..' . '/modules/hrm/sms-notification/Module.php',
        'WeDevs\\WSL_ERP\\HRM\\Workflow\\Module' => __DIR__ . '/../..' . '/modules/hrm/workflow/Module.php',
        'WeDevs\\WSL_ERP\\Install\\Installer' => __DIR__ . '/../..' . '/includes/Install/Installer.php',
        'WeDevs\\WSL_ERP\\Module' => __DIR__ . '/../..' . '/includes/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\AdvancedLeave\\Module' => __DIR__ . '/../..' . '/modules/pro/advanced-leave/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\AwesomeSupport\\Module' => __DIR__ . '/../..' . '/modules/pro/awesome-support/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\GravityForms\\Gravity_Forms' => __DIR__ . '/../..' . '/modules/pro/gravity_forms/gravityforms.php',
        'WeDevs\\WSL_ERP\\PRO\\GravityForms\\Module' => __DIR__ . '/../..' . '/modules/pro/gravity_forms/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\HelpScout\\Module' => __DIR__ . '/../..' . '/modules/pro/help-scout/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\HrFrontend\\Module' => __DIR__ . '/../..' . '/modules/pro/hr-frontend/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\Hubspot\\Module' => __DIR__ . '/../..' . '/modules/pro/hubspot/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\Mailchimp\\Module' => __DIR__ . '/../..' . '/modules/pro/mailchimp/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\Salesforce\\Module' => __DIR__ . '/../..' . '/modules/pro/salesforce/Module.php',
        'WeDevs\\WSL_ERP\\PRO\\Zendesk\\Module' => __DIR__ . '/../..' . '/modules/pro/zendesk/Module.php',
        'WeDevs\\WSL_ERP\\REST\\ModulesController' => __DIR__ . '/../..' . '/includes/REST/ModulesController.php',
        'WeDevs\\WSL_ERP\\Traits\\Singleton' => __DIR__ . '/../..' . '/includes/Traits/Singleton.php',
        'WeDevs\\WSL_ERP\\Updates\\BP\\Recruitment_Experience_Type_Migrator' => __DIR__ . '/../..' . '/includes/Updates/BP/Recruitment_Experience_Type_Migrator.php',
        'WeDevs\\WSL_ERP\\Updates\\Update_1_2_8' => __DIR__ . '/../..' . '/includes/Updates/Update-1.2.8.php',
        'WeDevs\\WSL_ERP\\Updates\\Updater' => __DIR__ . '/../..' . '/includes/Updates/Updater.php',
        'WeDevs\\Erp_Inventory\\Api\\InventoryController' => __DIR__ . '/../..' . '/modules/accounting/inventory/includes/Api/InventoryController.php',
        'WeDevs\\Erp_Inventory\\Api\\InventoryReportController' => __DIR__ . '/../..' . '/modules/accounting/inventory/includes/Api/InventoryReportController.php',
        'WeDevs\\Erp_Inventory\\Api\\REST_API' => __DIR__ . '/../..' . '/modules/accounting/inventory/includes/Api/REST_API.php',
        'WeDevs\\Erp_Inventory\\Classes\\Admin' => __DIR__ . '/../..' . '/modules/accounting/inventory/includes/Classes/Admin.php',
        'WeDevs\\Erp_Inventory\\Classes\\Assets' => __DIR__ . '/../..' . '/modules/accounting/inventory/includes/Classes/Assets.php',
        'WeDevs\\Erp_Inventory\\Classes\\ERP_Inventory_i18n' => __DIR__ . '/../..' . '/modules/accounting/inventory/includes/Classes/ERP_Inventory_i18n.php',
        'WeDevs\\HelpScout\\AdminMenu' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/AdminMenu.php',
        'WeDevs\\HelpScout\\Ajax' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/Ajax.php',
        'WeDevs\\HelpScout\\Api\\HelpscoutCustomerController' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/Api/HelpscoutCustomerController.php',
        'WeDevs\\HelpScout\\Customer' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/Customer.php',
        'WeDevs\\HelpScout\\Deals' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/Deals.php',
        'WeDevs\\HelpScout\\EndPoint' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/EndPoint.php',
        'WeDevs\\HelpScout\\Request' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/Request.php',
        'WeDevs\\HelpScout\\Settings' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/Settings.php',
        'WeDevs\\HelpScout\\User' => __DIR__ . '/../..' . '/modules/pro/help-scout/includes/User.php',
        'WeDevs\\HrFrontend\\DashboardSettings' => __DIR__ . '/../..' . '/modules/pro/hr-frontend/includes/DashboardSettings.php',
        'WeDevs\\HrFrontend\\HrFrontendI18n' => __DIR__ . '/../..' . '/modules/pro/hr-frontend/includes/HrFrontendI18n.php',
        'WeDevs\\HrFrontend\\Rewrites' => __DIR__ . '/../..' . '/modules/pro/hr-frontend/includes/Rewrites.php',
        'WeDevs\\HrTraining\\Ajax' => __DIR__ . '/../..' . '/modules/hrm/hr-training/includes/Ajax.php',
        'WeDevs\\HrTraining\\Emails\\AfterAssignTraining' => __DIR__ . '/../..' . '/modules/hrm/hr-training/includes/Emails/AfterAssignTraining.php',
        'WeDevs\\HrTraining\\Emails\\Emailer' => __DIR__ . '/../..' . '/modules/hrm/hr-training/includes/Emails/Emailer.php',
        'WeDevs\\HrTraining\\Log' => __DIR__ . '/../..' . '/modules/hrm/hr-training/includes/Log.php',
        'WeDevs\\HrTraining\\TrainingEmployee' => __DIR__ . '/../..' . '/modules/hrm/hr-training/includes/TrainingEmployee.php',
        'WeDevs\\HrTraining\\TrainingPostType' => __DIR__ . '/../..' . '/modules/hrm/hr-training/includes/TrainingPostType.php',
        'WeDevs\\PaymentGateway\\Gateways\\Paypal' => __DIR__ . '/../..' . '/modules/accounting/payment-gateway/includes/Gateways/Paypal.php',
        'WeDevs\\PaymentGateway\\Gateways\\Stripe' => __DIR__ . '/../..' . '/modules/accounting/payment-gateway/includes/Gateways/Stripe.php',
        'WeDevs\\PaymentGateway\\GeneralSettings' => __DIR__ . '/../..' . '/modules/accounting/payment-gateway/includes/GeneralSettings.php',
        'WeDevs\\PaymentGateway\\PaymentGatewayHandler' => __DIR__ . '/../..' . '/modules/accounting/payment-gateway/includes/PaymentGatewayHandler.php',
        'WeDevs\\PaymentGateway\\PaymentGatewaySettings' => __DIR__ . '/../..' . '/modules/accounting/payment-gateway/includes/PaymentGatewaySettings.php',
        'WeDevs\\Payroll\\AdminMenu' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/AdminMenu.php',
        'WeDevs\\Payroll\\Admin\\SetupWizard' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Admin/SetupWizard.php',
        'WeDevs\\Payroll\\AjaxHandler' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/AjaxHandler.php',
        'WeDevs\\Payroll\\CLI' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/CLI.php',
        'WeDevs\\Payroll\\Emails\\EmailPayslip' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Emails/EmailPayslip.php',
        'WeDevs\\Payroll\\Emails\\EmailPayslipBulk' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Emails/EmailPayslipBulk.php',
        'WeDevs\\Payroll\\Emails\\EmailPayslipSingle' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Emails/EmailPayslipSingle.php',
        'WeDevs\\Payroll\\Emails\\Emailer' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Emails/Emailer.php',
        'WeDevs\\Payroll\\FormHandler' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/FormHandler.php',
        'WeDevs\\Payroll\\Installer' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Installer.php',
        'WeDevs\\Payroll\\PayrunListTable' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/PayrunListTable.php',
        'WeDevs\\Payroll\\Settings' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Settings.php',
        'WeDevs\\Payroll\\Updates' => __DIR__ . '/../..' . '/modules/hrm/payroll/includes/Updates.php',
        'WeDevs\\Recruitment\\AdminMenu' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/AdminMenu.php',
        'WeDevs\\Recruitment\\AjaxHandler' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/AjaxHandler.php',
        'WeDevs\\Recruitment\\Api\\RecruitmentController' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Api/RecruitmentController.php',
        'WeDevs\\Recruitment\\Emails\\CandidateReport' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Emails/CandidateReport.php',
        'WeDevs\\Recruitment\\Emails\\ConfirmationOfSuccessfulSubmission' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Emails/ConfirmationOfSuccessfulSubmission.php',
        'WeDevs\\Recruitment\\Emails\\Emailer' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Emails/Emailer.php',
        'WeDevs\\Recruitment\\Emails\\NewInterview' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Emails/NewInterview.php',
        'WeDevs\\Recruitment\\Emails\\NewJobApplicationSubmitted' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Emails/NewJobApplicationSubmitted.php',
        'WeDevs\\Recruitment\\Emails\\NewTodo' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Emails/NewTodo.php',
        'WeDevs\\Recruitment\\Emails\\OpeningReport' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Emails/OpeningReport.php',
        'WeDevs\\Recruitment\\FormHandler' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/FormHandler.php',
        'WeDevs\\Recruitment\\HrQuestionnaire' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/HrQuestionnaire.php',
        'WeDevs\\Recruitment\\Installer' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Installer.php',
        'WeDevs\\Recruitment\\JobSchemaManager' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/JobSchemaManager.php',
        'WeDevs\\Recruitment\\JobSeekerListTable' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/JobSeekerListTable.php',
        'WeDevs\\Recruitment\\Recruitment' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Recruitment.php',
        'WeDevs\\Recruitment\\Updates' => __DIR__ . '/../..' . '/modules/hrm/recruitment/includes/Updates.php',
        'WeDevs\\Reimbursement\\Api\\EmployeeRequestsController' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/Api/EmployeeRequestsController.php',
        'WeDevs\\Reimbursement\\Api\\PeopleTrnController' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/Api/PeopleTrnController.php',
        'WeDevs\\Reimbursement\\Api\\REST_API' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/Api/REST_API.php',
        'WeDevs\\Reimbursement\\Classes\\Admin' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/classes/Admin.php',
        'WeDevs\\Reimbursement\\Classes\\Assets' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/classes/Assets.php',
        'WeDevs\\Reimbursement\\Classes\\ReimbursementI18n' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/classes/ReimbursementI18n.php',
        'WeDevs\\Reimbursement\\Classes\\Updates' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/classes/Updates.php',
        'WeDevs\\Reimbursement\\Updates\\BP\\OldReimbRequestMigration' => __DIR__ . '/../..' . '/modules/hrm/reimbursement/includes/classes/updates/bp/OldReimbRequestMigration.php',
        'Wikimedia\\Composer\\Merge\\V2\\ExtraPackage' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/ExtraPackage.php',
        'Wikimedia\\Composer\\Merge\\V2\\Logger' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/Logger.php',
        'Wikimedia\\Composer\\Merge\\V2\\MergePlugin' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/MergePlugin.php',
        'Wikimedia\\Composer\\Merge\\V2\\MissingFileException' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/MissingFileException.php',
        'Wikimedia\\Composer\\Merge\\V2\\MultiConstraint' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/MultiConstraint.php',
        'Wikimedia\\Composer\\Merge\\V2\\NestedArray' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/NestedArray.php',
        'Wikimedia\\Composer\\Merge\\V2\\PluginState' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/PluginState.php',
        'Wikimedia\\Composer\\Merge\\V2\\StabilityFlags' => __DIR__ . '/..' . '/wikimedia/composer-merge-plugin/src/StabilityFlags.php',
        'infobip\\api\\AbstractApiClient' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/AbstractApiClient.php',
        'infobip\\api\\client\\GetAccountBalance' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/GetAccountBalance.php',
        'infobip\\api\\client\\GetNumberContextLogs' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/GetNumberContextLogs.php',
        'infobip\\api\\client\\GetReceivedMessages' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/GetReceivedMessages.php',
        'infobip\\api\\client\\GetReceivedSmsLogs' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/GetReceivedSmsLogs.php',
        'infobip\\api\\client\\GetSentSmsDeliveryReports' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/GetSentSmsDeliveryReports.php',
        'infobip\\api\\client\\GetSentSmsLogs' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/GetSentSmsLogs.php',
        'infobip\\api\\client\\NumberContextNotify' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/NumberContextNotify.php',
        'infobip\\api\\client\\NumberContextQuery' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/NumberContextQuery.php',
        'infobip\\api\\client\\SendMultipleBinarySms' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleBinarySms.php',
        'infobip\\api\\client\\SendMultipleSmsBinaryAdvanced' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleSmsBinaryAdvanced.php',
        'infobip\\api\\client\\SendMultipleSmsTextual' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleSmsTextual.php',
        'infobip\\api\\client\\SendMultipleTextualSmsAdvanced' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleTextualSmsAdvanced.php',
        'infobip\\api\\client\\SendSingleBinarySms' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/SendSingleBinarySms.php',
        'infobip\\api\\client\\SendSingleTextualSms' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/client/SendSingleTextualSms.php',
        'infobip\\api\\configuration\\ApiKeyAuthConfiguration' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/configuration/ApiKeyAuthConfiguration.php',
        'infobip\\api\\configuration\\BasicAuthConfiguration' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/configuration/BasicAuthConfiguration.php',
        'infobip\\api\\configuration\\Configuration' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/configuration/Configuration.php',
        'infobip\\api\\configuration\\IbssoAuthConfiguration' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/configuration/IbssoAuthConfiguration.php',
        'infobip\\api\\model\\Destination' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/Destination.php',
        'infobip\\api\\model\\Error' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/Error.php',
        'infobip\\api\\model\\Price' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/Price.php',
        'infobip\\api\\model\\Status' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/Status.php',
        'infobip\\api\\model\\account\\AccountBalance' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/account/AccountBalance.php',
        'infobip\\api\\model\\nc\\Network' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/Network.php',
        'infobip\\api\\model\\nc\\logs\\GetNumberContextLogsExecuteContext' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs/GetNumberContextLogsExecuteContext.php',
        'infobip\\api\\model\\nc\\logs\\NumberContextLog' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs/NumberContextLog.php',
        'infobip\\api\\model\\nc\\logs\\NumberContextLogsResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs/NumberContextLogsResponse.php',
        'infobip\\api\\model\\nc\\notify\\NumberContextRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify/NumberContextRequest.php',
        'infobip\\api\\model\\nc\\notify\\NumberContextResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify/NumberContextResponse.php',
        'infobip\\api\\model\\nc\\notify\\NumberContextResponseDetails' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify/NumberContextResponseDetails.php',
        'infobip\\api\\model\\nc\\query\\NumberContextRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/query/NumberContextRequest.php',
        'infobip\\api\\model\\nc\\query\\NumberContextResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/query/NumberContextResponse.php',
        'infobip\\api\\model\\nc\\query\\NumberContextResponseDetails' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/nc/query/NumberContextResponseDetails.php',
        'infobip\\api\\model\\sms\\mo\\logs\\GetReceivedSmsLogsExecuteContext' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs/GetReceivedSmsLogsExecuteContext.php',
        'infobip\\api\\model\\sms\\mo\\logs\\MOLog' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs/MOLog.php',
        'infobip\\api\\model\\sms\\mo\\logs\\MOLogsResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs/MOLogsResponse.php',
        'infobip\\api\\model\\sms\\mo\\reports\\GetReceivedMessagesExecuteContext' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports/GetReceivedMessagesExecuteContext.php',
        'infobip\\api\\model\\sms\\mo\\reports\\MOReport' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports/MOReport.php',
        'infobip\\api\\model\\sms\\mo\\reports\\MOReportResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports/MOReportResponse.php',
        'infobip\\api\\model\\sms\\mt\\logs\\GetSentSmsLogsExecuteContext' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs/GetSentSmsLogsExecuteContext.php',
        'infobip\\api\\model\\sms\\mt\\logs\\SMSLog' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs/SMSLog.php',
        'infobip\\api\\model\\sms\\mt\\logs\\SMSLogsResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs/SMSLogsResponse.php',
        'infobip\\api\\model\\sms\\mt\\reports\\GetSentSmsDeliveryReportsExecuteContext' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports/GetSentSmsDeliveryReportsExecuteContext.php',
        'infobip\\api\\model\\sms\\mt\\reports\\SMSReport' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports/SMSReport.php',
        'infobip\\api\\model\\sms\\mt\\reports\\SMSReportResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports/SMSReportResponse.php',
        'infobip\\api\\model\\sms\\mt\\send\\Language' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/Language.php',
        'infobip\\api\\model\\sms\\mt\\send\\Message' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/Message.php',
        'infobip\\api\\model\\sms\\mt\\send\\SMSResponse' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/SMSResponse.php',
        'infobip\\api\\model\\sms\\mt\\send\\SMSResponseDetails' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/SMSResponseDetails.php',
        'infobip\\api\\model\\sms\\mt\\send\\Tracking' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/Tracking.php',
        'infobip\\api\\model\\sms\\mt\\send\\binary\\BinaryContent' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/BinaryContent.php',
        'infobip\\api\\model\\sms\\mt\\send\\binary\\SMSAdvancedBinaryRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/SMSAdvancedBinaryRequest.php',
        'infobip\\api\\model\\sms\\mt\\send\\binary\\SMSBinaryRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/SMSBinaryRequest.php',
        'infobip\\api\\model\\sms\\mt\\send\\binary\\SMSMultiBinaryRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/SMSMultiBinaryRequest.php',
        'infobip\\api\\model\\sms\\mt\\send\\textual\\SMSAdvancedTextualRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual/SMSAdvancedTextualRequest.php',
        'infobip\\api\\model\\sms\\mt\\send\\textual\\SMSMultiTextualRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual/SMSMultiTextualRequest.php',
        'infobip\\api\\model\\sms\\mt\\send\\textual\\SMSTextualRequest' => __DIR__ . '/..' . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual/SMSTextualRequest.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitb060a29ae342cc8ca6e6289c6155a89e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitb060a29ae342cc8ca6e6289c6155a89e::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInitb060a29ae342cc8ca6e6289c6155a89e::$prefixesPsr0;
            $loader->fallbackDirsPsr0 = ComposerStaticInitb060a29ae342cc8ca6e6289c6155a89e::$fallbackDirsPsr0;
            $loader->classMap = ComposerStaticInitb060a29ae342cc8ca6e6289c6155a89e::$classMap;

        }, null, ClassLoader::class);
    }
}
