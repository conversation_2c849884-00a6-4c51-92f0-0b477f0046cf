<?php
// Test file to check if WSL ERP loads correctly
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Testing WSL ERP loading...\n";

try {
    require_once 'wsl-erp.php';
    echo "WSL ERP loaded successfully!\n";
    
    if (class_exists('WSL_ERP')) {
        echo "WSL_ERP class exists!\n";
    } else {
        echo "WSL_ERP class NOT found!\n";
    }
    
    if (class_exists('WeDevs_ERP')) {
        echo "WeDevs_ERP class exists!\n";
    } else {
        echo "WeDevs_ERP class NOT found!\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage() . "\n";
}
