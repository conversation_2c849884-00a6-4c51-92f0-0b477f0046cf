<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Clickatell\\Api\\ClickatellHttp' => $vendorDir . '/arcturial/clickatell/src/Api/ClickatellHttp.php',
    'Clickatell\\Api\\ClickatellHttpTest' => $vendorDir . '/arcturial/clickatell/test/Api/ClickatellHttpTest.php',
    'Clickatell\\Api\\ClickatellRest' => $vendorDir . '/arcturial/clickatell/src/Api/ClickatellRest.php',
    'Clickatell\\Callback' => $vendorDir . '/arcturial/clickatell/src/Callback.php',
    'Clickatell\\CallbackTest' => $vendorDir . '/arcturial/clickatell/test/CallbackTest.php',
    'Clickatell\\Clickatell' => $vendorDir . '/arcturial/clickatell/src/Clickatell.php',
    'Clickatell\\ClickatellEvent' => $vendorDir . '/arcturial/clickatell/src/ClickatellEvent.php',
    'Clickatell\\ClickatellOtpTest' => $vendorDir . '/arcturial/clickatell/test/Otp/ClickatellOtpTest.php',
    'Clickatell\\ClickatellTest' => $vendorDir . '/arcturial/clickatell/test/ClickatellTest.php',
    'Clickatell\\Decoder' => $vendorDir . '/arcturial/clickatell/src/Decoder.php',
    'Clickatell\\DecoderTest' => $vendorDir . '/arcturial/clickatell/test/DecoderTest.php',
    'Clickatell\\Diagnostic' => $vendorDir . '/arcturial/clickatell/src/Diagnostic.php',
    'Clickatell\\DiagnosticTest' => $vendorDir . '/arcturial/clickatell/test/DiagnosticTest.php',
    'Clickatell\\Event' => $vendorDir . '/arcturial/clickatell/src/Event.php',
    'Clickatell\\Otp\\ClickatellOtp' => $vendorDir . '/arcturial/clickatell/src/Otp/ClickatellOtp.php',
    'Clickatell\\Otp\\SessionStorage' => $vendorDir . '/arcturial/clickatell/src/Otp/SessionStorage.php',
    'Clickatell\\Otp\\StorageInterface' => $vendorDir . '/arcturial/clickatell/src/Otp/StorageInterface.php',
    'Clickatell\\Symfony\\ClickatellBundle' => $vendorDir . '/arcturial/clickatell/src/Symfony/ClickatellBundle.php',
    'Clickatell\\Symfony\\DependencyInjection\\ClickatellExtension' => $vendorDir . '/arcturial/clickatell/src/Symfony/DependencyInjection/ClickatellExtension.php',
    'Clickatell\\Symfony\\DependencyInjection\\Configuration' => $vendorDir . '/arcturial/clickatell/src/Symfony/DependencyInjection/Configuration.php',
    'Clickatell\\TransportInterface' => $vendorDir . '/arcturial/clickatell/src/TransportInterface.php',
    'Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin' => $vendorDir . '/dealerdirect/phpcodesniffer-composer-installer/src/Plugin.php',
    'JsonMapper' => $vendorDir . '/netresearch/jsonmapper/src/JsonMapper.php',
    'JsonMapper_Exception' => $vendorDir . '/netresearch/jsonmapper/src/JsonMapper/Exception.php',
    'Stripe\\Account' => $vendorDir . '/stripe/stripe-php/lib/Account.php',
    'Stripe\\AccountLink' => $vendorDir . '/stripe/stripe-php/lib/AccountLink.php',
    'Stripe\\AlipayAccount' => $vendorDir . '/stripe/stripe-php/lib/AlipayAccount.php',
    'Stripe\\ApiOperations\\All' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/All.php',
    'Stripe\\ApiOperations\\Create' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Create.php',
    'Stripe\\ApiOperations\\Delete' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Delete.php',
    'Stripe\\ApiOperations\\NestedResource' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/NestedResource.php',
    'Stripe\\ApiOperations\\Request' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Request.php',
    'Stripe\\ApiOperations\\Retrieve' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Retrieve.php',
    'Stripe\\ApiOperations\\Search' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Search.php',
    'Stripe\\ApiOperations\\Update' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Update.php',
    'Stripe\\ApiRequestor' => $vendorDir . '/stripe/stripe-php/lib/ApiRequestor.php',
    'Stripe\\ApiResource' => $vendorDir . '/stripe/stripe-php/lib/ApiResource.php',
    'Stripe\\ApiResponse' => $vendorDir . '/stripe/stripe-php/lib/ApiResponse.php',
    'Stripe\\ApplePayDomain' => $vendorDir . '/stripe/stripe-php/lib/ApplePayDomain.php',
    'Stripe\\ApplicationFee' => $vendorDir . '/stripe/stripe-php/lib/ApplicationFee.php',
    'Stripe\\ApplicationFeeRefund' => $vendorDir . '/stripe/stripe-php/lib/ApplicationFeeRefund.php',
    'Stripe\\Balance' => $vendorDir . '/stripe/stripe-php/lib/Balance.php',
    'Stripe\\BalanceTransaction' => $vendorDir . '/stripe/stripe-php/lib/BalanceTransaction.php',
    'Stripe\\BankAccount' => $vendorDir . '/stripe/stripe-php/lib/BankAccount.php',
    'Stripe\\BaseStripeClient' => $vendorDir . '/stripe/stripe-php/lib/BaseStripeClient.php',
    'Stripe\\BaseStripeClientInterface' => $vendorDir . '/stripe/stripe-php/lib/BaseStripeClientInterface.php',
    'Stripe\\BillingPortal\\Configuration' => $vendorDir . '/stripe/stripe-php/lib/BillingPortal/Configuration.php',
    'Stripe\\BillingPortal\\Session' => $vendorDir . '/stripe/stripe-php/lib/BillingPortal/Session.php',
    'Stripe\\BitcoinReceiver' => $vendorDir . '/stripe/stripe-php/lib/BitcoinReceiver.php',
    'Stripe\\BitcoinTransaction' => $vendorDir . '/stripe/stripe-php/lib/BitcoinTransaction.php',
    'Stripe\\Capability' => $vendorDir . '/stripe/stripe-php/lib/Capability.php',
    'Stripe\\Card' => $vendorDir . '/stripe/stripe-php/lib/Card.php',
    'Stripe\\CashBalance' => $vendorDir . '/stripe/stripe-php/lib/CashBalance.php',
    'Stripe\\Charge' => $vendorDir . '/stripe/stripe-php/lib/Charge.php',
    'Stripe\\Checkout\\Session' => $vendorDir . '/stripe/stripe-php/lib/Checkout/Session.php',
    'Stripe\\Collection' => $vendorDir . '/stripe/stripe-php/lib/Collection.php',
    'Stripe\\CountrySpec' => $vendorDir . '/stripe/stripe-php/lib/CountrySpec.php',
    'Stripe\\Coupon' => $vendorDir . '/stripe/stripe-php/lib/Coupon.php',
    'Stripe\\CreditNote' => $vendorDir . '/stripe/stripe-php/lib/CreditNote.php',
    'Stripe\\CreditNoteLineItem' => $vendorDir . '/stripe/stripe-php/lib/CreditNoteLineItem.php',
    'Stripe\\Customer' => $vendorDir . '/stripe/stripe-php/lib/Customer.php',
    'Stripe\\CustomerBalanceTransaction' => $vendorDir . '/stripe/stripe-php/lib/CustomerBalanceTransaction.php',
    'Stripe\\Discount' => $vendorDir . '/stripe/stripe-php/lib/Discount.php',
    'Stripe\\Dispute' => $vendorDir . '/stripe/stripe-php/lib/Dispute.php',
    'Stripe\\EphemeralKey' => $vendorDir . '/stripe/stripe-php/lib/EphemeralKey.php',
    'Stripe\\ErrorObject' => $vendorDir . '/stripe/stripe-php/lib/ErrorObject.php',
    'Stripe\\Event' => $vendorDir . '/stripe/stripe-php/lib/Event.php',
    'Stripe\\Exception\\ApiConnectionException' => $vendorDir . '/stripe/stripe-php/lib/Exception/ApiConnectionException.php',
    'Stripe\\Exception\\ApiErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/ApiErrorException.php',
    'Stripe\\Exception\\AuthenticationException' => $vendorDir . '/stripe/stripe-php/lib/Exception/AuthenticationException.php',
    'Stripe\\Exception\\BadMethodCallException' => $vendorDir . '/stripe/stripe-php/lib/Exception/BadMethodCallException.php',
    'Stripe\\Exception\\CardException' => $vendorDir . '/stripe/stripe-php/lib/Exception/CardException.php',
    'Stripe\\Exception\\ExceptionInterface' => $vendorDir . '/stripe/stripe-php/lib/Exception/ExceptionInterface.php',
    'Stripe\\Exception\\IdempotencyException' => $vendorDir . '/stripe/stripe-php/lib/Exception/IdempotencyException.php',
    'Stripe\\Exception\\InvalidArgumentException' => $vendorDir . '/stripe/stripe-php/lib/Exception/InvalidArgumentException.php',
    'Stripe\\Exception\\InvalidRequestException' => $vendorDir . '/stripe/stripe-php/lib/Exception/InvalidRequestException.php',
    'Stripe\\Exception\\OAuth\\ExceptionInterface' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/ExceptionInterface.php',
    'Stripe\\Exception\\OAuth\\InvalidClientException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidClientException.php',
    'Stripe\\Exception\\OAuth\\InvalidGrantException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidGrantException.php',
    'Stripe\\Exception\\OAuth\\InvalidRequestException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidRequestException.php',
    'Stripe\\Exception\\OAuth\\InvalidScopeException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidScopeException.php',
    'Stripe\\Exception\\OAuth\\OAuthErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/OAuthErrorException.php',
    'Stripe\\Exception\\OAuth\\UnknownOAuthErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/UnknownOAuthErrorException.php',
    'Stripe\\Exception\\OAuth\\UnsupportedGrantTypeException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/UnsupportedGrantTypeException.php',
    'Stripe\\Exception\\OAuth\\UnsupportedResponseTypeException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/UnsupportedResponseTypeException.php',
    'Stripe\\Exception\\PermissionException' => $vendorDir . '/stripe/stripe-php/lib/Exception/PermissionException.php',
    'Stripe\\Exception\\RateLimitException' => $vendorDir . '/stripe/stripe-php/lib/Exception/RateLimitException.php',
    'Stripe\\Exception\\SignatureVerificationException' => $vendorDir . '/stripe/stripe-php/lib/Exception/SignatureVerificationException.php',
    'Stripe\\Exception\\UnexpectedValueException' => $vendorDir . '/stripe/stripe-php/lib/Exception/UnexpectedValueException.php',
    'Stripe\\Exception\\UnknownApiErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/UnknownApiErrorException.php',
    'Stripe\\ExchangeRate' => $vendorDir . '/stripe/stripe-php/lib/ExchangeRate.php',
    'Stripe\\File' => $vendorDir . '/stripe/stripe-php/lib/File.php',
    'Stripe\\FileLink' => $vendorDir . '/stripe/stripe-php/lib/FileLink.php',
    'Stripe\\FinancialConnections\\Account' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/Account.php',
    'Stripe\\FinancialConnections\\AccountOwner' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/AccountOwner.php',
    'Stripe\\FinancialConnections\\AccountOwnership' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/AccountOwnership.php',
    'Stripe\\FinancialConnections\\Session' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/Session.php',
    'Stripe\\FundingInstructions' => $vendorDir . '/stripe/stripe-php/lib/FundingInstructions.php',
    'Stripe\\HttpClient\\ClientInterface' => $vendorDir . '/stripe/stripe-php/lib/HttpClient/ClientInterface.php',
    'Stripe\\HttpClient\\CurlClient' => $vendorDir . '/stripe/stripe-php/lib/HttpClient/CurlClient.php',
    'Stripe\\HttpClient\\StreamingClientInterface' => $vendorDir . '/stripe/stripe-php/lib/HttpClient/StreamingClientInterface.php',
    'Stripe\\Identity\\VerificationReport' => $vendorDir . '/stripe/stripe-php/lib/Identity/VerificationReport.php',
    'Stripe\\Identity\\VerificationSession' => $vendorDir . '/stripe/stripe-php/lib/Identity/VerificationSession.php',
    'Stripe\\Invoice' => $vendorDir . '/stripe/stripe-php/lib/Invoice.php',
    'Stripe\\InvoiceItem' => $vendorDir . '/stripe/stripe-php/lib/InvoiceItem.php',
    'Stripe\\InvoiceLineItem' => $vendorDir . '/stripe/stripe-php/lib/InvoiceLineItem.php',
    'Stripe\\Issuing\\Authorization' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Authorization.php',
    'Stripe\\Issuing\\Card' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Card.php',
    'Stripe\\Issuing\\CardDetails' => $vendorDir . '/stripe/stripe-php/lib/Issuing/CardDetails.php',
    'Stripe\\Issuing\\Cardholder' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Cardholder.php',
    'Stripe\\Issuing\\Dispute' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Dispute.php',
    'Stripe\\Issuing\\Transaction' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Transaction.php',
    'Stripe\\LineItem' => $vendorDir . '/stripe/stripe-php/lib/LineItem.php',
    'Stripe\\LoginLink' => $vendorDir . '/stripe/stripe-php/lib/LoginLink.php',
    'Stripe\\Mandate' => $vendorDir . '/stripe/stripe-php/lib/Mandate.php',
    'Stripe\\OAuth' => $vendorDir . '/stripe/stripe-php/lib/OAuth.php',
    'Stripe\\OAuthErrorObject' => $vendorDir . '/stripe/stripe-php/lib/OAuthErrorObject.php',
    'Stripe\\Order' => $vendorDir . '/stripe/stripe-php/lib/Order.php',
    'Stripe\\OrderItem' => $vendorDir . '/stripe/stripe-php/lib/OrderItem.php',
    'Stripe\\OrderReturn' => $vendorDir . '/stripe/stripe-php/lib/OrderReturn.php',
    'Stripe\\PaymentIntent' => $vendorDir . '/stripe/stripe-php/lib/PaymentIntent.php',
    'Stripe\\PaymentLink' => $vendorDir . '/stripe/stripe-php/lib/PaymentLink.php',
    'Stripe\\PaymentMethod' => $vendorDir . '/stripe/stripe-php/lib/PaymentMethod.php',
    'Stripe\\Payout' => $vendorDir . '/stripe/stripe-php/lib/Payout.php',
    'Stripe\\Person' => $vendorDir . '/stripe/stripe-php/lib/Person.php',
    'Stripe\\Plan' => $vendorDir . '/stripe/stripe-php/lib/Plan.php',
    'Stripe\\Price' => $vendorDir . '/stripe/stripe-php/lib/Price.php',
    'Stripe\\Product' => $vendorDir . '/stripe/stripe-php/lib/Product.php',
    'Stripe\\PromotionCode' => $vendorDir . '/stripe/stripe-php/lib/PromotionCode.php',
    'Stripe\\Quote' => $vendorDir . '/stripe/stripe-php/lib/Quote.php',
    'Stripe\\Radar\\EarlyFraudWarning' => $vendorDir . '/stripe/stripe-php/lib/Radar/EarlyFraudWarning.php',
    'Stripe\\Radar\\ValueList' => $vendorDir . '/stripe/stripe-php/lib/Radar/ValueList.php',
    'Stripe\\Radar\\ValueListItem' => $vendorDir . '/stripe/stripe-php/lib/Radar/ValueListItem.php',
    'Stripe\\Recipient' => $vendorDir . '/stripe/stripe-php/lib/Recipient.php',
    'Stripe\\RecipientTransfer' => $vendorDir . '/stripe/stripe-php/lib/RecipientTransfer.php',
    'Stripe\\Refund' => $vendorDir . '/stripe/stripe-php/lib/Refund.php',
    'Stripe\\Reporting\\ReportRun' => $vendorDir . '/stripe/stripe-php/lib/Reporting/ReportRun.php',
    'Stripe\\Reporting\\ReportType' => $vendorDir . '/stripe/stripe-php/lib/Reporting/ReportType.php',
    'Stripe\\RequestTelemetry' => $vendorDir . '/stripe/stripe-php/lib/RequestTelemetry.php',
    'Stripe\\Review' => $vendorDir . '/stripe/stripe-php/lib/Review.php',
    'Stripe\\SKU' => $vendorDir . '/stripe/stripe-php/lib/SKU.php',
    'Stripe\\SearchResult' => $vendorDir . '/stripe/stripe-php/lib/SearchResult.php',
    'Stripe\\Service\\AbstractService' => $vendorDir . '/stripe/stripe-php/lib/Service/AbstractService.php',
    'Stripe\\Service\\AbstractServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/AbstractServiceFactory.php',
    'Stripe\\Service\\AccountLinkService' => $vendorDir . '/stripe/stripe-php/lib/Service/AccountLinkService.php',
    'Stripe\\Service\\AccountService' => $vendorDir . '/stripe/stripe-php/lib/Service/AccountService.php',
    'Stripe\\Service\\ApplePayDomainService' => $vendorDir . '/stripe/stripe-php/lib/Service/ApplePayDomainService.php',
    'Stripe\\Service\\ApplicationFeeService' => $vendorDir . '/stripe/stripe-php/lib/Service/ApplicationFeeService.php',
    'Stripe\\Service\\BalanceService' => $vendorDir . '/stripe/stripe-php/lib/Service/BalanceService.php',
    'Stripe\\Service\\BalanceTransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/BalanceTransactionService.php',
    'Stripe\\Service\\BillingPortal\\BillingPortalServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/BillingPortal/BillingPortalServiceFactory.php',
    'Stripe\\Service\\BillingPortal\\ConfigurationService' => $vendorDir . '/stripe/stripe-php/lib/Service/BillingPortal/ConfigurationService.php',
    'Stripe\\Service\\BillingPortal\\SessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/BillingPortal/SessionService.php',
    'Stripe\\Service\\ChargeService' => $vendorDir . '/stripe/stripe-php/lib/Service/ChargeService.php',
    'Stripe\\Service\\Checkout\\CheckoutServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Checkout/CheckoutServiceFactory.php',
    'Stripe\\Service\\Checkout\\SessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Checkout/SessionService.php',
    'Stripe\\Service\\CoreServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/CoreServiceFactory.php',
    'Stripe\\Service\\CountrySpecService' => $vendorDir . '/stripe/stripe-php/lib/Service/CountrySpecService.php',
    'Stripe\\Service\\CouponService' => $vendorDir . '/stripe/stripe-php/lib/Service/CouponService.php',
    'Stripe\\Service\\CreditNoteService' => $vendorDir . '/stripe/stripe-php/lib/Service/CreditNoteService.php',
    'Stripe\\Service\\CustomerService' => $vendorDir . '/stripe/stripe-php/lib/Service/CustomerService.php',
    'Stripe\\Service\\DisputeService' => $vendorDir . '/stripe/stripe-php/lib/Service/DisputeService.php',
    'Stripe\\Service\\EphemeralKeyService' => $vendorDir . '/stripe/stripe-php/lib/Service/EphemeralKeyService.php',
    'Stripe\\Service\\EventService' => $vendorDir . '/stripe/stripe-php/lib/Service/EventService.php',
    'Stripe\\Service\\ExchangeRateService' => $vendorDir . '/stripe/stripe-php/lib/Service/ExchangeRateService.php',
    'Stripe\\Service\\FileLinkService' => $vendorDir . '/stripe/stripe-php/lib/Service/FileLinkService.php',
    'Stripe\\Service\\FileService' => $vendorDir . '/stripe/stripe-php/lib/Service/FileService.php',
    'Stripe\\Service\\FinancialConnections\\AccountService' => $vendorDir . '/stripe/stripe-php/lib/Service/FinancialConnections/AccountService.php',
    'Stripe\\Service\\FinancialConnections\\FinancialConnectionsServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/FinancialConnections/FinancialConnectionsServiceFactory.php',
    'Stripe\\Service\\FinancialConnections\\SessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/FinancialConnections/SessionService.php',
    'Stripe\\Service\\Identity\\IdentityServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Identity/IdentityServiceFactory.php',
    'Stripe\\Service\\Identity\\VerificationReportService' => $vendorDir . '/stripe/stripe-php/lib/Service/Identity/VerificationReportService.php',
    'Stripe\\Service\\Identity\\VerificationSessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Identity/VerificationSessionService.php',
    'Stripe\\Service\\InvoiceItemService' => $vendorDir . '/stripe/stripe-php/lib/Service/InvoiceItemService.php',
    'Stripe\\Service\\InvoiceService' => $vendorDir . '/stripe/stripe-php/lib/Service/InvoiceService.php',
    'Stripe\\Service\\Issuing\\AuthorizationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/AuthorizationService.php',
    'Stripe\\Service\\Issuing\\CardService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/CardService.php',
    'Stripe\\Service\\Issuing\\CardholderService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/CardholderService.php',
    'Stripe\\Service\\Issuing\\DisputeService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/DisputeService.php',
    'Stripe\\Service\\Issuing\\IssuingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/IssuingServiceFactory.php',
    'Stripe\\Service\\Issuing\\TransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/TransactionService.php',
    'Stripe\\Service\\MandateService' => $vendorDir . '/stripe/stripe-php/lib/Service/MandateService.php',
    'Stripe\\Service\\OAuthService' => $vendorDir . '/stripe/stripe-php/lib/Service/OAuthService.php',
    'Stripe\\Service\\OrderReturnService' => $vendorDir . '/stripe/stripe-php/lib/Service/OrderReturnService.php',
    'Stripe\\Service\\OrderService' => $vendorDir . '/stripe/stripe-php/lib/Service/OrderService.php',
    'Stripe\\Service\\PaymentIntentService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentIntentService.php',
    'Stripe\\Service\\PaymentLinkService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentLinkService.php',
    'Stripe\\Service\\PaymentMethodService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentMethodService.php',
    'Stripe\\Service\\PayoutService' => $vendorDir . '/stripe/stripe-php/lib/Service/PayoutService.php',
    'Stripe\\Service\\PlanService' => $vendorDir . '/stripe/stripe-php/lib/Service/PlanService.php',
    'Stripe\\Service\\PriceService' => $vendorDir . '/stripe/stripe-php/lib/Service/PriceService.php',
    'Stripe\\Service\\ProductService' => $vendorDir . '/stripe/stripe-php/lib/Service/ProductService.php',
    'Stripe\\Service\\PromotionCodeService' => $vendorDir . '/stripe/stripe-php/lib/Service/PromotionCodeService.php',
    'Stripe\\Service\\QuoteService' => $vendorDir . '/stripe/stripe-php/lib/Service/QuoteService.php',
    'Stripe\\Service\\Radar\\EarlyFraudWarningService' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/EarlyFraudWarningService.php',
    'Stripe\\Service\\Radar\\RadarServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/RadarServiceFactory.php',
    'Stripe\\Service\\Radar\\ValueListItemService' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/ValueListItemService.php',
    'Stripe\\Service\\Radar\\ValueListService' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/ValueListService.php',
    'Stripe\\Service\\RefundService' => $vendorDir . '/stripe/stripe-php/lib/Service/RefundService.php',
    'Stripe\\Service\\Reporting\\ReportRunService' => $vendorDir . '/stripe/stripe-php/lib/Service/Reporting/ReportRunService.php',
    'Stripe\\Service\\Reporting\\ReportTypeService' => $vendorDir . '/stripe/stripe-php/lib/Service/Reporting/ReportTypeService.php',
    'Stripe\\Service\\Reporting\\ReportingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Reporting/ReportingServiceFactory.php',
    'Stripe\\Service\\ReviewService' => $vendorDir . '/stripe/stripe-php/lib/Service/ReviewService.php',
    'Stripe\\Service\\SetupAttemptService' => $vendorDir . '/stripe/stripe-php/lib/Service/SetupAttemptService.php',
    'Stripe\\Service\\SetupIntentService' => $vendorDir . '/stripe/stripe-php/lib/Service/SetupIntentService.php',
    'Stripe\\Service\\ShippingRateService' => $vendorDir . '/stripe/stripe-php/lib/Service/ShippingRateService.php',
    'Stripe\\Service\\Sigma\\ScheduledQueryRunService' => $vendorDir . '/stripe/stripe-php/lib/Service/Sigma/ScheduledQueryRunService.php',
    'Stripe\\Service\\Sigma\\SigmaServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Sigma/SigmaServiceFactory.php',
    'Stripe\\Service\\SkuService' => $vendorDir . '/stripe/stripe-php/lib/Service/SkuService.php',
    'Stripe\\Service\\SourceService' => $vendorDir . '/stripe/stripe-php/lib/Service/SourceService.php',
    'Stripe\\Service\\SubscriptionItemService' => $vendorDir . '/stripe/stripe-php/lib/Service/SubscriptionItemService.php',
    'Stripe\\Service\\SubscriptionScheduleService' => $vendorDir . '/stripe/stripe-php/lib/Service/SubscriptionScheduleService.php',
    'Stripe\\Service\\SubscriptionService' => $vendorDir . '/stripe/stripe-php/lib/Service/SubscriptionService.php',
    'Stripe\\Service\\TaxCodeService' => $vendorDir . '/stripe/stripe-php/lib/Service/TaxCodeService.php',
    'Stripe\\Service\\TaxRateService' => $vendorDir . '/stripe/stripe-php/lib/Service/TaxRateService.php',
    'Stripe\\Service\\Terminal\\ConfigurationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/ConfigurationService.php',
    'Stripe\\Service\\Terminal\\ConnectionTokenService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/ConnectionTokenService.php',
    'Stripe\\Service\\Terminal\\LocationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/LocationService.php',
    'Stripe\\Service\\Terminal\\ReaderService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/ReaderService.php',
    'Stripe\\Service\\Terminal\\TerminalServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/TerminalServiceFactory.php',
    'Stripe\\Service\\TestHelpers\\RefundService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/RefundService.php',
    'Stripe\\Service\\TestHelpers\\Terminal\\ReaderService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Terminal/ReaderService.php',
    'Stripe\\Service\\TestHelpers\\Terminal\\TerminalServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Terminal/TerminalServiceFactory.php',
    'Stripe\\Service\\TestHelpers\\TestClockService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/TestClockService.php',
    'Stripe\\Service\\TestHelpers\\TestHelpersServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/TestHelpersServiceFactory.php',
    'Stripe\\Service\\TokenService' => $vendorDir . '/stripe/stripe-php/lib/Service/TokenService.php',
    'Stripe\\Service\\TopupService' => $vendorDir . '/stripe/stripe-php/lib/Service/TopupService.php',
    'Stripe\\Service\\TransferService' => $vendorDir . '/stripe/stripe-php/lib/Service/TransferService.php',
    'Stripe\\Service\\WebhookEndpointService' => $vendorDir . '/stripe/stripe-php/lib/Service/WebhookEndpointService.php',
    'Stripe\\SetupAttempt' => $vendorDir . '/stripe/stripe-php/lib/SetupAttempt.php',
    'Stripe\\SetupIntent' => $vendorDir . '/stripe/stripe-php/lib/SetupIntent.php',
    'Stripe\\ShippingRate' => $vendorDir . '/stripe/stripe-php/lib/ShippingRate.php',
    'Stripe\\Sigma\\ScheduledQueryRun' => $vendorDir . '/stripe/stripe-php/lib/Sigma/ScheduledQueryRun.php',
    'Stripe\\SingletonApiResource' => $vendorDir . '/stripe/stripe-php/lib/SingletonApiResource.php',
    'Stripe\\Source' => $vendorDir . '/stripe/stripe-php/lib/Source.php',
    'Stripe\\SourceTransaction' => $vendorDir . '/stripe/stripe-php/lib/SourceTransaction.php',
    'Stripe\\Stripe' => $vendorDir . '/stripe/stripe-php/lib/Stripe.php',
    'Stripe\\StripeClient' => $vendorDir . '/stripe/stripe-php/lib/StripeClient.php',
    'Stripe\\StripeClientInterface' => $vendorDir . '/stripe/stripe-php/lib/StripeClientInterface.php',
    'Stripe\\StripeObject' => $vendorDir . '/stripe/stripe-php/lib/StripeObject.php',
    'Stripe\\StripeStreamingClientInterface' => $vendorDir . '/stripe/stripe-php/lib/StripeStreamingClientInterface.php',
    'Stripe\\Subscription' => $vendorDir . '/stripe/stripe-php/lib/Subscription.php',
    'Stripe\\SubscriptionItem' => $vendorDir . '/stripe/stripe-php/lib/SubscriptionItem.php',
    'Stripe\\SubscriptionSchedule' => $vendorDir . '/stripe/stripe-php/lib/SubscriptionSchedule.php',
    'Stripe\\TaxCode' => $vendorDir . '/stripe/stripe-php/lib/TaxCode.php',
    'Stripe\\TaxId' => $vendorDir . '/stripe/stripe-php/lib/TaxId.php',
    'Stripe\\TaxRate' => $vendorDir . '/stripe/stripe-php/lib/TaxRate.php',
    'Stripe\\Terminal\\Configuration' => $vendorDir . '/stripe/stripe-php/lib/Terminal/Configuration.php',
    'Stripe\\Terminal\\ConnectionToken' => $vendorDir . '/stripe/stripe-php/lib/Terminal/ConnectionToken.php',
    'Stripe\\Terminal\\Location' => $vendorDir . '/stripe/stripe-php/lib/Terminal/Location.php',
    'Stripe\\Terminal\\Reader' => $vendorDir . '/stripe/stripe-php/lib/Terminal/Reader.php',
    'Stripe\\TestHelpers\\TestClock' => $vendorDir . '/stripe/stripe-php/lib/TestHelpers/TestClock.php',
    'Stripe\\ThreeDSecure' => $vendorDir . '/stripe/stripe-php/lib/ThreeDSecure.php',
    'Stripe\\Token' => $vendorDir . '/stripe/stripe-php/lib/Token.php',
    'Stripe\\Topup' => $vendorDir . '/stripe/stripe-php/lib/Topup.php',
    'Stripe\\Transfer' => $vendorDir . '/stripe/stripe-php/lib/Transfer.php',
    'Stripe\\TransferReversal' => $vendorDir . '/stripe/stripe-php/lib/TransferReversal.php',
    'Stripe\\UsageRecord' => $vendorDir . '/stripe/stripe-php/lib/UsageRecord.php',
    'Stripe\\UsageRecordSummary' => $vendorDir . '/stripe/stripe-php/lib/UsageRecordSummary.php',
    'Stripe\\Util\\CaseInsensitiveArray' => $vendorDir . '/stripe/stripe-php/lib/Util/CaseInsensitiveArray.php',
    'Stripe\\Util\\DefaultLogger' => $vendorDir . '/stripe/stripe-php/lib/Util/DefaultLogger.php',
    'Stripe\\Util\\LoggerInterface' => $vendorDir . '/stripe/stripe-php/lib/Util/LoggerInterface.php',
    'Stripe\\Util\\ObjectTypes' => $vendorDir . '/stripe/stripe-php/lib/Util/ObjectTypes.php',
    'Stripe\\Util\\RandomGenerator' => $vendorDir . '/stripe/stripe-php/lib/Util/RandomGenerator.php',
    'Stripe\\Util\\RequestOptions' => $vendorDir . '/stripe/stripe-php/lib/Util/RequestOptions.php',
    'Stripe\\Util\\Set' => $vendorDir . '/stripe/stripe-php/lib/Util/Set.php',
    'Stripe\\Util\\Util' => $vendorDir . '/stripe/stripe-php/lib/Util/Util.php',
    'Stripe\\Webhook' => $vendorDir . '/stripe/stripe-php/lib/Webhook.php',
    'Stripe\\WebhookEndpoint' => $vendorDir . '/stripe/stripe-php/lib/WebhookEndpoint.php',
    'Stripe\\WebhookSignature' => $vendorDir . '/stripe/stripe-php/lib/WebhookSignature.php',
    'Twilio\\Deserialize' => $vendorDir . '/twilio/sdk/src/Twilio/Deserialize.php',
    'Twilio\\Domain' => $vendorDir . '/twilio/sdk/src/Twilio/Domain.php',
    'Twilio\\Exceptions\\ConfigurationException' => $vendorDir . '/twilio/sdk/src/Twilio/Exceptions/ConfigurationException.php',
    'Twilio\\Exceptions\\DeserializeException' => $vendorDir . '/twilio/sdk/src/Twilio/Exceptions/DeserializeException.php',
    'Twilio\\Exceptions\\EnvironmentException' => $vendorDir . '/twilio/sdk/src/Twilio/Exceptions/EnvironmentException.php',
    'Twilio\\Exceptions\\HttpException' => $vendorDir . '/twilio/sdk/src/Twilio/Exceptions/HttpException.php',
    'Twilio\\Exceptions\\RestException' => $vendorDir . '/twilio/sdk/src/Twilio/Exceptions/RestException.php',
    'Twilio\\Exceptions\\TwilioException' => $vendorDir . '/twilio/sdk/src/Twilio/Exceptions/TwilioException.php',
    'Twilio\\Exceptions\\TwimlException' => $vendorDir . '/twilio/sdk/src/Twilio/Exceptions/TwimlException.php',
    'Twilio\\Http\\Client' => $vendorDir . '/twilio/sdk/src/Twilio/Http/Client.php',
    'Twilio\\Http\\CurlClient' => $vendorDir . '/twilio/sdk/src/Twilio/Http/CurlClient.php',
    'Twilio\\Http\\GuzzleClient' => $vendorDir . '/twilio/sdk/src/Twilio/Http/GuzzleClient.php',
    'Twilio\\Http\\Response' => $vendorDir . '/twilio/sdk/src/Twilio/Http/Response.php',
    'Twilio\\InstanceContext' => $vendorDir . '/twilio/sdk/src/Twilio/InstanceContext.php',
    'Twilio\\InstanceResource' => $vendorDir . '/twilio/sdk/src/Twilio/InstanceResource.php',
    'Twilio\\Jwt\\AccessToken' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/AccessToken.php',
    'Twilio\\Jwt\\ClientToken' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/ClientToken.php',
    'Twilio\\Jwt\\Client\\ScopeURI' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/Client/ScopeURI.php',
    'Twilio\\Jwt\\Grants\\ChatGrant' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/Grants/ChatGrant.php',
    'Twilio\\Jwt\\Grants\\Grant' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/Grants/Grant.php',
    'Twilio\\Jwt\\Grants\\SyncGrant' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/Grants/SyncGrant.php',
    'Twilio\\Jwt\\Grants\\TaskRouterGrant' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/Grants/TaskRouterGrant.php',
    'Twilio\\Jwt\\Grants\\VideoGrant' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/Grants/VideoGrant.php',
    'Twilio\\Jwt\\Grants\\VoiceGrant' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/Grants/VoiceGrant.php',
    'Twilio\\Jwt\\JWT' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/JWT.php',
    'Twilio\\Jwt\\TaskRouter\\CapabilityToken' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/CapabilityToken.php',
    'Twilio\\Jwt\\TaskRouter\\Policy' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/Policy.php',
    'Twilio\\Jwt\\TaskRouter\\TaskQueueCapability' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/TaskQueueCapability.php',
    'Twilio\\Jwt\\TaskRouter\\WorkerCapability' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/WorkerCapability.php',
    'Twilio\\Jwt\\TaskRouter\\WorkspaceCapability' => $vendorDir . '/twilio/sdk/src/Twilio/Jwt/TaskRouter/WorkspaceCapability.php',
    'Twilio\\ListResource' => $vendorDir . '/twilio/sdk/src/Twilio/ListResource.php',
    'Twilio\\Options' => $vendorDir . '/twilio/sdk/src/Twilio/Options.php',
    'Twilio\\Page' => $vendorDir . '/twilio/sdk/src/Twilio/Page.php',
    'Twilio\\Rest\\Accounts' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts.php',
    'Twilio\\Rest\\Accounts\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1.php',
    'Twilio\\Rest\\Accounts\\V1\\CredentialInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/CredentialInstance.php',
    'Twilio\\Rest\\Accounts\\V1\\CredentialList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/CredentialList.php',
    'Twilio\\Rest\\Accounts\\V1\\CredentialPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/CredentialPage.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsContext.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsInstance.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsList.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsOptions.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\AwsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsPage.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\CreateAwsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsOptions.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\CreatePublicKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyOptions.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyContext.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyInstance.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyList.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyOptions.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\PublicKeyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyPage.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\UpdateAwsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/AwsOptions.php',
    'Twilio\\Rest\\Accounts\\V1\\Credential\\UpdatePublicKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Accounts/V1/Credential/PublicKeyOptions.php',
    'Twilio\\Rest\\Api' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api.php',
    'Twilio\\Rest\\Api\\V2010' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010.php',
    'Twilio\\Rest\\Api\\V2010\\AccountContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountContext.php',
    'Twilio\\Rest\\Api\\V2010\\AccountInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountInstance.php',
    'Twilio\\Rest\\Api\\V2010\\AccountList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountList.php',
    'Twilio\\Rest\\Api\\V2010\\AccountOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
    'Twilio\\Rest\\Api\\V2010\\AccountPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AddressContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AddressInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AddressList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AddressOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AddressPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Address\\DependentPhoneNumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Address/DependentPhoneNumberInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Address\\DependentPhoneNumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Address/DependentPhoneNumberList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Address\\DependentPhoneNumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Address/DependentPhoneNumberPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ApplicationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AuthorizedConnectAppPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AuthorizedConnectAppPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountryPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\LocalPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachineInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachineList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachineOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MachineToMachinePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachinePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobileInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobileList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobileOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\MobilePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobilePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\NationalPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadLocalOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/LocalOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadMachineToMachineOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MachineToMachineOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadMobileOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/MobileOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadNationalOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/NationalOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadSharedCostOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadTollFreeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\ReadVoipOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\SharedCostPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/SharedCostPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\TollFreePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/TollFreePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\AvailablePhoneNumberCountry\\VoipPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AvailablePhoneNumberCountry/VoipPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\BalanceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/BalanceInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\BalanceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/BalanceList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\BalancePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/BalancePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CallContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CallInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CallList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CallPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreateFeedbackOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreateFeedbackSummaryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreatePaymentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\CreateRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\FeedbackSummaryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackSummaryPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\NotificationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\PaymentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\ReadNotificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/NotificationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\ReadRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\RecordingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\UpdateFeedbackOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/FeedbackOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\UpdatePaymentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/PaymentOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Call\\UpdateRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Call/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConferenceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConferencePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferencePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\CreateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ParticipantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ReadParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\ReadRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\RecordingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\UpdateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/ParticipantOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Conference\\UpdateRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Conference/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ConnectAppPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateAddressOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateApplicationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateCallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateIncomingPhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateNewKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateNewSigningKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateQueueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateTokenOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\CreateValidationRequestOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOnPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOnPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\AssignedAddOn\\AssignedAddOnExtensionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/AssignedAddOn/AssignedAddOnExtensionPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\CreateLocalOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\CreateMobileOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\CreateTollFreeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\LocalPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobileInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobileList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobileOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\MobilePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobilePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\ReadLocalOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/LocalOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\ReadMobileOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/MobileOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\ReadTollFreeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\IncomingPhoneNumber\\TollFreePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumber/TollFreePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\KeyContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\KeyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\KeyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\KeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\KeyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\MessageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\MessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\MessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\MessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\MessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessagePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\CreateFeedbackOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\FeedbackPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/FeedbackPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\MediaPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Message\\ReadMediaOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Message/MediaOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewKeyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewKeyPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NewSigningKeyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NewSigningKeyPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NotificationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NotificationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NotificationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NotificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\NotificationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\OutgoingCallerIdPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\QueueContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\QueueInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\QueueList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\QueueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\QueuePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueuePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\MemberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Queue\\UpdateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Queue/MemberOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadAddressOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadApplicationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadCallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadConferenceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadIncomingPhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/MessageOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadNotificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/NotificationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadOutgoingCallerIdOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ReadShortCodeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\RecordingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\RecordingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\RecordingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\RecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\RecordingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/RecordingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResultPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResultPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\AddOnResult\\PayloadPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/AddOnResult/PayloadPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Recording\\TranscriptionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Recording/TranscriptionPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ShortCodePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SigningKeyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SipInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SipInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SipList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SipList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\SipPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SipPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CreateDomainOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialListPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialListPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\CredentialPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\CredentialList\\UpdateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/CredentialList/CredentialOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\DomainPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypesInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypesInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypesList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypesList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypesPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypesPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCallsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCallsInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCallsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCallsList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCallsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCallsPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsCredentialListMappingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsCredentialListMappingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeCalls\\AuthCallsIpAccessControlListMappingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeCalls/AuthCallsIpAccessControlListMappingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrationsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrationsInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrationsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrationsList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrationsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrationsPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\AuthTypes\\AuthTypeRegistrations\\AuthRegistrationsCredentialListMappingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/AuthTypes/AuthTypeRegistrations/AuthRegistrationsCredentialListMappingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\CredentialListMappingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/CredentialListMappingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\Domain\\IpAccessControlListMappingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/Domain/IpAccessControlListMappingPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlListPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlListPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\CreateIpAddressOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\IpAddressPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\IpAccessControlList\\UpdateIpAddressOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/IpAccessControlList/IpAddressOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Sip\\UpdateDomainOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Sip/DomainOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TokenInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TokenList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TokenOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TokenPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TokenPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\TranscriptionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/TranscriptionPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateAddressOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/AddressOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateApplicationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ApplicationOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateCallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/CallOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateConferenceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConferenceOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateConnectAppOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ConnectAppOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateIncomingPhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/IncomingPhoneNumberOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/KeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateOutgoingCallerIdOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/OutgoingCallerIdOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateQueueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/QueueOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateShortCodeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ShortCodeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UpdateSigningKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/SigningKeyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UsageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/UsageInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UsageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/UsageList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\UsagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/UsagePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\CreateTriggerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\ReadRecordOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\ReadTriggerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\RecordPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/RecordPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\AllTimePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimePage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\DailyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\LastMonthPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\MonthlyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadAllTimeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/AllTimeOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadDailyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/DailyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadLastMonthOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/LastMonthOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadMonthlyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/MonthlyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadThisMonthOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadTodayOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadYearlyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ReadYesterdayOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\ThisMonthPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/ThisMonthPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\TodayPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/TodayPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YearlyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YearlyPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\Record\\YesterdayPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/Record/YesterdayPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerContext.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\TriggerPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerPage.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\Usage\\UpdateTriggerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/Usage/TriggerOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestInstance.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestList.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestOptions.php',
    'Twilio\\Rest\\Api\\V2010\\Account\\ValidationRequestPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/Account/ValidationRequestPage.php',
    'Twilio\\Rest\\Api\\V2010\\CreateAccountOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
    'Twilio\\Rest\\Api\\V2010\\ReadAccountOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
    'Twilio\\Rest\\Api\\V2010\\UpdateAccountOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Api/V2010/AccountOptions.php',
    'Twilio\\Rest\\Authy' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy.php',
    'Twilio\\Rest\\Authy\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1.php',
    'Twilio\\Rest\\Authy\\V1\\FormContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormContext.php',
    'Twilio\\Rest\\Authy\\V1\\FormInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormInstance.php',
    'Twilio\\Rest\\Authy\\V1\\FormList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormList.php',
    'Twilio\\Rest\\Authy\\V1\\FormPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/FormPage.php',
    'Twilio\\Rest\\Authy\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceContext.php',
    'Twilio\\Rest\\Authy\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceInstance.php',
    'Twilio\\Rest\\Authy\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceList.php',
    'Twilio\\Rest\\Authy\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceOptions.php',
    'Twilio\\Rest\\Authy\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServicePage.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\EntityContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityContext.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\EntityInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityInstance.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\EntityList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityList.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\EntityPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/EntityPage.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorContext.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorInstance.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorList.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorOptions.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\FactorPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorPage.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeContext.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeInstance.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeList.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeOptions.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\ChallengePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengePage.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\CreateChallengeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeOptions.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\Factor\\UpdateChallengeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/Factor/ChallengeOptions.php',
    'Twilio\\Rest\\Authy\\V1\\Service\\Entity\\UpdateFactorOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/Service/Entity/FactorOptions.php',
    'Twilio\\Rest\\Authy\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Authy/V1/ServiceOptions.php',
    'Twilio\\Rest\\Autopilot' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot.php',
    'Twilio\\Rest\\Autopilot\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1.php',
    'Twilio\\Rest\\Autopilot\\V1\\AssistantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\AssistantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\AssistantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantList.php',
    'Twilio\\Rest\\Autopilot\\V1\\AssistantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\AssistantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateFieldTypeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateModelBuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateQueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateTaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\CreateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DefaultsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialogueContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialogueContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialogueInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialogueInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialogueList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialogueList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\DialoguePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DialoguePage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ExportAssistantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ExportAssistantPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldTypePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypePage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\CreateFieldValueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\FieldValuePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValuePage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\FieldType\\ReadFieldValueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldType/FieldValueOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ModelBuildPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\QueryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\ReadQueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\StyleSheetPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\TaskPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\CreateSampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\FieldPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/FieldPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\ReadSampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\SamplePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SamplePage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskActionsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\TaskStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskStatisticsPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\UpdateSampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\Task\\UpdateTaskActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/Task/TaskActionsOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateDefaultsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/DefaultsOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateFieldTypeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/FieldTypeOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateModelBuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/ModelBuildOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateQueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateStyleSheetOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/StyleSheetOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateTaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/TaskOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\UpdateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookContext.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookInstance.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookList.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\Assistant\\WebhookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/Assistant/WebhookPage.php',
    'Twilio\\Rest\\Autopilot\\V1\\CreateAssistantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantOptions.php',
    'Twilio\\Rest\\Autopilot\\V1\\UpdateAssistantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Autopilot/V1/AssistantOptions.php',
    'Twilio\\Rest\\Chat' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat.php',
    'Twilio\\Rest\\Chat\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1.php',
    'Twilio\\Rest\\Chat\\V1\\CreateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialOptions.php',
    'Twilio\\Rest\\Chat\\V1\\CredentialContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialContext.php',
    'Twilio\\Rest\\Chat\\V1\\CredentialInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialInstance.php',
    'Twilio\\Rest\\Chat\\V1\\CredentialList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialList.php',
    'Twilio\\Rest\\Chat\\V1\\CredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialOptions.php',
    'Twilio\\Rest\\Chat\\V1\\CredentialPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialPage.php',
    'Twilio\\Rest\\Chat\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceContext.php',
    'Twilio\\Rest\\Chat\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceInstance.php',
    'Twilio\\Rest\\Chat\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceList.php',
    'Twilio\\Rest\\Chat\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceOptions.php',
    'Twilio\\Rest\\Chat\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServicePage.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\ChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelContext.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\ChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelInstance.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\ChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelList.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\ChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\ChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelPage.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\CreateInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\CreateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\CreateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteContext.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteInstance.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteList.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\InvitePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InvitePage.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberContext.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberInstance.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberList.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MemberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberPage.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageContext.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageInstance.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageList.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\MessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessagePage.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\ReadInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\ReadMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\ReadMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\UpdateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\Channel\\UpdateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\CreateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\CreateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\ReadChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\RoleContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RoleContext.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\RoleInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RoleInstance.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\RoleList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RoleList.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\RolePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/RolePage.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\UpdateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\UpdateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\UserContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserContext.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\UserInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserInstance.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\UserList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserList.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\UserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserOptions.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\UserPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/UserPage.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\User\\UserChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/User/UserChannelInstance.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\User\\UserChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/User/UserChannelList.php',
    'Twilio\\Rest\\Chat\\V1\\Service\\User\\UserChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/Service/User/UserChannelPage.php',
    'Twilio\\Rest\\Chat\\V1\\UpdateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialOptions.php',
    'Twilio\\Rest\\Chat\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V1/ServiceOptions.php',
    'Twilio\\Rest\\Chat\\V2' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2.php',
    'Twilio\\Rest\\Chat\\V2\\CreateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialOptions.php',
    'Twilio\\Rest\\Chat\\V2\\CredentialContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialContext.php',
    'Twilio\\Rest\\Chat\\V2\\CredentialInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialInstance.php',
    'Twilio\\Rest\\Chat\\V2\\CredentialList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialList.php',
    'Twilio\\Rest\\Chat\\V2\\CredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialOptions.php',
    'Twilio\\Rest\\Chat\\V2\\CredentialPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialPage.php',
    'Twilio\\Rest\\Chat\\V2\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceContext.php',
    'Twilio\\Rest\\Chat\\V2\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceInstance.php',
    'Twilio\\Rest\\Chat\\V2\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceList.php',
    'Twilio\\Rest\\Chat\\V2\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceOptions.php',
    'Twilio\\Rest\\Chat\\V2\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServicePage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\BindingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\BindingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\BindingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\BindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\BindingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingPage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\ChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\ChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\ChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\ChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\ChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelPage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\CreateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\InvitePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InvitePage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MemberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberPage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\MessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessagePage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\ReadInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\ReadMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\ReadMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\UpdateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\UpdateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\UpdateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\Channel\\WebhookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/Channel/WebhookPage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\CreateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\CreateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\ReadBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/BindingOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\ReadChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\RoleContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RoleContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\RoleInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RoleInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\RoleList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RoleList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\RolePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/RolePage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\UpdateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\UpdateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\UserContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\UserInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\UserList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\UserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\UserPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/UserPage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\ReadUserBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UpdateUserChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserBindingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserBindingPage.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelContext.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelInstance.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelList.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelOptions.php',
    'Twilio\\Rest\\Chat\\V2\\Service\\User\\UserChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/Service/User/UserChannelPage.php',
    'Twilio\\Rest\\Chat\\V2\\UpdateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/CredentialOptions.php',
    'Twilio\\Rest\\Chat\\V2\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Chat/V2/ServiceOptions.php',
    'Twilio\\Rest\\Client' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Client.php',
    'Twilio\\Rest\\Conversations' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations.php',
    'Twilio\\Rest\\Conversations\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1.php',
    'Twilio\\Rest\\Conversations\\V1\\ConversationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationContext.php',
    'Twilio\\Rest\\Conversations\\V1\\ConversationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationInstance.php',
    'Twilio\\Rest\\Conversations\\V1\\ConversationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationList.php',
    'Twilio\\Rest\\Conversations\\V1\\ConversationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\ConversationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationPage.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\CreateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\CreateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\CreateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageContext.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageInstance.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageList.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\MessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessagePage.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantContext.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantInstance.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantList.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\ParticipantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantPage.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\UpdateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/MessageOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\UpdateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/ParticipantOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\UpdateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookContext.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookInstance.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookList.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\Conversation\\WebhookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/Conversation/WebhookPage.php',
    'Twilio\\Rest\\Conversations\\V1\\CreateConversationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\UpdateConversationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/ConversationOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\UpdateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\WebhookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookContext.php',
    'Twilio\\Rest\\Conversations\\V1\\WebhookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookInstance.php',
    'Twilio\\Rest\\Conversations\\V1\\WebhookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookList.php',
    'Twilio\\Rest\\Conversations\\V1\\WebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookOptions.php',
    'Twilio\\Rest\\Conversations\\V1\\WebhookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Conversations/V1/WebhookPage.php',
    'Twilio\\Rest\\Fax' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax.php',
    'Twilio\\Rest\\Fax\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1.php',
    'Twilio\\Rest\\Fax\\V1\\CreateFaxOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
    'Twilio\\Rest\\Fax\\V1\\FaxContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxContext.php',
    'Twilio\\Rest\\Fax\\V1\\FaxInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxInstance.php',
    'Twilio\\Rest\\Fax\\V1\\FaxList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxList.php',
    'Twilio\\Rest\\Fax\\V1\\FaxOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
    'Twilio\\Rest\\Fax\\V1\\FaxPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxPage.php',
    'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaContext.php',
    'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaInstance.php',
    'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaList.php',
    'Twilio\\Rest\\Fax\\V1\\Fax\\FaxMediaPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/Fax/FaxMediaPage.php',
    'Twilio\\Rest\\Fax\\V1\\ReadFaxOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
    'Twilio\\Rest\\Fax\\V1\\UpdateFaxOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Fax/V1/FaxOptions.php',
    'Twilio\\Rest\\FlexApi' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi.php',
    'Twilio\\Rest\\FlexApi\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1.php',
    'Twilio\\Rest\\FlexApi\\V1\\ChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelContext.php',
    'Twilio\\Rest\\FlexApi\\V1\\ChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelInstance.php',
    'Twilio\\Rest\\FlexApi\\V1\\ChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelList.php',
    'Twilio\\Rest\\FlexApi\\V1\\ChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\ChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelPage.php',
    'Twilio\\Rest\\FlexApi\\V1\\ConfigurationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationContext.php',
    'Twilio\\Rest\\FlexApi\\V1\\ConfigurationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationInstance.php',
    'Twilio\\Rest\\FlexApi\\V1\\ConfigurationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationList.php',
    'Twilio\\Rest\\FlexApi\\V1\\ConfigurationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\ConfigurationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationPage.php',
    'Twilio\\Rest\\FlexApi\\V1\\CreateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ChannelOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\CreateFlexFlowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\CreateWebChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\FetchConfigurationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/ConfigurationOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\FlexFlowContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowContext.php',
    'Twilio\\Rest\\FlexApi\\V1\\FlexFlowInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowInstance.php',
    'Twilio\\Rest\\FlexApi\\V1\\FlexFlowList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowList.php',
    'Twilio\\Rest\\FlexApi\\V1\\FlexFlowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\FlexFlowPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowPage.php',
    'Twilio\\Rest\\FlexApi\\V1\\ReadFlexFlowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\UpdateFlexFlowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/FlexFlowOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\UpdateWebChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\WebChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelContext.php',
    'Twilio\\Rest\\FlexApi\\V1\\WebChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelInstance.php',
    'Twilio\\Rest\\FlexApi\\V1\\WebChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelList.php',
    'Twilio\\Rest\\FlexApi\\V1\\WebChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelOptions.php',
    'Twilio\\Rest\\FlexApi\\V1\\WebChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/FlexApi/V1/WebChannelPage.php',
    'Twilio\\Rest\\Insights' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights.php',
    'Twilio\\Rest\\Insights\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1.php',
    'Twilio\\Rest\\Insights\\V1\\CallContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallContext.php',
    'Twilio\\Rest\\Insights\\V1\\CallInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallInstance.php',
    'Twilio\\Rest\\Insights\\V1\\CallList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallList.php',
    'Twilio\\Rest\\Insights\\V1\\CallPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/CallPage.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryContext.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryInstance.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryList.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryOptions.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\CallSummaryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryPage.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\EventInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventInstance.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\EventList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventList.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\EventOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventOptions.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\EventPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventPage.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\FetchCallSummaryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/CallSummaryOptions.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\MetricInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricInstance.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\MetricList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricList.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\MetricOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricOptions.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\MetricPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricPage.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\ReadEventOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/EventOptions.php',
    'Twilio\\Rest\\Insights\\V1\\Call\\ReadMetricOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Insights/V1/Call/MetricOptions.php',
    'Twilio\\Rest\\IpMessaging' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging.php',
    'Twilio\\Rest\\IpMessaging\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1.php',
    'Twilio\\Rest\\IpMessaging\\V1\\CreateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\CredentialContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\CredentialInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\CredentialList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\CredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\CredentialPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialPage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServicePage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\ChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelPage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\CreateInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\CreateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\CreateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\InvitePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InvitePage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MemberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberPage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\MessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessagePage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\ReadInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\ReadMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\ReadMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\UpdateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\Channel\\UpdateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\CreateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\CreateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\ReadChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\RoleContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RoleContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\RoleInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RoleInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\RoleList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RoleList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\RolePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/RolePage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\UpdateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\UpdateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserContext.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\UserPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/UserPage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\User\\UserChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/User/UserChannelInstance.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\User\\UserChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/User/UserChannelList.php',
    'Twilio\\Rest\\IpMessaging\\V1\\Service\\User\\UserChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/Service/User/UserChannelPage.php',
    'Twilio\\Rest\\IpMessaging\\V1\\UpdateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/CredentialOptions.php',
    'Twilio\\Rest\\IpMessaging\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V1/ServiceOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2.php',
    'Twilio\\Rest\\IpMessaging\\V2\\CreateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\CredentialContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\CredentialInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\CredentialList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\CredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\CredentialPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServicePage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\BindingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\ChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\CreateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\InvitePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InvitePage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MemberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\MessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessagePage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\ReadInviteOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/InviteOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\ReadMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\ReadMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\UpdateMemberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MemberOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\UpdateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/MessageOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\UpdateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\Channel\\WebhookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/Channel/WebhookPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\CreateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\CreateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\ReadBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/BindingOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\ReadChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\RoleContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RoleContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\RoleInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RoleInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\RoleList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RoleList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\RolePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/RolePage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\UpdateChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/ChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\UpdateUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\UserPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/UserPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\ReadUserBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UpdateUserChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserBindingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserBindingPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelContext.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelInstance.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelList.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\Service\\User\\UserChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/Service/User/UserChannelPage.php',
    'Twilio\\Rest\\IpMessaging\\V2\\UpdateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/CredentialOptions.php',
    'Twilio\\Rest\\IpMessaging\\V2\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/IpMessaging/V2/ServiceOptions.php',
    'Twilio\\Rest\\Lookups' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups.php',
    'Twilio\\Rest\\Lookups\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups/V1.php',
    'Twilio\\Rest\\Lookups\\V1\\FetchPhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberOptions.php',
    'Twilio\\Rest\\Lookups\\V1\\PhoneNumberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberContext.php',
    'Twilio\\Rest\\Lookups\\V1\\PhoneNumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberInstance.php',
    'Twilio\\Rest\\Lookups\\V1\\PhoneNumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberList.php',
    'Twilio\\Rest\\Lookups\\V1\\PhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberOptions.php',
    'Twilio\\Rest\\Lookups\\V1\\PhoneNumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Lookups/V1/PhoneNumberPage.php',
    'Twilio\\Rest\\Messaging' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging.php',
    'Twilio\\Rest\\Messaging\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1.php',
    'Twilio\\Rest\\Messaging\\V1\\CreateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\CreateSessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceContext.php',
    'Twilio\\Rest\\Messaging\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceList.php',
    'Twilio\\Rest\\Messaging\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServicePage.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderContext.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderList.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\AlphaSenderPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/AlphaSenderPage.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberContext.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberList.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\PhoneNumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/PhoneNumberPage.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodeContext.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodeInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodeList.php',
    'Twilio\\Rest\\Messaging\\V1\\Service\\ShortCodePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Service/ShortCodePage.php',
    'Twilio\\Rest\\Messaging\\V1\\SessionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionContext.php',
    'Twilio\\Rest\\Messaging\\V1\\SessionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\SessionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionList.php',
    'Twilio\\Rest\\Messaging\\V1\\SessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\SessionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionPage.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\CreateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\CreateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\CreateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\MessageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageContext.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\MessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\MessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageList.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\MessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\MessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessagePage.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantContext.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantList.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\ParticipantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantPage.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\UpdateMessageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/MessageOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\UpdateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/ParticipantOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\UpdateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookContext.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookList.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\Session\\WebhookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/Session/WebhookPage.php',
    'Twilio\\Rest\\Messaging\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/ServiceOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\UpdateSessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/SessionOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\UpdateWebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\WebhookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookContext.php',
    'Twilio\\Rest\\Messaging\\V1\\WebhookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookInstance.php',
    'Twilio\\Rest\\Messaging\\V1\\WebhookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookList.php',
    'Twilio\\Rest\\Messaging\\V1\\WebhookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookOptions.php',
    'Twilio\\Rest\\Messaging\\V1\\WebhookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Messaging/V1/WebhookPage.php',
    'Twilio\\Rest\\Monitor' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor.php',
    'Twilio\\Rest\\Monitor\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1.php',
    'Twilio\\Rest\\Monitor\\V1\\AlertContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertContext.php',
    'Twilio\\Rest\\Monitor\\V1\\AlertInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertInstance.php',
    'Twilio\\Rest\\Monitor\\V1\\AlertList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertList.php',
    'Twilio\\Rest\\Monitor\\V1\\AlertOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertOptions.php',
    'Twilio\\Rest\\Monitor\\V1\\AlertPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertPage.php',
    'Twilio\\Rest\\Monitor\\V1\\EventContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventContext.php',
    'Twilio\\Rest\\Monitor\\V1\\EventInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventInstance.php',
    'Twilio\\Rest\\Monitor\\V1\\EventList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventList.php',
    'Twilio\\Rest\\Monitor\\V1\\EventOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventOptions.php',
    'Twilio\\Rest\\Monitor\\V1\\EventPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventPage.php',
    'Twilio\\Rest\\Monitor\\V1\\ReadAlertOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/AlertOptions.php',
    'Twilio\\Rest\\Monitor\\V1\\ReadEventOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Monitor/V1/EventOptions.php',
    'Twilio\\Rest\\Notify' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify.php',
    'Twilio\\Rest\\Notify\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1.php',
    'Twilio\\Rest\\Notify\\V1\\CreateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialOptions.php',
    'Twilio\\Rest\\Notify\\V1\\CreateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
    'Twilio\\Rest\\Notify\\V1\\CredentialContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialContext.php',
    'Twilio\\Rest\\Notify\\V1\\CredentialInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialInstance.php',
    'Twilio\\Rest\\Notify\\V1\\CredentialList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialList.php',
    'Twilio\\Rest\\Notify\\V1\\CredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialOptions.php',
    'Twilio\\Rest\\Notify\\V1\\CredentialPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialPage.php',
    'Twilio\\Rest\\Notify\\V1\\ReadServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
    'Twilio\\Rest\\Notify\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceContext.php',
    'Twilio\\Rest\\Notify\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceInstance.php',
    'Twilio\\Rest\\Notify\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceList.php',
    'Twilio\\Rest\\Notify\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
    'Twilio\\Rest\\Notify\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServicePage.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\BindingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingContext.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\BindingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingInstance.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\BindingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingList.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\BindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingOptions.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\BindingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingPage.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\CreateBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingOptions.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\CreateNotificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationOptions.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\NotificationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationInstance.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\NotificationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationList.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\NotificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationOptions.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\NotificationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/NotificationPage.php',
    'Twilio\\Rest\\Notify\\V1\\Service\\ReadBindingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/Service/BindingOptions.php',
    'Twilio\\Rest\\Notify\\V1\\UpdateCredentialOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialOptions.php',
    'Twilio\\Rest\\Notify\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Notify/V1/ServiceOptions.php',
    'Twilio\\Rest\\Numbers' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers.php',
    'Twilio\\Rest\\Numbers\\V2' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryComplianceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryComplianceInstance.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryComplianceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryComplianceList.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliancePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliancePage.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleContext.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleInstance.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleList.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\BundlePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundlePage.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentContext.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentInstance.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentList.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\Bundle\\ItemAssignmentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/Bundle/ItemAssignmentPage.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\CreateBundleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\CreateEndUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\CreateSupportingDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserContext.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserInstance.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserList.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserPage.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypeContext.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypeInstance.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypeList.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\EndUserTypePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserTypePage.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\ReadBundleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentContext.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentInstance.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentList.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentPage.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypeContext.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypeInstance.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypeList.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\SupportingDocumentTypePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentTypePage.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\UpdateBundleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/BundleOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\UpdateEndUserOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/EndUserOptions.php',
    'Twilio\\Rest\\Numbers\\V2\\RegulatoryCompliance\\UpdateSupportingDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Numbers/V2/RegulatoryCompliance/SupportingDocumentOptions.php',
    'Twilio\\Rest\\Preview' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview.php',
    'Twilio\\Rest\\Preview\\BulkExports' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationContext.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationInstance.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationList.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationOptions.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportConfigurationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationPage.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportContext.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportInstance.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportList.php',
    'Twilio\\Rest\\Preview\\BulkExports\\ExportPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportPage.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\CreateExportCustomJobOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobOptions.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\DayInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/DayInstance.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\DayList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/DayList.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\DayPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/DayPage.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobInstance.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobList.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobOptions.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\ExportCustomJobPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobPage.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobContext.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobInstance.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobList.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\JobPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/JobPage.php',
    'Twilio\\Rest\\Preview\\BulkExports\\Export\\ReadExportCustomJobOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/Export/ExportCustomJobOptions.php',
    'Twilio\\Rest\\Preview\\BulkExports\\UpdateExportConfigurationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/BulkExports/ExportConfigurationOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\CreateFleetOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\FleetContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetContext.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\FleetInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetInstance.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\FleetList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetList.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\FleetOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\FleetPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetPage.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateContext.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateInstance.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateList.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CertificatePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificatePage.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateCertificateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateDeploymentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateDeviceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\CreateKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentContext.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentInstance.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentList.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeploymentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentPage.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceContext.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceInstance.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceList.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DeviceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\DevicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DevicePage.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyContext.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyInstance.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyList.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\KeyPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyPage.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\ReadCertificateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\ReadDeviceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\ReadKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateCertificateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/CertificateOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateDeploymentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeploymentOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateDeviceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/DeviceOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\Fleet\\UpdateKeyOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/Fleet/KeyOptions.php',
    'Twilio\\Rest\\Preview\\DeployedDevices\\UpdateFleetOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/DeployedDevices/FleetOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentContext.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentInstance.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentList.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocumentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentPage.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderInstance.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderList.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\DependentHostedNumberOrderPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderPage.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\AuthorizationDocument\\ReadDependentHostedNumberOrderOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocument/DependentHostedNumberOrderOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\CreateAuthorizationDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\CreateHostedNumberOrderOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderContext.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderInstance.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderList.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\HostedNumberOrderPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderPage.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\ReadAuthorizationDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\ReadHostedNumberOrderOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\UpdateAuthorizationDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/AuthorizationDocumentOptions.php',
    'Twilio\\Rest\\Preview\\HostedNumbers\\UpdateHostedNumberOrderOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/HostedNumbers/HostedNumberOrderOptions.php',
    'Twilio\\Rest\\Preview\\Marketplace' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnContext.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnInstance.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnList.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOnPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOnPage.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionContext.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionInstance.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionList.php',
    'Twilio\\Rest\\Preview\\Marketplace\\AvailableAddOn\\AvailableAddOnExtensionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/AvailableAddOn/AvailableAddOnExtensionPage.php',
    'Twilio\\Rest\\Preview\\Marketplace\\CreateInstalledAddOnOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnOptions.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnContext.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnInstance.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnList.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnOptions.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOnPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnPage.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionContext.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionInstance.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionList.php',
    'Twilio\\Rest\\Preview\\Marketplace\\InstalledAddOn\\InstalledAddOnExtensionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOn/InstalledAddOnExtensionPage.php',
    'Twilio\\Rest\\Preview\\Marketplace\\UpdateInstalledAddOnOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Marketplace/InstalledAddOnOptions.php',
    'Twilio\\Rest\\Preview\\Sync' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync.php',
    'Twilio\\Rest\\Preview\\Sync\\CreateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceContext.php',
    'Twilio\\Rest\\Preview\\Sync\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceList.php',
    'Twilio\\Rest\\Preview\\Sync\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServicePage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\CreateDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\CreateSyncListOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\CreateSyncMapOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\DocumentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/DocumentPage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\Document\\DocumentPermissionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/Document/DocumentPermissionPage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncListPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncListPage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\ReadSyncListItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListItemPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListItemPage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncList\\SyncListPermissionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncList/SyncListPermissionPage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMapPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMapPage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\ReadSyncMapItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemOptions.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapItemPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapItemPage.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionContext.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionInstance.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionList.php',
    'Twilio\\Rest\\Preview\\Sync\\Service\\SyncMap\\SyncMapPermissionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/Service/SyncMap/SyncMapPermissionPage.php',
    'Twilio\\Rest\\Preview\\Sync\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Sync/ServiceOptions.php',
    'Twilio\\Rest\\Preview\\TrustedComms' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallOptions.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BrandedCallPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallPage.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BusinessContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessContext.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BusinessInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BusinessList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\BusinessPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BusinessPage.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\InsightsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/InsightsInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\InsightsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/InsightsList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\InsightsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/InsightsPage.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\FetchSuccessRateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateOptions.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateContext.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRateOptions.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\Business\\Insights\\SuccessRatePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/Business/Insights/SuccessRatePage.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CpsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsContext.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CpsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CpsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CpsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CpsPage.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CreateBrandedCallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/BrandedCallOptions.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CreatePhoneCallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallOptions.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallContext.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\CurrentCallPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/CurrentCallPage.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\DeviceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/DeviceInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\DeviceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/DeviceList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\DevicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/DevicePage.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallInstance.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallList.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallOptions.php',
    'Twilio\\Rest\\Preview\\TrustedComms\\PhoneCallPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/TrustedComms/PhoneCallPage.php',
    'Twilio\\Rest\\Preview\\Understand' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand.php',
    'Twilio\\Rest\\Preview\\Understand\\AssistantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantContext.php',
    'Twilio\\Rest\\Preview\\Understand\\AssistantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\AssistantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantList.php',
    'Twilio\\Rest\\Preview\\Understand\\AssistantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\AssistantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantFallbackActionsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\AssistantInitiationActionsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateFieldTypeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateModelBuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateQueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\CreateTaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialogueContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialogueContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialogueInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialogueInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialogueList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialogueList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\DialoguePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/DialoguePage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldTypePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypePage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\CreateFieldValueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\FieldValuePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValuePage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\FieldType\\ReadFieldValueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldType/FieldValueOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\ModelBuildPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\QueryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\ReadQueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\StyleSheetPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\TaskPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\CreateSampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\FieldPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/FieldPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\ReadSampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\SamplePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SamplePage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskActionsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsContext.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsInstance.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsList.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\TaskStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskStatisticsPage.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\UpdateSampleOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/SampleOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\Task\\UpdateTaskActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/Task/TaskActionsOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateAssistantFallbackActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantFallbackActionsOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateAssistantInitiationActionsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/AssistantInitiationActionsOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateFieldTypeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/FieldTypeOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateModelBuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/ModelBuildOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateQueryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/QueryOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateStyleSheetOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/StyleSheetOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\Assistant\\UpdateTaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/Assistant/TaskOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\CreateAssistantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantOptions.php',
    'Twilio\\Rest\\Preview\\Understand\\UpdateAssistantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Understand/AssistantOptions.php',
    'Twilio\\Rest\\Preview\\Wireless' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless.php',
    'Twilio\\Rest\\Preview\\Wireless\\CommandContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandContext.php',
    'Twilio\\Rest\\Preview\\Wireless\\CommandInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandInstance.php',
    'Twilio\\Rest\\Preview\\Wireless\\CommandList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandList.php',
    'Twilio\\Rest\\Preview\\Wireless\\CommandOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\CommandPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandPage.php',
    'Twilio\\Rest\\Preview\\Wireless\\CreateCommandOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\CreateRatePlanOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\RatePlanContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanContext.php',
    'Twilio\\Rest\\Preview\\Wireless\\RatePlanInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanInstance.php',
    'Twilio\\Rest\\Preview\\Wireless\\RatePlanList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanList.php',
    'Twilio\\Rest\\Preview\\Wireless\\RatePlanOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\RatePlanPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanPage.php',
    'Twilio\\Rest\\Preview\\Wireless\\ReadCommandOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/CommandOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\ReadSimOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\SimContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimContext.php',
    'Twilio\\Rest\\Preview\\Wireless\\SimInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimInstance.php',
    'Twilio\\Rest\\Preview\\Wireless\\SimList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimList.php',
    'Twilio\\Rest\\Preview\\Wireless\\SimOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\SimPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimPage.php',
    'Twilio\\Rest\\Preview\\Wireless\\Sim\\FetchUsageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageContext.php',
    'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageInstance.php',
    'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageList.php',
    'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsageOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsageOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\Sim\\UsagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/Sim/UsagePage.php',
    'Twilio\\Rest\\Preview\\Wireless\\UpdateRatePlanOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/RatePlanOptions.php',
    'Twilio\\Rest\\Preview\\Wireless\\UpdateSimOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Preview/Wireless/SimOptions.php',
    'Twilio\\Rest\\Pricing' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing.php',
    'Twilio\\Rest\\Pricing\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1.php',
    'Twilio\\Rest\\Pricing\\V1\\MessagingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/MessagingInstance.php',
    'Twilio\\Rest\\Pricing\\V1\\MessagingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/MessagingList.php',
    'Twilio\\Rest\\Pricing\\V1\\MessagingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/MessagingPage.php',
    'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryContext.php',
    'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryInstance.php',
    'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryList.php',
    'Twilio\\Rest\\Pricing\\V1\\Messaging\\CountryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Messaging/CountryPage.php',
    'Twilio\\Rest\\Pricing\\V1\\PhoneNumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumberInstance.php',
    'Twilio\\Rest\\Pricing\\V1\\PhoneNumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumberList.php',
    'Twilio\\Rest\\Pricing\\V1\\PhoneNumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumberPage.php',
    'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryContext.php',
    'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryInstance.php',
    'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryList.php',
    'Twilio\\Rest\\Pricing\\V1\\PhoneNumber\\CountryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/PhoneNumber/CountryPage.php',
    'Twilio\\Rest\\Pricing\\V1\\VoiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/VoiceInstance.php',
    'Twilio\\Rest\\Pricing\\V1\\VoiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/VoiceList.php',
    'Twilio\\Rest\\Pricing\\V1\\VoicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/VoicePage.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryContext.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryInstance.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryList.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\CountryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/CountryPage.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberContext.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberInstance.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberList.php',
    'Twilio\\Rest\\Pricing\\V1\\Voice\\NumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V1/Voice/NumberPage.php',
    'Twilio\\Rest\\Pricing\\V2' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2.php',
    'Twilio\\Rest\\Pricing\\V2\\VoiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/VoiceInstance.php',
    'Twilio\\Rest\\Pricing\\V2\\VoiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/VoiceList.php',
    'Twilio\\Rest\\Pricing\\V2\\VoicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/VoicePage.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryContext.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryInstance.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryList.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\CountryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/CountryPage.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\FetchNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberOptions.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberContext.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberInstance.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberList.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberOptions.php',
    'Twilio\\Rest\\Pricing\\V2\\Voice\\NumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Pricing/V2/Voice/NumberPage.php',
    'Twilio\\Rest\\Proxy' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy.php',
    'Twilio\\Rest\\Proxy\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1.php',
    'Twilio\\Rest\\Proxy\\V1\\CreateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceContext.php',
    'Twilio\\Rest\\Proxy\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceInstance.php',
    'Twilio\\Rest\\Proxy\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceList.php',
    'Twilio\\Rest\\Proxy\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServicePage.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\CreatePhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\CreateSessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberContext.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberInstance.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberList.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\PhoneNumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberPage.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\SessionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionContext.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\SessionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionInstance.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\SessionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionList.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\SessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\SessionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionPage.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\CreateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionContext.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionInstance.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionList.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\InteractionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/InteractionPage.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantContext.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantInstance.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantList.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\ParticipantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/ParticipantPage.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\CreateMessageInteractionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionContext.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionInstance.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionList.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\Session\\Participant\\MessageInteractionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/Session/Participant/MessageInteractionPage.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeContext.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeInstance.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeList.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\ShortCodePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodePage.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\UpdatePhoneNumberOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/PhoneNumberOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\UpdateSessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/SessionOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\Service\\UpdateShortCodeOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/Service/ShortCodeOptions.php',
    'Twilio\\Rest\\Proxy\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Proxy/V1/ServiceOptions.php',
    'Twilio\\Rest\\Serverless' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless.php',
    'Twilio\\Rest\\Serverless\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1.php',
    'Twilio\\Rest\\Serverless\\V1\\CreateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceContext.php',
    'Twilio\\Rest\\Serverless\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceList.php',
    'Twilio\\Rest\\Serverless\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServicePage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\AssetContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\AssetInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\AssetList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\AssetPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/AssetPage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Asset\\AssetVersionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Asset/AssetVersionPage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\BuildContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\BuildInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\BuildList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\BuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\BuildPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildPage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\CreateBuildOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/BuildOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\CreateEnvironmentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\EnvironmentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/EnvironmentPage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\DeploymentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/DeploymentPage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\LogPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogPage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\ReadLogOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/LogOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\UpdateVariableOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariableOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariableOptions.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\Environment\\VariablePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/Environment/VariablePage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\FunctionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/FunctionPage.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionContext.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionInstance.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionList.php',
    'Twilio\\Rest\\Serverless\\V1\\Service\\TwilioFunction\\FunctionVersionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/Service/TwilioFunction/FunctionVersionPage.php',
    'Twilio\\Rest\\Serverless\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Serverless/V1/ServiceOptions.php',
    'Twilio\\Rest\\Studio' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio.php',
    'Twilio\\Rest\\Studio\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1.php',
    'Twilio\\Rest\\Studio\\V1\\FlowContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowContext.php',
    'Twilio\\Rest\\Studio\\V1\\FlowInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowInstance.php',
    'Twilio\\Rest\\Studio\\V1\\FlowList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowList.php',
    'Twilio\\Rest\\Studio\\V1\\FlowPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/FlowPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\CreateEngagementOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementOptions.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\CreateExecutionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionOptions.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementOptions.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\EngagementPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/EngagementPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\EngagementContextPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/EngagementContextPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\StepPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/StepPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Engagement\\Step\\StepContextPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Engagement/Step/StepContextPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionOptions.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\ExecutionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionContextPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionContextPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStepPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStepPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextContext.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextInstance.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextList.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\Execution\\ExecutionStep\\ExecutionStepContextPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/Execution/ExecutionStep/ExecutionStepContextPage.php',
    'Twilio\\Rest\\Studio\\V1\\Flow\\ReadExecutionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V1/Flow/ExecutionOptions.php',
    'Twilio\\Rest\\Studio\\V2' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2.php',
    'Twilio\\Rest\\Studio\\V2\\CreateFlowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowOptions.php',
    'Twilio\\Rest\\Studio\\V2\\FlowContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowContext.php',
    'Twilio\\Rest\\Studio\\V2\\FlowInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowInstance.php',
    'Twilio\\Rest\\Studio\\V2\\FlowList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowList.php',
    'Twilio\\Rest\\Studio\\V2\\FlowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowOptions.php',
    'Twilio\\Rest\\Studio\\V2\\FlowPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowPage.php',
    'Twilio\\Rest\\Studio\\V2\\FlowValidateInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateInstance.php',
    'Twilio\\Rest\\Studio\\V2\\FlowValidateList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateList.php',
    'Twilio\\Rest\\Studio\\V2\\FlowValidateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateOptions.php',
    'Twilio\\Rest\\Studio\\V2\\FlowValidatePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidatePage.php',
    'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionContext.php',
    'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionInstance.php',
    'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionList.php',
    'Twilio\\Rest\\Studio\\V2\\Flow\\FlowRevisionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/Flow/FlowRevisionPage.php',
    'Twilio\\Rest\\Studio\\V2\\UpdateFlowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowOptions.php',
    'Twilio\\Rest\\Studio\\V2\\UpdateFlowValidateOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Studio/V2/FlowValidateOptions.php',
    'Twilio\\Rest\\Sync' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync.php',
    'Twilio\\Rest\\Sync\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1.php',
    'Twilio\\Rest\\Sync\\V1\\CreateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceOptions.php',
    'Twilio\\Rest\\Sync\\V1\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceContext.php',
    'Twilio\\Rest\\Sync\\V1\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceInstance.php',
    'Twilio\\Rest\\Sync\\V1\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceList.php',
    'Twilio\\Rest\\Sync\\V1\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceOptions.php',
    'Twilio\\Rest\\Sync\\V1\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServicePage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\CreateDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\CreateSyncListOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\CreateSyncMapOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\CreateSyncStreamOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\DocumentContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\DocumentInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\DocumentList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\DocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\DocumentPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\Document\\DocumentPermissionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/Document/DocumentPermissionPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncListContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncListInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncListList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncListOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncListPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\CreateSyncListItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\ReadSyncListItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListItemPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\SyncListPermissionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListPermissionPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncList\\UpdateSyncListItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncList/SyncListItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMapPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\CreateSyncMapItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\ReadSyncMapItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapItemPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\SyncMapPermissionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapPermissionPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncMap\\UpdateSyncMapItemOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMap/SyncMapItemOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamContext.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStreamPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamPage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStream\\StreamMessageInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStream/StreamMessageInstance.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStream\\StreamMessageList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStream/StreamMessageList.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\SyncStream\\StreamMessagePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStream/StreamMessagePage.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\UpdateDocumentOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/DocumentOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\UpdateSyncListOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncListOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\UpdateSyncMapOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncMapOptions.php',
    'Twilio\\Rest\\Sync\\V1\\Service\\UpdateSyncStreamOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/Service/SyncStreamOptions.php',
    'Twilio\\Rest\\Sync\\V1\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Sync/V1/ServiceOptions.php',
    'Twilio\\Rest\\Taskrouter' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter.php',
    'Twilio\\Rest\\Taskrouter\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1.php',
    'Twilio\\Rest\\Taskrouter\\V1\\CreateWorkspaceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\ReadWorkspaceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\UpdateWorkspaceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\WorkspaceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspaceOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\WorkspacePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/WorkspacePage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ActivityPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateActivityOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateTaskChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateTaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateTaskQueueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateWorkerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\CreateWorkflowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\EventPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\FetchWorkspaceCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\FetchWorkspaceRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\FetchWorkspaceStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadActivityOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadEventOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/EventOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadTaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadTaskQueueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadWorkerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\ReadWorkflowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueuePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueuePage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\FetchTaskQueueCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\FetchTaskQueueRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\FetchTaskQueueStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\ReadTaskQueuesStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueCumulativeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueCumulativeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueRealTimeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueRealTimeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueueStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueueStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\TaskQueue\\TaskQueuesStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueue/TaskQueuesStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReadReservationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\ReservationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Task\\UpdateReservationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Task/ReservationOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateActivityOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/ActivityOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateTaskChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskChannelOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateTaskOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateTaskQueueOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/TaskQueueOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateWorkerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\UpdateWorkflowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkerPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkerPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkerStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkersCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkersRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\FetchWorkersStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReadReservationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\ReservationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\UpdateReservationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/ReservationOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\UpdateWorkerChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerChannelPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerChannelPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkerStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkerStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersCumulativeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersCumulativeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersRealTimeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersRealTimeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Worker\\WorkersStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Worker/WorkersStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkflowPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkflowPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\FetchWorkflowCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\FetchWorkflowRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\FetchWorkflowStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowCumulativeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowCumulativeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowRealTimeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowRealTimeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\Workflow\\WorkflowStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/Workflow/WorkflowStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceCumulativeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceCumulativeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceRealTimeStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceRealTimeStatisticsPage.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsContext.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsInstance.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsList.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsOptions.php',
    'Twilio\\Rest\\Taskrouter\\V1\\Workspace\\WorkspaceStatisticsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Taskrouter/V1/Workspace/WorkspaceStatisticsPage.php',
    'Twilio\\Rest\\Trunking' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking.php',
    'Twilio\\Rest\\Trunking\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1.php',
    'Twilio\\Rest\\Trunking\\V1\\CreateTrunkOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkOptions.php',
    'Twilio\\Rest\\Trunking\\V1\\TrunkContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkContext.php',
    'Twilio\\Rest\\Trunking\\V1\\TrunkInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkInstance.php',
    'Twilio\\Rest\\Trunking\\V1\\TrunkList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkList.php',
    'Twilio\\Rest\\Trunking\\V1\\TrunkOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkOptions.php',
    'Twilio\\Rest\\Trunking\\V1\\TrunkPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkPage.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListContext.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListInstance.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListList.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\CredentialListPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/CredentialListPage.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListContext.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListInstance.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListList.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\IpAccessControlListPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/IpAccessControlListPage.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlContext.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlInstance.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlList.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlOptions.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\OriginationUrlPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlPage.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberContext.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberInstance.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberList.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\PhoneNumberPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/PhoneNumberPage.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainContext.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainInstance.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainList.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\TerminatingSipDomainPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/TerminatingSipDomainPage.php',
    'Twilio\\Rest\\Trunking\\V1\\Trunk\\UpdateOriginationUrlOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/Trunk/OriginationUrlOptions.php',
    'Twilio\\Rest\\Trunking\\V1\\UpdateTrunkOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Trunking/V1/TrunkOptions.php',
    'Twilio\\Rest\\Verify' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify.php',
    'Twilio\\Rest\\Verify\\V2' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2.php',
    'Twilio\\Rest\\Verify\\V2\\CreateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceOptions.php',
    'Twilio\\Rest\\Verify\\V2\\ServiceContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceContext.php',
    'Twilio\\Rest\\Verify\\V2\\ServiceInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceInstance.php',
    'Twilio\\Rest\\Verify\\V2\\ServiceList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceList.php',
    'Twilio\\Rest\\Verify\\V2\\ServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceOptions.php',
    'Twilio\\Rest\\Verify\\V2\\ServicePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServicePage.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\CreateRateLimitOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\CreateVerificationCheckOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\CreateVerificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationContext.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationInstance.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationList.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\MessagingConfigurationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/MessagingConfigurationPage.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitContext.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitInstance.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitList.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimitPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitPage.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketContext.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketInstance.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketList.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\BucketPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketPage.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\RateLimit\\UpdateBucketOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimit/BucketOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\UpdateRateLimitOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/RateLimitOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckInstance.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckList.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationCheckPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationCheckPage.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationContext.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationInstance.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationList.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationOptions.php',
    'Twilio\\Rest\\Verify\\V2\\Service\\VerificationPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/Service/VerificationPage.php',
    'Twilio\\Rest\\Verify\\V2\\UpdateServiceOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Verify/V2/ServiceOptions.php',
    'Twilio\\Rest\\Video' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video.php',
    'Twilio\\Rest\\Video\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1.php',
    'Twilio\\Rest\\Video\\V1\\CompositionContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionContext.php',
    'Twilio\\Rest\\Video\\V1\\CompositionHookContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookContext.php',
    'Twilio\\Rest\\Video\\V1\\CompositionHookInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookInstance.php',
    'Twilio\\Rest\\Video\\V1\\CompositionHookList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookList.php',
    'Twilio\\Rest\\Video\\V1\\CompositionHookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
    'Twilio\\Rest\\Video\\V1\\CompositionHookPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookPage.php',
    'Twilio\\Rest\\Video\\V1\\CompositionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionInstance.php',
    'Twilio\\Rest\\Video\\V1\\CompositionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionList.php',
    'Twilio\\Rest\\Video\\V1\\CompositionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionOptions.php',
    'Twilio\\Rest\\Video\\V1\\CompositionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionPage.php',
    'Twilio\\Rest\\Video\\V1\\CompositionSettingsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsContext.php',
    'Twilio\\Rest\\Video\\V1\\CompositionSettingsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsInstance.php',
    'Twilio\\Rest\\Video\\V1\\CompositionSettingsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsList.php',
    'Twilio\\Rest\\Video\\V1\\CompositionSettingsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsOptions.php',
    'Twilio\\Rest\\Video\\V1\\CompositionSettingsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsPage.php',
    'Twilio\\Rest\\Video\\V1\\CreateCompositionHookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
    'Twilio\\Rest\\Video\\V1\\CreateCompositionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionOptions.php',
    'Twilio\\Rest\\Video\\V1\\CreateCompositionSettingsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionSettingsOptions.php',
    'Twilio\\Rest\\Video\\V1\\CreateRecordingSettingsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsOptions.php',
    'Twilio\\Rest\\Video\\V1\\CreateRoomOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomOptions.php',
    'Twilio\\Rest\\Video\\V1\\ReadCompositionHookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
    'Twilio\\Rest\\Video\\V1\\ReadCompositionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionOptions.php',
    'Twilio\\Rest\\Video\\V1\\ReadRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingOptions.php',
    'Twilio\\Rest\\Video\\V1\\ReadRoomOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomOptions.php',
    'Twilio\\Rest\\Video\\V1\\RecordingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingContext.php',
    'Twilio\\Rest\\Video\\V1\\RecordingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingInstance.php',
    'Twilio\\Rest\\Video\\V1\\RecordingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingList.php',
    'Twilio\\Rest\\Video\\V1\\RecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingOptions.php',
    'Twilio\\Rest\\Video\\V1\\RecordingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingPage.php',
    'Twilio\\Rest\\Video\\V1\\RecordingSettingsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsContext.php',
    'Twilio\\Rest\\Video\\V1\\RecordingSettingsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsInstance.php',
    'Twilio\\Rest\\Video\\V1\\RecordingSettingsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsList.php',
    'Twilio\\Rest\\Video\\V1\\RecordingSettingsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsOptions.php',
    'Twilio\\Rest\\Video\\V1\\RecordingSettingsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RecordingSettingsPage.php',
    'Twilio\\Rest\\Video\\V1\\RoomContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomContext.php',
    'Twilio\\Rest\\Video\\V1\\RoomInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomInstance.php',
    'Twilio\\Rest\\Video\\V1\\RoomList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomList.php',
    'Twilio\\Rest\\Video\\V1\\RoomOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomOptions.php',
    'Twilio\\Rest\\Video\\V1\\RoomPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/RoomPage.php',
    'Twilio\\Rest\\Video\\V1\\Room\\ParticipantContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantContext.php',
    'Twilio\\Rest\\Video\\V1\\Room\\ParticipantInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantInstance.php',
    'Twilio\\Rest\\Video\\V1\\Room\\ParticipantList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantList.php',
    'Twilio\\Rest\\Video\\V1\\Room\\ParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantOptions.php',
    'Twilio\\Rest\\Video\\V1\\Room\\ParticipantPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantPage.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackContext.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackInstance.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackList.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\PublishedTrackPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/PublishedTrackPage.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesInstance.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesList.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesOptions.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribeRulesPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesPage.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackContext.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackInstance.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackList.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\SubscribedTrackPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribedTrackPage.php',
    'Twilio\\Rest\\Video\\V1\\Room\\Participant\\UpdateSubscribeRulesOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/Participant/SubscribeRulesOptions.php',
    'Twilio\\Rest\\Video\\V1\\Room\\ReadParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantOptions.php',
    'Twilio\\Rest\\Video\\V1\\Room\\ReadRoomRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingOptions.php',
    'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingContext.php',
    'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingInstance.php',
    'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingList.php',
    'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingOptions.php',
    'Twilio\\Rest\\Video\\V1\\Room\\RoomRecordingPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/RoomRecordingPage.php',
    'Twilio\\Rest\\Video\\V1\\Room\\UpdateParticipantOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/Room/ParticipantOptions.php',
    'Twilio\\Rest\\Video\\V1\\UpdateCompositionHookOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Video/V1/CompositionHookOptions.php',
    'Twilio\\Rest\\Voice' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice.php',
    'Twilio\\Rest\\Voice\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissionsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissionsInstance.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissionsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissionsList.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissionsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissionsPage.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\BulkCountryUpdateInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/BulkCountryUpdateInstance.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\BulkCountryUpdateList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/BulkCountryUpdateList.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\BulkCountryUpdatePage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/BulkCountryUpdatePage.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryContext.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryInstance.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryList.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryOptions.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\CountryPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryPage.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\Country\\HighriskSpecialPrefixInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/Country/HighriskSpecialPrefixInstance.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\Country\\HighriskSpecialPrefixList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/Country/HighriskSpecialPrefixList.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\Country\\HighriskSpecialPrefixPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/Country/HighriskSpecialPrefixPage.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\ReadCountryOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/CountryOptions.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsContext.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsInstance.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsList.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsOptions.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\SettingsPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsPage.php',
    'Twilio\\Rest\\Voice\\V1\\DialingPermissions\\UpdateSettingsOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Voice/V1/DialingPermissions/SettingsOptions.php',
    'Twilio\\Rest\\Wireless' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless.php',
    'Twilio\\Rest\\Wireless\\V1' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1.php',
    'Twilio\\Rest\\Wireless\\V1\\CommandContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandContext.php',
    'Twilio\\Rest\\Wireless\\V1\\CommandInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandInstance.php',
    'Twilio\\Rest\\Wireless\\V1\\CommandList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandList.php',
    'Twilio\\Rest\\Wireless\\V1\\CommandOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\CommandPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandPage.php',
    'Twilio\\Rest\\Wireless\\V1\\CreateCommandOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\CreateRatePlanOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\RatePlanContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanContext.php',
    'Twilio\\Rest\\Wireless\\V1\\RatePlanInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanInstance.php',
    'Twilio\\Rest\\Wireless\\V1\\RatePlanList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanList.php',
    'Twilio\\Rest\\Wireless\\V1\\RatePlanOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\RatePlanPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanPage.php',
    'Twilio\\Rest\\Wireless\\V1\\ReadCommandOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/CommandOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\ReadSimOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\ReadUsageRecordOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\SimContext' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimContext.php',
    'Twilio\\Rest\\Wireless\\V1\\SimInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimInstance.php',
    'Twilio\\Rest\\Wireless\\V1\\SimList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimList.php',
    'Twilio\\Rest\\Wireless\\V1\\SimOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\SimPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimPage.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionInstance.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionList.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\DataSessionPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionPage.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\ReadDataSessionOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/DataSessionOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\ReadUsageRecordOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordInstance.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordList.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\Sim\\UsageRecordPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/Sim/UsageRecordPage.php',
    'Twilio\\Rest\\Wireless\\V1\\UpdateRatePlanOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/RatePlanOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\UpdateSimOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/SimOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\UsageRecordInstance' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordInstance.php',
    'Twilio\\Rest\\Wireless\\V1\\UsageRecordList' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordList.php',
    'Twilio\\Rest\\Wireless\\V1\\UsageRecordOptions' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordOptions.php',
    'Twilio\\Rest\\Wireless\\V1\\UsageRecordPage' => $vendorDir . '/twilio/sdk/src/Twilio/Rest/Wireless/V1/UsageRecordPage.php',
    'Twilio\\Security\\RequestValidator' => $vendorDir . '/twilio/sdk/src/Twilio/Security/RequestValidator.php',
    'Twilio\\Serialize' => $vendorDir . '/twilio/sdk/src/Twilio/Serialize.php',
    'Twilio\\Stream' => $vendorDir . '/twilio/sdk/src/Twilio/Stream.php',
    'Twilio\\TaskRouter\\WorkflowConfiguration' => $vendorDir . '/twilio/sdk/src/Twilio/TaskRouter/WorkflowConfiguration.php',
    'Twilio\\TaskRouter\\WorkflowRule' => $vendorDir . '/twilio/sdk/src/Twilio/TaskRouter/WorkflowRule.php',
    'Twilio\\TaskRouter\\WorkflowRuleTarget' => $vendorDir . '/twilio/sdk/src/Twilio/TaskRouter/WorkflowRuleTarget.php',
    'Twilio\\TwiML\\FaxResponse' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/FaxResponse.php',
    'Twilio\\TwiML\\Fax\\Receive' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Fax/Receive.php',
    'Twilio\\TwiML\\GenericNode' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/GenericNode.php',
    'Twilio\\TwiML\\MessagingResponse' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/MessagingResponse.php',
    'Twilio\\TwiML\\Messaging\\Body' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Messaging/Body.php',
    'Twilio\\TwiML\\Messaging\\Media' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Messaging/Media.php',
    'Twilio\\TwiML\\Messaging\\Message' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Messaging/Message.php',
    'Twilio\\TwiML\\Messaging\\Redirect' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Messaging/Redirect.php',
    'Twilio\\TwiML\\TwiML' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/TwiML.php',
    'Twilio\\TwiML\\Video\\Room' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Video/Room.php',
    'Twilio\\TwiML\\VoiceResponse' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/VoiceResponse.php',
    'Twilio\\TwiML\\Voice\\Autopilot' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Autopilot.php',
    'Twilio\\TwiML\\Voice\\Client' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Client.php',
    'Twilio\\TwiML\\Voice\\Conference' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Conference.php',
    'Twilio\\TwiML\\Voice\\Connect' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Connect.php',
    'Twilio\\TwiML\\Voice\\Dial' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Dial.php',
    'Twilio\\TwiML\\Voice\\Echo_' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Echo.php',
    'Twilio\\TwiML\\Voice\\Enqueue' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Enqueue.php',
    'Twilio\\TwiML\\Voice\\Gather' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Gather.php',
    'Twilio\\TwiML\\Voice\\Hangup' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Hangup.php',
    'Twilio\\TwiML\\Voice\\Identity' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Identity.php',
    'Twilio\\TwiML\\Voice\\Leave' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Leave.php',
    'Twilio\\TwiML\\Voice\\Number' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Number.php',
    'Twilio\\TwiML\\Voice\\Parameter' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Parameter.php',
    'Twilio\\TwiML\\Voice\\Pause' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Pause.php',
    'Twilio\\TwiML\\Voice\\Pay' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Pay.php',
    'Twilio\\TwiML\\Voice\\Play' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Play.php',
    'Twilio\\TwiML\\Voice\\Prompt' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Prompt.php',
    'Twilio\\TwiML\\Voice\\Queue' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Queue.php',
    'Twilio\\TwiML\\Voice\\Record' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Record.php',
    'Twilio\\TwiML\\Voice\\Redirect' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Redirect.php',
    'Twilio\\TwiML\\Voice\\Refer' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Refer.php',
    'Twilio\\TwiML\\Voice\\ReferSip' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/ReferSip.php',
    'Twilio\\TwiML\\Voice\\Reject' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Reject.php',
    'Twilio\\TwiML\\Voice\\Room' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Room.php',
    'Twilio\\TwiML\\Voice\\Say' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Say.php',
    'Twilio\\TwiML\\Voice\\Sim' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Sim.php',
    'Twilio\\TwiML\\Voice\\Sip' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Sip.php',
    'Twilio\\TwiML\\Voice\\Siprec' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Siprec.php',
    'Twilio\\TwiML\\Voice\\Sms' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Sms.php',
    'Twilio\\TwiML\\Voice\\SsmlBreak' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlBreak.php',
    'Twilio\\TwiML\\Voice\\SsmlEmphasis' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlEmphasis.php',
    'Twilio\\TwiML\\Voice\\SsmlLang' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlLang.php',
    'Twilio\\TwiML\\Voice\\SsmlP' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlP.php',
    'Twilio\\TwiML\\Voice\\SsmlPhoneme' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlPhoneme.php',
    'Twilio\\TwiML\\Voice\\SsmlProsody' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlProsody.php',
    'Twilio\\TwiML\\Voice\\SsmlS' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlS.php',
    'Twilio\\TwiML\\Voice\\SsmlSayAs' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlSayAs.php',
    'Twilio\\TwiML\\Voice\\SsmlSub' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlSub.php',
    'Twilio\\TwiML\\Voice\\SsmlW' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/SsmlW.php',
    'Twilio\\TwiML\\Voice\\Start' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Start.php',
    'Twilio\\TwiML\\Voice\\Stop' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Stop.php',
    'Twilio\\TwiML\\Voice\\Stream' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Stream.php',
    'Twilio\\TwiML\\Voice\\Task' => $vendorDir . '/twilio/sdk/src/Twilio/TwiML/Voice/Task.php',
    'Twilio\\Values' => $vendorDir . '/twilio/sdk/src/Twilio/Values.php',
    'Twilio\\Version' => $vendorDir . '/twilio/sdk/src/Twilio/Version.php',
    'Twilio\\VersionInfo' => $vendorDir . '/twilio/sdk/src/Twilio/VersionInfo.php',
    'WeDevs\\AdvancedLeave\\Accrual\\Accrual' => $baseDir . '/modules/pro/advanced-leave/includes/Accrual/Accrual.php',
    'WeDevs\\AdvancedLeave\\Accrual\\AccrualBgProcess' => $baseDir . '/modules/pro/advanced-leave/includes/Accrual/AccrualBgProcess.php',
    'WeDevs\\AdvancedLeave\\Forward\\Forward' => $baseDir . '/modules/pro/advanced-leave/includes/Forward/Forward.php',
    'WeDevs\\AdvancedLeave\\Forward\\LeaveCarryForwardBgProcess' => $baseDir . '/modules/pro/advanced-leave/includes/Forward/LeaveCarryForwardBgProcess.php',
    'WeDevs\\AdvancedLeave\\Forward\\LeaveForwardListTable' => $baseDir . '/modules/pro/advanced-leave/includes/Forward/LeaveForwardListTable.php',
    'WeDevs\\AdvancedLeave\\Halfday\\Halfday' => $baseDir . '/modules/pro/advanced-leave/includes/Halfday/Halfday.php',
    'WeDevs\\AdvancedLeave\\Multilevel\\ForwardedLeaveRequest' => $baseDir . '/modules/pro/advanced-leave/includes/Multilevel/ForwardedLeaveRequest.php',
    'WeDevs\\AdvancedLeave\\Multilevel\\Multilevel' => $baseDir . '/modules/pro/advanced-leave/includes/Multilevel/Multilevel.php',
    'WeDevs\\AdvancedLeave\\Segregation\\Segregation' => $baseDir . '/modules/pro/advanced-leave/includes/Segregation/Segregation.php',
    'WeDevs\\AdvancedLeave\\Unpaid\\LeaveUnpaidListTable' => $baseDir . '/modules/pro/advanced-leave/includes/Unpaid/LeaveUnpaidListTable.php',
    'WeDevs\\AdvancedLeave\\Unpaid\\Unpaid' => $baseDir . '/modules/pro/advanced-leave/includes/Unpaid/Unpaid.php',
    'WeDevs\\AssetManagement\\AdminMenu' => $baseDir . '/modules/hrm/asset-management/includes/AdminMenu.php',
    'WeDevs\\AssetManagement\\AjaxHandler' => $baseDir . '/modules/hrm/asset-management/includes/AjaxHandler.php',
    'WeDevs\\AssetManagement\\AllottmentListTable' => $baseDir . '/modules/hrm/asset-management/includes/AllottmentListTable.php',
    'WeDevs\\AssetManagement\\AssetsListTable' => $baseDir . '/modules/hrm/asset-management/includes/AssetsListTable.php',
    'WeDevs\\AssetManagement\\EmployeeAssetApprove' => $baseDir . '/modules/hrm/asset-management/includes/EmployeeAssetApprove.php',
    'WeDevs\\AssetManagement\\EmployeeAssetOverdue' => $baseDir . '/modules/hrm/asset-management/includes/EmployeeAssetOverdue.php',
    'WeDevs\\AssetManagement\\EmployeeAssetRequest' => $baseDir . '/modules/hrm/asset-management/includes/EmployeeAssetRequest.php',
    'WeDevs\\AssetManagement\\EmployeeAssetRequestReject' => $baseDir . '/modules/hrm/asset-management/includes/EmployeeAssetRequestReject.php',
    'WeDevs\\AssetManagement\\FormHandler' => $baseDir . '/modules/hrm/asset-management/includes/FormHandler.php',
    'WeDevs\\AssetManagement\\Log' => $baseDir . '/modules/hrm/asset-management/includes/Log.php',
    'WeDevs\\AssetManagement\\RequestListTable' => $baseDir . '/modules/hrm/asset-management/includes/RequestListTable.php',
    'WeDevs\\Attendance\\ActionFilter' => $baseDir . '/modules/hrm/attendance/includes/ActionFilter.php',
    'WeDevs\\Attendance\\Admin' => $baseDir . '/modules/hrm/attendance/includes/Admin.php',
    'WeDevs\\Attendance\\Ajax' => $baseDir . '/modules/hrm/attendance/includes/Ajax.php',
    'WeDevs\\Attendance\\Api\\AttendanceController' => $baseDir . '/modules/hrm/attendance/includes/Api/AttendanceController.php',
    'WeDevs\\Attendance\\Assets' => $baseDir . '/modules/hrm/attendance/includes/Assets.php',
    'WeDevs\\Attendance\\AssignedShiftEmployees' => $baseDir . '/modules/hrm/attendance/includes/AssignedShiftEmployees.php',
    'WeDevs\\Attendance\\AttendanceListTable' => $baseDir . '/modules/hrm/attendance/includes/AttendanceListTable.php',
    'WeDevs\\Attendance\\AttendanceReportEmployeeBased' => $baseDir . '/modules/hrm/attendance/includes/AttendanceReportEmployeeBased.php',
    'WeDevs\\Attendance\\AttendanceShifts' => $baseDir . '/modules/hrm/attendance/includes/AttendanceShifts.php',
    'WeDevs\\Attendance\\AttendanceSingleListTable' => $baseDir . '/modules/hrm/attendance/includes/AttendanceSingleListTable.php',
    'WeDevs\\Attendance\\Attendance_Controller' => $baseDir . '/modules/hrm/attendance/includes/api/prev-class-attendance-controller.php',
    'WeDevs\\Attendance\\Emails\\AttendanceReminder' => $baseDir . '/modules/hrm/attendance/includes/Emails/AttendanceReminder.php',
    'WeDevs\\Attendance\\Emails\\Emailer' => $baseDir . '/modules/hrm/attendance/includes/Emails/Emailer.php',
    'WeDevs\\Attendance\\FormHandler' => $baseDir . '/modules/hrm/attendance/includes/FormHandler.php',
    'WeDevs\\Attendance\\Frontend' => $baseDir . '/modules/hrm/attendance/includes/Frontend.php',
    'WeDevs\\Attendance\\Install' => $baseDir . '/modules/hrm/attendance/includes/Install.php',
    'WeDevs\\Attendance\\IpUtils' => $baseDir . '/modules/hrm/attendance/includes/IpUtils.php',
    'WeDevs\\Attendance\\Log' => $baseDir . '/modules/hrm/attendance/includes/Log.php',
    'WeDevs\\Attendance\\Notification' => $baseDir . '/modules/hrm/attendance/includes/Notification.php',
    'WeDevs\\Attendance\\Queries\\AttendanceByDate' => $baseDir . '/modules/hrm/attendance/includes/Queries/AttendanceByDate.php',
    'WeDevs\\Attendance\\REST_API' => $baseDir . '/modules/hrm/attendance/includes/REST_API.php',
    'WeDevs\\Attendance\\Updates' => $baseDir . '/modules/hrm/attendance/includes/Updates.php',
    'WeDevs\\Attendance\\Updates\\BP\\ERP_Att_Migrate_Attendance' => $baseDir . '/modules/hrm/attendance/includes/Updates/BP/ERP_Att_Migrate_Attendance.php',
    'WeDevs\\Attendance\\Widgets' => $baseDir . '/modules/hrm/attendance/includes/Widgets.php',
    'WeDevs\\AwesomeSupport\\Import' => $baseDir . '/modules/pro/awesome-support/includes/Import.php',
    'WeDevs\\AwesomeSupport\\Settings' => $baseDir . '/modules/pro/awesome-support/includes/Settings.php',
    'WeDevs\\AwesomeSupport\\Widget' => $baseDir . '/modules/pro/awesome-support/includes/Widget.php',
    'WeDevs\\CustomFieldBuilder\\API\\CustomFieldBuilderController' => $baseDir . '/modules/hrm/custom-field-builder/includes/API/CustomFieldBuilderController.php',
    'WeDevs\\CustomFieldBuilder\\API\\REST_API' => $baseDir . '/modules/hrm/custom-field-builder/includes/API/REST_API.php',
    'WeDevs\\CustomFieldBuilder\\Classes\\Admin' => $baseDir . '/modules/hrm/custom-field-builder/includes/Classes/Admin.php',
    'WeDevs\\CustomFieldBuilder\\Classes\\Assets' => $baseDir . '/modules/hrm/custom-field-builder/includes/Classes/Assets.php',
    'WeDevs\\Deals\\Admin' => $baseDir . '/modules/crm/deals/includes/Admin.php',
    'WeDevs\\Deals\\Deal_Ajax' => $baseDir . '/modules/crm/deals/includes/Deal_Ajax.php',
    'WeDevs\\Deals\\Deals' => $baseDir . '/modules/crm/deals/includes/Deals.php',
    'WeDevs\\Deals\\Frontend' => $baseDir . '/modules/crm/deals/includes/Frontend.php',
    'WeDevs\\Deals\\Helpers' => $baseDir . '/modules/crm/deals/includes/Helpers.php',
    'WeDevs\\Deals\\Hooks' => $baseDir . '/modules/crm/deals/includes/Hooks.php',
    'WeDevs\\Deals\\Log' => $baseDir . '/modules/crm/deals/includes/Log.php',
    'WeDevs\\Deals\\Models\\Activity' => $baseDir . '/modules/crm/deals/includes/Models/Activity.php',
    'WeDevs\\Deals\\Models\\ActivityType' => $baseDir . '/modules/crm/deals/includes/Models/ActivityType.php',
    'WeDevs\\Deals\\Models\\Agent' => $baseDir . '/modules/crm/deals/includes/Models/Agent.php',
    'WeDevs\\Deals\\Models\\Attachment' => $baseDir . '/modules/crm/deals/includes/Models/Attachment.php',
    'WeDevs\\Deals\\Models\\Competitor' => $baseDir . '/modules/crm/deals/includes/Models/Competitor.php',
    'WeDevs\\Deals\\Models\\Deal' => $baseDir . '/modules/crm/deals/includes/Models/Deal.php',
    'WeDevs\\Deals\\Models\\Email' => $baseDir . '/modules/crm/deals/includes/Models/Email.php',
    'WeDevs\\Deals\\Models\\LostReason' => $baseDir . '/modules/crm/deals/includes/Models/LostReason.php',
    'WeDevs\\Deals\\Models\\Note' => $baseDir . '/modules/crm/deals/includes/Models/Note.php',
    'WeDevs\\Deals\\Models\\Participant' => $baseDir . '/modules/crm/deals/includes/Models/Participant.php',
    'WeDevs\\Deals\\Models\\Pipeline' => $baseDir . '/modules/crm/deals/includes/Models/Pipeline.php',
    'WeDevs\\Deals\\Models\\PipelineStage' => $baseDir . '/modules/crm/deals/includes/Models/PipelineStage.php',
    'WeDevs\\Deals\\Models\\StageHistory' => $baseDir . '/modules/crm/deals/includes/Models/StageHistory.php',
    'WeDevs\\Deals\\Shortcodes' => $baseDir . '/modules/crm/deals/includes/Shortcodes.php',
    'WeDevs\\Deals\\Statistics' => $baseDir . '/modules/crm/deals/includes/Statistics.php',
    'WeDevs\\DocumentManager\\API\\DocumentController' => $baseDir . '/modules/hrm/document-manager/includes/API/DocumentController.php',
    'WeDevs\\DocumentManager\\AjaxHandler' => $baseDir . '/modules/hrm/document-manager/includes/AjaxHandler.php',
    'WeDevs\\DocumentManager\\AjaxHandlerBack' => $baseDir . '/modules/hrm/document-manager/includes/class-doc-ajax_back.php',
    'WeDevs\\DocumentManager\\DocLog' => $baseDir . '/modules/hrm/document-manager/includes/DocLog.php',
    'WeDevs\\DocumentManager\\DocumentInstaller' => $baseDir . '/modules/hrm/document-manager/includes/DocumentInstaller.php',
    'WeDevs\\DocumentManager\\Emails\\DmFileShareNotification' => $baseDir . '/modules/hrm/document-manager/includes/Emails/DmFileShareNotification.php',
    'WeDevs\\DocumentManager\\Emails\\Emailer' => $baseDir . '/modules/hrm/document-manager/includes/Emails/Emailer.php',
    'WeDevs\\DocumentManager\\FormHandler' => $baseDir . '/modules/hrm/document-manager/includes/FormHandler.php',
    'WeDevs\\DocumentManager\\Settings' => $baseDir . '/modules/hrm/document-manager/includes/class-settings.php',
    'WeDevs\\DocumentManager\\Updates' => $baseDir . '/modules/hrm/document-manager/includes/Updates.php',
    'WeDevs\\ERP\\Accounting\\Reimbursement\\Admin' => $baseDir . '/modules/hrm/reimbursement/deprecated/includes/Admin.php',
    'WeDevs\\ERP\\Accounting\\Reimbursement\\ReimbursementTransactionListTable' => $baseDir . '/modules/hrm/reimbursement/deprecated/includes/ReimbursementTransactionListTable.php',
    'WeDevs\\ERP\\Hubspot\\AdminMenu' => $baseDir . '/modules/pro/hubspot/includes/AdminMenu.php',
    'WeDevs\\ERP\\Hubspot\\AjaxHandler' => $baseDir . '/modules/pro/hubspot/includes/AjaxHandler.php',
    'WeDevs\\ERP\\Hubspot\\Http_Client' => $baseDir . '/modules/pro/hubspot/includes/Http_Client.php',
    'WeDevs\\ERP\\Hubspot\\Hubspot' => $baseDir . '/modules/pro/hubspot/includes/Hubspot.php',
    'WeDevs\\ERP\\Hubspot\\Hubspot_Integration' => $baseDir . '/modules/pro/hubspot/includes/Hubspot_Integration.php',
    'WeDevs\\ERP\\Mailchimp\\AdminMenu' => $baseDir . '/modules/pro/mailchimp/includes/AdminMenu.php',
    'WeDevs\\ERP\\Mailchimp\\AjaxHandler' => $baseDir . '/modules/pro/mailchimp/includes/AjaxHandler.php',
    'WeDevs\\ERP\\Mailchimp\\Http_Client' => $baseDir . '/modules/pro/mailchimp/includes/Http_Client.php',
    'WeDevs\\ERP\\Mailchimp\\Mailchimp' => $baseDir . '/modules/pro/mailchimp/includes/Mailchimp.php',
    'WeDevs\\ERP\\Mailchimp\\Mailchimp_Integration' => $baseDir . '/modules/pro/mailchimp/includes/Mailchimp_Integration.php',
    'WeDevs\\ERP\\Mailchimp\\Sync_Handler' => $baseDir . '/modules/pro/mailchimp/includes/Sync_Handler.php',
    'WeDevs\\ERP\\Mailchimp\\Webhook_Controller' => $baseDir . '/modules/pro/mailchimp/includes/Webhook_Controller.php',
    'WeDevs\\ERP\\Mailchimp\\Webhook_Manager' => $baseDir . '/modules/pro/mailchimp/includes/Webhook_Manager.php',
    'WeDevs\\ERP\\SMS\\Clickatell' => $baseDir . '/modules/hrm/sms-notification/includes/Clickatell.php',
    'WeDevs\\ERP\\SMS\\GatewayHandler' => $baseDir . '/modules/hrm/sms-notification/includes/GatewayHandler.php',
    'WeDevs\\ERP\\SMS\\GatewayInterface' => $baseDir . '/modules/hrm/sms-notification/includes/GatewayInterface.php',
    'WeDevs\\ERP\\SMS\\Hoiio' => $baseDir . '/modules/hrm/sms-notification/includes/Hoiio.php',
    'WeDevs\\ERP\\SMS\\Infobip' => $baseDir . '/modules/hrm/sms-notification/includes/Infobip.php',
    'WeDevs\\ERP\\SMS\\Intellisms' => $baseDir . '/modules/hrm/sms-notification/includes/Intellisms.php',
    'WeDevs\\ERP\\SMS\\Nexmo' => $baseDir . '/modules/hrm/sms-notification/includes/Nexmo.php',
    'WeDevs\\ERP\\SMS\\SmsSettings' => $baseDir . '/modules/hrm/sms-notification/includes/SmsSettings.php',
    'WeDevs\\ERP\\SMS\\Smsglobal' => $baseDir . '/modules/hrm/sms-notification/includes/Smsglobal.php',
    'WeDevs\\ERP\\SMS\\Twilio' => $baseDir . '/modules/hrm/sms-notification/includes/Twilio.php',
    'WeDevs\\ERP\\Salesforce\\AdminMenu' => $baseDir . '/modules/pro/salesforce/includes/AdminMenu.php',
    'WeDevs\\ERP\\Salesforce\\AjaxHandler' => $baseDir . '/modules/pro/salesforce/includes/AjaxHandler.php',
    'WeDevs\\ERP\\Salesforce\\HttpClient' => $baseDir . '/modules/pro/salesforce/includes/HttpClient.php',
    'WeDevs\\ERP\\Salesforce\\Salesforce' => $baseDir . '/modules/pro/salesforce/includes/Salesforce.php',
    'WeDevs\\ERP\\Salesforce\\SalesforceIntegration' => $baseDir . '/modules/pro/salesforce/includes/SalesforceIntegration.php',
    'WeDevs\\ERP\\Workflow\\AdminMenu' => $baseDir . '/modules/hrm/workflow/includes/AdminMenu.php',
    'WeDevs\\ERP\\Workflow\\AjaxHandler' => $baseDir . '/modules/hrm/workflow/includes/AjaxHandler.php',
    'WeDevs\\ERP\\Workflow\\Models\\Action' => $baseDir . '/modules/hrm/workflow/includes/Models/Action.php',
    'WeDevs\\ERP\\Workflow\\Models\\Condition' => $baseDir . '/modules/hrm/workflow/includes/Models/Condition.php',
    'WeDevs\\ERP\\Workflow\\Models\\Email' => $baseDir . '/modules/hrm/workflow/includes/Models/Email.php',
    'WeDevs\\ERP\\Workflow\\Models\\Log' => $baseDir . '/modules/hrm/workflow/includes/Models/Log.php',
    'WeDevs\\ERP\\Workflow\\Models\\Workflow' => $baseDir . '/modules/hrm/workflow/includes/Models/Workflow.php',
    'WeDevs\\ERP\\Workflow\\Workflows' => $baseDir . '/modules/hrm/workflow/includes/Workflows.php',
    'WeDevs\\ERP\\Workflow\\WorkflowsListTable' => $baseDir . '/modules/hrm/workflow/includes/WorkflowsListTable.php',
    'WeDevs\\ERP\\Zendesk\\Ajax' => $baseDir . '/modules/pro/zendesk/includes/Ajax.php',
    'WeDevs\\ERP\\Zendesk\\Settings' => $baseDir . '/modules/pro/zendesk/includes/Settings.php',
    'WeDevs\\ERP\\Zendesk\\User' => $baseDir . '/modules/pro/zendesk/includes/User.php',
    'WeDevs\\ERP_PRO\\Accounting\\Inventory\\Module' => $baseDir . '/modules/accounting/inventory/Module.php',
    'WeDevs\\ERP_PRO\\Accounting\\woocommerce\\Module' => $baseDir . '/modules/accounting/woocommerce/Module.php',
    'WeDevs\\ERP_PRO\\Admin\\Admin' => $baseDir . '/includes/Admin/Admin.php',
    'WeDevs\\ERP_PRO\\Admin\\Ajax' => $baseDir . '/includes/Admin/Ajax.php',
    'WeDevs\\ERP_PRO\\Admin\\ComposerUpgradeNotice' => $baseDir . '/includes/Admin/ComposerUpgradeNotice.php',
    'WeDevs\\ERP_PRO\\Admin\\Menu\\Extensions' => $baseDir . '/includes/Admin/Menu/Extensions.php',
    'WeDevs\\ERP_PRO\\Admin\\Update' => $baseDir . '/includes/Admin/Update.php',
    'WeDevs\\ERP_PRO\\CRM\\Deals\\Module' => $baseDir . '/modules/crm/deals/Module.php',
    'WeDevs\\ERP_PRO\\Feature\\Accounting\\Api\\PurchaseReturnController' => $baseDir . '/includes/Feature/Accounting/Api/PurchaseReturnController.php',
    'WeDevs\\ERP_PRO\\Feature\\Accounting\\Api\\ReportsController' => $baseDir . '/includes/Feature/Accounting/Api/ReportsController.php',
    'WeDevs\\ERP_PRO\\Feature\\Accounting\\Api\\Rest_Controller' => $baseDir . '/includes/Feature/Accounting/Api/Rest_Controller.php',
    'WeDevs\\ERP_PRO\\Feature\\Accounting\\Api\\SalesReturnController' => $baseDir . '/includes/Feature/Accounting/Api/SalesReturnController.php',
    'WeDevs\\ERP_PRO\\Feature\\Accounting\\Base' => $baseDir . '/includes/Feature/Accounting/Base.php',
    'WeDevs\\ERP_PRO\\Feature\\Accounting\\Core\\Assets' => $baseDir . '/includes/Feature/Accounting/Core/Assets.php',
    'WeDevs\\ERP_PRO\\Feature\\Accounting\\Core\\Core' => $baseDir . '/includes/Feature/Accounting/Core/Core.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Base' => $baseDir . '/includes/Feature/CRM/Base.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Core\\Core' => $baseDir . '/includes/Feature/CRM/Core/Core.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Life_Stages\\Ajax' => $baseDir . '/includes/Feature/CRM/Life_Stages/Ajax.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Life_Stages\\Helpers' => $baseDir . '/includes/Feature/CRM/Life_Stages/Helpers.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Life_Stages\\Hooks' => $baseDir . '/includes/Feature/CRM/Life_Stages/Hooks.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Life_Stages\\Main' => $baseDir . '/includes/Feature/CRM/Life_Stages/Main.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Tasks\\Admin' => $baseDir . '/includes/Feature/CRM/Tasks/Admin.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Tasks\\Ajax' => $baseDir . '/includes/Feature/CRM/Tasks/Ajax.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Tasks\\Helpers' => $baseDir . '/includes/Feature/CRM/Tasks/Helpers.php',
    'WeDevs\\ERP_PRO\\Feature\\CRM\\Tasks\\Main' => $baseDir . '/includes/Feature/CRM/Tasks/Main.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Base' => $baseDir . '/includes/Feature/HRM/Base.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Core\\Assets' => $baseDir . '/includes/Feature/HRM/Core/Assets.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Core\\Core' => $baseDir . '/includes/Feature/HRM/Core/Core.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Digest_Email\\Main' => $baseDir . '/includes/Feature/HRM/Digest_Email/Main.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Org_Chart\\Ajax' => $baseDir . '/includes/Feature/HRM/Org_Chart/Ajax.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Org_Chart\\Helpers' => $baseDir . '/includes/Feature/HRM/Org_Chart/Helpers.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Org_Chart\\Org_Chart' => $baseDir . '/includes/Feature/HRM/Org_Chart/Org_Chart.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Remote_Work\\Ajax' => $baseDir . '/includes/Feature/HRM/Requests/Remote_Work/Ajax.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Remote_Work\\Hooks' => $baseDir . '/includes/Feature/HRM/Requests/Remote_Work/Hooks.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Remote_Work\\Main' => $baseDir . '/includes/Feature/HRM/Requests/Remote_Work/Main.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Remote_Work\\Settings' => $baseDir . '/includes/Feature/HRM/Requests/Remote_Work/Settings.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Requests' => $baseDir . '/includes/Feature/HRM/Requests/Requests.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Resignation\\Ajax' => $baseDir . '/includes/Feature/HRM/Requests/Resignation/Ajax.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Resignation\\Email' => $baseDir . '/includes/Feature/HRM/Requests/Resignation/Email.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Resignation\\Hooks' => $baseDir . '/includes/Feature/HRM/Requests/Resignation/Hooks.php',
    'WeDevs\\ERP_PRO\\Feature\\HRM\\Requests\\Resignation\\Main' => $baseDir . '/includes/Feature/HRM/Requests/Resignation/Main.php',
    'WeDevs\\ERP_PRO\\HRM\\AssetManagement\\Module' => $baseDir . '/modules/hrm/asset-management/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\Attendance\\Module' => $baseDir . '/modules/hrm/attendance/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\CustomFieldBuilder\\Module' => $baseDir . '/modules/hrm/custom-field-builder/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\DocumentManager\\Module' => $baseDir . '/modules/hrm/document-manager/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\HrTraining\\Module' => $baseDir . '/modules/hrm/hr-training/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\Payroll\\Module' => $baseDir . '/modules/hrm/payroll/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\Recruitment\\Module' => $baseDir . '/modules/hrm/recruitment/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\Reimbursement\\Module' => $baseDir . '/modules/hrm/reimbursement/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\SmsNotification\\Module' => $baseDir . '/modules/hrm/sms-notification/Module.php',
    'WeDevs\\ERP_PRO\\HRM\\Workflow\\Module' => $baseDir . '/modules/hrm/workflow/Module.php',
    'WeDevs\\ERP_PRO\\Install\\Installer' => $baseDir . '/includes/Install/Installer.php',
    'WeDevs\\ERP_PRO\\Module' => $baseDir . '/includes/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\AdvancedLeave\\Module' => $baseDir . '/modules/pro/advanced-leave/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\AwesomeSupport\\Module' => $baseDir . '/modules/pro/awesome-support/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\GravityForms\\Gravity_Forms' => $baseDir . '/modules/pro/gravity_forms/gravityforms.php',
    'WeDevs\\ERP_PRO\\PRO\\GravityForms\\Module' => $baseDir . '/modules/pro/gravity_forms/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\HelpScout\\Module' => $baseDir . '/modules/pro/help-scout/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\HrFrontend\\Module' => $baseDir . '/modules/pro/hr-frontend/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\Hubspot\\Module' => $baseDir . '/modules/pro/hubspot/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\Mailchimp\\Module' => $baseDir . '/modules/pro/mailchimp/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\Salesforce\\Module' => $baseDir . '/modules/pro/salesforce/Module.php',
    'WeDevs\\ERP_PRO\\PRO\\Zendesk\\Module' => $baseDir . '/modules/pro/zendesk/Module.php',
    'WeDevs\\ERP_PRO\\REST\\ModulesController' => $baseDir . '/includes/REST/ModulesController.php',
    'WeDevs\\ERP_PRO\\Traits\\Singleton' => $baseDir . '/includes/Traits/Singleton.php',
    'WeDevs\\ERP_PRO\\Updates\\BP\\Recruitment_Experience_Type_Migrator' => $baseDir . '/includes/Updates/BP/Recruitment_Experience_Type_Migrator.php',
    'WeDevs\\ERP_PRO\\Updates\\Update_1_2_8' => $baseDir . '/includes/Updates/Update-1.2.8.php',
    'WeDevs\\ERP_PRO\\Updates\\Updater' => $baseDir . '/includes/Updates/Updater.php',
    'WeDevs\\Erp_Inventory\\Api\\InventoryController' => $baseDir . '/modules/accounting/inventory/includes/Api/InventoryController.php',
    'WeDevs\\Erp_Inventory\\Api\\InventoryReportController' => $baseDir . '/modules/accounting/inventory/includes/Api/InventoryReportController.php',
    'WeDevs\\Erp_Inventory\\Api\\REST_API' => $baseDir . '/modules/accounting/inventory/includes/Api/REST_API.php',
    'WeDevs\\Erp_Inventory\\Classes\\Admin' => $baseDir . '/modules/accounting/inventory/includes/Classes/Admin.php',
    'WeDevs\\Erp_Inventory\\Classes\\Assets' => $baseDir . '/modules/accounting/inventory/includes/Classes/Assets.php',
    'WeDevs\\Erp_Inventory\\Classes\\ERP_Inventory_i18n' => $baseDir . '/modules/accounting/inventory/includes/Classes/ERP_Inventory_i18n.php',
    'WeDevs\\HelpScout\\AdminMenu' => $baseDir . '/modules/pro/help-scout/includes/AdminMenu.php',
    'WeDevs\\HelpScout\\Ajax' => $baseDir . '/modules/pro/help-scout/includes/Ajax.php',
    'WeDevs\\HelpScout\\Api\\HelpscoutCustomerController' => $baseDir . '/modules/pro/help-scout/includes/Api/HelpscoutCustomerController.php',
    'WeDevs\\HelpScout\\Customer' => $baseDir . '/modules/pro/help-scout/includes/Customer.php',
    'WeDevs\\HelpScout\\Deals' => $baseDir . '/modules/pro/help-scout/includes/Deals.php',
    'WeDevs\\HelpScout\\EndPoint' => $baseDir . '/modules/pro/help-scout/includes/EndPoint.php',
    'WeDevs\\HelpScout\\Request' => $baseDir . '/modules/pro/help-scout/includes/Request.php',
    'WeDevs\\HelpScout\\Settings' => $baseDir . '/modules/pro/help-scout/includes/Settings.php',
    'WeDevs\\HelpScout\\User' => $baseDir . '/modules/pro/help-scout/includes/User.php',
    'WeDevs\\HrFrontend\\DashboardSettings' => $baseDir . '/modules/pro/hr-frontend/includes/DashboardSettings.php',
    'WeDevs\\HrFrontend\\HrFrontendI18n' => $baseDir . '/modules/pro/hr-frontend/includes/HrFrontendI18n.php',
    'WeDevs\\HrFrontend\\Rewrites' => $baseDir . '/modules/pro/hr-frontend/includes/Rewrites.php',
    'WeDevs\\HrTraining\\Ajax' => $baseDir . '/modules/hrm/hr-training/includes/Ajax.php',
    'WeDevs\\HrTraining\\Emails\\AfterAssignTraining' => $baseDir . '/modules/hrm/hr-training/includes/Emails/AfterAssignTraining.php',
    'WeDevs\\HrTraining\\Emails\\Emailer' => $baseDir . '/modules/hrm/hr-training/includes/Emails/Emailer.php',
    'WeDevs\\HrTraining\\Log' => $baseDir . '/modules/hrm/hr-training/includes/Log.php',
    'WeDevs\\HrTraining\\TrainingEmployee' => $baseDir . '/modules/hrm/hr-training/includes/TrainingEmployee.php',
    'WeDevs\\HrTraining\\TrainingPostType' => $baseDir . '/modules/hrm/hr-training/includes/TrainingPostType.php',
    'WeDevs\\PaymentGateway\\Gateways\\Paypal' => $baseDir . '/modules/accounting/payment-gateway/includes/Gateways/Paypal.php',
    'WeDevs\\PaymentGateway\\Gateways\\Stripe' => $baseDir . '/modules/accounting/payment-gateway/includes/Gateways/Stripe.php',
    'WeDevs\\PaymentGateway\\GeneralSettings' => $baseDir . '/modules/accounting/payment-gateway/includes/GeneralSettings.php',
    'WeDevs\\PaymentGateway\\PaymentGatewayHandler' => $baseDir . '/modules/accounting/payment-gateway/includes/PaymentGatewayHandler.php',
    'WeDevs\\PaymentGateway\\PaymentGatewaySettings' => $baseDir . '/modules/accounting/payment-gateway/includes/PaymentGatewaySettings.php',
    'WeDevs\\Payroll\\AdminMenu' => $baseDir . '/modules/hrm/payroll/includes/AdminMenu.php',
    'WeDevs\\Payroll\\Admin\\SetupWizard' => $baseDir . '/modules/hrm/payroll/includes/Admin/SetupWizard.php',
    'WeDevs\\Payroll\\AjaxHandler' => $baseDir . '/modules/hrm/payroll/includes/AjaxHandler.php',
    'WeDevs\\Payroll\\CLI' => $baseDir . '/modules/hrm/payroll/includes/CLI.php',
    'WeDevs\\Payroll\\Emails\\EmailPayslip' => $baseDir . '/modules/hrm/payroll/includes/Emails/EmailPayslip.php',
    'WeDevs\\Payroll\\Emails\\EmailPayslipBulk' => $baseDir . '/modules/hrm/payroll/includes/Emails/EmailPayslipBulk.php',
    'WeDevs\\Payroll\\Emails\\EmailPayslipSingle' => $baseDir . '/modules/hrm/payroll/includes/Emails/EmailPayslipSingle.php',
    'WeDevs\\Payroll\\Emails\\Emailer' => $baseDir . '/modules/hrm/payroll/includes/Emails/Emailer.php',
    'WeDevs\\Payroll\\FormHandler' => $baseDir . '/modules/hrm/payroll/includes/FormHandler.php',
    'WeDevs\\Payroll\\Installer' => $baseDir . '/modules/hrm/payroll/includes/Installer.php',
    'WeDevs\\Payroll\\PayrunListTable' => $baseDir . '/modules/hrm/payroll/includes/PayrunListTable.php',
    'WeDevs\\Payroll\\Settings' => $baseDir . '/modules/hrm/payroll/includes/Settings.php',
    'WeDevs\\Payroll\\Updates' => $baseDir . '/modules/hrm/payroll/includes/Updates.php',
    'WeDevs\\Recruitment\\AdminMenu' => $baseDir . '/modules/hrm/recruitment/includes/AdminMenu.php',
    'WeDevs\\Recruitment\\AjaxHandler' => $baseDir . '/modules/hrm/recruitment/includes/AjaxHandler.php',
    'WeDevs\\Recruitment\\Api\\RecruitmentController' => $baseDir . '/modules/hrm/recruitment/includes/Api/RecruitmentController.php',
    'WeDevs\\Recruitment\\Emails\\CandidateReport' => $baseDir . '/modules/hrm/recruitment/includes/Emails/CandidateReport.php',
    'WeDevs\\Recruitment\\Emails\\ConfirmationOfSuccessfulSubmission' => $baseDir . '/modules/hrm/recruitment/includes/Emails/ConfirmationOfSuccessfulSubmission.php',
    'WeDevs\\Recruitment\\Emails\\Emailer' => $baseDir . '/modules/hrm/recruitment/includes/Emails/Emailer.php',
    'WeDevs\\Recruitment\\Emails\\NewInterview' => $baseDir . '/modules/hrm/recruitment/includes/Emails/NewInterview.php',
    'WeDevs\\Recruitment\\Emails\\NewJobApplicationSubmitted' => $baseDir . '/modules/hrm/recruitment/includes/Emails/NewJobApplicationSubmitted.php',
    'WeDevs\\Recruitment\\Emails\\NewTodo' => $baseDir . '/modules/hrm/recruitment/includes/Emails/NewTodo.php',
    'WeDevs\\Recruitment\\Emails\\OpeningReport' => $baseDir . '/modules/hrm/recruitment/includes/Emails/OpeningReport.php',
    'WeDevs\\Recruitment\\FormHandler' => $baseDir . '/modules/hrm/recruitment/includes/FormHandler.php',
    'WeDevs\\Recruitment\\HrQuestionnaire' => $baseDir . '/modules/hrm/recruitment/includes/HrQuestionnaire.php',
    'WeDevs\\Recruitment\\Installer' => $baseDir . '/modules/hrm/recruitment/includes/Installer.php',
    'WeDevs\\Recruitment\\JobSchemaManager' => $baseDir . '/modules/hrm/recruitment/includes/JobSchemaManager.php',
    'WeDevs\\Recruitment\\JobSeekerListTable' => $baseDir . '/modules/hrm/recruitment/includes/JobSeekerListTable.php',
    'WeDevs\\Recruitment\\Recruitment' => $baseDir . '/modules/hrm/recruitment/includes/Recruitment.php',
    'WeDevs\\Recruitment\\Updates' => $baseDir . '/modules/hrm/recruitment/includes/Updates.php',
    'WeDevs\\Reimbursement\\Api\\EmployeeRequestsController' => $baseDir . '/modules/hrm/reimbursement/includes/Api/EmployeeRequestsController.php',
    'WeDevs\\Reimbursement\\Api\\PeopleTrnController' => $baseDir . '/modules/hrm/reimbursement/includes/Api/PeopleTrnController.php',
    'WeDevs\\Reimbursement\\Api\\REST_API' => $baseDir . '/modules/hrm/reimbursement/includes/Api/REST_API.php',
    'WeDevs\\Reimbursement\\Classes\\Admin' => $baseDir . '/modules/hrm/reimbursement/includes/classes/Admin.php',
    'WeDevs\\Reimbursement\\Classes\\Assets' => $baseDir . '/modules/hrm/reimbursement/includes/classes/Assets.php',
    'WeDevs\\Reimbursement\\Classes\\ReimbursementI18n' => $baseDir . '/modules/hrm/reimbursement/includes/classes/ReimbursementI18n.php',
    'WeDevs\\Reimbursement\\Classes\\Updates' => $baseDir . '/modules/hrm/reimbursement/includes/classes/Updates.php',
    'WeDevs\\Reimbursement\\Updates\\BP\\OldReimbRequestMigration' => $baseDir . '/modules/hrm/reimbursement/includes/classes/updates/bp/OldReimbRequestMigration.php',
    'Wikimedia\\Composer\\Merge\\V2\\ExtraPackage' => $vendorDir . '/wikimedia/composer-merge-plugin/src/ExtraPackage.php',
    'Wikimedia\\Composer\\Merge\\V2\\Logger' => $vendorDir . '/wikimedia/composer-merge-plugin/src/Logger.php',
    'Wikimedia\\Composer\\Merge\\V2\\MergePlugin' => $vendorDir . '/wikimedia/composer-merge-plugin/src/MergePlugin.php',
    'Wikimedia\\Composer\\Merge\\V2\\MissingFileException' => $vendorDir . '/wikimedia/composer-merge-plugin/src/MissingFileException.php',
    'Wikimedia\\Composer\\Merge\\V2\\MultiConstraint' => $vendorDir . '/wikimedia/composer-merge-plugin/src/MultiConstraint.php',
    'Wikimedia\\Composer\\Merge\\V2\\NestedArray' => $vendorDir . '/wikimedia/composer-merge-plugin/src/NestedArray.php',
    'Wikimedia\\Composer\\Merge\\V2\\PluginState' => $vendorDir . '/wikimedia/composer-merge-plugin/src/PluginState.php',
    'Wikimedia\\Composer\\Merge\\V2\\StabilityFlags' => $vendorDir . '/wikimedia/composer-merge-plugin/src/StabilityFlags.php',
    'infobip\\api\\AbstractApiClient' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/AbstractApiClient.php',
    'infobip\\api\\client\\GetAccountBalance' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/GetAccountBalance.php',
    'infobip\\api\\client\\GetNumberContextLogs' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/GetNumberContextLogs.php',
    'infobip\\api\\client\\GetReceivedMessages' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/GetReceivedMessages.php',
    'infobip\\api\\client\\GetReceivedSmsLogs' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/GetReceivedSmsLogs.php',
    'infobip\\api\\client\\GetSentSmsDeliveryReports' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/GetSentSmsDeliveryReports.php',
    'infobip\\api\\client\\GetSentSmsLogs' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/GetSentSmsLogs.php',
    'infobip\\api\\client\\NumberContextNotify' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/NumberContextNotify.php',
    'infobip\\api\\client\\NumberContextQuery' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/NumberContextQuery.php',
    'infobip\\api\\client\\SendMultipleBinarySms' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleBinarySms.php',
    'infobip\\api\\client\\SendMultipleSmsBinaryAdvanced' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleSmsBinaryAdvanced.php',
    'infobip\\api\\client\\SendMultipleSmsTextual' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleSmsTextual.php',
    'infobip\\api\\client\\SendMultipleTextualSmsAdvanced' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/SendMultipleTextualSmsAdvanced.php',
    'infobip\\api\\client\\SendSingleBinarySms' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/SendSingleBinarySms.php',
    'infobip\\api\\client\\SendSingleTextualSms' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/client/SendSingleTextualSms.php',
    'infobip\\api\\configuration\\ApiKeyAuthConfiguration' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/configuration/ApiKeyAuthConfiguration.php',
    'infobip\\api\\configuration\\BasicAuthConfiguration' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/configuration/BasicAuthConfiguration.php',
    'infobip\\api\\configuration\\Configuration' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/configuration/Configuration.php',
    'infobip\\api\\configuration\\IbssoAuthConfiguration' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/configuration/IbssoAuthConfiguration.php',
    'infobip\\api\\model\\Destination' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/Destination.php',
    'infobip\\api\\model\\Error' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/Error.php',
    'infobip\\api\\model\\Price' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/Price.php',
    'infobip\\api\\model\\Status' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/Status.php',
    'infobip\\api\\model\\account\\AccountBalance' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/account/AccountBalance.php',
    'infobip\\api\\model\\nc\\Network' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/Network.php',
    'infobip\\api\\model\\nc\\logs\\GetNumberContextLogsExecuteContext' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs/GetNumberContextLogsExecuteContext.php',
    'infobip\\api\\model\\nc\\logs\\NumberContextLog' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs/NumberContextLog.php',
    'infobip\\api\\model\\nc\\logs\\NumberContextLogsResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/logs/NumberContextLogsResponse.php',
    'infobip\\api\\model\\nc\\notify\\NumberContextRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify/NumberContextRequest.php',
    'infobip\\api\\model\\nc\\notify\\NumberContextResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify/NumberContextResponse.php',
    'infobip\\api\\model\\nc\\notify\\NumberContextResponseDetails' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/notify/NumberContextResponseDetails.php',
    'infobip\\api\\model\\nc\\query\\NumberContextRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/query/NumberContextRequest.php',
    'infobip\\api\\model\\nc\\query\\NumberContextResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/query/NumberContextResponse.php',
    'infobip\\api\\model\\nc\\query\\NumberContextResponseDetails' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/nc/query/NumberContextResponseDetails.php',
    'infobip\\api\\model\\sms\\mo\\logs\\GetReceivedSmsLogsExecuteContext' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs/GetReceivedSmsLogsExecuteContext.php',
    'infobip\\api\\model\\sms\\mo\\logs\\MOLog' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs/MOLog.php',
    'infobip\\api\\model\\sms\\mo\\logs\\MOLogsResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/logs/MOLogsResponse.php',
    'infobip\\api\\model\\sms\\mo\\reports\\GetReceivedMessagesExecuteContext' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports/GetReceivedMessagesExecuteContext.php',
    'infobip\\api\\model\\sms\\mo\\reports\\MOReport' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports/MOReport.php',
    'infobip\\api\\model\\sms\\mo\\reports\\MOReportResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mo/reports/MOReportResponse.php',
    'infobip\\api\\model\\sms\\mt\\logs\\GetSentSmsLogsExecuteContext' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs/GetSentSmsLogsExecuteContext.php',
    'infobip\\api\\model\\sms\\mt\\logs\\SMSLog' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs/SMSLog.php',
    'infobip\\api\\model\\sms\\mt\\logs\\SMSLogsResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/logs/SMSLogsResponse.php',
    'infobip\\api\\model\\sms\\mt\\reports\\GetSentSmsDeliveryReportsExecuteContext' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports/GetSentSmsDeliveryReportsExecuteContext.php',
    'infobip\\api\\model\\sms\\mt\\reports\\SMSReport' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports/SMSReport.php',
    'infobip\\api\\model\\sms\\mt\\reports\\SMSReportResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/reports/SMSReportResponse.php',
    'infobip\\api\\model\\sms\\mt\\send\\Language' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/Language.php',
    'infobip\\api\\model\\sms\\mt\\send\\Message' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/Message.php',
    'infobip\\api\\model\\sms\\mt\\send\\SMSResponse' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/SMSResponse.php',
    'infobip\\api\\model\\sms\\mt\\send\\SMSResponseDetails' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/SMSResponseDetails.php',
    'infobip\\api\\model\\sms\\mt\\send\\Tracking' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/Tracking.php',
    'infobip\\api\\model\\sms\\mt\\send\\binary\\BinaryContent' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/BinaryContent.php',
    'infobip\\api\\model\\sms\\mt\\send\\binary\\SMSAdvancedBinaryRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/SMSAdvancedBinaryRequest.php',
    'infobip\\api\\model\\sms\\mt\\send\\binary\\SMSBinaryRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/SMSBinaryRequest.php',
    'infobip\\api\\model\\sms\\mt\\send\\binary\\SMSMultiBinaryRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/binary/SMSMultiBinaryRequest.php',
    'infobip\\api\\model\\sms\\mt\\send\\textual\\SMSAdvancedTextualRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual/SMSAdvancedTextualRequest.php',
    'infobip\\api\\model\\sms\\mt\\send\\textual\\SMSMultiTextualRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual/SMSMultiTextualRequest.php',
    'infobip\\api\\model\\sms\\mt\\send\\textual\\SMSTextualRequest' => $vendorDir . '/infobip/infobip-api-php-client/infobip/api/model/sms/mt/send/textual/SMSTextualRequest.php',
);
