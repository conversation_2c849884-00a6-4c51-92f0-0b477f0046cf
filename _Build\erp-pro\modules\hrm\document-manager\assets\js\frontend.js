/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./modules/hrm/document-manager/assets/src/frontend/main.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./modules/hrm/document-manager/assets/less/style.less":
/*!*************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/less/style.less ***!
  \*************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/less/style.less?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue":
/*!******************************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue ***!
  \******************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _document_manager_vue_vue_type_template_id_0c5cf9ac_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true& */ \"./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true&\");\n/* harmony import */ var _document_manager_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./document_manager.vue?vue&type=script&lang=js& */ \"./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\n  _document_manager_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  _document_manager_vue_vue_type_template_id_0c5cf9ac_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _document_manager_vue_vue_type_template_id_0c5cf9ac_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  \"0c5cf9ac\",\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=script&lang=js&":
/*!*******************************************************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../node_modules/babel-loader/lib!../../../../../../../node_modules/vue-loader/lib??vue-loader-options!./document_manager.vue?vue&type=script&lang=js& */ \"./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__[\"default\"] = (_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[\"default\"]); \n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true&":
/*!*************************************************************************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true& ***!
  \*************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_vue_vue_type_template_id_0c5cf9ac_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../node_modules/vue-loader/lib??vue-loader-options!./document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true& */ \"./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true&\");\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_vue_vue_type_template_id_0c5cf9ac_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"render\"]; });\n\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_vue_vue_type_template_id_0c5cf9ac_scoped_true___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"]; });\n\n\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue":
/*!**************************************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue ***!
  \**************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _document_manager_indexed_vue_vue_type_template_id_11a997dc___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./document_manager_indexed.vue?vue&type=template&id=11a997dc& */ \"./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=template&id=11a997dc&\");\n/* harmony import */ var _document_manager_indexed_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./document_manager_indexed.vue?vue&type=script&lang=js& */ \"./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport *//* harmony import */ var _document_manager_indexed_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./document_manager_indexed.vue?vue&type=style&index=0&lang=css& */ \"./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=style&index=0&lang=css&\");\n/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ \"./node_modules/vue-loader/lib/runtime/componentNormalizer.js\");\n\n\n\n\n\n\n/* normalize component */\n\nvar component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\n  _document_manager_indexed_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  _document_manager_indexed_vue_vue_type_template_id_11a997dc___WEBPACK_IMPORTED_MODULE_0__[\"render\"],\n  _document_manager_indexed_vue_vue_type_template_id_11a997dc___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"],\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (false) { var api; }\ncomponent.options.__file = \"modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue\"\n/* harmony default export */ __webpack_exports__[\"default\"] = (component.exports);\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=script&lang=js&":
/*!***************************************************************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../node_modules/babel-loader/lib!../../../../../../../node_modules/vue-loader/lib??vue-loader-options!./document_manager_indexed.vue?vue&type=script&lang=js& */ \"./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=script&lang=js&\");\n/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__[\"default\"] = (_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[\"default\"]); \n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=style&index=0&lang=css&":
/*!***********************************************************************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=style&index=0&lang=css& ***!
  \***********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_mini_css_extract_plugin_dist_loader_js_node_modules_css_loader_dist_cjs_js_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_sass_loader_lib_loader_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../node_modules/mini-css-extract-plugin/dist/loader.js!../../../../../../../node_modules/css-loader/dist/cjs.js!../../../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../node_modules/sass-loader/lib/loader.js!../../../../../../../node_modules/vue-loader/lib??vue-loader-options!./document_manager_indexed.vue?vue&type=style&index=0&lang=css& */ \"./node_modules/mini-css-extract-plugin/dist/loader.js!./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/sass-loader/lib/loader.js!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=style&index=0&lang=css&\");\n/* harmony import */ var _node_modules_mini_css_extract_plugin_dist_loader_js_node_modules_css_loader_dist_cjs_js_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_sass_loader_lib_loader_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_mini_css_extract_plugin_dist_loader_js_node_modules_css_loader_dist_cjs_js_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_sass_loader_lib_loader_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_mini_css_extract_plugin_dist_loader_js_node_modules_css_loader_dist_cjs_js_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_sass_loader_lib_loader_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if([\"default\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_mini_css_extract_plugin_dist_loader_js_node_modules_css_loader_dist_cjs_js_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_sass_loader_lib_loader_js_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));\n\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=template&id=11a997dc&":
/*!*********************************************************************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=template&id=11a997dc& ***!
  \*********************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_template_id_11a997dc___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../node_modules/vue-loader/lib??vue-loader-options!./document_manager_indexed.vue?vue&type=template&id=11a997dc& */ \"./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=template&id=11a997dc&\");\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_template_id_11a997dc___WEBPACK_IMPORTED_MODULE_0__[\"render\"]; });\n\n/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return _node_modules_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_vue_loader_lib_index_js_vue_loader_options_document_manager_indexed_vue_vue_type_template_id_11a997dc___WEBPACK_IMPORTED_MODULE_0__[\"staticRenderFns\"]; });\n\n\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/helper/helper.js":
/*!***************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/helper/helper.js ***!
  \***************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  getComponenet(path, root) {\n    var component = root.filter(function (filtered) {\n      return filtered.path == path;\n    })[0];\n    return component;\n  }\n\n});\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/helper/helper.js?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/hooks/add_action.js":
/*!******************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/hooks/add_action.js ***!
  \******************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("window.erpHrHooks.addAction('addRouter', 'WPDOC', () => {\n  var routerData = [{\n    id: 1,\n    name: 'DocumentManagerIndexed',\n    icon: 'icon-document',\n    text: window.erpHr.doc_manager.documents,\n    text_domain: 'erp-hr-frontend',\n    is_cap: window.erpHrVue.userCan('erp_hr_manager')\n  }];\n  window.erpHrVue.$store.commit('setStateCommon', state => {\n    state.addOnNav = routerData;\n  });\n}, 10);\nwindow.erpHrHooks.addAction('addEmployeeRouter', 'WPDOC1', () => {\n  var routerData = [{\n    id: 1,\n    name: 'DocumentManagerIndexedEmployee',\n    icon: 'fa fa-folder-open',\n    text: window.erpHr.doc_manager.documents,\n    text_domain: 'erp-hr-frontend',\n    is_cap: window.erpHrVue.userCan('erp_hr_manager') || window.erpHrVue.isCurrentUserProfile()\n  }];\n  window.erpHrVue.$store.commit('setStateCommon', state => {\n    state.employeeAddOnNav = routerData;\n  });\n}, 10);\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/hooks/add_action.js?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/main.js":
/*!******************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/main.js ***!
  \******************************************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _router_routes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./router/routes */ \"./modules/hrm/document-manager/assets/src/frontend/router/routes.js\");\n/* harmony import */ var _store_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./store/store */ \"./modules/hrm/document-manager/assets/src/frontend/store/store.js\");\n/* harmony import */ var _less_style_less__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../less/style.less */ \"./modules/hrm/document-manager/assets/less/style.less\");\n/* harmony import */ var _less_style_less__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_less_style_less__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_add_action__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hooks/add_action */ \"./modules/hrm/document-manager/assets/src/frontend/hooks/add_action.js\");\n/* harmony import */ var _hooks_add_action__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_hooks_add_action__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nwindow._.merge(window.erpHrVue.$store.state, _store_store__WEBPACK_IMPORTED_MODULE_1__[\"default\"].state);\n\n\nwindow.erpHrVue.$router.addRoutes(_router_routes__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/main.js?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/router/routes.js":
/*!***************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/router/routes.js ***!
  \***************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _helper_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helper/helper */ \"./modules/hrm/document-manager/assets/src/frontend/helper/helper.js\");\n/* harmony import */ var _components_document_manager_vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./../components/document_manager.vue */ \"./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue\");\n/* harmony import */ var _components_document_manager_indexed_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./../components/document_manager_indexed.vue */ \"./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue\");\n\n\n\nvar hrRoute = window.erpHrVue.$router.options.routes;\nvar employeeComponent = _helper_helper__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComponenet('/employees', hrRoute);\nvar employeeComponent_id = _helper_helper__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComponenet(':id', employeeComponent.children);\nconst routes = [{\n  path: '/document_manager',\n  component: _components_document_manager_vue__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  children: [{\n    path: '',\n    name: 'DocumentManagerIndexed',\n    component: _components_document_manager_indexed_vue__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n  }]\n}, {\n  path: '/employees',\n  component: employeeComponent.component,\n  children: [{\n    path: ':id',\n    component: employeeComponent_id.component,\n    children: [{\n      path: 'employee_documents',\n      name: 'DocumentManagerIndexedEmployee',\n      component: _components_document_manager_indexed_vue__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    }]\n  }]\n}];\n/* harmony default export */ __webpack_exports__[\"default\"] = (routes);\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/router/routes.js?");

/***/ }),

/***/ "./modules/hrm/document-manager/assets/src/frontend/store/store.js":
/*!*************************************************************************!*\
  !*** ./modules/hrm/document-manager/assets/src/frontend/store/store.js ***!
  \*************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\nconst store = {\n  state: {\n    addOnNav: [],\n    employeeAddOnNav: []\n  }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (store);\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/store/store.js?");

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=script&lang=js&":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n//\n//\n//\n//\n//\n//\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: \"DocumentManager\",\n\n  data() {\n    return {};\n  },\n\n  created() {},\n\n  mounted() {},\n\n  methods: {}\n});\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=script&lang=js&":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  name: \"DocumentManagerIndexed\",\n  props: ['employee'],\n\n  data() {\n    return {\n      http: erpHrVue.httpReq(),\n      file_data: [],\n      current_dir_id: 0,\n      create_folder_modal_open: \"\",\n      create_folder_modal_open_clipped: \"\",\n      rename_folder_modal_open: \"\",\n      rename_folder_modal_open_clipped: \"\",\n      move_to_modal_open: \"\",\n      move_to_modal_open_clipped: \"\",\n      share_to_modal_open: \"\",\n      share_to_modal_open_clipped: \"\",\n      dir_name: '',\n      employee_id: this.$route.params.id ? this.$route.params.id : window.erpHrVue.$store.state.user.user_id,\n      upload_files: [],\n      movable: {},\n      move_or_dlt: false,\n      show_move_or_dlt: 'none',\n      movable_folder: [],\n      target_folder: '',\n      single_employee_class_wrap: '',\n      skey: '',\n      view_type: 'list',\n      view_type_parent_class: 'list-table',\n      view_type_child_class: 'list-table-body',\n      view_type_child_content: 'list-body list-table-row',\n      is_show: 'show_header',\n      breadcrumb: [{\n        dir_id: 0,\n        name: '<span class=\"fa fa-home\"></span>',\n        data: {\n          source: \"\"\n        }\n      }],\n      loader: 'is-busy',\n      rename_data: {\n        dir_file_type: '',\n        employee_id: '',\n        parent_id: '',\n        target_dir_id: '',\n        dirName: ''\n      },\n      doc_str: window.erpHr.doc_manager,\n      doc_option_str: window.erpHr.doc_option_str,\n      employees: [],\n      departments: [],\n      designations: [],\n      source: 'owned_by_me',\n      can_upload: true,\n      can_prompt: true,\n      can_move: true,\n      can_remove: true,\n      can_share: true\n    };\n  },\n\n  created() {\n    this.get_employee_dir_files();\n    this.get_employees();\n    this.get_departments();\n    this.get_designations();\n    this.create_dropbox_employee_folders();\n  },\n\n  mounted() {\n    this.if_single_employee();\n  },\n\n  watch: {\n    current_dir_id(val) {\n      let self = this;\n\n      if (val == 0) {\n        self.get_dir_files_by_param(self.current_dir_id, self.employee_id, self.source, '', '');\n      }\n    },\n\n    movable(val) {},\n\n    move_or_dlt(val) {\n      if (val == true) {\n        this.show_move_or_dlt = 'inline-block';\n      } else {\n        this.show_move_or_dlt = 'none';\n      }\n    },\n\n    skey(val) {\n      /*let self = this;\n      self.file_data = [];\n      self.get_employee_folders_by_search();\n      self.get_employee_files_by_search();*/\n    },\n\n    source(val) {\n      let self = this;\n\n      if (val == 'owned_by_me') {\n        self.can_upload = true;\n        self.can_prompt = true;\n        self.can_move = true;\n        self.can_remove = true;\n        self.can_share = true;\n      }\n\n      if (val == 'shared_with_me') {\n        self.can_upload = false;\n        self.can_prompt = false;\n        self.can_move = false;\n        self.can_remove = false;\n        self.can_share = false;\n      }\n\n      if (val == 'my_dropbox') {\n        self.can_upload = true;\n        self.can_prompt = true;\n        self.can_move = false;\n        self.can_remove = true;\n        self.can_share = true;\n      }\n\n      self.employee_id = self.$route.params.id ? self.$route.params.id : window.erpHrVue.$store.state.user.user_id;\n      self.current_dir_id = 0;\n      self.breadcrumb.splice(1);\n      self.get_employee_dir_files();\n    }\n\n  },\n  methods: {\n    get_employee_dir_files() {\n      let self = this;\n      self.get_dir_files_by_param(self.current_dir_id, self.employee_id, self.source, '', '');\n    },\n\n    get_dir_files_by_param(dir_id, eid, source, search_string, direct_link) {\n      let self = this;\n      self.loader = 'is-busy';\n      self.file_data = [];\n      let formData = new FormData();\n      formData.append('action', 'wp-erp-rec-get-files-folders');\n      formData.append('employee_id', eid);\n      formData.append('dir_id', dir_id);\n      formData.append('source', source);\n      formData.append('search_string', search_string);\n      formData.append('direct_link', direct_link);\n      self.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData).then(function (res) {\n        res.data.data.directory.forEach((item, index) => {\n          self.file_data.push(item);\n        });\n        res.data.data.files.forEach((item, index) => {\n          self.file_data.push(item);\n        });\n        self.loader = '';\n        self.movable = {};\n        self.move_or_dlt = false;\n      });\n    },\n\n    link_clicked: function (click_dir_id) {\n      var self = this;\n\n      if (click_dir_id.source == 'dropbox') {\n        let formData = new FormData();\n        formData.append('action', 'wp-erp-download-files-from-dropbox');\n        formData.append('dir_id', click_dir_id.dir_id);\n        self.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData).then(function (res) {\n          if (res.data.success === true) {\n            window.location = res.data.data;\n          }\n        });\n      }\n    },\n\n    search_dir(e) {\n      let self = this;\n\n      if (e.keyCode === 13) {\n        self.get_dir_files_by_param(self.current_dir_id, self.employee_id, self.source, self.skey, '');\n      }\n    },\n\n    create_folder() {\n      let self = this;\n\n      if (self.dir_name.length == 0) {\n        erpHrVue.notifySuccess('You must provide a folder name');\n        return;\n      }\n\n      let formData = new FormData();\n      formData.append('action', 'wp-erp-rec-createDir');\n      formData.append('employee_id', self.employee_id);\n      formData.append('parent_id', self.current_dir_id);\n      formData.append('source', self.source);\n      formData.append('dirName', self.dir_name);\n      self.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData).then(function (res) {\n        if (res.data.success == true) {\n          setTimeout(function () {\n            self.dir_name = '';\n            self.get_employee_dir_files();\n            self.open_folder_popup_close();\n            erpHrVue.notifySuccess(res.data.data);\n          }, 1000);\n        } else {\n          erpHrVue.notifySuccess('Something went wrong. Please try again later.');\n        }\n      });\n    },\n\n    get_file() {\n      document.getElementById('upload_file').click();\n    },\n\n    upload_file() {\n      let self = this;\n      let counter = 0;\n      let uploaded_files = this.$refs.upload_file.files;\n\n      for (let i = 0; i < uploaded_files.length; i++) {\n        let formData = new FormData();\n        formData.append(\"doc_attachment\", uploaded_files[i]);\n        this.http.post(window.site_url + '/wp-admin/admin-ajax.php?action=file_dir_ajax_upload&_wpnonce=' + window.file_upload_nonce + '&employee_id=' + self.employee_id + '&parent_id=' + self.current_dir_id + '&source=' + self.source, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        }).then(function (res) {\n          counter++;\n\n          if (counter == uploaded_files.length) {\n            self.get_employee_dir_files();\n            erpHrVue.notifySuccess('File Uploaded Successfully');\n          }\n        });\n      } //http://127.0.0.1/projects/erp_projects/dm_with_hr_frontend/wp-admin/admin-ajax.php?action=file_dir_ajax_upload&_wpnonce=921acfa2b5&parent_id=0&employee_id=\n\n    },\n\n    delete_data() {\n      let self = this;\n      let formData = new FormData();\n      formData.append(\"action\", 'wp-erp-rec-deleteDirFile');\n      formData.append(\"parent_id\", this.current_dir_id);\n      formData.append(\"employee_id\", self.employee_id);\n      formData.append(\"source\", self.source);\n      let item = [];\n\n      for (const [key, value] of Object.entries(this.movable)) {\n        item.push(value.dir_id);\n      }\n\n      formData.append(\"selected_dir_file_id\", JSON.stringify(item));\n      this.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      }).then(function (res) {\n        self.get_employee_dir_files();\n        erpHrVue.notifySuccess('File deleted Successfully');\n      });\n    },\n\n    delete_item(itemdata) {\n      let self = this;\n      let formData = new FormData();\n      formData.append(\"action\", 'wp-erp-rec-deleteDirFile');\n      formData.append(\"parent_id\", this.current_dir_id);\n      formData.append(\"employee_id\", self.employee_id);\n      formData.append(\"source\", self.source);\n      let item = [];\n      item.push(itemdata.dir_id);\n      formData.append(\"selected_dir_file_id\", JSON.stringify(item));\n      this.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      }).then(function (res) {\n        self.get_employee_dir_files();\n        erpHrVue.notifySuccess('File deleted Successfully');\n      });\n    },\n\n    open_folder_create_popup() {\n      this.create_folder_modal_open = 'is-active';\n      this.create_folder_modal_open_clipped = 'is-clipped';\n    },\n\n    open_folder_popup_close() {\n      this.create_folder_modal_open = '';\n      this.create_folder_modal_open_clipped = '';\n    },\n\n    move_to_popup_close() {\n      this.move_to_modal_open = '';\n      this.move_to_modal_open_clipped = '';\n    },\n\n    jump_to_folder(fd) {\n      let self = this;\n      this.current_dir_id = fd.dir_id;\n      /* if( this.source == 'owned_by_me' ){\n           self.employee_id = ( self.$route.params.id ) ? self.$route.params.id : window.erpHrVue.$store.state.user.user_id;\n       } else {\n           self.employee_id = fd.eid;\n       }*/\n\n      let cur_source;\n\n      if (fd.source == 'local') {\n        cur_source = 'owned_by_me';\n      }\n\n      if (fd.source == 'dropbox') {\n        cur_source = 'my_dropbox';\n      }\n\n      self.get_dir_files_by_param(fd.dir_id, fd.eid, cur_source, '', 'yes');\n      this.breadcrumb.push({\n        dir_id: fd.dir_id,\n        name: fd.dir_file_name,\n        data: fd\n      });\n    },\n\n    bread_crumb_action(fd, index) {\n      this.current_dir_id = fd.dir_id;\n      this.breadcrumb.splice(index + 1);\n\n      if (fd.dir_id != 0) {\n        let cur_source;\n\n        if (fd.data.source == 'local') {\n          cur_source = 'owned_by_me';\n        }\n\n        if (fd.data.source == 'dropbox') {\n          cur_source = 'my_dropbox';\n        }\n\n        this.get_dir_files_by_param(fd.data.dir_id, fd.data.eid, cur_source, '', 'yes');\n      }\n    },\n\n    is_checked(fd, elm) {\n      var self = this;\n\n      if (elm.target.checked) {\n        self.movable[elm.target.id] = fd;\n      } else {\n        delete self.movable[elm.target.id];\n      }\n\n      let length = Object.keys(self.movable).length;\n\n      if (length > 0) {\n        self.move_or_dlt = true;\n      } else {\n        self.move_or_dlt = false;\n      }\n    },\n\n    move_to() {\n      let self = this;\n      this.http.get(window.site_url + '/wp-admin/admin-ajax.php?action=wp-erp-doc-loadParentNodes&employee_id=' + self.employee_id).then(function (res) {\n        self.move_to_modal_open = 'is-active';\n        self.create_folder_modal_open_clipped = 'is-clipped';\n        self.movable_folder = res.data.children;\n      });\n    },\n\n    set_target_folder(val) {\n      this.target_folder = val;\n      jQuery('.folder_tree a').on('click', function () {\n        jQuery('.folder_tree a.current').removeClass('current');\n        jQuery(event.target).addClass('current');\n      });\n    },\n\n    move() {\n      let self = this;\n      let formData = new FormData();\n      formData.append(\"action\", 'wp-erp-doc-moveNow');\n      formData.append(\"parent_id\", this.current_dir_id);\n      formData.append(\"new_parent_id\", this.target_folder);\n      formData.append(\"employee_id\", this.employee_id);\n      let item = [];\n\n      for (const [key, value] of Object.entries(this.movable)) {\n        item.push(value.dir_id);\n      }\n\n      formData.append(\"selectedDirFile\", JSON.stringify(item));\n      this.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      }).then(function (res) {\n        self.get_employee_dir_files();\n        self.move_to_popup_close();\n        erpHrVue.notifySuccess('File moved Successfully');\n      });\n    },\n\n    if_single_employee() {\n      if (this.employee) {\n        this.single_employee_class_wrap = ' single_employee_class_wrap';\n      }\n    },\n\n    view(type) {\n      if (type == 'list') {\n        this.view_type_parent_class = 'list-table';\n        this.view_type_child_class = 'list-table-body';\n        this.view_type_child_content = 'list-body list-table-row';\n        this.is_show = 'show_header';\n      }\n\n      if (type == 'grid') {\n        this.view_type_parent_class = 'columns';\n        this.view_type_child_class = 'column is-3';\n        this.view_type_child_content = 'grid-body';\n        this.is_show = 'hide_header';\n      }\n    },\n\n    rename(fd) {\n      var self = this;\n      self.rename_data.dir_file_type = fd.is_dir ? 'folder' : 'file';\n      self.rename_data.employee_id = fd.eid;\n      self.rename_data.parent_id = 0;\n      self.rename_data.target_dir_id = fd.dir_id;\n      self.rename_data.dirName = fd.dir_file_name;\n      self.rename_folder_modal_open = 'is-active';\n      self.rename_folder_modal_open_clipped = 'is-clipped';\n    },\n\n    rename_folder_popup_close() {\n      var self = this;\n      self.rename_data.dir_file_type = '';\n      self.rename_data.employee_id = '';\n      self.rename_data.parent_id = '';\n      self.rename_data.target_dir_id = '';\n      self.rename_data.dirName = '';\n      self.rename_folder_modal_open = '';\n      self.rename_folder_modal_open_clipped = '';\n    },\n\n    rename_folder() {\n      let self = this;\n      this.http.get(window.site_url + '/wp-admin/admin-ajax.php?action=wp-erp-rec-renameDirFile&dir_file_type=' + self.rename_data.dir_file_type + '&employee_id=' + self.rename_data.employee_id + '&parent_id=' + self.rename_data.parent_id + '&target_dir_id=' + self.rename_data.target_dir_id + '&dirName=' + self.rename_data.dirName + '&source=' + self.source).then(function (res) {\n        erpHrVue.notifySuccess('Renamed Successfully');\n        self.rename_folder_popup_close();\n        self.get_employee_dir_files();\n      });\n    },\n\n    create_dir_by_ekey(e) {\n      let self = this;\n\n      if (e.keyCode === 13) {\n        self.create_folder();\n      }\n    },\n\n    enter_to_rename(e) {\n      let self = this;\n\n      if (e.keyCode === 13) {\n        self.rename_folder();\n      }\n    },\n\n    share_data() {\n      let self = this;\n      self.share_to_modal_open = 'is-active';\n      self.share_to_modal_open_clipped = 'is-clipped';\n      jQuery('#share_by').change(function (e) {\n        jQuery('.share_files_control.no-show').css('display', 'none');\n        jQuery('.share_files_control.' + e.target.value).css('display', 'block');\n      });\n    },\n\n    share_to_popup_close() {\n      let self = this;\n      self.share_to_modal_open = '';\n      self.share_to_modal_open_clipped = '';\n    },\n\n    get_employees() {\n      let self = this;\n      self.http.get('hrm/employees?per_page=100', {\n        baseURL: window.site_url + '/wp-json/erp/v1',\n        headers: {\n          'X-WP-Nonce': window.rest_nonce\n        }\n      }).then(function (res) {\n        self.employees = res.data;\n      });\n    },\n\n    get_departments() {\n      let self = this;\n      self.http.get('hrm/departments', {\n        baseURL: window.site_url + '/wp-json/erp/v1',\n        headers: {\n          'X-WP-Nonce': window.rest_nonce\n        }\n      }).then(function (res) {\n        self.departments = res.data;\n      });\n    },\n\n    get_designations() {\n      let self = this;\n      self.http.get('hrm/designations', {\n        baseURL: window.site_url + '/wp-json/erp/v1',\n        headers: {\n          'X-WP-Nonce': window.rest_nonce\n        }\n      }).then(function (res) {\n        self.designations = res.data;\n      });\n    },\n\n    share_dir_file() {\n      let self = this;\n      let form_data = jQuery('#erp-doc-share-template').serialize();\n      let items = [];\n\n      for (const [key, value] of Object.entries(this.movable)) {\n        items.push(value.dir_id);\n      }\n\n      let formData = new FormData();\n      formData.append(\"action\", 'wp-erp-rec-share-files-folders');\n      formData.append(\"formdata\", form_data);\n      formData.append(\"shared_source\", self.source);\n      formData.append(\"selectedDirFile\", JSON.stringify(items));\n      formData.append(\"file_data\", JSON.stringify(self.file_data));\n      formData.append(\"dir_data\", JSON.stringify([]));\n      this.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      }).then(function (res) {\n        if (res.data.success) {\n          self.share_to_popup_close();\n          erpHrVue.notifySuccess(res.data.data);\n        }\n      });\n    },\n\n    create_dropbox_employee_folders() {\n      let self = this;\n      let formData = new FormData();\n      formData.append('action', 'wp-erp-sync-employees-dropbox');\n      formData.append('sync', 'yes');\n      self.http.post(window.site_url + '/wp-admin/admin-ajax.php', formData).then(function (res) {// console.log(res);\n      });\n    }\n\n  }\n});\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/mini-css-extract-plugin/dist/loader.js!./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/sass-loader/lib/loader.js!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=style&index=0&lang=css&":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js!./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/sass-loader/lib/loader.js!./node_modules/vue-loader/lib??vue-loader-options!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=style&index=0&lang=css& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// extracted by mini-css-extract-plugin\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?./node_modules/mini-css-extract-plugin/dist/loader.js!./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/sass-loader/lib/loader.js!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true&":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?vue&type=template&id=0c5cf9ac&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return render; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return staticRenderFns; });\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"document-manager\" }, [_c(\"router-view\")], 1)\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\n\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager.vue?./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=template&id=11a997dc&":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?vue&type=template&id=11a997dc& ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"render\", function() { return render; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"staticRenderFns\", function() { return staticRenderFns; });\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"section\",\n    { class: \"hr_frontend_dm \" + _vm.single_employee_class_wrap },\n    [\n      _c(\"div\", { staticClass: \"dm_container\" }, [\n        _c(\"div\", { staticClass: \"columns\" }, [\n          _c(\"div\", { staticClass: \"column\" }, [\n            _c(\"h1\", { staticClass: \"title\" }, [\n              _vm._v(\n                \"\\n                    \" +\n                  _vm._s(_vm.doc_str.documents) +\n                  \"\\n                \"\n              ),\n            ]),\n          ]),\n          _vm._v(\" \"),\n          _c(\"div\", { staticClass: \"column\" }, [\n            _c(\"div\", { staticClass: \"field\" }, [\n              _c(\"p\", { staticClass: \"control has-icons-right\" }, [\n                _c(\"input\", {\n                  directives: [\n                    {\n                      name: \"model\",\n                      rawName: \"v-model\",\n                      value: _vm.skey,\n                      expression: \"skey\",\n                    },\n                  ],\n                  staticClass: \"input\",\n                  attrs: { type: \"text\", placeholder: _vm.doc_str.search },\n                  domProps: { value: _vm.skey },\n                  on: {\n                    keyup: _vm.search_dir,\n                    input: function ($event) {\n                      if ($event.target.composing) {\n                        return\n                      }\n                      _vm.skey = $event.target.value\n                    },\n                  },\n                }),\n                _vm._v(\" \"),\n                _vm._m(0),\n              ]),\n            ]),\n          ]),\n        ]),\n        _vm._v(\" \"),\n        _c(\"div\", { staticClass: \"columns\" }, [\n          _c(\"div\", { staticClass: \"column\" }, [\n            _c(\"div\", { staticClass: \"columns\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"column operator\" },\n                [\n                  _c(\"span\", [\n                    _vm.can_prompt\n                      ? _c(\n                          \"a\",\n                          {\n                            staticClass: \"button light-black is-small\",\n                            attrs: { href: \"javascript:void(0)\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.open_folder_create_popup()\n                              },\n                            },\n                          },\n                          [\n                            _c(\"span\", { staticClass: \"fa fa-folder-open\" }),\n                            _vm._v(\"   \" + _vm._s(_vm.doc_str.create_folder)),\n                          ]\n                        )\n                      : _vm._e(),\n                    _vm._v(\" \"),\n                    _vm.can_prompt\n                      ? _c(\n                          \"a\",\n                          {\n                            staticClass: \"button is-link is-small\",\n                            attrs: { href: \"javascript:void(0)\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.get_file()\n                              },\n                            },\n                          },\n                          [\n                            _c(\"span\", { staticClass: \"fa fa-upload\" }),\n                            _vm._v(\"   \" + _vm._s(_vm.doc_str.upload)),\n                          ]\n                        )\n                      : _vm._e(),\n                    _vm._v(\" \"),\n                    _c(\"form\", { attrs: { enctype: \"multipart/form-data\" } }, [\n                      _c(\"input\", {\n                        ref: \"upload_file\",\n                        staticStyle: { display: \"none\" },\n                        attrs: {\n                          type: \"file\",\n                          id: \"upload_file\",\n                          multiple: \"\",\n                        },\n                        on: {\n                          change: function ($event) {\n                            return _vm.upload_file()\n                          },\n                        },\n                      }),\n                    ]),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"span\", { style: \"display:\" + _vm.show_move_or_dlt }, [\n                    _vm.can_move\n                      ? _c(\n                          \"button\",\n                          {\n                            staticClass: \"button is-small is-info\",\n                            on: {\n                              click: function ($event) {\n                                return _vm.move_to()\n                              },\n                            },\n                          },\n                          [_vm._v(\" \" + _vm._s(_vm.doc_str.move) + \" \")]\n                        )\n                      : _vm._e(),\n                    _vm._v(\" \"),\n                    _vm.can_remove\n                      ? _c(\n                          \"button\",\n                          {\n                            staticClass: \"button is-small is-danger\",\n                            on: {\n                              click: function ($event) {\n                                return _vm.delete_data()\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \" \" + _vm._s(_vm.doc_str.move_to_trash) + \" \"\n                            ),\n                          ]\n                        )\n                      : _vm._e(),\n                    _vm._v(\" \"),\n                    _vm.can_share\n                      ? _c(\n                          \"button\",\n                          {\n                            staticClass: \"button is-small is-primary\",\n                            on: {\n                              click: function ($event) {\n                                return _vm.share_data()\n                              },\n                            },\n                          },\n                          [_vm._v(\" Share \")]\n                        )\n                      : _vm._e(),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"br\"),\n                  _c(\"br\"),\n                  _c(\"br\"),\n                  _vm._v(\" \"),\n                  _vm._l(_vm.breadcrumb, function (bc, index) {\n                    return _c(\"span\", [\n                      _c(\"a\", {\n                        staticClass: \"bc_single\",\n                        attrs: { href: \"javascript:void(0)\" },\n                        domProps: { innerHTML: _vm._s(bc.name) },\n                        on: {\n                          click: function ($event) {\n                            return _vm.bread_crumb_action(bc, index)\n                          },\n                        },\n                      }),\n                    ])\n                  }),\n                ],\n                2\n              ),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"column has-text-right\" }, [\n                _c(\"select\", {\n                  directives: [\n                    {\n                      name: \"model\",\n                      rawName: \"v-model\",\n                      value: _vm.source,\n                      expression: \"source\",\n                    },\n                  ],\n                  staticClass: \"button is-light ash-white\",\n                  attrs: { id: \"source\" },\n                  domProps: { innerHTML: _vm._s(_vm.doc_option_str) },\n                  on: {\n                    change: function ($event) {\n                      var $$selectedVal = Array.prototype.filter\n                        .call($event.target.options, function (o) {\n                          return o.selected\n                        })\n                        .map(function (o) {\n                          var val = \"_value\" in o ? o._value : o.value\n                          return val\n                        })\n                      _vm.source = $event.target.multiple\n                        ? $$selectedVal\n                        : $$selectedVal[0]\n                    },\n                  },\n                }),\n                _vm._v(\" \"),\n                _c(\n                  \"a\",\n                  {\n                    staticClass: \"button is-light ash-white\",\n                    attrs: { href: \"javascript:void(0)\" },\n                    on: {\n                      click: function ($event) {\n                        return _vm.view(\"list\")\n                      },\n                    },\n                  },\n                  [_c(\"span\", { staticClass: \"fa fa-bars\" })]\n                ),\n                _vm._v(\" \"),\n                _c(\n                  \"a\",\n                  {\n                    staticClass: \"button is-light ash-white\",\n                    attrs: { href: \"javascript:void(0)\" },\n                    on: {\n                      click: function ($event) {\n                        return _vm.view(\"grid\")\n                      },\n                    },\n                  },\n                  [_c(\"span\", { staticClass: \"fa fa-th-large\" })]\n                ),\n              ]),\n            ]),\n          ]),\n        ]),\n        _vm._v(\" \"),\n        _c(\"div\", { class: \"columns \" + _vm.loader }, [\n          _c(\"div\", { staticClass: \"column\" }, [\n            _c(\"div\", { staticClass: \"dash-list\" }, [\n              _c(\"ul\", { class: \"list-header \" + _vm.is_show }, [\n                _c(\"li\", [_vm._v(\"   \")]),\n                _vm._v(\" \"),\n                _c(\"li\", [_vm._v(\" \" + _vm._s(_vm.doc_str.file_name) + \" \")]),\n                _vm._v(\" \"),\n                _c(\"li\", [_vm._v(\" \" + _vm._s(_vm.doc_str.modified) + \" \")]),\n                _vm._v(\" \"),\n                _c(\"li\", [_vm._v(\" \" + _vm._s(_vm.doc_str.created_by) + \" \")]),\n                _vm._v(\" \"),\n                _c(\"li\", [_vm._v(\" \" + _vm._s(_vm.doc_str.file_size) + \" \")]),\n                _vm._v(\" \"),\n                _c(\"li\", [_vm._v(\"   \")]),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { class: _vm.view_type_parent_class },\n                [\n                  _vm._l(_vm.file_data, function (fd) {\n                    return _vm.file_data.length > 0\n                      ? _c(\"div\", { class: _vm.view_type_child_class }, [\n                          _c(\"div\", { class: _vm.view_type_child_content }, [\n                            _c(\n                              \"div\",\n                              { staticClass: \"has-checkbox list-table-cell\" },\n                              [\n                                _c(\"input\", {\n                                  staticClass: \"is-checkbox\",\n                                  attrs: {\n                                    disabled: _vm.source == \"shared_with_me\",\n                                    type: \"checkbox\",\n                                    id: \"doc_\" + fd.dir_id,\n                                  },\n                                  domProps: { value: fd.id },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.is_checked(fd, $event)\n                                    },\n                                  },\n                                }),\n                                _vm._v(\" \"),\n                                _c(\"label\", {\n                                  attrs: { for: \"doc_\" + fd.dir_id },\n                                }),\n                              ]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\n                              \"div\",\n                              { staticClass: \"img-and-name list-table-cell\" },\n                              [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"align-part\" },\n                                  [\n                                    fd.is_dir != true\n                                      ? [\n                                          _c(\"span\", [\n                                            fd.attachment_type == \"image/png\"\n                                              ? _c(\"i\", {\n                                                  staticClass:\n                                                    \"fa fa-file-picture-o\",\n                                                })\n                                              : fd.attachment_type == \"text/csv\"\n                                              ? _c(\"i\", {\n                                                  staticClass:\n                                                    \"fa fa-file-text-o\",\n                                                })\n                                              : fd.attachment_type ==\n                                                \"application/zip\"\n                                              ? _c(\"i\", {\n                                                  staticClass:\n                                                    \"fa fa-file-zip-o\",\n                                                })\n                                              : fd.attachment_type ==\n                                                \"text/plain\"\n                                              ? _c(\"i\", {\n                                                  staticClass:\n                                                    \"fa fa-file-text-o\",\n                                                })\n                                              : fd.attachment_type ==\n                                                \"application/pdf\"\n                                              ? _c(\"i\", {\n                                                  staticClass:\n                                                    \"fa fa-file-pdf-o\",\n                                                })\n                                              : fd.attachment_type ==\n                                                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n                                              ? _c(\"i\", {\n                                                  staticClass:\n                                                    \"fa fa-file-excel-o\",\n                                                })\n                                              : fd.attachment_type ==\n                                                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n                                              ? _c(\"i\", {\n                                                  staticClass:\n                                                    \"fa fa-file-word-o\",\n                                                })\n                                              : _c(\"i\", {\n                                                  staticClass: \"fa fa-file\",\n                                                }),\n                                          ]),\n                                          _vm._v(\" \"),\n                                          _c(\"span\", [\n                                            _c(\n                                              \"a\",\n                                              {\n                                                attrs: {\n                                                  href: fd.attactment_url,\n                                                },\n                                              },\n                                              [\n                                                _vm._v(\n                                                  \" \" +\n                                                    _vm._s(fd.dir_file_name) +\n                                                    \" \"\n                                                ),\n                                              ]\n                                            ),\n                                          ]),\n                                        ]\n                                      : _vm._e(),\n                                    _vm._v(\" \"),\n                                    fd.is_dir == true\n                                      ? [\n                                          _vm._m(1, true),\n                                          _vm._v(\" \"),\n                                          _c(\"span\", [\n                                            _c(\n                                              \"a\",\n                                              {\n                                                attrs: {\n                                                  href: \"javascript:void(0)\",\n                                                },\n                                                on: {\n                                                  click: function ($event) {\n                                                    return _vm.jump_to_folder(\n                                                      fd\n                                                    )\n                                                  },\n                                                },\n                                              },\n                                              [\n                                                _vm._v(\n                                                  \" \" +\n                                                    _vm._s(fd.dir_file_name) +\n                                                    \" \"\n                                                ),\n                                              ]\n                                            ),\n                                          ]),\n                                        ]\n                                      : _vm._e(),\n                                  ],\n                                  2\n                                ),\n                              ]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\n                              \"div\",\n                              { staticClass: \"box-view list-table-cell\" },\n                              [_vm._v(\" \" + _vm._s(fd.updated_at) + \" \")]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\n                              \"div\",\n                              { staticClass: \"box-view list-table-cell\" },\n                              [_vm._v(\" \" + _vm._s(fd.user_nicename) + \" \")]\n                            ),\n                            _vm._v(\" \"),\n                            fd.is_dir == true\n                              ? _c(\"div\", {\n                                  staticClass: \"box-view list-table-cell\",\n                                })\n                              : _vm._e(),\n                            _vm._v(\" \"),\n                            fd.is_dir != true\n                              ? _c(\n                                  \"div\",\n                                  { staticClass: \"box-view list-table-cell\" },\n                                  [_vm._v(\" \" + _vm._s(fd.file_size) + \" \")]\n                                )\n                              : _vm._e(),\n                            _vm._v(\" \"),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"box-view action list-table-cell\",\n                              },\n                              [\n                                _c(\n                                  \"a\",\n                                  {\n                                    staticClass: \"doc_action\",\n                                    attrs: { href: \"javascript:void(0)\" },\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n                                       ...\\n                                        \"\n                                    ),\n                                    _vm.source == \"owned_by_me\"\n                                      ? [\n                                          _c(\n                                            \"ul\",\n                                            {\n                                              staticClass:\n                                                \"doc_action_dropdown\",\n                                            },\n                                            [\n                                              fd.is_dir != true\n                                                ? _c(\"li\", [\n                                                    _c(\n                                                      \"a\",\n                                                      {\n                                                        attrs: {\n                                                          target: \"_blank\",\n                                                          href: fd.attactment_url,\n                                                          download: \"\",\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"span\", {\n                                                          staticClass:\n                                                            \"fa fa-download\",\n                                                        }),\n                                                        _vm._v(\n                                                          \" \" +\n                                                            _vm._s(\n                                                              _vm.doc_str\n                                                                .download\n                                                            )\n                                                        ),\n                                                      ]\n                                                    ),\n                                                  ])\n                                                : _vm._e(),\n                                              _vm._v(\" \"),\n                                              _c(\"li\", [\n                                                _c(\n                                                  \"a\",\n                                                  {\n                                                    attrs: {\n                                                      href: \"javascript:void(0)\",\n                                                    },\n                                                    on: {\n                                                      click: function ($event) {\n                                                        return _vm.rename(fd)\n                                                      },\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"span\", {\n                                                      staticClass:\n                                                        \"fa fa-pencil\",\n                                                    }),\n                                                    _vm._v(\n                                                      \" \" +\n                                                        _vm._s(\n                                                          _vm.doc_str.rename\n                                                        )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]),\n                                              _vm._v(\" \"),\n                                              _c(\"li\", [\n                                                _c(\n                                                  \"a\",\n                                                  {\n                                                    attrs: {\n                                                      href: \"javascript:void(0)\",\n                                                    },\n                                                    on: {\n                                                      click: function ($event) {\n                                                        return _vm.delete_item(\n                                                          fd\n                                                        )\n                                                      },\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"span\", {\n                                                      staticClass:\n                                                        \"fa fa-trash\",\n                                                    }),\n                                                    _vm._v(\n                                                      \" \" +\n                                                        _vm._s(\n                                                          _vm.doc_str.delete\n                                                        )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]),\n                                            ]\n                                          ),\n                                        ]\n                                      : _vm._e(),\n                                    _vm._v(\" \"),\n                                    _vm.source == \"my_dropbox\"\n                                      ? [\n                                          _c(\n                                            \"ul\",\n                                            {\n                                              staticClass:\n                                                \"doc_action_dropdown\",\n                                            },\n                                            [\n                                              fd.is_dir != true\n                                                ? _c(\"li\", [\n                                                    _c(\n                                                      \"a\",\n                                                      {\n                                                        attrs: {\n                                                          href: \"javascript:void(0)\",\n                                                        },\n                                                        on: {\n                                                          click: function (\n                                                            $event\n                                                          ) {\n                                                            return _vm.link_clicked(\n                                                              fd\n                                                            )\n                                                          },\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"span\", {\n                                                          staticClass:\n                                                            \"fa fa-download\",\n                                                        }),\n                                                        _vm._v(\n                                                          \" \" +\n                                                            _vm._s(\n                                                              _vm.doc_str\n                                                                .download\n                                                            )\n                                                        ),\n                                                      ]\n                                                    ),\n                                                  ])\n                                                : _vm._e(),\n                                              _vm._v(\" \"),\n                                              _c(\"li\", [\n                                                _c(\n                                                  \"a\",\n                                                  {\n                                                    attrs: {\n                                                      href: \"javascript:void(0)\",\n                                                    },\n                                                    on: {\n                                                      click: function ($event) {\n                                                        return _vm.rename(fd)\n                                                      },\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"span\", {\n                                                      staticClass:\n                                                        \"fa fa-pencil\",\n                                                    }),\n                                                    _vm._v(\n                                                      \" \" +\n                                                        _vm._s(\n                                                          _vm.doc_str.rename\n                                                        )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]),\n                                              _vm._v(\" \"),\n                                              _c(\"li\", [\n                                                _c(\n                                                  \"a\",\n                                                  {\n                                                    attrs: {\n                                                      href: \"javascript:void(0)\",\n                                                    },\n                                                    on: {\n                                                      click: function ($event) {\n                                                        return _vm.delete_item(\n                                                          fd\n                                                        )\n                                                      },\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"span\", {\n                                                      staticClass:\n                                                        \"fa fa-trash\",\n                                                    }),\n                                                    _vm._v(\n                                                      \" \" +\n                                                        _vm._s(\n                                                          _vm.doc_str.delete\n                                                        )\n                                                    ),\n                                                  ]\n                                                ),\n                                              ]),\n                                            ]\n                                          ),\n                                        ]\n                                      : _vm._e(),\n                                    _vm._v(\" \"),\n                                    _vm.source == \"shared_with_me\"\n                                      ? [\n                                          fd.is_dir != true\n                                            ? _c(\n                                                \"ul\",\n                                                {\n                                                  staticClass:\n                                                    \"doc_action_dropdown\",\n                                                },\n                                                [\n                                                  fd.source == \"local\"\n                                                    ? _c(\"li\", [\n                                                        _c(\n                                                          \"a\",\n                                                          {\n                                                            attrs: {\n                                                              target: \"_blank\",\n                                                              href: fd.attactment_url,\n                                                              download: \"\",\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\"span\", {\n                                                              staticClass:\n                                                                \"fa fa-download\",\n                                                            }),\n                                                            _vm._v(\n                                                              \" \" +\n                                                                _vm._s(\n                                                                  _vm.doc_str\n                                                                    .download\n                                                                )\n                                                            ),\n                                                          ]\n                                                        ),\n                                                      ])\n                                                    : _vm._e(),\n                                                  _vm._v(\" \"),\n                                                  fd.source == \"dropbox\"\n                                                    ? _c(\"li\", [\n                                                        _c(\n                                                          \"a\",\n                                                          {\n                                                            attrs: {\n                                                              href: \"javascript:void(0)\",\n                                                            },\n                                                            on: {\n                                                              click: function (\n                                                                $event\n                                                              ) {\n                                                                return _vm.link_clicked(\n                                                                  fd\n                                                                )\n                                                              },\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\"span\", {\n                                                              staticClass:\n                                                                \"fa fa-download\",\n                                                            }),\n                                                            _vm._v(\n                                                              \" \" +\n                                                                _vm._s(\n                                                                  _vm.doc_str\n                                                                    .download\n                                                                )\n                                                            ),\n                                                          ]\n                                                        ),\n                                                      ])\n                                                    : _vm._e(),\n                                                ]\n                                              )\n                                            : _vm._e(),\n                                        ]\n                                      : _vm._e(),\n                                  ],\n                                  2\n                                ),\n                              ]\n                            ),\n                          ]),\n                        ])\n                      : _vm._e()\n                  }),\n                  _vm._v(\" \"),\n                  _vm.file_data.length == 0\n                    ? _c(\"div\", { class: _vm.view_type_child_class }, [\n                        _c(\"div\", { staticClass: \"list-body list-table-row\" }, [\n                          _c(\n                            \"div\",\n                            { staticClass: \"box-view list-table-cell\" },\n                            [\n                              _vm._m(2),\n                              _vm._v(\" \"),\n                              _c(\"div\", [\n                                _vm._v(_vm._s(_vm.doc_str.empty_list)),\n                              ]),\n                            ]\n                          ),\n                        ]),\n                      ])\n                    : _vm._e(),\n                ],\n                2\n              ),\n            ]),\n          ]),\n        ]),\n      ]),\n      _vm._v(\" \"),\n      _c(\"div\", { class: \"modal \" + _vm.create_folder_modal_open }, [\n        _c(\"div\", { staticClass: \"modal-background\" }),\n        _vm._v(\" \"),\n        _c(\n          \"div\",\n          {\n            class:\n              \"md-cont modal-content \" + _vm.create_folder_modal_open_clipped,\n          },\n          [\n            _c(\"div\", { staticClass: \"columns\" }, [\n              _c(\"div\", { staticClass: \"column\" }, [\n                _c(\"div\", { staticClass: \"modal_title\" }, [\n                  _vm._v(\" \" + _vm._s(_vm.doc_str.create_folder) + \" \"),\n                ]),\n                _vm._v(\" \"),\n                _c(\"div\", { staticClass: \"field\" }, [\n                  _c(\"div\", { staticClass: \"control\" }, [\n                    _c(\"input\", {\n                      directives: [\n                        {\n                          name: \"model\",\n                          rawName: \"v-model\",\n                          value: _vm.dir_name,\n                          expression: \"dir_name\",\n                        },\n                      ],\n                      staticClass: \"input is-primary\",\n                      attrs: {\n                        type: \"text\",\n                        placeholder: _vm.doc_str.plz_enter_folder_name,\n                      },\n                      domProps: { value: _vm.dir_name },\n                      on: {\n                        keyup: _vm.create_dir_by_ekey,\n                        input: function ($event) {\n                          if ($event.target.composing) {\n                            return\n                          }\n                          _vm.dir_name = $event.target.value\n                        },\n                      },\n                    }),\n                  ]),\n                ]),\n                _vm._v(\" \"),\n                _c(\"div\", { staticClass: \"control\" }, [\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"button is-primary\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.create_folder()\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.doc_str.submit))]\n                  ),\n                ]),\n              ]),\n            ]),\n          ]\n        ),\n        _vm._v(\" \"),\n        _c(\"button\", {\n          staticClass: \"modal-close is-large\",\n          attrs: { \"aria-label\": \"close\" },\n          on: {\n            click: function ($event) {\n              return _vm.open_folder_popup_close()\n            },\n          },\n        }),\n      ]),\n      _vm._v(\" \"),\n      _c(\"div\", { class: \"modal \" + _vm.move_to_modal_open }, [\n        _c(\"div\", { staticClass: \"modal-background\" }),\n        _vm._v(\" \"),\n        _c(\n          \"div\",\n          { class: \"md-cont modal-content \" + _vm.move_to_modal_open_clipped },\n          [\n            _c(\"div\", { staticClass: \"columns\" }, [\n              _c(\"div\", { staticClass: \"column\" }, [\n                _c(\"div\", { staticClass: \"modal_title\" }, [\n                  _vm._v(\" \" + _vm._s(_vm.doc_str.move_folder_to) + \" \"),\n                ]),\n                _vm._v(\" \"),\n                _c(\"div\", { staticClass: \"field\" }, [\n                  _vm.movable_folder.length > 0\n                    ? _c(\"div\", { staticClass: \"folder_tree\" }, [\n                        _c(\n                          \"ul\",\n                          _vm._l(_vm.movable_folder, function (mf) {\n                            return _c(\"li\", [\n                              _c(\n                                \"a\",\n                                {\n                                  attrs: { href: \"javascript:void(0)\" },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.set_target_folder(mf.id)\n                                    },\n                                  },\n                                },\n                                [\n                                  _c(\"i\", { staticClass: \"fa fa-folder-open\" }),\n                                  _vm._v(\" \" + _vm._s(mf.text) + \" \"),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              mf.children.length > 0\n                                ? _c(\n                                    \"ul\",\n                                    _vm._l(mf.children, function (mfc1) {\n                                      return _c(\"li\", [\n                                        _c(\n                                          \"a\",\n                                          {\n                                            attrs: {\n                                              href: \"javascript:void(0)\",\n                                            },\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.set_target_folder(\n                                                  mfc1.id\n                                                )\n                                              },\n                                            },\n                                          },\n                                          [\n                                            _c(\"i\", {\n                                              staticClass: \"fa fa-folder-open\",\n                                            }),\n                                            _vm._v(\n                                              \" \" + _vm._s(mfc1.text) + \" \"\n                                            ),\n                                          ]\n                                        ),\n                                        _vm._v(\" \"),\n                                        mfc1.children.length > 0\n                                          ? _c(\n                                              \"ul\",\n                                              _vm._l(\n                                                mfc1.children,\n                                                function (mfc2) {\n                                                  return _c(\"li\", [\n                                                    _c(\n                                                      \"a\",\n                                                      {\n                                                        attrs: {\n                                                          href: \"javascript:void(0)\",\n                                                        },\n                                                        on: {\n                                                          click: function (\n                                                            $event\n                                                          ) {\n                                                            return _vm.set_target_folder(\n                                                              mfc2.id\n                                                            )\n                                                          },\n                                                        },\n                                                      },\n                                                      [\n                                                        _c(\"i\", {\n                                                          staticClass:\n                                                            \"fa fa-folder-open\",\n                                                        }),\n                                                        _vm._v(\n                                                          \" \" +\n                                                            _vm._s(mfc2.text) +\n                                                            \" \"\n                                                        ),\n                                                      ]\n                                                    ),\n                                                    _vm._v(\" \"),\n                                                    mfc2.children.length > 0\n                                                      ? _c(\n                                                          \"ul\",\n                                                          _vm._l(\n                                                            mfc2.children,\n                                                            function (mfc3) {\n                                                              return _c(\"li\", [\n                                                                _c(\n                                                                  \"a\",\n                                                                  {\n                                                                    attrs: {\n                                                                      href: \"javascript:void(0)\",\n                                                                    },\n                                                                    on: {\n                                                                      click:\n                                                                        function (\n                                                                          $event\n                                                                        ) {\n                                                                          return _vm.set_target_folder(\n                                                                            mfc3.id\n                                                                          )\n                                                                        },\n                                                                    },\n                                                                  },\n                                                                  [\n                                                                    _c(\"i\", {\n                                                                      staticClass:\n                                                                        \"fa fa-folder-open\",\n                                                                    }),\n                                                                    _vm._v(\n                                                                      \" \" +\n                                                                        _vm._s(\n                                                                          mfc3.text\n                                                                        ) +\n                                                                        \" \"\n                                                                    ),\n                                                                  ]\n                                                                ),\n                                                                _vm._v(\" \"),\n                                                                mfc3.children\n                                                                  .length > 0\n                                                                  ? _c(\n                                                                      \"ul\",\n                                                                      _vm._l(\n                                                                        mfc3.children,\n                                                                        function (\n                                                                          mfc4\n                                                                        ) {\n                                                                          return _c(\n                                                                            \"li\",\n                                                                            [\n                                                                              _c(\n                                                                                \"a\",\n                                                                                {\n                                                                                  attrs:\n                                                                                    {\n                                                                                      href: \"javascript:void(0)\",\n                                                                                    },\n                                                                                  on: {\n                                                                                    click:\n                                                                                      function (\n                                                                                        $event\n                                                                                      ) {\n                                                                                        return _vm.set_target_folder(\n                                                                                          mfc4.id\n                                                                                        )\n                                                                                      },\n                                                                                  },\n                                                                                },\n                                                                                [\n                                                                                  _c(\n                                                                                    \"i\",\n                                                                                    {\n                                                                                      staticClass:\n                                                                                        \"fa fa-folder-open\",\n                                                                                    }\n                                                                                  ),\n                                                                                  _vm._v(\n                                                                                    \" \" +\n                                                                                      _vm._s(\n                                                                                        mfc4.text\n                                                                                      ) +\n                                                                                      \" \"\n                                                                                  ),\n                                                                                ]\n                                                                              ),\n                                                                              _vm._v(\n                                                                                \" \"\n                                                                              ),\n                                                                              mfc4\n                                                                                .children\n                                                                                .length >\n                                                                              0\n                                                                                ? _c(\n                                                                                    \"ul\",\n                                                                                    _vm._l(\n                                                                                      mfc4.children,\n                                                                                      function (\n                                                                                        mfc5\n                                                                                      ) {\n                                                                                        return _c(\n                                                                                          \"li\",\n                                                                                          [\n                                                                                            _c(\n                                                                                              \"a\",\n                                                                                              {\n                                                                                                attrs:\n                                                                                                  {\n                                                                                                    href: \"javascript:void(0)\",\n                                                                                                  },\n                                                                                                on: {\n                                                                                                  click:\n                                                                                                    function (\n                                                                                                      $event\n                                                                                                    ) {\n                                                                                                      return _vm.set_target_folder(\n                                                                                                        mfc5.id\n                                                                                                      )\n                                                                                                    },\n                                                                                                },\n                                                                                              },\n                                                                                              [\n                                                                                                _c(\n                                                                                                  \"i\",\n                                                                                                  {\n                                                                                                    staticClass:\n                                                                                                      \"fa fa-folder-open\",\n                                                                                                  }\n                                                                                                ),\n                                                                                                _vm._v(\n                                                                                                  \" \" +\n                                                                                                    _vm._s(\n                                                                                                      mfc5.text\n                                                                                                    ) +\n                                                                                                    \" \"\n                                                                                                ),\n                                                                                              ]\n                                                                                            ),\n                                                                                            _vm._v(\n                                                                                              \" \"\n                                                                                            ),\n                                                                                            mfc5\n                                                                                              .children\n                                                                                              .length >\n                                                                                            0\n                                                                                              ? _c(\n                                                                                                  \"ul\",\n                                                                                                  _vm._l(\n                                                                                                    mfc5.children,\n                                                                                                    function (\n                                                                                                      mfc6\n                                                                                                    ) {\n                                                                                                      return _c(\n                                                                                                        \"li\",\n                                                                                                        [\n                                                                                                          _c(\n                                                                                                            \"a\",\n                                                                                                            {\n                                                                                                              attrs:\n                                                                                                                {\n                                                                                                                  href: \"javascript:void(0)\",\n                                                                                                                },\n                                                                                                              on: {\n                                                                                                                click:\n                                                                                                                  function (\n                                                                                                                    $event\n                                                                                                                  ) {\n                                                                                                                    return _vm.set_target_folder(\n                                                                                                                      mfc6.id\n                                                                                                                    )\n                                                                                                                  },\n                                                                                                              },\n                                                                                                            },\n                                                                                                            [\n                                                                                                              _c(\n                                                                                                                \"i\",\n                                                                                                                {\n                                                                                                                  staticClass:\n                                                                                                                    \"fa fa-folder-open\",\n                                                                                                                }\n                                                                                                              ),\n                                                                                                              _vm._v(\n                                                                                                                \" \" +\n                                                                                                                  _vm._s(\n                                                                                                                    mfc6.text\n                                                                                                                  ) +\n                                                                                                                  \" \"\n                                                                                                              ),\n                                                                                                            ]\n                                                                                                          ),\n                                                                                                          _vm._v(\n                                                                                                            \" \"\n                                                                                                          ),\n                                                                                                          mfc6\n                                                                                                            .children\n                                                                                                            .length >\n                                                                                                          0\n                                                                                                            ? _c(\n                                                                                                                \"ul\",\n                                                                                                                _vm._l(\n                                                                                                                  mfc6.children,\n                                                                                                                  function (\n                                                                                                                    mfc7\n                                                                                                                  ) {\n                                                                                                                    return _c(\n                                                                                                                      \"li\",\n                                                                                                                      [\n                                                                                                                        _c(\n                                                                                                                          \"a\",\n                                                                                                                          {\n                                                                                                                            attrs:\n                                                                                                                              {\n                                                                                                                                href: \"javascript:void(0)\",\n                                                                                                                              },\n                                                                                                                            on: {\n                                                                                                                              click:\n                                                                                                                                function (\n                                                                                                                                  $event\n                                                                                                                                ) {\n                                                                                                                                  return _vm.set_target_folder(\n                                                                                                                                    mfc7.id\n                                                                                                                                  )\n                                                                                                                                },\n                                                                                                                            },\n                                                                                                                          },\n                                                                                                                          [\n                                                                                                                            _c(\n                                                                                                                              \"i\",\n                                                                                                                              {\n                                                                                                                                staticClass:\n                                                                                                                                  \"fa fa-folder-open\",\n                                                                                                                              }\n                                                                                                                            ),\n                                                                                                                            _vm._v(\n                                                                                                                              \" \" +\n                                                                                                                                _vm._s(\n                                                                                                                                  mfc7.text\n                                                                                                                                ) +\n                                                                                                                                \" \"\n                                                                                                                            ),\n                                                                                                                          ]\n                                                                                                                        ),\n                                                                                                                        _vm._v(\n                                                                                                                          \" \"\n                                                                                                                        ),\n                                                                                                                        mfc7\n                                                                                                                          .children\n                                                                                                                          .length >\n                                                                                                                        0\n                                                                                                                          ? _c(\n                                                                                                                              \"ul\",\n                                                                                                                              _vm._l(\n                                                                                                                                mfc7.children,\n                                                                                                                                function (\n                                                                                                                                  mfc8\n                                                                                                                                ) {\n                                                                                                                                  return _c(\n                                                                                                                                    \"li\",\n                                                                                                                                    [\n                                                                                                                                      _c(\n                                                                                                                                        \"a\",\n                                                                                                                                        {\n                                                                                                                                          attrs:\n                                                                                                                                            {\n                                                                                                                                              href: \"javascript:void(0)\",\n                                                                                                                                            },\n                                                                                                                                          on: {\n                                                                                                                                            click:\n                                                                                                                                              function (\n                                                                                                                                                $event\n                                                                                                                                              ) {\n                                                                                                                                                return _vm.set_target_folder(\n                                                                                                                                                  mfc8.id\n                                                                                                                                                )\n                                                                                                                                              },\n                                                                                                                                          },\n                                                                                                                                        },\n                                                                                                                                        [\n                                                                                                                                          _c(\n                                                                                                                                            \"i\",\n                                                                                                                                            {\n                                                                                                                                              staticClass:\n                                                                                                                                                \"fa fa-folder-open\",\n                                                                                                                                            }\n                                                                                                                                          ),\n                                                                                                                                          _vm._v(\n                                                                                                                                            \" \" +\n                                                                                                                                              _vm._s(\n                                                                                                                                                mfc8.text\n                                                                                                                                              ) +\n                                                                                                                                              \" \"\n                                                                                                                                          ),\n                                                                                                                                        ]\n                                                                                                                                      ),\n                                                                                                                                      _vm._v(\n                                                                                                                                        \" \"\n                                                                                                                                      ),\n                                                                                                                                      mfc8\n                                                                                                                                        .children\n                                                                                                                                        .length >\n                                                                                                                                      0\n                                                                                                                                        ? _c(\n                                                                                                                                            \"ul\",\n                                                                                                                                            _vm._l(\n                                                                                                                                              mfc8.children,\n                                                                                                                                              function (\n                                                                                                                                                mfc9\n                                                                                                                                              ) {\n                                                                                                                                                return _c(\n                                                                                                                                                  \"li\",\n                                                                                                                                                  [\n                                                                                                                                                    _c(\n                                                                                                                                                      \"a\",\n                                                                                                                                                      {\n                                                                                                                                                        attrs:\n                                                                                                                                                          {\n                                                                                                                                                            href: \"javascript:void(0)\",\n                                                                                                                                                          },\n                                                                                                                                                        on: {\n                                                                                                                                                          click:\n                                                                                                                                                            function (\n                                                                                                                                                              $event\n                                                                                                                                                            ) {\n                                                                                                                                                              return _vm.set_target_folder(\n                                                                                                                                                                mfc9.id\n                                                                                                                                                              )\n                                                                                                                                                            },\n                                                                                                                                                        },\n                                                                                                                                                      },\n                                                                                                                                                      [\n                                                                                                                                                        _c(\n                                                                                                                                                          \"i\",\n                                                                                                                                                          {\n                                                                                                                                                            staticClass:\n                                                                                                                                                              \"fa fa-folder-open\",\n                                                                                                                                                          }\n                                                                                                                                                        ),\n                                                                                                                                                        _vm._v(\n                                                                                                                                                          \" \" +\n                                                                                                                                                            _vm._s(\n                                                                                                                                                              mfc9.text\n                                                                                                                                                            ) +\n                                                                                                                                                            \" \"\n                                                                                                                                                        ),\n                                                                                                                                                      ]\n                                                                                                                                                    ),\n                                                                                                                                                    _vm._v(\n                                                                                                                                                      \" \"\n                                                                                                                                                    ),\n                                                                                                                                                    mfc9\n                                                                                                                                                      .children\n                                                                                                                                                      .length >\n                                                                                                                                                    0\n                                                                                                                                                      ? _c(\n                                                                                                                                                          \"ul\",\n                                                                                                                                                          _vm._l(\n                                                                                                                                                            mfc9.children,\n                                                                                                                                                            function (\n                                                                                                                                                              mfc10\n                                                                                                                                                            ) {\n                                                                                                                                                              return _c(\n                                                                                                                                                                \"li\",\n                                                                                                                                                                [\n                                                                                                                                                                  _c(\n                                                                                                                                                                    \"a\",\n                                                                                                                                                                    {\n                                                                                                                                                                      attrs:\n                                                                                                                                                                        {\n                                                                                                                                                                          href: \"javascript:void(0)\",\n                                                                                                                                                                        },\n                                                                                                                                                                      on: {\n                                                                                                                                                                        click:\n                                                                                                                                                                          function (\n                                                                                                                                                                            $event\n                                                                                                                                                                          ) {\n                                                                                                                                                                            return _vm.set_target_folder(\n                                                                                                                                                                              mfc10.id\n                                                                                                                                                                            )\n                                                                                                                                                                          },\n                                                                                                                                                                      },\n                                                                                                                                                                    },\n                                                                                                                                                                    [\n                                                                                                                                                                      _c(\n                                                                                                                                                                        \"i\",\n                                                                                                                                                                        {\n                                                                                                                                                                          staticClass:\n                                                                                                                                                                            \"fa fa-folder-open\",\n                                                                                                                                                                        }\n                                                                                                                                                                      ),\n                                                                                                                                                                      _vm._v(\n                                                                                                                                                                        \" \" +\n                                                                                                                                                                          _vm._s(\n                                                                                                                                                                            mfc10.text\n                                                                                                                                                                          ) +\n                                                                                                                                                                          \" \"\n                                                                                                                                                                      ),\n                                                                                                                                                                    ]\n                                                                                                                                                                  ),\n                                                                                                                                                                  _vm._v(\n                                                                                                                                                                    \" \"\n                                                                                                                                                                  ),\n                                                                                                                                                                  mfc10\n                                                                                                                                                                    .children\n                                                                                                                                                                    .length >\n                                                                                                                                                                  0\n                                                                                                                                                                    ? _c(\n                                                                                                                                                                        \"ul\",\n                                                                                                                                                                        _vm._l(\n                                                                                                                                                                          mfc10.children,\n                                                                                                                                                                          function (\n                                                                                                                                                                            mfc11\n                                                                                                                                                                          ) {\n                                                                                                                                                                            return _c(\n                                                                                                                                                                              \"li\",\n                                                                                                                                                                              [\n                                                                                                                                                                                _c(\n                                                                                                                                                                                  \"a\",\n                                                                                                                                                                                  {\n                                                                                                                                                                                    attrs:\n                                                                                                                                                                                      {\n                                                                                                                                                                                        href: \"javascript:void(0)\",\n                                                                                                                                                                                      },\n                                                                                                                                                                                    on: {\n                                                                                                                                                                                      click:\n                                                                                                                                                                                        function (\n                                                                                                                                                                                          $event\n                                                                                                                                                                                        ) {\n                                                                                                                                                                                          return _vm.set_target_folder(\n                                                                                                                                                                                            mfc11.id\n                                                                                                                                                                                          )\n                                                                                                                                                                                        },\n                                                                                                                                                                                    },\n                                                                                                                                                                                  },\n                                                                                                                                                                                  [\n                                                                                                                                                                                    _c(\n                                                                                                                                                                                      \"i\",\n                                                                                                                                                                                      {\n                                                                                                                                                                                        staticClass:\n                                                                                                                                                                                          \"fa fa-folder-open\",\n                                                                                                                                                                                      }\n                                                                                                                                                                                    ),\n                                                                                                                                                                                    _vm._v(\n                                                                                                                                                                                      \" \" +\n                                                                                                                                                                                        _vm._s(\n                                                                                                                                                                                          mfc11.text\n                                                                                                                                                                                        ) +\n                                                                                                                                                                                        \" \"\n                                                                                                                                                                                    ),\n                                                                                                                                                                                  ]\n                                                                                                                                                                                ),\n                                                                                                                                                                                _vm._v(\n                                                                                                                                                                                  \" \"\n                                                                                                                                                                                ),\n                                                                                                                                                                                mfc11\n                                                                                                                                                                                  .children\n                                                                                                                                                                                  .length >\n                                                                                                                                                                                0\n                                                                                                                                                                                  ? _c(\n                                                                                                                                                                                      \"ul\",\n                                                                                                                                                                                      _vm._l(\n                                                                                                                                                                                        mfc11.children,\n                                                                                                                                                                                        function (\n                                                                                                                                                                                          mfc12\n                                                                                                                                                                                        ) {\n                                                                                                                                                                                          return _c(\n                                                                                                                                                                                            \"li\",\n                                                                                                                                                                                            [\n                                                                                                                                                                                              _c(\n                                                                                                                                                                                                \"a\",\n                                                                                                                                                                                                {\n                                                                                                                                                                                                  attrs:\n                                                                                                                                                                                                    {\n                                                                                                                                                                                                      href: \"javascript:void(0)\",\n                                                                                                                                                                                                    },\n                                                                                                                                                                                                  on: {\n                                                                                                                                                                                                    click:\n                                                                                                                                                                                                      function (\n                                                                                                                                                                                                        $event\n                                                                                                                                                                                                      ) {\n                                                                                                                                                                                                        return _vm.set_target_folder(\n                                                                                                                                                                                                          mfc12.id\n                                                                                                                                                                                                        )\n                                                                                                                                                                                                      },\n                                                                                                                                                                                                  },\n                                                                                                                                                                                                },\n                                                                                                                                                                                                [\n                                                                                                                                                                                                  _c(\n                                                                                                                                                                                                    \"i\",\n                                                                                                                                                                                                    {\n                                                                                                                                                                                                      staticClass:\n                                                                                                                                                                                                        \"fa fa-folder-open\",\n                                                                                                                                                                                                    }\n                                                                                                                                                                                                  ),\n                                                                                                                                                                                                  _vm._v(\n                                                                                                                                                                                                    \" \" +\n                                                                                                                                                                                                      _vm._s(\n                                                                                                                                                                                                        mfc12.text\n                                                                                                                                                                                                      ) +\n                                                                                                                                                                                                      \" \"\n                                                                                                                                                                                                  ),\n                                                                                                                                                                                                ]\n                                                                                                                                                                                              ),\n                                                                                                                                                                                              _vm._v(\n                                                                                                                                                                                                \" \"\n                                                                                                                                                                                              ),\n                                                                                                                                                                                              mfc12\n                                                                                                                                                                                                .children\n                                                                                                                                                                                                .length >\n                                                                                                                                                                                              0\n                                                                                                                                                                                                ? _c(\n                                                                                                                                                                                                    \"ul\",\n                                                                                                                                                                                                    _vm._l(\n                                                                                                                                                                                                      mfc12.children,\n                                                                                                                                                                                                      function (\n                                                                                                                                                                                                        mfc13\n                                                                                                                                                                                                      ) {\n                                                                                                                                                                                                        return _c(\n                                                                                                                                                                                                          \"li\",\n                                                                                                                                                                                                          [\n                                                                                                                                                                                                            _c(\n                                                                                                                                                                                                              \"a\",\n                                                                                                                                                                                                              {\n                                                                                                                                                                                                                attrs:\n                                                                                                                                                                                                                  {\n                                                                                                                                                                                                                    href: \"javascript:void(0)\",\n                                                                                                                                                                                                                  },\n                                                                                                                                                                                                                on: {\n                                                                                                                                                                                                                  click:\n                                                                                                                                                                                                                    function (\n                                                                                                                                                                                                                      $event\n                                                                                                                                                                                                                    ) {\n                                                                                                                                                                                                                      return _vm.set_target_folder(\n                                                                                                                                                                                                                        mfc13.id\n                                                                                                                                                                                                                      )\n                                                                                                                                                                                                                    },\n                                                                                                                                                                                                                },\n                                                                                                                                                                                                              },\n                                                                                                                                                                                                              [\n                                                                                                                                                                                                                _c(\n                                                                                                                                                                                                                  \"i\",\n                                                                                                                                                                                                                  {\n                                                                                                                                                                                                                    staticClass:\n                                                                                                                                                                                                                      \"fa fa-folder-open\",\n                                                                                                                                                                                                                  }\n                                                                                                                                                                                                                ),\n                                                                                                                                                                                                                _vm._v(\n                                                                                                                                                                                                                  \" \" +\n                                                                                                                                                                                                                    _vm._s(\n                                                                                                                                                                                                                      mfc13.text\n                                                                                                                                                                                                                    ) +\n                                                                                                                                                                                                                    \" \"\n                                                                                                                                                                                                                ),\n                                                                                                                                                                                                              ]\n                                                                                                                                                                                                            ),\n                                                                                                                                                                                                            _vm._v(\n                                                                                                                                                                                                              \" \"\n                                                                                                                                                                                                            ),\n                                                                                                                                                                                                            mfc13\n                                                                                                                                                                                                              .children\n                                                                                                                                                                                                              .length >\n                                                                                                                                                                                                            0\n                                                                                                                                                                                                              ? _c(\n                                                                                                                                                                                                                  \"ul\",\n                                                                                                                                                                                                                  _vm._l(\n                                                                                                                                                                                                                    mfc13.children,\n                                                                                                                                                                                                                    function (\n                                                                                                                                                                                                                      mfc14\n                                                                                                                                                                                                                    ) {\n                                                                                                                                                                                                                      return _c(\n                                                                                                                                                                                                                        \"li\",\n                                                                                                                                                                                                                        [\n                                                                                                                                                                                                                          _c(\n                                                                                                                                                                                                                            \"a\",\n                                                                                                                                                                                                                            {\n                                                                                                                                                                                                                              attrs:\n                                                                                                                                                                                                                                {\n                                                                                                                                                                                                                                  href: \"javascript:void(0)\",\n                                                                                                                                                                                                                                },\n                                                                                                                                                                                                                              on: {\n                                                                                                                                                                                                                                click:\n                                                                                                                                                                                                                                  function (\n                                                                                                                                                                                                                                    $event\n                                                                                                                                                                                                                                  ) {\n                                                                                                                                                                                                                                    return _vm.set_target_folder(\n                                                                                                                                                                                                                                      mfc14.id\n                                                                                                                                                                                                                                    )\n                                                                                                                                                                                                                                  },\n                                                                                                                                                                                                                              },\n                                                                                                                                                                                                                            },\n                                                                                                                                                                                                                            [\n                                                                                                                                                                                                                              _c(\n                                                                                                                                                                                                                                \"i\",\n                                                                                                                                                                                                                                {\n                                                                                                                                                                                                                                  staticClass:\n                                                                                                                                                                                                                                    \"fa fa-folder-open\",\n                                                                                                                                                                                                                                }\n                                                                                                                                                                                                                              ),\n                                                                                                                                                                                                                              _vm._v(\n                                                                                                                                                                                                                                \" \" +\n                                                                                                                                                                                                                                  _vm._s(\n                                                                                                                                                                                                                                    mfc14.text\n                                                                                                                                                                                                                                  ) +\n                                                                                                                                                                                                                                  \" \"\n                                                                                                                                                                                                                              ),\n                                                                                                                                                                                                                            ]\n                                                                                                                                                                                                                          ),\n                                                                                                                                                                                                                        ]\n                                                                                                                                                                                                                      )\n                                                                                                                                                                                                                    }\n                                                                                                                                                                                                                  ),\n                                                                                                                                                                                                                  0\n                                                                                                                                                                                                                )\n                                                                                                                                                                                                              : _vm._e(),\n                                                                                                                                                                                                          ]\n                                                                                                                                                                                                        )\n                                                                                                                                                                                                      }\n                                                                                                                                                                                                    ),\n                                                                                                                                                                                                    0\n                                                                                                                                                                                                  )\n                                                                                                                                                                                                : _vm._e(),\n                                                                                                                                                                                            ]\n                                                                                                                                                                                          )\n                                                                                                                                                                                        }\n                                                                                                                                                                                      ),\n                                                                                                                                                                                      0\n                                                                                                                                                                                    )\n                                                                                                                                                                                  : _vm._e(),\n                                                                                                                                                                              ]\n                                                                                                                                                                            )\n                                                                                                                                                                          }\n                                                                                                                                                                        ),\n                                                                                                                                                                        0\n                                                                                                                                                                      )\n                                                                                                                                                                    : _vm._e(),\n                                                                                                                                                                ]\n                                                                                                                                                              )\n                                                                                                                                                            }\n                                                                                                                                                          ),\n                                                                                                                                                          0\n                                                                                                                                                        )\n                                                                                                                                                      : _vm._e(),\n                                                                                                                                                  ]\n                                                                                                                                                )\n                                                                                                                                              }\n                                                                                                                                            ),\n                                                                                                                                            0\n                                                                                                                                          )\n                                                                                                                                        : _vm._e(),\n                                                                                                                                    ]\n                                                                                                                                  )\n                                                                                                                                }\n                                                                                                                              ),\n                                                                                                                              0\n                                                                                                                            )\n                                                                                                                          : _vm._e(),\n                                                                                                                      ]\n                                                                                                                    )\n                                                                                                                  }\n                                                                                                                ),\n                                                                                                                0\n                                                                                                              )\n                                                                                                            : _vm._e(),\n                                                                                                        ]\n                                                                                                      )\n                                                                                                    }\n                                                                                                  ),\n                                                                                                  0\n                                                                                                )\n                                                                                              : _vm._e(),\n                                                                                          ]\n                                                                                        )\n                                                                                      }\n                                                                                    ),\n                                                                                    0\n                                                                                  )\n                                                                                : _vm._e(),\n                                                                            ]\n                                                                          )\n                                                                        }\n                                                                      ),\n                                                                      0\n                                                                    )\n                                                                  : _vm._e(),\n                                                              ])\n                                                            }\n                                                          ),\n                                                          0\n                                                        )\n                                                      : _vm._e(),\n                                                  ])\n                                                }\n                                              ),\n                                              0\n                                            )\n                                          : _vm._e(),\n                                      ])\n                                    }),\n                                    0\n                                  )\n                                : _vm._e(),\n                            ])\n                          }),\n                          0\n                        ),\n                      ])\n                    : _c(\"div\", { staticClass: \"folder_tree\" }, [\n                        _vm._v(\n                          \" There is no available folder right now. Please create one. \"\n                        ),\n                      ]),\n                ]),\n                _vm._v(\" \"),\n                _c(\"div\", { staticClass: \"control\" }, [\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"button is-primary is-pulled-right\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.move()\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.doc_str.move))]\n                  ),\n                ]),\n              ]),\n            ]),\n          ]\n        ),\n        _vm._v(\" \"),\n        _c(\"button\", {\n          staticClass: \"modal-close is-large\",\n          attrs: { \"aria-label\": \"close\" },\n          on: {\n            click: function ($event) {\n              return _vm.move_to_popup_close()\n            },\n          },\n        }),\n      ]),\n      _vm._v(\" \"),\n      _c(\"div\", { class: \"modal \" + _vm.rename_folder_modal_open }, [\n        _c(\"div\", { staticClass: \"modal-background\" }),\n        _vm._v(\" \"),\n        _c(\n          \"div\",\n          {\n            class:\n              \"md-cont modal-content \" + _vm.rename_folder_modal_open_clipped,\n          },\n          [\n            _c(\"div\", { staticClass: \"columns\" }, [\n              _c(\"div\", { staticClass: \"column\" }, [\n                _c(\"div\", { staticClass: \"modal_title\" }, [\n                  _vm._v(\" \" + _vm._s(_vm.doc_str.rename_folder_to) + \" \"),\n                ]),\n                _vm._v(\" \"),\n                _c(\"div\", { staticClass: \"field\" }, [\n                  _c(\"div\", { staticClass: \"control\" }, [\n                    _c(\"input\", {\n                      directives: [\n                        {\n                          name: \"model\",\n                          rawName: \"v-model\",\n                          value: _vm.rename_data.dirName,\n                          expression: \"rename_data.dirName\",\n                        },\n                      ],\n                      staticClass: \"input is-primary\",\n                      attrs: {\n                        type: \"text\",\n                        placeholder: _vm.doc_str.plz_enter_folder_name,\n                      },\n                      domProps: { value: _vm.rename_data.dirName },\n                      on: {\n                        keyup: _vm.enter_to_rename,\n                        input: function ($event) {\n                          if ($event.target.composing) {\n                            return\n                          }\n                          _vm.$set(\n                            _vm.rename_data,\n                            \"dirName\",\n                            $event.target.value\n                          )\n                        },\n                      },\n                    }),\n                  ]),\n                ]),\n                _vm._v(\" \"),\n                _c(\"div\", { staticClass: \"control\" }, [\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"button is-primary\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.rename_folder()\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.doc_str.submit))]\n                  ),\n                ]),\n              ]),\n            ]),\n          ]\n        ),\n        _vm._v(\" \"),\n        _c(\"button\", {\n          staticClass: \"modal-close is-large\",\n          attrs: { \"aria-label\": \"close\" },\n          on: {\n            click: function ($event) {\n              return _vm.rename_folder_popup_close()\n            },\n          },\n        }),\n      ]),\n      _vm._v(\" \"),\n      _c(\"div\", { class: \"modal \" + _vm.share_to_modal_open }, [\n        _c(\"div\", { staticClass: \"modal-background\" }),\n        _vm._v(\" \"),\n        _c(\n          \"div\",\n          { class: \"md-cont modal-content \" + _vm.share_to_modal_open_clipped },\n          [\n            _c(\"div\", { staticClass: \"columns\" }, [\n              _c(\"form\", { attrs: { id: \"erp-doc-share-template\" } }, [\n                _c(\"div\", { staticClass: \"column\" }, [\n                  _c(\"div\", { staticClass: \"modal_title\" }, [\n                    _vm._v(\" Share to \"),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"field\" }, [\n                    _vm._m(3),\n                    _vm._v(\" \"),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass:\n                          \"control share_files_control by_employee no-show\",\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"select is-multiple columns\" },\n                          [\n                            _c(\"label\", { staticClass: \"column\" }, [\n                              _vm._v(\"Selected Employees \"),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"select\",\n                              {\n                                staticClass: \"column\",\n                                attrs: {\n                                  id: \"selected_emp[]\",\n                                  name: \"selected_emp[]\",\n                                },\n                              },\n                              _vm._l(_vm.employees, function (employee) {\n                                return _c(\n                                  \"option\",\n                                  { domProps: { value: employee.user_id } },\n                                  [_vm._v(_vm._s(employee.full_name))]\n                                )\n                              }),\n                              0\n                            ),\n                          ]\n                        ),\n                      ]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass:\n                          \"control share_files_control by_department no-show\",\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"select is-multiple columns\" },\n                          [\n                            _c(\"label\", { staticClass: \"column\" }, [\n                              _vm._v(\" Departments \"),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"select\",\n                              {\n                                staticClass: \"column\",\n                                attrs: { id: \"department\", name: \"department\" },\n                              },\n                              [\n                                _c(\"option\", { attrs: { value: \"-1\" } }, [\n                                  _vm._v(\"All departments\"),\n                                ]),\n                                _vm._v(\" \"),\n                                _vm._l(_vm.departments, function (department) {\n                                  return _c(\n                                    \"option\",\n                                    { domProps: { value: department.id } },\n                                    [_vm._v(_vm._s(department.title))]\n                                  )\n                                }),\n                              ],\n                              2\n                            ),\n                          ]\n                        ),\n                      ]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass:\n                          \"control share_files_control by_designation no-show\",\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"select is-multiple columns\" },\n                          [\n                            _c(\"label\", { staticClass: \"column\" }, [\n                              _vm._v(\" Designations \"),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"select\",\n                              {\n                                staticClass: \"column\",\n                                attrs: {\n                                  id: \"designation\",\n                                  name: \"designation\",\n                                },\n                              },\n                              [\n                                _c(\"option\", { attrs: { value: \"-1\" } }, [\n                                  _vm._v(\"All designations\"),\n                                ]),\n                                _vm._v(\" \"),\n                                _vm._l(\n                                  _vm.designations,\n                                  function (designation) {\n                                    return _c(\n                                      \"option\",\n                                      { domProps: { value: designation.id } },\n                                      [_vm._v(_vm._s(designation.title))]\n                                    )\n                                  }\n                                ),\n                              ],\n                              2\n                            ),\n                          ]\n                        ),\n                      ]\n                    ),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"input\", {\n                    attrs: { type: \"hidden\", name: \"owner_id\" },\n                    domProps: { value: _vm.employee_id },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"control\" }, [\n                    _c(\n                      \"button\",\n                      {\n                        staticClass: \"button is-primary\",\n                        attrs: { type: \"button\" },\n                        on: {\n                          click: function ($event) {\n                            return _vm.share_dir_file()\n                          },\n                        },\n                      },\n                      [_vm._v(\"Share\")]\n                    ),\n                  ]),\n                ]),\n              ]),\n            ]),\n          ]\n        ),\n        _vm._v(\" \"),\n        _c(\"button\", {\n          staticClass: \"modal-close is-large\",\n          attrs: { \"aria-label\": \"close\" },\n          on: {\n            click: function ($event) {\n              return _vm.share_to_popup_close()\n            },\n          },\n        }),\n      ]),\n    ]\n  )\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"span\", { staticClass: \"icon is-small is-right\" }, [\n      _c(\"i\", { staticClass: \"fa fa-search\" }),\n    ])\n  },\n  function () {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"span\", [_c(\"i\", { staticClass: \"fa fa-folder-open\" })])\n  },\n  function () {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticStyle: { \"font-size\": \"30px\" } }, [\n      _c(\"i\", { staticClass: \"fa fa-folder\" }),\n    ])\n  },\n  function () {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"control share_files_control\" }, [\n      _c(\"div\", { staticClass: \"select is-multiple columns\" }, [\n        _c(\"label\", { staticClass: \"column\" }, [_vm._v(\" Shared by \")]),\n        _vm._v(\" \"),\n        _c(\n          \"select\",\n          {\n            staticClass: \"column\",\n            attrs: { id: \"share_by\", name: \"share_by\" },\n          },\n          [\n            _c(\"option\", { attrs: { value: \"all_employees\" } }, [\n              _vm._v(\"All employees\"),\n            ]),\n            _vm._v(\" \"),\n            _c(\"option\", { attrs: { value: \"by_department\" } }, [\n              _vm._v(\"By department\"),\n            ]),\n            _vm._v(\" \"),\n            _c(\"option\", { attrs: { value: \"by_designation\" } }, [\n              _vm._v(\"By designation\"),\n            ]),\n            _vm._v(\" \"),\n            _c(\"option\", { attrs: { value: \"by_employee\" } }, [\n              _vm._v(\"By employee\"),\n            ]),\n          ]\n        ),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\n\n\n//# sourceURL=webpack:///./modules/hrm/document-manager/assets/src/frontend/components/document_manager_indexed.vue?./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options");

/***/ }),

/***/ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js":
/*!********************************************************************!*\
  !*** ./node_modules/vue-loader/lib/runtime/componentNormalizer.js ***!
  \********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return normalizeComponent; });\n/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nfunction normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () {\n        injectStyles.call(\n          this,\n          (options.functional ? this.parent : this).$root.$options.shadowRoot\n        )\n      }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functional component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n\n\n//# sourceURL=webpack:///./node_modules/vue-loader/lib/runtime/componentNormalizer.js?");

/***/ })

/******/ });