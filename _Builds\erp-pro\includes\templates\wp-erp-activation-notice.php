<div class="updated" id="wp-wsl-erp-installer-notice" style="padding: 1em; position: relative;">
    <h2><?php _e( 'Your ERP Pro is almost ready!', 'wsl-erp' ); ?></h2>

    <a href="<?php echo wp_nonce_url( 'plugins.php?action=deactivate&amp;plugin=' . $plugin_file . '&amp;plugin_status=all&amp;paged=1&amp;s=', 'deactivate-plugin_' . $plugin_file ); ?>" class="notice-dismiss" style="text-decoration: none;" title="<?php _e( 'Dismiss this notice', 'wsl-erp' ); ?>"></a>

    <?php if ( file_exists( WP_PLUGIN_DIR . '/' . $core_plugin_file ) && is_plugin_inactive( 'wsl-erp' ) ): ?>
        <p><?php echo sprintf( __( 'You just need to activate the <strong>%s</strong> to make it functional.', 'wsl-erp' ), 'WP ERP – Complete WordPress Business Manager with HR, CRM & Accounting Systems for Small Businesses' ); ?></p>
        <p>
            <a class="button button-primary" href="<?php echo wp_nonce_url( 'plugins.php?action=activate&amp;plugin=' . $core_plugin_file . '&amp;plugin_status=all&amp;paged=1&amp;s=', 'activate-plugin_' . $core_plugin_file ); ?>"  title="<?php _e( 'Activate this plugin', 'wsl-erp' ); ?>"><?php _e( 'Activate', 'wsl-erp' ); ?></a>
        </p>
    <?php else: ?>
        <p><?php echo sprintf( __( "You just need to install the %sCore Plugin%s to make it functional.", "wsl-erp" ), '<a target="_blank" href="https://wordpress.org/plugins/erp/">', '</a>' ); ?></p>

        <p>
            <button id="wp-wsl-erp-installer" class="button"><?php _e( 'Install Now', 'wsl-erp' ); ?></button>
        </p>
    <?php endif ?>
</div>

<script type="text/javascript">
    ( function ( $ ) {
        $( '#wpbody' ).on( 'click', '#wp-wsl-erp-installer', function ( e ) {
            e.preventDefault();
            $( this ).addClass( 'install-now updating-message' );
            $( this ).text( '<?php echo esc_js( 'Installing...', 'wp-erp' ); ?>' );

            var data = {
                action: 'wp_WSL_ERP_install_erp',
                _wpnonce: '<?php echo wp_create_nonce( 'wp-wsl-erp-installer-nonce' ); ?>'
            };

            $.post( ajaxurl, data, function ( response ) {
                if ( response.success ) {
                    $( '#wp-wsl-erp-installer-notice #wp-wsl-erp-installer' ).attr( 'disabled', 'disabled' );
                    $( '#wp-wsl-erp-installer-notice #wp-wsl-erp-installer' ).removeClass( 'install-now updating-message' );
                    $( '#wp-wsl-erp-installer-notice #wp-wsl-erp-installer' ).text( '<?php echo esc_js( 'Installed', 'wsl-erp' ); ?>' );
                    window.location.reload();
                }
            } );
        } );
    } )( jQuery );
</script>
