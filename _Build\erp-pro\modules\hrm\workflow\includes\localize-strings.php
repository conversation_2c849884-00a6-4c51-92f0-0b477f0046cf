<?php

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

return $localize_strings = [
    'no_action_added'          => __( 'No action added', 'erp-pro' ),
    'choose_an_action'         => __( 'Choose an action', 'erp-pro' ),
    'action_name'              => __( 'Action Name', 'erp-pro' ),
    'role'                     => __( 'Role', 'erp-pro' ),
    'users'                    => __( 'Users', 'erp-pro' ),
    'employees'                => __( 'Employees', 'erp-pro' ),
    'task_title'               => __( 'Task Title', 'erp-pro' ),
    'description'              => __( 'Description', 'erp-pro' ),
    'select_condition'         => __( 'Select Condition', 'erp-pro' ),
    'hook_name'                => __( 'Hook Name', 'erp-pro' ),
    'subject'                  => __( 'Subject', 'erp-pro' ),
    'to'                       => __( 'To', 'erp-pro' ),
    'contact_itself'           => __( 'The Contact Itself', 'erp-pro' ),
    'user_itself'              => __( 'The User Itself', 'erp-pro' ),
    'customer_itself'          => __( 'The Customer Itself', 'erp-pro' ),
    'employee_itself'          => __( 'The Employee Itself', 'erp-pro' ),
    'message'                  => __( 'Message', 'erp-pro' ),
    'log_type'                 => __( 'Log Type', 'erp-pro' ),
    'date'                     => __( 'Date', 'erp-pro' ),
    'time'                     => __( 'Time', 'erp-pro' ),
    'invite_crm_user'          => __( 'Invite CRM User', 'erp-pro' ),
    'add_this_action'          => __( 'Add this action', 'erp-pro' ),
    'update_this_action'       => __( 'Update this action', 'erp-pro' ),
    'choose_a_contact'         => __( 'Choose a contact', 'erp-pro' ),
    'schedule_title'           => __( 'Schedule Title', 'erp-pro' ),
    'start_date'               => __( 'Start Date', 'erp-pro' ),
    'end_date'                 => __( 'End Date', 'erp-pro' ),
    'all_day'                  => __( 'All Day', 'erp-pro' ),
    'schedule_type'            => __( 'Schedule Type', 'erp-pro' ),
    'allow_notification'       => __( 'Allow Notification', 'erp-pro' ),
    'notify_via'               => __( 'Notify Via', 'erp-pro' ),
    'notify_before'            => __( 'Notify Before', 'erp-pro' ),
    'field_name'               => __( 'Field Name', 'erp-pro' ),
    'field_value'              => __( 'Field Value', 'erp-pro' ),
    'notification'             => __( 'Notification', 'erp-pro' ),
    'allow'                    => __( 'Allow', 'erp-pro' ),
    'text_variables'           => __( 'Text Variables', 'erp-pro' ),
    'insert'                   => __( 'Insert', 'erp-pro' ),
    'enter_value'              => __( 'Enter value', 'erp-pro' ),
    'text_variables_help_text' => __( 'The text variables are coming from the current event. <br/>Select any of the following text variable and click on the insert button.', 'erp-pro' ),
    'required_message'         => __( 'This field is required', 'erp-pro' ),
    'select_contacts'          => __( 'Select Contact(s)', 'erp-pro' ),
    'select_employees'         => __( 'Select Employee(s)', 'erp-pro' ),
    'select_crm_users'         => __( 'Select CRM User(s)', 'erp-pro' ),
    'or'                       => __( 'OR', 'erp-pro' ),
    'atleast_an_action'        => __( 'You should add at least an action!', 'erp-pro' ),
    'successfully_saved'       => __( 'Successfully saved!', 'erp-pro' ),
];
