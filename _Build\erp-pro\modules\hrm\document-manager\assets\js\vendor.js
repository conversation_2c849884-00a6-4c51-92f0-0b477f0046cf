/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 15);
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/process/browser.js":
/*!*****************************************!*\
  !*** ./node_modules/process/browser.js ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("// shim for using process in browser\nvar process = module.exports = {}; // cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n  throw new Error('setTimeout has not been defined');\n}\n\nfunction defaultClearTimeout() {\n  throw new Error('clearTimeout has not been defined');\n}\n\n(function () {\n  try {\n    if (typeof setTimeout === 'function') {\n      cachedSetTimeout = setTimeout;\n    } else {\n      cachedSetTimeout = defaultSetTimout;\n    }\n  } catch (e) {\n    cachedSetTimeout = defaultSetTimout;\n  }\n\n  try {\n    if (typeof clearTimeout === 'function') {\n      cachedClearTimeout = clearTimeout;\n    } else {\n      cachedClearTimeout = defaultClearTimeout;\n    }\n  } catch (e) {\n    cachedClearTimeout = defaultClearTimeout;\n  }\n})();\n\nfunction runTimeout(fun) {\n  if (cachedSetTimeout === setTimeout) {\n    //normal enviroments in sane situations\n    return setTimeout(fun, 0);\n  } // if setTimeout wasn't available but was latter defined\n\n\n  if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n    cachedSetTimeout = setTimeout;\n    return setTimeout(fun, 0);\n  }\n\n  try {\n    // when when somebody has screwed with setTimeout but no I.E. maddness\n    return cachedSetTimeout(fun, 0);\n  } catch (e) {\n    try {\n      // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n      return cachedSetTimeout.call(null, fun, 0);\n    } catch (e) {\n      // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n      return cachedSetTimeout.call(this, fun, 0);\n    }\n  }\n}\n\nfunction runClearTimeout(marker) {\n  if (cachedClearTimeout === clearTimeout) {\n    //normal enviroments in sane situations\n    return clearTimeout(marker);\n  } // if clearTimeout wasn't available but was latter defined\n\n\n  if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n    cachedClearTimeout = clearTimeout;\n    return clearTimeout(marker);\n  }\n\n  try {\n    // when when somebody has screwed with setTimeout but no I.E. maddness\n    return cachedClearTimeout(marker);\n  } catch (e) {\n    try {\n      // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n      return cachedClearTimeout.call(null, marker);\n    } catch (e) {\n      // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n      // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n      return cachedClearTimeout.call(this, marker);\n    }\n  }\n}\n\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n  if (!draining || !currentQueue) {\n    return;\n  }\n\n  draining = false;\n\n  if (currentQueue.length) {\n    queue = currentQueue.concat(queue);\n  } else {\n    queueIndex = -1;\n  }\n\n  if (queue.length) {\n    drainQueue();\n  }\n}\n\nfunction drainQueue() {\n  if (draining) {\n    return;\n  }\n\n  var timeout = runTimeout(cleanUpNextTick);\n  draining = true;\n  var len = queue.length;\n\n  while (len) {\n    currentQueue = queue;\n    queue = [];\n\n    while (++queueIndex < len) {\n      if (currentQueue) {\n        currentQueue[queueIndex].run();\n      }\n    }\n\n    queueIndex = -1;\n    len = queue.length;\n  }\n\n  currentQueue = null;\n  draining = false;\n  runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n  var args = new Array(arguments.length - 1);\n\n  if (arguments.length > 1) {\n    for (var i = 1; i < arguments.length; i++) {\n      args[i - 1] = arguments[i];\n    }\n  }\n\n  queue.push(new Item(fun, args));\n\n  if (queue.length === 1 && !draining) {\n    runTimeout(drainQueue);\n  }\n}; // v8 likes predictible objects\n\n\nfunction Item(fun, array) {\n  this.fun = fun;\n  this.array = array;\n}\n\nItem.prototype.run = function () {\n  this.fun.apply(null, this.array);\n};\n\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\n\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) {\n  return [];\n};\n\nprocess.binding = function (name) {\n  throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () {\n  return '/';\n};\n\nprocess.chdir = function (dir) {\n  throw new Error('process.chdir is not supported');\n};\n\nprocess.umask = function () {\n  return 0;\n};\n\n//# sourceURL=webpack:///./node_modules/process/browser.js?");

/***/ }),

/***/ "./node_modules/setimmediate/setImmediate.js":
/*!***************************************************!*\
  !*** ./node_modules/setimmediate/setImmediate.js ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("/* WEBPACK VAR INJECTION */(function(global, process) {(function (global, undefined) {\n  \"use strict\";\n\n  if (global.setImmediate) {\n    return;\n  }\n\n  var nextHandle = 1; // Spec says greater than zero\n\n  var tasksByHandle = {};\n  var currentlyRunningATask = false;\n  var doc = global.document;\n  var registerImmediate;\n\n  function setImmediate(callback) {\n    // Callback can either be a function or a string\n    if (typeof callback !== \"function\") {\n      callback = new Function(\"\" + callback);\n    } // Copy function arguments\n\n\n    var args = new Array(arguments.length - 1);\n\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i + 1];\n    } // Store and register the task\n\n\n    var task = {\n      callback: callback,\n      args: args\n    };\n    tasksByHandle[nextHandle] = task;\n    registerImmediate(nextHandle);\n    return nextHandle++;\n  }\n\n  function clearImmediate(handle) {\n    delete tasksByHandle[handle];\n  }\n\n  function run(task) {\n    var callback = task.callback;\n    var args = task.args;\n\n    switch (args.length) {\n      case 0:\n        callback();\n        break;\n\n      case 1:\n        callback(args[0]);\n        break;\n\n      case 2:\n        callback(args[0], args[1]);\n        break;\n\n      case 3:\n        callback(args[0], args[1], args[2]);\n        break;\n\n      default:\n        callback.apply(undefined, args);\n        break;\n    }\n  }\n\n  function runIfPresent(handle) {\n    // From the spec: \"Wait until any invocations of this algorithm started before this one have completed.\"\n    // So if we're currently running a task, we'll need to delay this invocation.\n    if (currentlyRunningATask) {\n      // Delay by doing a setTimeout. setImmediate was tried instead, but in Firefox 7 it generated a\n      // \"too much recursion\" error.\n      setTimeout(runIfPresent, 0, handle);\n    } else {\n      var task = tasksByHandle[handle];\n\n      if (task) {\n        currentlyRunningATask = true;\n\n        try {\n          run(task);\n        } finally {\n          clearImmediate(handle);\n          currentlyRunningATask = false;\n        }\n      }\n    }\n  }\n\n  function installNextTickImplementation() {\n    registerImmediate = function (handle) {\n      process.nextTick(function () {\n        runIfPresent(handle);\n      });\n    };\n  }\n\n  function canUsePostMessage() {\n    // The test against `importScripts` prevents this implementation from being installed inside a web worker,\n    // where `global.postMessage` means something completely different and can't be used for this purpose.\n    if (global.postMessage && !global.importScripts) {\n      var postMessageIsAsynchronous = true;\n      var oldOnMessage = global.onmessage;\n\n      global.onmessage = function () {\n        postMessageIsAsynchronous = false;\n      };\n\n      global.postMessage(\"\", \"*\");\n      global.onmessage = oldOnMessage;\n      return postMessageIsAsynchronous;\n    }\n  }\n\n  function installPostMessageImplementation() {\n    // Installs an event handler on `global` for the `message` event: see\n    // * https://developer.mozilla.org/en/DOM/window.postMessage\n    // * http://www.whatwg.org/specs/web-apps/current-work/multipage/comms.html#crossDocumentMessages\n    var messagePrefix = \"setImmediate$\" + Math.random() + \"$\";\n\n    var onGlobalMessage = function (event) {\n      if (event.source === global && typeof event.data === \"string\" && event.data.indexOf(messagePrefix) === 0) {\n        runIfPresent(+event.data.slice(messagePrefix.length));\n      }\n    };\n\n    if (global.addEventListener) {\n      global.addEventListener(\"message\", onGlobalMessage, false);\n    } else {\n      global.attachEvent(\"onmessage\", onGlobalMessage);\n    }\n\n    registerImmediate = function (handle) {\n      global.postMessage(messagePrefix + handle, \"*\");\n    };\n  }\n\n  function installMessageChannelImplementation() {\n    var channel = new MessageChannel();\n\n    channel.port1.onmessage = function (event) {\n      var handle = event.data;\n      runIfPresent(handle);\n    };\n\n    registerImmediate = function (handle) {\n      channel.port2.postMessage(handle);\n    };\n  }\n\n  function installReadyStateChangeImplementation() {\n    var html = doc.documentElement;\n\n    registerImmediate = function (handle) {\n      // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n      // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n      var script = doc.createElement(\"script\");\n\n      script.onreadystatechange = function () {\n        runIfPresent(handle);\n        script.onreadystatechange = null;\n        html.removeChild(script);\n        script = null;\n      };\n\n      html.appendChild(script);\n    };\n  }\n\n  function installSetTimeoutImplementation() {\n    registerImmediate = function (handle) {\n      setTimeout(runIfPresent, 0, handle);\n    };\n  } // If supported, we should attach to the prototype of global, since that is where setTimeout et al. live.\n\n\n  var attachTo = Object.getPrototypeOf && Object.getPrototypeOf(global);\n  attachTo = attachTo && attachTo.setTimeout ? attachTo : global; // Don't get fooled by e.g. browserify environments.\n\n  if ({}.toString.call(global.process) === \"[object process]\") {\n    // For Node.js before 0.9\n    installNextTickImplementation();\n  } else if (canUsePostMessage()) {\n    // For non-IE10 modern browsers\n    installPostMessageImplementation();\n  } else if (global.MessageChannel) {\n    // For web workers, where supported\n    installMessageChannelImplementation();\n  } else if (doc && \"onreadystatechange\" in doc.createElement(\"script\")) {\n    // For IE 6–8\n    installReadyStateChangeImplementation();\n  } else {\n    // For older browsers\n    installSetTimeoutImplementation();\n  }\n\n  attachTo.setImmediate = setImmediate;\n  attachTo.clearImmediate = clearImmediate;\n})(typeof self === \"undefined\" ? typeof global === \"undefined\" ? this : global : self);\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../webpack/buildin/global.js */ \"./node_modules/webpack/buildin/global.js\"), __webpack_require__(/*! ./../process/browser.js */ \"./node_modules/process/browser.js\")))\n\n//# sourceURL=webpack:///./node_modules/setimmediate/setImmediate.js?");

/***/ }),

/***/ "./node_modules/timers-browserify/main.js":
/*!************************************************!*\
  !*** ./node_modules/timers-browserify/main.js ***!
  \************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("/* WEBPACK VAR INJECTION */(function(global) {var scope = typeof global !== \"undefined\" && global || typeof self !== \"undefined\" && self || window;\nvar apply = Function.prototype.apply; // DOM APIs, for completeness\n\nexports.setTimeout = function () {\n  return new Timeout(apply.call(setTimeout, scope, arguments), clearTimeout);\n};\n\nexports.setInterval = function () {\n  return new Timeout(apply.call(setInterval, scope, arguments), clearInterval);\n};\n\nexports.clearTimeout = exports.clearInterval = function (timeout) {\n  if (timeout) {\n    timeout.close();\n  }\n};\n\nfunction Timeout(id, clearFn) {\n  this._id = id;\n  this._clearFn = clearFn;\n}\n\nTimeout.prototype.unref = Timeout.prototype.ref = function () {};\n\nTimeout.prototype.close = function () {\n  this._clearFn.call(scope, this._id);\n}; // Does not start the time, just sets up the members needed.\n\n\nexports.enroll = function (item, msecs) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = msecs;\n};\n\nexports.unenroll = function (item) {\n  clearTimeout(item._idleTimeoutId);\n  item._idleTimeout = -1;\n};\n\nexports._unrefActive = exports.active = function (item) {\n  clearTimeout(item._idleTimeoutId);\n  var msecs = item._idleTimeout;\n\n  if (msecs >= 0) {\n    item._idleTimeoutId = setTimeout(function onTimeout() {\n      if (item._onTimeout) item._onTimeout();\n    }, msecs);\n  }\n}; // setimmediate attaches itself to the global object\n\n\n__webpack_require__(/*! setimmediate */ \"./node_modules/setimmediate/setImmediate.js\"); // On some exotic environments, it's not clear which object `setimmediate` was\n// able to install onto.  Search each possibility in the same order as the\n// `setimmediate` library.\n\n\nexports.setImmediate = typeof self !== \"undefined\" && self.setImmediate || typeof global !== \"undefined\" && global.setImmediate || this && this.setImmediate;\nexports.clearImmediate = typeof self !== \"undefined\" && self.clearImmediate || typeof global !== \"undefined\" && global.clearImmediate || this && this.clearImmediate;\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../webpack/buildin/global.js */ \"./node_modules/webpack/buildin/global.js\")))\n\n//# sourceURL=webpack:///./node_modules/timers-browserify/main.js?");

/***/ }),

/***/ "./node_modules/vue-router/dist/vue-router.esm.js":
/*!********************************************************!*\
  !*** ./node_modules/vue-router/dist/vue-router.esm.js ***!
  \********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/*!\n  * vue-router v3.5.4\n  * (c) 2022 Evan You\n  * @license MIT\n  */\n\n/*  */\nfunction assert(condition, message) {\n  if (!condition) {\n    throw new Error(\"[vue-router] \" + message);\n  }\n}\n\nfunction warn(condition, message) {\n  if (!condition) {\n    typeof console !== 'undefined' && console.warn(\"[vue-router] \" + message);\n  }\n}\n\nfunction extend(a, b) {\n  for (var key in b) {\n    a[key] = b[key];\n  }\n\n  return a;\n}\n/*  */\n\n\nvar encodeReserveRE = /[!'()*]/g;\n\nvar encodeReserveReplacer = function (c) {\n  return '%' + c.charCodeAt(0).toString(16);\n};\n\nvar commaRE = /%2C/g; // fixed encodeURIComponent which is more conformant to RFC3986:\n// - escapes [!'()*]\n// - preserve commas\n\nvar encode = function (str) {\n  return encodeURIComponent(str).replace(encodeReserveRE, encodeReserveReplacer).replace(commaRE, ',');\n};\n\nfunction decode(str) {\n  try {\n    return decodeURIComponent(str);\n  } catch (err) {\n    if (true) {\n      warn(false, \"Error decoding \\\"\" + str + \"\\\". Leaving it intact.\");\n    }\n  }\n\n  return str;\n}\n\nfunction resolveQuery(query, extraQuery, _parseQuery) {\n  if (extraQuery === void 0) extraQuery = {};\n  var parse = _parseQuery || parseQuery;\n  var parsedQuery;\n\n  try {\n    parsedQuery = parse(query || '');\n  } catch (e) {\n     true && warn(false, e.message);\n    parsedQuery = {};\n  }\n\n  for (var key in extraQuery) {\n    var value = extraQuery[key];\n    parsedQuery[key] = Array.isArray(value) ? value.map(castQueryParamValue) : castQueryParamValue(value);\n  }\n\n  return parsedQuery;\n}\n\nvar castQueryParamValue = function (value) {\n  return value == null || typeof value === 'object' ? value : String(value);\n};\n\nfunction parseQuery(query) {\n  var res = {};\n  query = query.trim().replace(/^(\\?|#|&)/, '');\n\n  if (!query) {\n    return res;\n  }\n\n  query.split('&').forEach(function (param) {\n    var parts = param.replace(/\\+/g, ' ').split('=');\n    var key = decode(parts.shift());\n    var val = parts.length > 0 ? decode(parts.join('=')) : null;\n\n    if (res[key] === undefined) {\n      res[key] = val;\n    } else if (Array.isArray(res[key])) {\n      res[key].push(val);\n    } else {\n      res[key] = [res[key], val];\n    }\n  });\n  return res;\n}\n\nfunction stringifyQuery(obj) {\n  var res = obj ? Object.keys(obj).map(function (key) {\n    var val = obj[key];\n\n    if (val === undefined) {\n      return '';\n    }\n\n    if (val === null) {\n      return encode(key);\n    }\n\n    if (Array.isArray(val)) {\n      var result = [];\n      val.forEach(function (val2) {\n        if (val2 === undefined) {\n          return;\n        }\n\n        if (val2 === null) {\n          result.push(encode(key));\n        } else {\n          result.push(encode(key) + '=' + encode(val2));\n        }\n      });\n      return result.join('&');\n    }\n\n    return encode(key) + '=' + encode(val);\n  }).filter(function (x) {\n    return x.length > 0;\n  }).join('&') : null;\n  return res ? \"?\" + res : '';\n}\n/*  */\n\n\nvar trailingSlashRE = /\\/?$/;\n\nfunction createRoute(record, location, redirectedFrom, router) {\n  var stringifyQuery = router && router.options.stringifyQuery;\n  var query = location.query || {};\n\n  try {\n    query = clone(query);\n  } catch (e) {}\n\n  var route = {\n    name: location.name || record && record.name,\n    meta: record && record.meta || {},\n    path: location.path || '/',\n    hash: location.hash || '',\n    query: query,\n    params: location.params || {},\n    fullPath: getFullPath(location, stringifyQuery),\n    matched: record ? formatMatch(record) : []\n  };\n\n  if (redirectedFrom) {\n    route.redirectedFrom = getFullPath(redirectedFrom, stringifyQuery);\n  }\n\n  return Object.freeze(route);\n}\n\nfunction clone(value) {\n  if (Array.isArray(value)) {\n    return value.map(clone);\n  } else if (value && typeof value === 'object') {\n    var res = {};\n\n    for (var key in value) {\n      res[key] = clone(value[key]);\n    }\n\n    return res;\n  } else {\n    return value;\n  }\n} // the starting route that represents the initial state\n\n\nvar START = createRoute(null, {\n  path: '/'\n});\n\nfunction formatMatch(record) {\n  var res = [];\n\n  while (record) {\n    res.unshift(record);\n    record = record.parent;\n  }\n\n  return res;\n}\n\nfunction getFullPath(ref, _stringifyQuery) {\n  var path = ref.path;\n  var query = ref.query;\n  if (query === void 0) query = {};\n  var hash = ref.hash;\n  if (hash === void 0) hash = '';\n  var stringify = _stringifyQuery || stringifyQuery;\n  return (path || '/') + stringify(query) + hash;\n}\n\nfunction isSameRoute(a, b, onlyPath) {\n  if (b === START) {\n    return a === b;\n  } else if (!b) {\n    return false;\n  } else if (a.path && b.path) {\n    return a.path.replace(trailingSlashRE, '') === b.path.replace(trailingSlashRE, '') && (onlyPath || a.hash === b.hash && isObjectEqual(a.query, b.query));\n  } else if (a.name && b.name) {\n    return a.name === b.name && (onlyPath || a.hash === b.hash && isObjectEqual(a.query, b.query) && isObjectEqual(a.params, b.params));\n  } else {\n    return false;\n  }\n}\n\nfunction isObjectEqual(a, b) {\n  if (a === void 0) a = {};\n  if (b === void 0) b = {}; // handle null value #1566\n\n  if (!a || !b) {\n    return a === b;\n  }\n\n  var aKeys = Object.keys(a).sort();\n  var bKeys = Object.keys(b).sort();\n\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n\n  return aKeys.every(function (key, i) {\n    var aVal = a[key];\n    var bKey = bKeys[i];\n\n    if (bKey !== key) {\n      return false;\n    }\n\n    var bVal = b[key]; // query values can be null and undefined\n\n    if (aVal == null || bVal == null) {\n      return aVal === bVal;\n    } // check nested equality\n\n\n    if (typeof aVal === 'object' && typeof bVal === 'object') {\n      return isObjectEqual(aVal, bVal);\n    }\n\n    return String(aVal) === String(bVal);\n  });\n}\n\nfunction isIncludedRoute(current, target) {\n  return current.path.replace(trailingSlashRE, '/').indexOf(target.path.replace(trailingSlashRE, '/')) === 0 && (!target.hash || current.hash === target.hash) && queryIncludes(current.query, target.query);\n}\n\nfunction queryIncludes(current, target) {\n  for (var key in target) {\n    if (!(key in current)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction handleRouteEntered(route) {\n  for (var i = 0; i < route.matched.length; i++) {\n    var record = route.matched[i];\n\n    for (var name in record.instances) {\n      var instance = record.instances[name];\n      var cbs = record.enteredCbs[name];\n\n      if (!instance || !cbs) {\n        continue;\n      }\n\n      delete record.enteredCbs[name];\n\n      for (var i$1 = 0; i$1 < cbs.length; i$1++) {\n        if (!instance._isBeingDestroyed) {\n          cbs[i$1](instance);\n        }\n      }\n    }\n  }\n}\n\nvar View = {\n  name: 'RouterView',\n  functional: true,\n  props: {\n    name: {\n      type: String,\n      default: 'default'\n    }\n  },\n  render: function render(_, ref) {\n    var props = ref.props;\n    var children = ref.children;\n    var parent = ref.parent;\n    var data = ref.data; // used by devtools to display a router-view badge\n\n    data.routerView = true; // directly use parent context's createElement() function\n    // so that components rendered by router-view can resolve named slots\n\n    var h = parent.$createElement;\n    var name = props.name;\n    var route = parent.$route;\n    var cache = parent._routerViewCache || (parent._routerViewCache = {}); // determine current view depth, also check to see if the tree\n    // has been toggled inactive but kept-alive.\n\n    var depth = 0;\n    var inactive = false;\n\n    while (parent && parent._routerRoot !== parent) {\n      var vnodeData = parent.$vnode ? parent.$vnode.data : {};\n\n      if (vnodeData.routerView) {\n        depth++;\n      }\n\n      if (vnodeData.keepAlive && parent._directInactive && parent._inactive) {\n        inactive = true;\n      }\n\n      parent = parent.$parent;\n    }\n\n    data.routerViewDepth = depth; // render previous view if the tree is inactive and kept-alive\n\n    if (inactive) {\n      var cachedData = cache[name];\n      var cachedComponent = cachedData && cachedData.component;\n\n      if (cachedComponent) {\n        // #2301\n        // pass props\n        if (cachedData.configProps) {\n          fillPropsinData(cachedComponent, data, cachedData.route, cachedData.configProps);\n        }\n\n        return h(cachedComponent, data, children);\n      } else {\n        // render previous empty view\n        return h();\n      }\n    }\n\n    var matched = route.matched[depth];\n    var component = matched && matched.components[name]; // render empty node if no matched route or no config component\n\n    if (!matched || !component) {\n      cache[name] = null;\n      return h();\n    } // cache component\n\n\n    cache[name] = {\n      component: component\n    }; // attach instance registration hook\n    // this will be called in the instance's injected lifecycle hooks\n\n    data.registerRouteInstance = function (vm, val) {\n      // val could be undefined for unregistration\n      var current = matched.instances[name];\n\n      if (val && current !== vm || !val && current === vm) {\n        matched.instances[name] = val;\n      }\n    } // also register instance in prepatch hook\n    // in case the same component instance is reused across different routes\n    ;\n\n    (data.hook || (data.hook = {})).prepatch = function (_, vnode) {\n      matched.instances[name] = vnode.componentInstance;\n    }; // register instance in init hook\n    // in case kept-alive component be actived when routes changed\n\n\n    data.hook.init = function (vnode) {\n      if (vnode.data.keepAlive && vnode.componentInstance && vnode.componentInstance !== matched.instances[name]) {\n        matched.instances[name] = vnode.componentInstance;\n      } // if the route transition has already been confirmed then we weren't\n      // able to call the cbs during confirmation as the component was not\n      // registered yet, so we call it here.\n\n\n      handleRouteEntered(route);\n    };\n\n    var configProps = matched.props && matched.props[name]; // save route and configProps in cache\n\n    if (configProps) {\n      extend(cache[name], {\n        route: route,\n        configProps: configProps\n      });\n      fillPropsinData(component, data, route, configProps);\n    }\n\n    return h(component, data, children);\n  }\n};\n\nfunction fillPropsinData(component, data, route, configProps) {\n  // resolve props\n  var propsToPass = data.props = resolveProps(route, configProps);\n\n  if (propsToPass) {\n    // clone to prevent mutation\n    propsToPass = data.props = extend({}, propsToPass); // pass non-declared props as attrs\n\n    var attrs = data.attrs = data.attrs || {};\n\n    for (var key in propsToPass) {\n      if (!component.props || !(key in component.props)) {\n        attrs[key] = propsToPass[key];\n        delete propsToPass[key];\n      }\n    }\n  }\n}\n\nfunction resolveProps(route, config) {\n  switch (typeof config) {\n    case 'undefined':\n      return;\n\n    case 'object':\n      return config;\n\n    case 'function':\n      return config(route);\n\n    case 'boolean':\n      return config ? route.params : undefined;\n\n    default:\n      if (true) {\n        warn(false, \"props in \\\"\" + route.path + \"\\\" is a \" + typeof config + \", \" + \"expecting an object, function or boolean.\");\n      }\n\n  }\n}\n/*  */\n\n\nfunction resolvePath(relative, base, append) {\n  var firstChar = relative.charAt(0);\n\n  if (firstChar === '/') {\n    return relative;\n  }\n\n  if (firstChar === '?' || firstChar === '#') {\n    return base + relative;\n  }\n\n  var stack = base.split('/'); // remove trailing segment if:\n  // - not appending\n  // - appending to trailing slash (last segment is empty)\n\n  if (!append || !stack[stack.length - 1]) {\n    stack.pop();\n  } // resolve relative path\n\n\n  var segments = relative.replace(/^\\//, '').split('/');\n\n  for (var i = 0; i < segments.length; i++) {\n    var segment = segments[i];\n\n    if (segment === '..') {\n      stack.pop();\n    } else if (segment !== '.') {\n      stack.push(segment);\n    }\n  } // ensure leading slash\n\n\n  if (stack[0] !== '') {\n    stack.unshift('');\n  }\n\n  return stack.join('/');\n}\n\nfunction parsePath(path) {\n  var hash = '';\n  var query = '';\n  var hashIndex = path.indexOf('#');\n\n  if (hashIndex >= 0) {\n    hash = path.slice(hashIndex);\n    path = path.slice(0, hashIndex);\n  }\n\n  var queryIndex = path.indexOf('?');\n\n  if (queryIndex >= 0) {\n    query = path.slice(queryIndex + 1);\n    path = path.slice(0, queryIndex);\n  }\n\n  return {\n    path: path,\n    query: query,\n    hash: hash\n  };\n}\n\nfunction cleanPath(path) {\n  return path.replace(/\\/(?:\\s*\\/)+/g, '/');\n}\n\nvar isarray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n/**\n * Expose `pathToRegexp`.\n */\n\n\nvar pathToRegexp_1 = pathToRegexp;\nvar parse_1 = parse;\nvar compile_1 = compile;\nvar tokensToFunction_1 = tokensToFunction;\nvar tokensToRegExp_1 = tokensToRegExp;\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\n\nvar PATH_REGEXP = new RegExp([// Match escaped characters that would otherwise appear in future matches.\n// This allows the user to escape special characters that won't transform.\n'(\\\\\\\\.)', // Match Express-style parameters and un-named parameters with a prefix\n// and optional suffixes. Matches appear as:\n//\n// \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n// \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n// \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n'([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'].join('|'), 'g');\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\n\nfunction parse(str, options) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var defaultDelimiter = options && options.delimiter || '/';\n  var res;\n\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length; // Ignore already escaped sequences.\n\n    if (escaped) {\n      path += escaped[1];\n      continue;\n    }\n\n    var next = str[index];\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var modifier = res[6];\n    var asterisk = res[7]; // Push the current path onto the tokens.\n\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n\n    var partial = prefix != null && next != null && next !== prefix;\n    var repeat = modifier === '+' || modifier === '*';\n    var optional = modifier === '?' || modifier === '*';\n    var delimiter = res[2] || defaultDelimiter;\n    var pattern = capture || group;\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : asterisk ? '.*' : '[^' + escapeString(delimiter) + ']+?'\n    });\n  } // Match any characters still remaining.\n\n\n  if (index < str.length) {\n    path += str.substr(index);\n  } // If the path exists, push it onto the end.\n\n\n  if (path) {\n    tokens.push(path);\n  }\n\n  return tokens;\n}\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\n\n\nfunction compile(str, options) {\n  return tokensToFunction(parse(str, options), options);\n}\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\n\n\nfunction encodeURIComponentPretty(str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\n\n\nfunction encodeAsterisk(str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n/**\n * Expose a method for transforming tokens into the path function.\n */\n\n\nfunction tokensToFunction(tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length); // Compile all the patterns before compilation.\n\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options));\n    }\n  }\n\n  return function (obj, opts) {\n    var path = '';\n    var data = obj || {};\n    var options = opts || {};\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent;\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n\n      if (typeof token === 'string') {\n        path += token;\n        continue;\n      }\n\n      var value = data[token.name];\n      var segment;\n\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix;\n          }\n\n          continue;\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined');\n        }\n      }\n\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`');\n        }\n\n        if (value.length === 0) {\n          if (token.optional) {\n            continue;\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty');\n          }\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j]);\n\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`');\n          }\n\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n\n        continue;\n      }\n\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value);\n\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"');\n      }\n\n      path += token.prefix + segment;\n    }\n\n    return path;\n  };\n}\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\n\n\nfunction escapeString(str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1');\n}\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\n\n\nfunction escapeGroup(group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1');\n}\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\n\n\nfunction attachKeys(re, keys) {\n  re.keys = keys;\n  return re;\n}\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\n\n\nfunction flags(options) {\n  return options && options.sensitive ? '' : 'i';\n}\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\n\n\nfunction regexpToRegexp(path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      });\n    }\n  }\n\n  return attachKeys(path, keys);\n}\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\n\n\nfunction arrayToRegexp(path, keys, options) {\n  var parts = [];\n\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n  return attachKeys(regexp, keys);\n}\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\n\n\nfunction stringToRegexp(path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options);\n}\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\n\n\nfunction tokensToRegExp(tokens, keys, options) {\n  if (!isarray(keys)) {\n    options =\n    /** @type {!Object} */\n    keys || options;\n    keys = [];\n  }\n\n  options = options || {};\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = ''; // Iterate over the tokens and create our regexp string.\n\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = '(?:' + token.pattern + ')';\n      keys.push(token);\n\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = prefix + '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n\n      route += capture;\n    }\n  }\n\n  var delimiter = escapeString(options.delimiter || '/');\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter; // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?';\n  }\n\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)';\n  }\n\n  return attachKeys(new RegExp('^' + route, flags(options)), keys);\n}\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\n\n\nfunction pathToRegexp(path, keys, options) {\n  if (!isarray(keys)) {\n    options =\n    /** @type {!Object} */\n    keys || options;\n    keys = [];\n  }\n\n  options = options || {};\n\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path,\n    /** @type {!Array} */\n    keys);\n  }\n\n  if (isarray(path)) {\n    return arrayToRegexp(\n    /** @type {!Array} */\n    path,\n    /** @type {!Array} */\n    keys, options);\n  }\n\n  return stringToRegexp(\n  /** @type {string} */\n  path,\n  /** @type {!Array} */\n  keys, options);\n}\n\npathToRegexp_1.parse = parse_1;\npathToRegexp_1.compile = compile_1;\npathToRegexp_1.tokensToFunction = tokensToFunction_1;\npathToRegexp_1.tokensToRegExp = tokensToRegExp_1;\n/*  */\n// $flow-disable-line\n\nvar regexpCompileCache = Object.create(null);\n\nfunction fillParams(path, params, routeMsg) {\n  params = params || {};\n\n  try {\n    var filler = regexpCompileCache[path] || (regexpCompileCache[path] = pathToRegexp_1.compile(path)); // Fix #2505 resolving asterisk routes { name: 'not-found', params: { pathMatch: '/not-found' }}\n    // and fix #3106 so that you can work with location descriptor object having params.pathMatch equal to empty string\n\n    if (typeof params.pathMatch === 'string') {\n      params[0] = params.pathMatch;\n    }\n\n    return filler(params, {\n      pretty: true\n    });\n  } catch (e) {\n    if (true) {\n      // Fix #3072 no warn if `pathMatch` is string\n      warn(typeof params.pathMatch === 'string', \"missing param for \" + routeMsg + \": \" + e.message);\n    }\n\n    return '';\n  } finally {\n    // delete the 0 if it was added\n    delete params[0];\n  }\n}\n/*  */\n\n\nfunction normalizeLocation(raw, current, append, router) {\n  var next = typeof raw === 'string' ? {\n    path: raw\n  } : raw; // named target\n\n  if (next._normalized) {\n    return next;\n  } else if (next.name) {\n    next = extend({}, raw);\n    var params = next.params;\n\n    if (params && typeof params === 'object') {\n      next.params = extend({}, params);\n    }\n\n    return next;\n  } // relative params\n\n\n  if (!next.path && next.params && current) {\n    next = extend({}, next);\n    next._normalized = true;\n    var params$1 = extend(extend({}, current.params), next.params);\n\n    if (current.name) {\n      next.name = current.name;\n      next.params = params$1;\n    } else if (current.matched.length) {\n      var rawPath = current.matched[current.matched.length - 1].path;\n      next.path = fillParams(rawPath, params$1, \"path \" + current.path);\n    } else if (true) {\n      warn(false, \"relative params navigation requires a current route.\");\n    }\n\n    return next;\n  }\n\n  var parsedPath = parsePath(next.path || '');\n  var basePath = current && current.path || '/';\n  var path = parsedPath.path ? resolvePath(parsedPath.path, basePath, append || next.append) : basePath;\n  var query = resolveQuery(parsedPath.query, next.query, router && router.options.parseQuery);\n  var hash = next.hash || parsedPath.hash;\n\n  if (hash && hash.charAt(0) !== '#') {\n    hash = \"#\" + hash;\n  }\n\n  return {\n    _normalized: true,\n    path: path,\n    query: query,\n    hash: hash\n  };\n}\n/*  */\n// work around weird flow bug\n\n\nvar toTypes = [String, Object];\nvar eventTypes = [String, Array];\n\nvar noop = function () {};\n\nvar warnedCustomSlot;\nvar warnedTagProp;\nvar warnedEventProp;\nvar Link = {\n  name: 'RouterLink',\n  props: {\n    to: {\n      type: toTypes,\n      required: true\n    },\n    tag: {\n      type: String,\n      default: 'a'\n    },\n    custom: Boolean,\n    exact: Boolean,\n    exactPath: Boolean,\n    append: Boolean,\n    replace: Boolean,\n    activeClass: String,\n    exactActiveClass: String,\n    ariaCurrentValue: {\n      type: String,\n      default: 'page'\n    },\n    event: {\n      type: eventTypes,\n      default: 'click'\n    }\n  },\n  render: function render(h) {\n    var this$1 = this;\n    var router = this.$router;\n    var current = this.$route;\n    var ref = router.resolve(this.to, current, this.append);\n    var location = ref.location;\n    var route = ref.route;\n    var href = ref.href;\n    var classes = {};\n    var globalActiveClass = router.options.linkActiveClass;\n    var globalExactActiveClass = router.options.linkExactActiveClass; // Support global empty active class\n\n    var activeClassFallback = globalActiveClass == null ? 'router-link-active' : globalActiveClass;\n    var exactActiveClassFallback = globalExactActiveClass == null ? 'router-link-exact-active' : globalExactActiveClass;\n    var activeClass = this.activeClass == null ? activeClassFallback : this.activeClass;\n    var exactActiveClass = this.exactActiveClass == null ? exactActiveClassFallback : this.exactActiveClass;\n    var compareTarget = route.redirectedFrom ? createRoute(null, normalizeLocation(route.redirectedFrom), null, router) : route;\n    classes[exactActiveClass] = isSameRoute(current, compareTarget, this.exactPath);\n    classes[activeClass] = this.exact || this.exactPath ? classes[exactActiveClass] : isIncludedRoute(current, compareTarget);\n    var ariaCurrentValue = classes[exactActiveClass] ? this.ariaCurrentValue : null;\n\n    var handler = function (e) {\n      if (guardEvent(e)) {\n        if (this$1.replace) {\n          router.replace(location, noop);\n        } else {\n          router.push(location, noop);\n        }\n      }\n    };\n\n    var on = {\n      click: guardEvent\n    };\n\n    if (Array.isArray(this.event)) {\n      this.event.forEach(function (e) {\n        on[e] = handler;\n      });\n    } else {\n      on[this.event] = handler;\n    }\n\n    var data = {\n      class: classes\n    };\n    var scopedSlot = !this.$scopedSlots.$hasNormal && this.$scopedSlots.default && this.$scopedSlots.default({\n      href: href,\n      route: route,\n      navigate: handler,\n      isActive: classes[activeClass],\n      isExactActive: classes[exactActiveClass]\n    });\n\n    if (scopedSlot) {\n      if ( true && !this.custom) {\n        !warnedCustomSlot && warn(false, 'In Vue Router 4, the v-slot API will by default wrap its content with an <a> element. Use the custom prop to remove this warning:\\n<router-link v-slot=\"{ navigate, href }\" custom></router-link>\\n');\n        warnedCustomSlot = true;\n      }\n\n      if (scopedSlot.length === 1) {\n        return scopedSlot[0];\n      } else if (scopedSlot.length > 1 || !scopedSlot.length) {\n        if (true) {\n          warn(false, \"<router-link> with to=\\\"\" + this.to + \"\\\" is trying to use a scoped slot but it didn't provide exactly one child. Wrapping the content with a span element.\");\n        }\n\n        return scopedSlot.length === 0 ? h() : h('span', {}, scopedSlot);\n      }\n    }\n\n    if (true) {\n      if ('tag' in this.$options.propsData && !warnedTagProp) {\n        warn(false, \"<router-link>'s tag prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\");\n        warnedTagProp = true;\n      }\n\n      if ('event' in this.$options.propsData && !warnedEventProp) {\n        warn(false, \"<router-link>'s event prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\");\n        warnedEventProp = true;\n      }\n    }\n\n    if (this.tag === 'a') {\n      data.on = on;\n      data.attrs = {\n        href: href,\n        'aria-current': ariaCurrentValue\n      };\n    } else {\n      // find the first <a> child and apply listener and href\n      var a = findAnchor(this.$slots.default);\n\n      if (a) {\n        // in case the <a> is a static node\n        a.isStatic = false;\n        var aData = a.data = extend({}, a.data);\n        aData.on = aData.on || {}; // transform existing events in both objects into arrays so we can push later\n\n        for (var event in aData.on) {\n          var handler$1 = aData.on[event];\n\n          if (event in on) {\n            aData.on[event] = Array.isArray(handler$1) ? handler$1 : [handler$1];\n          }\n        } // append new listeners for router-link\n\n\n        for (var event$1 in on) {\n          if (event$1 in aData.on) {\n            // on[event] is always a function\n            aData.on[event$1].push(on[event$1]);\n          } else {\n            aData.on[event$1] = handler;\n          }\n        }\n\n        var aAttrs = a.data.attrs = extend({}, a.data.attrs);\n        aAttrs.href = href;\n        aAttrs['aria-current'] = ariaCurrentValue;\n      } else {\n        // doesn't have <a> child, apply listener to self\n        data.on = on;\n      }\n    }\n\n    return h(this.tag, data, this.$slots.default);\n  }\n};\n\nfunction guardEvent(e) {\n  // don't redirect with control keys\n  if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) {\n    return;\n  } // don't redirect when preventDefault called\n\n\n  if (e.defaultPrevented) {\n    return;\n  } // don't redirect on right click\n\n\n  if (e.button !== undefined && e.button !== 0) {\n    return;\n  } // don't redirect if `target=\"_blank\"`\n\n\n  if (e.currentTarget && e.currentTarget.getAttribute) {\n    var target = e.currentTarget.getAttribute('target');\n\n    if (/\\b_blank\\b/i.test(target)) {\n      return;\n    }\n  } // this may be a Weex event which doesn't have this method\n\n\n  if (e.preventDefault) {\n    e.preventDefault();\n  }\n\n  return true;\n}\n\nfunction findAnchor(children) {\n  if (children) {\n    var child;\n\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n\n      if (child.tag === 'a') {\n        return child;\n      }\n\n      if (child.children && (child = findAnchor(child.children))) {\n        return child;\n      }\n    }\n  }\n}\n\nvar _Vue;\n\nfunction install(Vue) {\n  if (install.installed && _Vue === Vue) {\n    return;\n  }\n\n  install.installed = true;\n  _Vue = Vue;\n\n  var isDef = function (v) {\n    return v !== undefined;\n  };\n\n  var registerInstance = function (vm, callVal) {\n    var i = vm.$options._parentVnode;\n\n    if (isDef(i) && isDef(i = i.data) && isDef(i = i.registerRouteInstance)) {\n      i(vm, callVal);\n    }\n  };\n\n  Vue.mixin({\n    beforeCreate: function beforeCreate() {\n      if (isDef(this.$options.router)) {\n        this._routerRoot = this;\n        this._router = this.$options.router;\n\n        this._router.init(this);\n\n        Vue.util.defineReactive(this, '_route', this._router.history.current);\n      } else {\n        this._routerRoot = this.$parent && this.$parent._routerRoot || this;\n      }\n\n      registerInstance(this, this);\n    },\n    destroyed: function destroyed() {\n      registerInstance(this);\n    }\n  });\n  Object.defineProperty(Vue.prototype, '$router', {\n    get: function get() {\n      return this._routerRoot._router;\n    }\n  });\n  Object.defineProperty(Vue.prototype, '$route', {\n    get: function get() {\n      return this._routerRoot._route;\n    }\n  });\n  Vue.component('RouterView', View);\n  Vue.component('RouterLink', Link);\n  var strats = Vue.config.optionMergeStrategies; // use the same hook merging strategy for route hooks\n\n  strats.beforeRouteEnter = strats.beforeRouteLeave = strats.beforeRouteUpdate = strats.created;\n}\n/*  */\n\n\nvar inBrowser = typeof window !== 'undefined';\n/*  */\n\nfunction createRouteMap(routes, oldPathList, oldPathMap, oldNameMap, parentRoute) {\n  // the path list is used to control path matching priority\n  var pathList = oldPathList || []; // $flow-disable-line\n\n  var pathMap = oldPathMap || Object.create(null); // $flow-disable-line\n\n  var nameMap = oldNameMap || Object.create(null);\n  routes.forEach(function (route) {\n    addRouteRecord(pathList, pathMap, nameMap, route, parentRoute);\n  }); // ensure wildcard routes are always at the end\n\n  for (var i = 0, l = pathList.length; i < l; i++) {\n    if (pathList[i] === '*') {\n      pathList.push(pathList.splice(i, 1)[0]);\n      l--;\n      i--;\n    }\n  }\n\n  if (true) {\n    // warn if routes do not include leading slashes\n    var found = pathList // check for missing leading slash\n    .filter(function (path) {\n      return path && path.charAt(0) !== '*' && path.charAt(0) !== '/';\n    });\n\n    if (found.length > 0) {\n      var pathNames = found.map(function (path) {\n        return \"- \" + path;\n      }).join('\\n');\n      warn(false, \"Non-nested routes must include a leading slash character. Fix the following routes: \\n\" + pathNames);\n    }\n  }\n\n  return {\n    pathList: pathList,\n    pathMap: pathMap,\n    nameMap: nameMap\n  };\n}\n\nfunction addRouteRecord(pathList, pathMap, nameMap, route, parent, matchAs) {\n  var path = route.path;\n  var name = route.name;\n\n  if (true) {\n    assert(path != null, \"\\\"path\\\" is required in a route configuration.\");\n    assert(typeof route.component !== 'string', \"route config \\\"component\\\" for path: \" + String(path || name) + \" cannot be a \" + \"string id. Use an actual component instead.\");\n    warn( // eslint-disable-next-line no-control-regex\n    !/[^\\u0000-\\u007F]+/.test(path), \"Route with path \\\"\" + path + \"\\\" contains unencoded characters, make sure \" + \"your path is correctly encoded before passing it to the router. Use \" + \"encodeURI to encode static segments of your path.\");\n  }\n\n  var pathToRegexpOptions = route.pathToRegexpOptions || {};\n  var normalizedPath = normalizePath(path, parent, pathToRegexpOptions.strict);\n\n  if (typeof route.caseSensitive === 'boolean') {\n    pathToRegexpOptions.sensitive = route.caseSensitive;\n  }\n\n  var record = {\n    path: normalizedPath,\n    regex: compileRouteRegex(normalizedPath, pathToRegexpOptions),\n    components: route.components || {\n      default: route.component\n    },\n    alias: route.alias ? typeof route.alias === 'string' ? [route.alias] : route.alias : [],\n    instances: {},\n    enteredCbs: {},\n    name: name,\n    parent: parent,\n    matchAs: matchAs,\n    redirect: route.redirect,\n    beforeEnter: route.beforeEnter,\n    meta: route.meta || {},\n    props: route.props == null ? {} : route.components ? route.props : {\n      default: route.props\n    }\n  };\n\n  if (route.children) {\n    // Warn if route is named, does not redirect and has a default child route.\n    // If users navigate to this route by name, the default child will\n    // not be rendered (GH Issue #629)\n    if (true) {\n      if (route.name && !route.redirect && route.children.some(function (child) {\n        return /^\\/?$/.test(child.path);\n      })) {\n        warn(false, \"Named Route '\" + route.name + \"' has a default child route. \" + \"When navigating to this named route (:to=\\\"{name: '\" + route.name + \"'}\\\"), \" + \"the default child route will not be rendered. Remove the name from \" + \"this route and use the name of the default child route for named \" + \"links instead.\");\n      }\n    }\n\n    route.children.forEach(function (child) {\n      var childMatchAs = matchAs ? cleanPath(matchAs + \"/\" + child.path) : undefined;\n      addRouteRecord(pathList, pathMap, nameMap, child, record, childMatchAs);\n    });\n  }\n\n  if (!pathMap[record.path]) {\n    pathList.push(record.path);\n    pathMap[record.path] = record;\n  }\n\n  if (route.alias !== undefined) {\n    var aliases = Array.isArray(route.alias) ? route.alias : [route.alias];\n\n    for (var i = 0; i < aliases.length; ++i) {\n      var alias = aliases[i];\n\n      if ( true && alias === path) {\n        warn(false, \"Found an alias with the same value as the path: \\\"\" + path + \"\\\". You have to remove that alias. It will be ignored in development.\"); // skip in dev to make it work\n\n        continue;\n      }\n\n      var aliasRoute = {\n        path: alias,\n        children: route.children\n      };\n      addRouteRecord(pathList, pathMap, nameMap, aliasRoute, parent, record.path || '/' // matchAs\n      );\n    }\n  }\n\n  if (name) {\n    if (!nameMap[name]) {\n      nameMap[name] = record;\n    } else if ( true && !matchAs) {\n      warn(false, \"Duplicate named routes definition: \" + \"{ name: \\\"\" + name + \"\\\", path: \\\"\" + record.path + \"\\\" }\");\n    }\n  }\n}\n\nfunction compileRouteRegex(path, pathToRegexpOptions) {\n  var regex = pathToRegexp_1(path, [], pathToRegexpOptions);\n\n  if (true) {\n    var keys = Object.create(null);\n    regex.keys.forEach(function (key) {\n      warn(!keys[key.name], \"Duplicate param keys in route with path: \\\"\" + path + \"\\\"\");\n      keys[key.name] = true;\n    });\n  }\n\n  return regex;\n}\n\nfunction normalizePath(path, parent, strict) {\n  if (!strict) {\n    path = path.replace(/\\/$/, '');\n  }\n\n  if (path[0] === '/') {\n    return path;\n  }\n\n  if (parent == null) {\n    return path;\n  }\n\n  return cleanPath(parent.path + \"/\" + path);\n}\n/*  */\n\n\nfunction createMatcher(routes, router) {\n  var ref = createRouteMap(routes);\n  var pathList = ref.pathList;\n  var pathMap = ref.pathMap;\n  var nameMap = ref.nameMap;\n\n  function addRoutes(routes) {\n    createRouteMap(routes, pathList, pathMap, nameMap);\n  }\n\n  function addRoute(parentOrRoute, route) {\n    var parent = typeof parentOrRoute !== 'object' ? nameMap[parentOrRoute] : undefined; // $flow-disable-line\n\n    createRouteMap([route || parentOrRoute], pathList, pathMap, nameMap, parent); // add aliases of parent\n\n    if (parent && parent.alias.length) {\n      createRouteMap( // $flow-disable-line route is defined if parent is\n      parent.alias.map(function (alias) {\n        return {\n          path: alias,\n          children: [route]\n        };\n      }), pathList, pathMap, nameMap, parent);\n    }\n  }\n\n  function getRoutes() {\n    return pathList.map(function (path) {\n      return pathMap[path];\n    });\n  }\n\n  function match(raw, currentRoute, redirectedFrom) {\n    var location = normalizeLocation(raw, currentRoute, false, router);\n    var name = location.name;\n\n    if (name) {\n      var record = nameMap[name];\n\n      if (true) {\n        warn(record, \"Route with name '\" + name + \"' does not exist\");\n      }\n\n      if (!record) {\n        return _createRoute(null, location);\n      }\n\n      var paramNames = record.regex.keys.filter(function (key) {\n        return !key.optional;\n      }).map(function (key) {\n        return key.name;\n      });\n\n      if (typeof location.params !== 'object') {\n        location.params = {};\n      }\n\n      if (currentRoute && typeof currentRoute.params === 'object') {\n        for (var key in currentRoute.params) {\n          if (!(key in location.params) && paramNames.indexOf(key) > -1) {\n            location.params[key] = currentRoute.params[key];\n          }\n        }\n      }\n\n      location.path = fillParams(record.path, location.params, \"named route \\\"\" + name + \"\\\"\");\n      return _createRoute(record, location, redirectedFrom);\n    } else if (location.path) {\n      location.params = {};\n\n      for (var i = 0; i < pathList.length; i++) {\n        var path = pathList[i];\n        var record$1 = pathMap[path];\n\n        if (matchRoute(record$1.regex, location.path, location.params)) {\n          return _createRoute(record$1, location, redirectedFrom);\n        }\n      }\n    } // no match\n\n\n    return _createRoute(null, location);\n  }\n\n  function redirect(record, location) {\n    var originalRedirect = record.redirect;\n    var redirect = typeof originalRedirect === 'function' ? originalRedirect(createRoute(record, location, null, router)) : originalRedirect;\n\n    if (typeof redirect === 'string') {\n      redirect = {\n        path: redirect\n      };\n    }\n\n    if (!redirect || typeof redirect !== 'object') {\n      if (true) {\n        warn(false, \"invalid redirect option: \" + JSON.stringify(redirect));\n      }\n\n      return _createRoute(null, location);\n    }\n\n    var re = redirect;\n    var name = re.name;\n    var path = re.path;\n    var query = location.query;\n    var hash = location.hash;\n    var params = location.params;\n    query = re.hasOwnProperty('query') ? re.query : query;\n    hash = re.hasOwnProperty('hash') ? re.hash : hash;\n    params = re.hasOwnProperty('params') ? re.params : params;\n\n    if (name) {\n      // resolved named direct\n      var targetRecord = nameMap[name];\n\n      if (true) {\n        assert(targetRecord, \"redirect failed: named route \\\"\" + name + \"\\\" not found.\");\n      }\n\n      return match({\n        _normalized: true,\n        name: name,\n        query: query,\n        hash: hash,\n        params: params\n      }, undefined, location);\n    } else if (path) {\n      // 1. resolve relative redirect\n      var rawPath = resolveRecordPath(path, record); // 2. resolve params\n\n      var resolvedPath = fillParams(rawPath, params, \"redirect route with path \\\"\" + rawPath + \"\\\"\"); // 3. rematch with existing query and hash\n\n      return match({\n        _normalized: true,\n        path: resolvedPath,\n        query: query,\n        hash: hash\n      }, undefined, location);\n    } else {\n      if (true) {\n        warn(false, \"invalid redirect option: \" + JSON.stringify(redirect));\n      }\n\n      return _createRoute(null, location);\n    }\n  }\n\n  function alias(record, location, matchAs) {\n    var aliasedPath = fillParams(matchAs, location.params, \"aliased route with path \\\"\" + matchAs + \"\\\"\");\n    var aliasedMatch = match({\n      _normalized: true,\n      path: aliasedPath\n    });\n\n    if (aliasedMatch) {\n      var matched = aliasedMatch.matched;\n      var aliasedRecord = matched[matched.length - 1];\n      location.params = aliasedMatch.params;\n      return _createRoute(aliasedRecord, location);\n    }\n\n    return _createRoute(null, location);\n  }\n\n  function _createRoute(record, location, redirectedFrom) {\n    if (record && record.redirect) {\n      return redirect(record, redirectedFrom || location);\n    }\n\n    if (record && record.matchAs) {\n      return alias(record, location, record.matchAs);\n    }\n\n    return createRoute(record, location, redirectedFrom, router);\n  }\n\n  return {\n    match: match,\n    addRoute: addRoute,\n    getRoutes: getRoutes,\n    addRoutes: addRoutes\n  };\n}\n\nfunction matchRoute(regex, path, params) {\n  var m = path.match(regex);\n\n  if (!m) {\n    return false;\n  } else if (!params) {\n    return true;\n  }\n\n  for (var i = 1, len = m.length; i < len; ++i) {\n    var key = regex.keys[i - 1];\n\n    if (key) {\n      // Fix #1994: using * with props: true generates a param named 0\n      params[key.name || 'pathMatch'] = typeof m[i] === 'string' ? decode(m[i]) : m[i];\n    }\n  }\n\n  return true;\n}\n\nfunction resolveRecordPath(path, record) {\n  return resolvePath(path, record.parent ? record.parent.path : '/', true);\n}\n/*  */\n// use User Timing api (if present) for more accurate key precision\n\n\nvar Time = inBrowser && window.performance && window.performance.now ? window.performance : Date;\n\nfunction genStateKey() {\n  return Time.now().toFixed(3);\n}\n\nvar _key = genStateKey();\n\nfunction getStateKey() {\n  return _key;\n}\n\nfunction setStateKey(key) {\n  return _key = key;\n}\n/*  */\n\n\nvar positionStore = Object.create(null);\n\nfunction setupScroll() {\n  // Prevent browser scroll behavior on History popstate\n  if ('scrollRestoration' in window.history) {\n    window.history.scrollRestoration = 'manual';\n  } // Fix for #1585 for Firefox\n  // Fix for #2195 Add optional third attribute to workaround a bug in safari https://bugs.webkit.org/show_bug.cgi?id=182678\n  // Fix for #2774 Support for apps loaded from Windows file shares not mapped to network drives: replaced location.origin with\n  // window.location.protocol + '//' + window.location.host\n  // location.host contains the port and location.hostname doesn't\n\n\n  var protocolAndPath = window.location.protocol + '//' + window.location.host;\n  var absolutePath = window.location.href.replace(protocolAndPath, ''); // preserve existing history state as it could be overriden by the user\n\n  var stateCopy = extend({}, window.history.state);\n  stateCopy.key = getStateKey();\n  window.history.replaceState(stateCopy, '', absolutePath);\n  window.addEventListener('popstate', handlePopState);\n  return function () {\n    window.removeEventListener('popstate', handlePopState);\n  };\n}\n\nfunction handleScroll(router, to, from, isPop) {\n  if (!router.app) {\n    return;\n  }\n\n  var behavior = router.options.scrollBehavior;\n\n  if (!behavior) {\n    return;\n  }\n\n  if (true) {\n    assert(typeof behavior === 'function', \"scrollBehavior must be a function\");\n  } // wait until re-render finishes before scrolling\n\n\n  router.app.$nextTick(function () {\n    var position = getScrollPosition();\n    var shouldScroll = behavior.call(router, to, from, isPop ? position : null);\n\n    if (!shouldScroll) {\n      return;\n    }\n\n    if (typeof shouldScroll.then === 'function') {\n      shouldScroll.then(function (shouldScroll) {\n        scrollToPosition(shouldScroll, position);\n      }).catch(function (err) {\n        if (true) {\n          assert(false, err.toString());\n        }\n      });\n    } else {\n      scrollToPosition(shouldScroll, position);\n    }\n  });\n}\n\nfunction saveScrollPosition() {\n  var key = getStateKey();\n\n  if (key) {\n    positionStore[key] = {\n      x: window.pageXOffset,\n      y: window.pageYOffset\n    };\n  }\n}\n\nfunction handlePopState(e) {\n  saveScrollPosition();\n\n  if (e.state && e.state.key) {\n    setStateKey(e.state.key);\n  }\n}\n\nfunction getScrollPosition() {\n  var key = getStateKey();\n\n  if (key) {\n    return positionStore[key];\n  }\n}\n\nfunction getElementPosition(el, offset) {\n  var docEl = document.documentElement;\n  var docRect = docEl.getBoundingClientRect();\n  var elRect = el.getBoundingClientRect();\n  return {\n    x: elRect.left - docRect.left - offset.x,\n    y: elRect.top - docRect.top - offset.y\n  };\n}\n\nfunction isValidPosition(obj) {\n  return isNumber(obj.x) || isNumber(obj.y);\n}\n\nfunction normalizePosition(obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : window.pageXOffset,\n    y: isNumber(obj.y) ? obj.y : window.pageYOffset\n  };\n}\n\nfunction normalizeOffset(obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : 0,\n    y: isNumber(obj.y) ? obj.y : 0\n  };\n}\n\nfunction isNumber(v) {\n  return typeof v === 'number';\n}\n\nvar hashStartsWithNumberRE = /^#\\d/;\n\nfunction scrollToPosition(shouldScroll, position) {\n  var isObject = typeof shouldScroll === 'object';\n\n  if (isObject && typeof shouldScroll.selector === 'string') {\n    // getElementById would still fail if the selector contains a more complicated query like #main[data-attr]\n    // but at the same time, it doesn't make much sense to select an element with an id and an extra selector\n    var el = hashStartsWithNumberRE.test(shouldScroll.selector) // $flow-disable-line\n    ? document.getElementById(shouldScroll.selector.slice(1)) // $flow-disable-line\n    : document.querySelector(shouldScroll.selector);\n\n    if (el) {\n      var offset = shouldScroll.offset && typeof shouldScroll.offset === 'object' ? shouldScroll.offset : {};\n      offset = normalizeOffset(offset);\n      position = getElementPosition(el, offset);\n    } else if (isValidPosition(shouldScroll)) {\n      position = normalizePosition(shouldScroll);\n    }\n  } else if (isObject && isValidPosition(shouldScroll)) {\n    position = normalizePosition(shouldScroll);\n  }\n\n  if (position) {\n    // $flow-disable-line\n    if ('scrollBehavior' in document.documentElement.style) {\n      window.scrollTo({\n        left: position.x,\n        top: position.y,\n        // $flow-disable-line\n        behavior: shouldScroll.behavior\n      });\n    } else {\n      window.scrollTo(position.x, position.y);\n    }\n  }\n}\n/*  */\n\n\nvar supportsPushState = inBrowser && function () {\n  var ua = window.navigator.userAgent;\n\n  if ((ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) && ua.indexOf('Mobile Safari') !== -1 && ua.indexOf('Chrome') === -1 && ua.indexOf('Windows Phone') === -1) {\n    return false;\n  }\n\n  return window.history && typeof window.history.pushState === 'function';\n}();\n\nfunction pushState(url, replace) {\n  saveScrollPosition(); // try...catch the pushState call to get around Safari\n  // DOM Exception 18 where it limits to 100 pushState calls\n\n  var history = window.history;\n\n  try {\n    if (replace) {\n      // preserve existing history state as it could be overriden by the user\n      var stateCopy = extend({}, history.state);\n      stateCopy.key = getStateKey();\n      history.replaceState(stateCopy, '', url);\n    } else {\n      history.pushState({\n        key: setStateKey(genStateKey())\n      }, '', url);\n    }\n  } catch (e) {\n    window.location[replace ? 'replace' : 'assign'](url);\n  }\n}\n\nfunction replaceState(url) {\n  pushState(url, true);\n}\n/*  */\n\n\nfunction runQueue(queue, fn, cb) {\n  var step = function (index) {\n    if (index >= queue.length) {\n      cb();\n    } else {\n      if (queue[index]) {\n        fn(queue[index], function () {\n          step(index + 1);\n        });\n      } else {\n        step(index + 1);\n      }\n    }\n  };\n\n  step(0);\n} // When changing thing, also edit router.d.ts\n\n\nvar NavigationFailureType = {\n  redirected: 2,\n  aborted: 4,\n  cancelled: 8,\n  duplicated: 16\n};\n\nfunction createNavigationRedirectedError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.redirected, \"Redirected when going from \\\"\" + from.fullPath + \"\\\" to \\\"\" + stringifyRoute(to) + \"\\\" via a navigation guard.\");\n}\n\nfunction createNavigationDuplicatedError(from, to) {\n  var error = createRouterError(from, to, NavigationFailureType.duplicated, \"Avoided redundant navigation to current location: \\\"\" + from.fullPath + \"\\\".\"); // backwards compatible with the first introduction of Errors\n\n  error.name = 'NavigationDuplicated';\n  return error;\n}\n\nfunction createNavigationCancelledError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.cancelled, \"Navigation cancelled from \\\"\" + from.fullPath + \"\\\" to \\\"\" + to.fullPath + \"\\\" with a new navigation.\");\n}\n\nfunction createNavigationAbortedError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.aborted, \"Navigation aborted from \\\"\" + from.fullPath + \"\\\" to \\\"\" + to.fullPath + \"\\\" via a navigation guard.\");\n}\n\nfunction createRouterError(from, to, type, message) {\n  var error = new Error(message);\n  error._isRouter = true;\n  error.from = from;\n  error.to = to;\n  error.type = type;\n  return error;\n}\n\nvar propertiesToLog = ['params', 'query', 'hash'];\n\nfunction stringifyRoute(to) {\n  if (typeof to === 'string') {\n    return to;\n  }\n\n  if ('path' in to) {\n    return to.path;\n  }\n\n  var location = {};\n  propertiesToLog.forEach(function (key) {\n    if (key in to) {\n      location[key] = to[key];\n    }\n  });\n  return JSON.stringify(location, null, 2);\n}\n\nfunction isError(err) {\n  return Object.prototype.toString.call(err).indexOf('Error') > -1;\n}\n\nfunction isNavigationFailure(err, errorType) {\n  return isError(err) && err._isRouter && (errorType == null || err.type === errorType);\n}\n/*  */\n\n\nfunction resolveAsyncComponents(matched) {\n  return function (to, from, next) {\n    var hasAsync = false;\n    var pending = 0;\n    var error = null;\n    flatMapComponents(matched, function (def, _, match, key) {\n      // if it's a function and doesn't have cid attached,\n      // assume it's an async component resolve function.\n      // we are not using Vue's default async resolving mechanism because\n      // we want to halt the navigation until the incoming component has been\n      // resolved.\n      if (typeof def === 'function' && def.cid === undefined) {\n        hasAsync = true;\n        pending++;\n        var resolve = once(function (resolvedDef) {\n          if (isESModule(resolvedDef)) {\n            resolvedDef = resolvedDef.default;\n          } // save resolved on async factory in case it's used elsewhere\n\n\n          def.resolved = typeof resolvedDef === 'function' ? resolvedDef : _Vue.extend(resolvedDef);\n          match.components[key] = resolvedDef;\n          pending--;\n\n          if (pending <= 0) {\n            next();\n          }\n        });\n        var reject = once(function (reason) {\n          var msg = \"Failed to resolve async component \" + key + \": \" + reason;\n           true && warn(false, msg);\n\n          if (!error) {\n            error = isError(reason) ? reason : new Error(msg);\n            next(error);\n          }\n        });\n        var res;\n\n        try {\n          res = def(resolve, reject);\n        } catch (e) {\n          reject(e);\n        }\n\n        if (res) {\n          if (typeof res.then === 'function') {\n            res.then(resolve, reject);\n          } else {\n            // new syntax in Vue 2.3\n            var comp = res.component;\n\n            if (comp && typeof comp.then === 'function') {\n              comp.then(resolve, reject);\n            }\n          }\n        }\n      }\n    });\n\n    if (!hasAsync) {\n      next();\n    }\n  };\n}\n\nfunction flatMapComponents(matched, fn) {\n  return flatten(matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) {\n      return fn(m.components[key], m.instances[key], m, key);\n    });\n  }));\n}\n\nfunction flatten(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\n\nvar hasSymbol = typeof Symbol === 'function' && typeof Symbol.toStringTag === 'symbol';\n\nfunction isESModule(obj) {\n  return obj.__esModule || hasSymbol && obj[Symbol.toStringTag] === 'Module';\n} // in Webpack 2, require.ensure now also returns a Promise\n// so the resolve/reject functions may get called an extra time\n// if the user uses an arrow function shorthand that happens to\n// return that Promise.\n\n\nfunction once(fn) {\n  var called = false;\n  return function () {\n    var args = [],\n        len = arguments.length;\n\n    while (len--) args[len] = arguments[len];\n\n    if (called) {\n      return;\n    }\n\n    called = true;\n    return fn.apply(this, args);\n  };\n}\n/*  */\n\n\nvar History = function History(router, base) {\n  this.router = router;\n  this.base = normalizeBase(base); // start with a route object that stands for \"nowhere\"\n\n  this.current = START;\n  this.pending = null;\n  this.ready = false;\n  this.readyCbs = [];\n  this.readyErrorCbs = [];\n  this.errorCbs = [];\n  this.listeners = [];\n};\n\nHistory.prototype.listen = function listen(cb) {\n  this.cb = cb;\n};\n\nHistory.prototype.onReady = function onReady(cb, errorCb) {\n  if (this.ready) {\n    cb();\n  } else {\n    this.readyCbs.push(cb);\n\n    if (errorCb) {\n      this.readyErrorCbs.push(errorCb);\n    }\n  }\n};\n\nHistory.prototype.onError = function onError(errorCb) {\n  this.errorCbs.push(errorCb);\n};\n\nHistory.prototype.transitionTo = function transitionTo(location, onComplete, onAbort) {\n  var this$1 = this;\n  var route; // catch redirect option https://github.com/vuejs/vue-router/issues/3201\n\n  try {\n    route = this.router.match(location, this.current);\n  } catch (e) {\n    this.errorCbs.forEach(function (cb) {\n      cb(e);\n    }); // Exception should still be thrown\n\n    throw e;\n  }\n\n  var prev = this.current;\n  this.confirmTransition(route, function () {\n    this$1.updateRoute(route);\n    onComplete && onComplete(route);\n    this$1.ensureURL();\n    this$1.router.afterHooks.forEach(function (hook) {\n      hook && hook(route, prev);\n    }); // fire ready cbs once\n\n    if (!this$1.ready) {\n      this$1.ready = true;\n      this$1.readyCbs.forEach(function (cb) {\n        cb(route);\n      });\n    }\n  }, function (err) {\n    if (onAbort) {\n      onAbort(err);\n    }\n\n    if (err && !this$1.ready) {\n      // Initial redirection should not mark the history as ready yet\n      // because it's triggered by the redirection instead\n      // https://github.com/vuejs/vue-router/issues/3225\n      // https://github.com/vuejs/vue-router/issues/3331\n      if (!isNavigationFailure(err, NavigationFailureType.redirected) || prev !== START) {\n        this$1.ready = true;\n        this$1.readyErrorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      }\n    }\n  });\n};\n\nHistory.prototype.confirmTransition = function confirmTransition(route, onComplete, onAbort) {\n  var this$1 = this;\n  var current = this.current;\n  this.pending = route;\n\n  var abort = function (err) {\n    // changed after adding errors with\n    // https://github.com/vuejs/vue-router/pull/3047 before that change,\n    // redirect and aborted navigation would produce an err == null\n    if (!isNavigationFailure(err) && isError(err)) {\n      if (this$1.errorCbs.length) {\n        this$1.errorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      } else {\n        if (true) {\n          warn(false, 'uncaught error during route navigation:');\n        }\n\n        console.error(err);\n      }\n    }\n\n    onAbort && onAbort(err);\n  };\n\n  var lastRouteIndex = route.matched.length - 1;\n  var lastCurrentIndex = current.matched.length - 1;\n\n  if (isSameRoute(route, current) && // in the case the route map has been dynamically appended to\n  lastRouteIndex === lastCurrentIndex && route.matched[lastRouteIndex] === current.matched[lastCurrentIndex]) {\n    this.ensureURL();\n\n    if (route.hash) {\n      handleScroll(this.router, current, route, false);\n    }\n\n    return abort(createNavigationDuplicatedError(current, route));\n  }\n\n  var ref = resolveQueue(this.current.matched, route.matched);\n  var updated = ref.updated;\n  var deactivated = ref.deactivated;\n  var activated = ref.activated;\n  var queue = [].concat( // in-component leave guards\n  extractLeaveGuards(deactivated), // global before hooks\n  this.router.beforeHooks, // in-component update hooks\n  extractUpdateHooks(updated), // in-config enter guards\n  activated.map(function (m) {\n    return m.beforeEnter;\n  }), // async components\n  resolveAsyncComponents(activated));\n\n  var iterator = function (hook, next) {\n    if (this$1.pending !== route) {\n      return abort(createNavigationCancelledError(current, route));\n    }\n\n    try {\n      hook(route, current, function (to) {\n        if (to === false) {\n          // next(false) -> abort navigation, ensure current URL\n          this$1.ensureURL(true);\n          abort(createNavigationAbortedError(current, route));\n        } else if (isError(to)) {\n          this$1.ensureURL(true);\n          abort(to);\n        } else if (typeof to === 'string' || typeof to === 'object' && (typeof to.path === 'string' || typeof to.name === 'string')) {\n          // next('/') or next({ path: '/' }) -> redirect\n          abort(createNavigationRedirectedError(current, route));\n\n          if (typeof to === 'object' && to.replace) {\n            this$1.replace(to);\n          } else {\n            this$1.push(to);\n          }\n        } else {\n          // confirm transition and pass on the value\n          next(to);\n        }\n      });\n    } catch (e) {\n      abort(e);\n    }\n  };\n\n  runQueue(queue, iterator, function () {\n    // wait until async components are resolved before\n    // extracting in-component enter guards\n    var enterGuards = extractEnterGuards(activated);\n    var queue = enterGuards.concat(this$1.router.resolveHooks);\n    runQueue(queue, iterator, function () {\n      if (this$1.pending !== route) {\n        return abort(createNavigationCancelledError(current, route));\n      }\n\n      this$1.pending = null;\n      onComplete(route);\n\n      if (this$1.router.app) {\n        this$1.router.app.$nextTick(function () {\n          handleRouteEntered(route);\n        });\n      }\n    });\n  });\n};\n\nHistory.prototype.updateRoute = function updateRoute(route) {\n  this.current = route;\n  this.cb && this.cb(route);\n};\n\nHistory.prototype.setupListeners = function setupListeners() {// Default implementation is empty\n};\n\nHistory.prototype.teardown = function teardown() {\n  // clean up event listeners\n  // https://github.com/vuejs/vue-router/issues/2341\n  this.listeners.forEach(function (cleanupListener) {\n    cleanupListener();\n  });\n  this.listeners = []; // reset current history route\n  // https://github.com/vuejs/vue-router/issues/3294\n\n  this.current = START;\n  this.pending = null;\n};\n\nfunction normalizeBase(base) {\n  if (!base) {\n    if (inBrowser) {\n      // respect <base> tag\n      var baseEl = document.querySelector('base');\n      base = baseEl && baseEl.getAttribute('href') || '/'; // strip full URL origin\n\n      base = base.replace(/^https?:\\/\\/[^\\/]+/, '');\n    } else {\n      base = '/';\n    }\n  } // make sure there's the starting slash\n\n\n  if (base.charAt(0) !== '/') {\n    base = '/' + base;\n  } // remove trailing slash\n\n\n  return base.replace(/\\/$/, '');\n}\n\nfunction resolveQueue(current, next) {\n  var i;\n  var max = Math.max(current.length, next.length);\n\n  for (i = 0; i < max; i++) {\n    if (current[i] !== next[i]) {\n      break;\n    }\n  }\n\n  return {\n    updated: next.slice(0, i),\n    activated: next.slice(i),\n    deactivated: current.slice(i)\n  };\n}\n\nfunction extractGuards(records, name, bind, reverse) {\n  var guards = flatMapComponents(records, function (def, instance, match, key) {\n    var guard = extractGuard(def, name);\n\n    if (guard) {\n      return Array.isArray(guard) ? guard.map(function (guard) {\n        return bind(guard, instance, match, key);\n      }) : bind(guard, instance, match, key);\n    }\n  });\n  return flatten(reverse ? guards.reverse() : guards);\n}\n\nfunction extractGuard(def, key) {\n  if (typeof def !== 'function') {\n    // extend now so that global mixins are applied.\n    def = _Vue.extend(def);\n  }\n\n  return def.options[key];\n}\n\nfunction extractLeaveGuards(deactivated) {\n  return extractGuards(deactivated, 'beforeRouteLeave', bindGuard, true);\n}\n\nfunction extractUpdateHooks(updated) {\n  return extractGuards(updated, 'beforeRouteUpdate', bindGuard);\n}\n\nfunction bindGuard(guard, instance) {\n  if (instance) {\n    return function boundRouteGuard() {\n      return guard.apply(instance, arguments);\n    };\n  }\n}\n\nfunction extractEnterGuards(activated) {\n  return extractGuards(activated, 'beforeRouteEnter', function (guard, _, match, key) {\n    return bindEnterGuard(guard, match, key);\n  });\n}\n\nfunction bindEnterGuard(guard, match, key) {\n  return function routeEnterGuard(to, from, next) {\n    return guard(to, from, function (cb) {\n      if (typeof cb === 'function') {\n        if (!match.enteredCbs[key]) {\n          match.enteredCbs[key] = [];\n        }\n\n        match.enteredCbs[key].push(cb);\n      }\n\n      next(cb);\n    });\n  };\n}\n/*  */\n\n\nvar HTML5History = /*@__PURE__*/function (History) {\n  function HTML5History(router, base) {\n    History.call(this, router, base);\n    this._startLocation = getLocation(this.base);\n  }\n\n  if (History) HTML5History.__proto__ = History;\n  HTML5History.prototype = Object.create(History && History.prototype);\n  HTML5History.prototype.constructor = HTML5History;\n\n  HTML5History.prototype.setupListeners = function setupListeners() {\n    var this$1 = this;\n\n    if (this.listeners.length > 0) {\n      return;\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1.current; // Avoiding first `popstate` event dispatched in some browsers but first\n      // history route not updated since async guard at the same time.\n\n      var location = getLocation(this$1.base);\n\n      if (this$1.current === START && location === this$1._startLocation) {\n        return;\n      }\n\n      this$1.transitionTo(location, function (route) {\n        if (supportsScroll) {\n          handleScroll(router, route, current, true);\n        }\n      });\n    };\n\n    window.addEventListener('popstate', handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener('popstate', handleRoutingEvent);\n    });\n  };\n\n  HTML5History.prototype.go = function go(n) {\n    window.history.go(n);\n  };\n\n  HTML5History.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushState(cleanPath(this$1.base + route.fullPath));\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceState(cleanPath(this$1.base + route.fullPath));\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.ensureURL = function ensureURL(push) {\n    if (getLocation(this.base) !== this.current.fullPath) {\n      var current = cleanPath(this.base + this.current.fullPath);\n      push ? pushState(current) : replaceState(current);\n    }\n  };\n\n  HTML5History.prototype.getCurrentLocation = function getCurrentLocation() {\n    return getLocation(this.base);\n  };\n\n  return HTML5History;\n}(History);\n\nfunction getLocation(base) {\n  var path = window.location.pathname;\n  var pathLowerCase = path.toLowerCase();\n  var baseLowerCase = base.toLowerCase(); // base=\"/a\" shouldn't turn path=\"/app\" into \"/a/pp\"\n  // https://github.com/vuejs/vue-router/issues/3555\n  // so we ensure the trailing slash in the base\n\n  if (base && (pathLowerCase === baseLowerCase || pathLowerCase.indexOf(cleanPath(baseLowerCase + '/')) === 0)) {\n    path = path.slice(base.length);\n  }\n\n  return (path || '/') + window.location.search + window.location.hash;\n}\n/*  */\n\n\nvar HashHistory = /*@__PURE__*/function (History) {\n  function HashHistory(router, base, fallback) {\n    History.call(this, router, base); // check history fallback deeplinking\n\n    if (fallback && checkFallback(this.base)) {\n      return;\n    }\n\n    ensureSlash();\n  }\n\n  if (History) HashHistory.__proto__ = History;\n  HashHistory.prototype = Object.create(History && History.prototype);\n  HashHistory.prototype.constructor = HashHistory; // this is delayed until the app mounts\n  // to avoid the hashchange listener being fired too early\n\n  HashHistory.prototype.setupListeners = function setupListeners() {\n    var this$1 = this;\n\n    if (this.listeners.length > 0) {\n      return;\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1.current;\n\n      if (!ensureSlash()) {\n        return;\n      }\n\n      this$1.transitionTo(getHash(), function (route) {\n        if (supportsScroll) {\n          handleScroll(this$1.router, route, current, true);\n        }\n\n        if (!supportsPushState) {\n          replaceHash(route.fullPath);\n        }\n      });\n    };\n\n    var eventType = supportsPushState ? 'popstate' : 'hashchange';\n    window.addEventListener(eventType, handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener(eventType, handleRoutingEvent);\n    });\n  };\n\n  HashHistory.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushHash(route.fullPath);\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HashHistory.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceHash(route.fullPath);\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HashHistory.prototype.go = function go(n) {\n    window.history.go(n);\n  };\n\n  HashHistory.prototype.ensureURL = function ensureURL(push) {\n    var current = this.current.fullPath;\n\n    if (getHash() !== current) {\n      push ? pushHash(current) : replaceHash(current);\n    }\n  };\n\n  HashHistory.prototype.getCurrentLocation = function getCurrentLocation() {\n    return getHash();\n  };\n\n  return HashHistory;\n}(History);\n\nfunction checkFallback(base) {\n  var location = getLocation(base);\n\n  if (!/^\\/#/.test(location)) {\n    window.location.replace(cleanPath(base + '/#' + location));\n    return true;\n  }\n}\n\nfunction ensureSlash() {\n  var path = getHash();\n\n  if (path.charAt(0) === '/') {\n    return true;\n  }\n\n  replaceHash('/' + path);\n  return false;\n}\n\nfunction getHash() {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var index = href.indexOf('#'); // empty path\n\n  if (index < 0) {\n    return '';\n  }\n\n  href = href.slice(index + 1);\n  return href;\n}\n\nfunction getUrl(path) {\n  var href = window.location.href;\n  var i = href.indexOf('#');\n  var base = i >= 0 ? href.slice(0, i) : href;\n  return base + \"#\" + path;\n}\n\nfunction pushHash(path) {\n  if (supportsPushState) {\n    pushState(getUrl(path));\n  } else {\n    window.location.hash = path;\n  }\n}\n\nfunction replaceHash(path) {\n  if (supportsPushState) {\n    replaceState(getUrl(path));\n  } else {\n    window.location.replace(getUrl(path));\n  }\n}\n/*  */\n\n\nvar AbstractHistory = /*@__PURE__*/function (History) {\n  function AbstractHistory(router, base) {\n    History.call(this, router, base);\n    this.stack = [];\n    this.index = -1;\n  }\n\n  if (History) AbstractHistory.__proto__ = History;\n  AbstractHistory.prototype = Object.create(History && History.prototype);\n  AbstractHistory.prototype.constructor = AbstractHistory;\n\n  AbstractHistory.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1 = this;\n    this.transitionTo(location, function (route) {\n      this$1.stack = this$1.stack.slice(0, this$1.index + 1).concat(route);\n      this$1.index++;\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  AbstractHistory.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1 = this;\n    this.transitionTo(location, function (route) {\n      this$1.stack = this$1.stack.slice(0, this$1.index).concat(route);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  AbstractHistory.prototype.go = function go(n) {\n    var this$1 = this;\n    var targetIndex = this.index + n;\n\n    if (targetIndex < 0 || targetIndex >= this.stack.length) {\n      return;\n    }\n\n    var route = this.stack[targetIndex];\n    this.confirmTransition(route, function () {\n      var prev = this$1.current;\n      this$1.index = targetIndex;\n      this$1.updateRoute(route);\n      this$1.router.afterHooks.forEach(function (hook) {\n        hook && hook(route, prev);\n      });\n    }, function (err) {\n      if (isNavigationFailure(err, NavigationFailureType.duplicated)) {\n        this$1.index = targetIndex;\n      }\n    });\n  };\n\n  AbstractHistory.prototype.getCurrentLocation = function getCurrentLocation() {\n    var current = this.stack[this.stack.length - 1];\n    return current ? current.fullPath : '/';\n  };\n\n  AbstractHistory.prototype.ensureURL = function ensureURL() {// noop\n  };\n\n  return AbstractHistory;\n}(History);\n/*  */\n\n\nvar VueRouter = function VueRouter(options) {\n  if (options === void 0) options = {};\n\n  if (true) {\n    warn(this instanceof VueRouter, \"Router must be called with the new operator.\");\n  }\n\n  this.app = null;\n  this.apps = [];\n  this.options = options;\n  this.beforeHooks = [];\n  this.resolveHooks = [];\n  this.afterHooks = [];\n  this.matcher = createMatcher(options.routes || [], this);\n  var mode = options.mode || 'hash';\n  this.fallback = mode === 'history' && !supportsPushState && options.fallback !== false;\n\n  if (this.fallback) {\n    mode = 'hash';\n  }\n\n  if (!inBrowser) {\n    mode = 'abstract';\n  }\n\n  this.mode = mode;\n\n  switch (mode) {\n    case 'history':\n      this.history = new HTML5History(this, options.base);\n      break;\n\n    case 'hash':\n      this.history = new HashHistory(this, options.base, this.fallback);\n      break;\n\n    case 'abstract':\n      this.history = new AbstractHistory(this, options.base);\n      break;\n\n    default:\n      if (true) {\n        assert(false, \"invalid mode: \" + mode);\n      }\n\n  }\n};\n\nvar prototypeAccessors = {\n  currentRoute: {\n    configurable: true\n  }\n};\n\nVueRouter.prototype.match = function match(raw, current, redirectedFrom) {\n  return this.matcher.match(raw, current, redirectedFrom);\n};\n\nprototypeAccessors.currentRoute.get = function () {\n  return this.history && this.history.current;\n};\n\nVueRouter.prototype.init = function init(app\n/* Vue component instance */\n) {\n  var this$1 = this;\n   true && assert(install.installed, \"not installed. Make sure to call `Vue.use(VueRouter)` \" + \"before creating root instance.\");\n  this.apps.push(app); // set up app destroyed handler\n  // https://github.com/vuejs/vue-router/issues/2639\n\n  app.$once('hook:destroyed', function () {\n    // clean out app from this.apps array once destroyed\n    var index = this$1.apps.indexOf(app);\n\n    if (index > -1) {\n      this$1.apps.splice(index, 1);\n    } // ensure we still have a main app or null if no apps\n    // we do not release the router so it can be reused\n\n\n    if (this$1.app === app) {\n      this$1.app = this$1.apps[0] || null;\n    }\n\n    if (!this$1.app) {\n      this$1.history.teardown();\n    }\n  }); // main app previously initialized\n  // return as we don't need to set up new history listener\n\n  if (this.app) {\n    return;\n  }\n\n  this.app = app;\n  var history = this.history;\n\n  if (history instanceof HTML5History || history instanceof HashHistory) {\n    var handleInitialScroll = function (routeOrError) {\n      var from = history.current;\n      var expectScroll = this$1.options.scrollBehavior;\n      var supportsScroll = supportsPushState && expectScroll;\n\n      if (supportsScroll && 'fullPath' in routeOrError) {\n        handleScroll(this$1, routeOrError, from, false);\n      }\n    };\n\n    var setupListeners = function (routeOrError) {\n      history.setupListeners();\n      handleInitialScroll(routeOrError);\n    };\n\n    history.transitionTo(history.getCurrentLocation(), setupListeners, setupListeners);\n  }\n\n  history.listen(function (route) {\n    this$1.apps.forEach(function (app) {\n      app._route = route;\n    });\n  });\n};\n\nVueRouter.prototype.beforeEach = function beforeEach(fn) {\n  return registerHook(this.beforeHooks, fn);\n};\n\nVueRouter.prototype.beforeResolve = function beforeResolve(fn) {\n  return registerHook(this.resolveHooks, fn);\n};\n\nVueRouter.prototype.afterEach = function afterEach(fn) {\n  return registerHook(this.afterHooks, fn);\n};\n\nVueRouter.prototype.onReady = function onReady(cb, errorCb) {\n  this.history.onReady(cb, errorCb);\n};\n\nVueRouter.prototype.onError = function onError(errorCb) {\n  this.history.onError(errorCb);\n};\n\nVueRouter.prototype.push = function push(location, onComplete, onAbort) {\n  var this$1 = this; // $flow-disable-line\n\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1.history.push(location, resolve, reject);\n    });\n  } else {\n    this.history.push(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.replace = function replace(location, onComplete, onAbort) {\n  var this$1 = this; // $flow-disable-line\n\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1.history.replace(location, resolve, reject);\n    });\n  } else {\n    this.history.replace(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.go = function go(n) {\n  this.history.go(n);\n};\n\nVueRouter.prototype.back = function back() {\n  this.go(-1);\n};\n\nVueRouter.prototype.forward = function forward() {\n  this.go(1);\n};\n\nVueRouter.prototype.getMatchedComponents = function getMatchedComponents(to) {\n  var route = to ? to.matched ? to : this.resolve(to).route : this.currentRoute;\n\n  if (!route) {\n    return [];\n  }\n\n  return [].concat.apply([], route.matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) {\n      return m.components[key];\n    });\n  }));\n};\n\nVueRouter.prototype.resolve = function resolve(to, current, append) {\n  current = current || this.history.current;\n  var location = normalizeLocation(to, current, append, this);\n  var route = this.match(location, current);\n  var fullPath = route.redirectedFrom || route.fullPath;\n  var base = this.history.base;\n  var href = createHref(base, fullPath, this.mode);\n  return {\n    location: location,\n    route: route,\n    href: href,\n    // for backwards compat\n    normalizedTo: location,\n    resolved: route\n  };\n};\n\nVueRouter.prototype.getRoutes = function getRoutes() {\n  return this.matcher.getRoutes();\n};\n\nVueRouter.prototype.addRoute = function addRoute(parentOrRoute, route) {\n  this.matcher.addRoute(parentOrRoute, route);\n\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\n\nVueRouter.prototype.addRoutes = function addRoutes(routes) {\n  if (true) {\n    warn(false, 'router.addRoutes() is deprecated and has been removed in Vue Router 4. Use router.addRoute() instead.');\n  }\n\n  this.matcher.addRoutes(routes);\n\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\n\nObject.defineProperties(VueRouter.prototype, prototypeAccessors);\n\nfunction registerHook(list, fn) {\n  list.push(fn);\n  return function () {\n    var i = list.indexOf(fn);\n\n    if (i > -1) {\n      list.splice(i, 1);\n    }\n  };\n}\n\nfunction createHref(base, fullPath, mode) {\n  var path = mode === 'hash' ? '#' + fullPath : fullPath;\n  return base ? cleanPath(base + '/' + path) : path;\n}\n\nVueRouter.install = install;\nVueRouter.version = '3.5.4';\nVueRouter.isNavigationFailure = isNavigationFailure;\nVueRouter.NavigationFailureType = NavigationFailureType;\nVueRouter.START_LOCATION = START;\n\nif (inBrowser && window.Vue) {\n  window.Vue.use(VueRouter);\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (VueRouter);\n\n//# sourceURL=webpack:///./node_modules/vue-router/dist/vue-router.esm.js?");

/***/ }),

/***/ "./node_modules/vue/dist/vue.esm.js":
/*!******************************************!*\
  !*** ./node_modules/vue/dist/vue.esm.js ***!
  \******************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(global, setImmediate) {/*!\n * Vue.js v2.6.14\n * (c) 2014-2021 Evan You\n * Released under the MIT License.\n */\n\n/*  */\nvar emptyObject = Object.freeze({}); // These helpers produce better VM code in JS engines due to their\n// explicitness and function inlining.\n\nfunction isUndef(v) {\n  return v === undefined || v === null;\n}\n\nfunction isDef(v) {\n  return v !== undefined && v !== null;\n}\n\nfunction isTrue(v) {\n  return v === true;\n}\n\nfunction isFalse(v) {\n  return v === false;\n}\n/**\n * Check if value is primitive.\n */\n\n\nfunction isPrimitive(value) {\n  return typeof value === 'string' || typeof value === 'number' || // $flow-disable-line\n  typeof value === 'symbol' || typeof value === 'boolean';\n}\n/**\n * Quick object check - this is primarily used to tell\n * Objects from primitive values when we know the value\n * is a JSON-compliant type.\n */\n\n\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object';\n}\n/**\n * Get the raw type string of a value, e.g., [object Object].\n */\n\n\nvar _toString = Object.prototype.toString;\n\nfunction toRawType(value) {\n  return _toString.call(value).slice(8, -1);\n}\n/**\n * Strict object type check. Only returns true\n * for plain JavaScript objects.\n */\n\n\nfunction isPlainObject(obj) {\n  return _toString.call(obj) === '[object Object]';\n}\n\nfunction isRegExp(v) {\n  return _toString.call(v) === '[object RegExp]';\n}\n/**\n * Check if val is a valid array index.\n */\n\n\nfunction isValidArrayIndex(val) {\n  var n = parseFloat(String(val));\n  return n >= 0 && Math.floor(n) === n && isFinite(val);\n}\n\nfunction isPromise(val) {\n  return isDef(val) && typeof val.then === 'function' && typeof val.catch === 'function';\n}\n/**\n * Convert a value to a string that is actually rendered.\n */\n\n\nfunction toString(val) {\n  return val == null ? '' : Array.isArray(val) || isPlainObject(val) && val.toString === _toString ? JSON.stringify(val, null, 2) : String(val);\n}\n/**\n * Convert an input value to a number for persistence.\n * If the conversion fails, return original string.\n */\n\n\nfunction toNumber(val) {\n  var n = parseFloat(val);\n  return isNaN(n) ? val : n;\n}\n/**\n * Make a map and return a function for checking if a key\n * is in that map.\n */\n\n\nfunction makeMap(str, expectsLowerCase) {\n  var map = Object.create(null);\n  var list = str.split(',');\n\n  for (var i = 0; i < list.length; i++) {\n    map[list[i]] = true;\n  }\n\n  return expectsLowerCase ? function (val) {\n    return map[val.toLowerCase()];\n  } : function (val) {\n    return map[val];\n  };\n}\n/**\n * Check if a tag is a built-in tag.\n */\n\n\nvar isBuiltInTag = makeMap('slot,component', true);\n/**\n * Check if an attribute is a reserved attribute.\n */\n\nvar isReservedAttribute = makeMap('key,ref,slot,slot-scope,is');\n/**\n * Remove an item from an array.\n */\n\nfunction remove(arr, item) {\n  if (arr.length) {\n    var index = arr.indexOf(item);\n\n    if (index > -1) {\n      return arr.splice(index, 1);\n    }\n  }\n}\n/**\n * Check whether an object has the property.\n */\n\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction hasOwn(obj, key) {\n  return hasOwnProperty.call(obj, key);\n}\n/**\n * Create a cached version of a pure function.\n */\n\n\nfunction cached(fn) {\n  var cache = Object.create(null);\n  return function cachedFn(str) {\n    var hit = cache[str];\n    return hit || (cache[str] = fn(str));\n  };\n}\n/**\n * Camelize a hyphen-delimited string.\n */\n\n\nvar camelizeRE = /-(\\w)/g;\nvar camelize = cached(function (str) {\n  return str.replace(camelizeRE, function (_, c) {\n    return c ? c.toUpperCase() : '';\n  });\n});\n/**\n * Capitalize a string.\n */\n\nvar capitalize = cached(function (str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n});\n/**\n * Hyphenate a camelCase string.\n */\n\nvar hyphenateRE = /\\B([A-Z])/g;\nvar hyphenate = cached(function (str) {\n  return str.replace(hyphenateRE, '-$1').toLowerCase();\n});\n/**\n * Simple bind polyfill for environments that do not support it,\n * e.g., PhantomJS 1.x. Technically, we don't need this anymore\n * since native bind is now performant enough in most browsers.\n * But removing it would mean breaking code that was able to run in\n * PhantomJS 1.x, so this must be kept for backward compatibility.\n */\n\n/* istanbul ignore next */\n\nfunction polyfillBind(fn, ctx) {\n  function boundFn(a) {\n    var l = arguments.length;\n    return l ? l > 1 ? fn.apply(ctx, arguments) : fn.call(ctx, a) : fn.call(ctx);\n  }\n\n  boundFn._length = fn.length;\n  return boundFn;\n}\n\nfunction nativeBind(fn, ctx) {\n  return fn.bind(ctx);\n}\n\nvar bind = Function.prototype.bind ? nativeBind : polyfillBind;\n/**\n * Convert an Array-like object to a real Array.\n */\n\nfunction toArray(list, start) {\n  start = start || 0;\n  var i = list.length - start;\n  var ret = new Array(i);\n\n  while (i--) {\n    ret[i] = list[i + start];\n  }\n\n  return ret;\n}\n/**\n * Mix properties into target object.\n */\n\n\nfunction extend(to, _from) {\n  for (var key in _from) {\n    to[key] = _from[key];\n  }\n\n  return to;\n}\n/**\n * Merge an Array of Objects into a single Object.\n */\n\n\nfunction toObject(arr) {\n  var res = {};\n\n  for (var i = 0; i < arr.length; i++) {\n    if (arr[i]) {\n      extend(res, arr[i]);\n    }\n  }\n\n  return res;\n}\n/* eslint-disable no-unused-vars */\n\n/**\n * Perform no operation.\n * Stubbing args to make Flow happy without leaving useless transpiled code\n * with ...rest (https://flow.org/blog/2017/05/07/Strict-Function-Call-Arity/).\n */\n\n\nfunction noop(a, b, c) {}\n/**\n * Always return false.\n */\n\n\nvar no = function (a, b, c) {\n  return false;\n};\n/* eslint-enable no-unused-vars */\n\n/**\n * Return the same value.\n */\n\n\nvar identity = function (_) {\n  return _;\n};\n/**\n * Generate a string containing static keys from compiler modules.\n */\n\n\nfunction genStaticKeys(modules) {\n  return modules.reduce(function (keys, m) {\n    return keys.concat(m.staticKeys || []);\n  }, []).join(',');\n}\n/**\n * Check if two values are loosely equal - that is,\n * if they are plain objects, do they have the same shape?\n */\n\n\nfunction looseEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  var isObjectA = isObject(a);\n  var isObjectB = isObject(b);\n\n  if (isObjectA && isObjectB) {\n    try {\n      var isArrayA = Array.isArray(a);\n      var isArrayB = Array.isArray(b);\n\n      if (isArrayA && isArrayB) {\n        return a.length === b.length && a.every(function (e, i) {\n          return looseEqual(e, b[i]);\n        });\n      } else if (a instanceof Date && b instanceof Date) {\n        return a.getTime() === b.getTime();\n      } else if (!isArrayA && !isArrayB) {\n        var keysA = Object.keys(a);\n        var keysB = Object.keys(b);\n        return keysA.length === keysB.length && keysA.every(function (key) {\n          return looseEqual(a[key], b[key]);\n        });\n      } else {\n        /* istanbul ignore next */\n        return false;\n      }\n    } catch (e) {\n      /* istanbul ignore next */\n      return false;\n    }\n  } else if (!isObjectA && !isObjectB) {\n    return String(a) === String(b);\n  } else {\n    return false;\n  }\n}\n/**\n * Return the first index at which a loosely equal value can be\n * found in the array (if value is a plain object, the array must\n * contain an object of the same shape), or -1 if it is not present.\n */\n\n\nfunction looseIndexOf(arr, val) {\n  for (var i = 0; i < arr.length; i++) {\n    if (looseEqual(arr[i], val)) {\n      return i;\n    }\n  }\n\n  return -1;\n}\n/**\n * Ensure a function is called only once.\n */\n\n\nfunction once(fn) {\n  var called = false;\n  return function () {\n    if (!called) {\n      called = true;\n      fn.apply(this, arguments);\n    }\n  };\n}\n\nvar SSR_ATTR = 'data-server-rendered';\nvar ASSET_TYPES = ['component', 'directive', 'filter'];\nvar LIFECYCLE_HOOKS = ['beforeCreate', 'created', 'beforeMount', 'mounted', 'beforeUpdate', 'updated', 'beforeDestroy', 'destroyed', 'activated', 'deactivated', 'errorCaptured', 'serverPrefetch'];\n/*  */\n\nvar config = {\n  /**\n   * Option merge strategies (used in core/util/options)\n   */\n  // $flow-disable-line\n  optionMergeStrategies: Object.create(null),\n\n  /**\n   * Whether to suppress warnings.\n   */\n  silent: false,\n\n  /**\n   * Show production mode tip message on boot?\n   */\n  productionTip: \"development\" !== 'production',\n\n  /**\n   * Whether to enable devtools\n   */\n  devtools: \"development\" !== 'production',\n\n  /**\n   * Whether to record perf\n   */\n  performance: false,\n\n  /**\n   * Error handler for watcher errors\n   */\n  errorHandler: null,\n\n  /**\n   * Warn handler for watcher warns\n   */\n  warnHandler: null,\n\n  /**\n   * Ignore certain custom elements\n   */\n  ignoredElements: [],\n\n  /**\n   * Custom user key aliases for v-on\n   */\n  // $flow-disable-line\n  keyCodes: Object.create(null),\n\n  /**\n   * Check if a tag is reserved so that it cannot be registered as a\n   * component. This is platform-dependent and may be overwritten.\n   */\n  isReservedTag: no,\n\n  /**\n   * Check if an attribute is reserved so that it cannot be used as a component\n   * prop. This is platform-dependent and may be overwritten.\n   */\n  isReservedAttr: no,\n\n  /**\n   * Check if a tag is an unknown element.\n   * Platform-dependent.\n   */\n  isUnknownElement: no,\n\n  /**\n   * Get the namespace of an element\n   */\n  getTagNamespace: noop,\n\n  /**\n   * Parse the real tag name for the specific platform.\n   */\n  parsePlatformTagName: identity,\n\n  /**\n   * Check if an attribute must be bound using property, e.g. value\n   * Platform-dependent.\n   */\n  mustUseProp: no,\n\n  /**\n   * Perform updates asynchronously. Intended to be used by Vue Test Utils\n   * This will significantly reduce performance if set to false.\n   */\n  async: true,\n\n  /**\n   * Exposed for legacy reasons\n   */\n  _lifecycleHooks: LIFECYCLE_HOOKS\n};\n/*  */\n\n/**\n * unicode letters used for parsing html tags, component names and property paths.\n * using https://www.w3.org/TR/html53/semantics-scripting.html#potentialcustomelementname\n * skipping \\u10000-\\uEFFFF due to it freezing up PhantomJS\n */\n\nvar unicodeRegExp = /a-zA-Z\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD/;\n/**\n * Check if a string starts with $ or _\n */\n\nfunction isReserved(str) {\n  var c = (str + '').charCodeAt(0);\n  return c === 0x24 || c === 0x5F;\n}\n/**\n * Define a property.\n */\n\n\nfunction def(obj, key, val, enumerable) {\n  Object.defineProperty(obj, key, {\n    value: val,\n    enumerable: !!enumerable,\n    writable: true,\n    configurable: true\n  });\n}\n/**\n * Parse simple path.\n */\n\n\nvar bailRE = new RegExp(\"[^\" + unicodeRegExp.source + \".$_\\\\d]\");\n\nfunction parsePath(path) {\n  if (bailRE.test(path)) {\n    return;\n  }\n\n  var segments = path.split('.');\n  return function (obj) {\n    for (var i = 0; i < segments.length; i++) {\n      if (!obj) {\n        return;\n      }\n\n      obj = obj[segments[i]];\n    }\n\n    return obj;\n  };\n}\n/*  */\n// can we use __proto__?\n\n\nvar hasProto = ('__proto__' in {}); // Browser environment sniffing\n\nvar inBrowser = typeof window !== 'undefined';\nvar inWeex = typeof WXEnvironment !== 'undefined' && !!WXEnvironment.platform;\nvar weexPlatform = inWeex && WXEnvironment.platform.toLowerCase();\nvar UA = inBrowser && window.navigator.userAgent.toLowerCase();\nvar isIE = UA && /msie|trident/.test(UA);\nvar isIE9 = UA && UA.indexOf('msie 9.0') > 0;\nvar isEdge = UA && UA.indexOf('edge/') > 0;\nvar isAndroid = UA && UA.indexOf('android') > 0 || weexPlatform === 'android';\nvar isIOS = UA && /iphone|ipad|ipod|ios/.test(UA) || weexPlatform === 'ios';\nvar isChrome = UA && /chrome\\/\\d+/.test(UA) && !isEdge;\nvar isPhantomJS = UA && /phantomjs/.test(UA);\nvar isFF = UA && UA.match(/firefox\\/(\\d+)/); // Firefox has a \"watch\" function on Object.prototype...\n\nvar nativeWatch = {}.watch;\nvar supportsPassive = false;\n\nif (inBrowser) {\n  try {\n    var opts = {};\n    Object.defineProperty(opts, 'passive', {\n      get: function get() {\n        /* istanbul ignore next */\n        supportsPassive = true;\n      }\n    }); // https://github.com/facebook/flow/issues/285\n\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n} // this needs to be lazy-evaled because vue may be required before\n// vue-server-renderer can set VUE_ENV\n\n\nvar _isServer;\n\nvar isServerRendering = function () {\n  if (_isServer === undefined) {\n    /* istanbul ignore if */\n    if (!inBrowser && !inWeex && typeof global !== 'undefined') {\n      // detect presence of vue-server-renderer and avoid\n      // Webpack shimming the process\n      _isServer = global['process'] && global['process'].env.VUE_ENV === 'server';\n    } else {\n      _isServer = false;\n    }\n  }\n\n  return _isServer;\n}; // detect devtools\n\n\nvar devtools = inBrowser && window.__VUE_DEVTOOLS_GLOBAL_HOOK__;\n/* istanbul ignore next */\n\nfunction isNative(Ctor) {\n  return typeof Ctor === 'function' && /native code/.test(Ctor.toString());\n}\n\nvar hasSymbol = typeof Symbol !== 'undefined' && isNative(Symbol) && typeof Reflect !== 'undefined' && isNative(Reflect.ownKeys);\n\nvar _Set;\n/* istanbul ignore if */\n// $flow-disable-line\n\n\nif (typeof Set !== 'undefined' && isNative(Set)) {\n  // use native Set when available.\n  _Set = Set;\n} else {\n  // a non-standard Set polyfill that only works with primitive keys.\n  _Set = /*@__PURE__*/function () {\n    function Set() {\n      this.set = Object.create(null);\n    }\n\n    Set.prototype.has = function has(key) {\n      return this.set[key] === true;\n    };\n\n    Set.prototype.add = function add(key) {\n      this.set[key] = true;\n    };\n\n    Set.prototype.clear = function clear() {\n      this.set = Object.create(null);\n    };\n\n    return Set;\n  }();\n}\n/*  */\n\n\nvar warn = noop;\nvar tip = noop;\nvar generateComponentTrace = noop; // work around flow check\n\nvar formatComponentName = noop;\n\nif (true) {\n  var hasConsole = typeof console !== 'undefined';\n  var classifyRE = /(?:^|[-_])(\\w)/g;\n\n  var classify = function (str) {\n    return str.replace(classifyRE, function (c) {\n      return c.toUpperCase();\n    }).replace(/[-_]/g, '');\n  };\n\n  warn = function (msg, vm) {\n    var trace = vm ? generateComponentTrace(vm) : '';\n\n    if (config.warnHandler) {\n      config.warnHandler.call(null, msg, vm, trace);\n    } else if (hasConsole && !config.silent) {\n      console.error(\"[Vue warn]: \" + msg + trace);\n    }\n  };\n\n  tip = function (msg, vm) {\n    if (hasConsole && !config.silent) {\n      console.warn(\"[Vue tip]: \" + msg + (vm ? generateComponentTrace(vm) : ''));\n    }\n  };\n\n  formatComponentName = function (vm, includeFile) {\n    if (vm.$root === vm) {\n      return '<Root>';\n    }\n\n    var options = typeof vm === 'function' && vm.cid != null ? vm.options : vm._isVue ? vm.$options || vm.constructor.options : vm;\n    var name = options.name || options._componentTag;\n    var file = options.__file;\n\n    if (!name && file) {\n      var match = file.match(/([^/\\\\]+)\\.vue$/);\n      name = match && match[1];\n    }\n\n    return (name ? \"<\" + classify(name) + \">\" : \"<Anonymous>\") + (file && includeFile !== false ? \" at \" + file : '');\n  };\n\n  var repeat = function (str, n) {\n    var res = '';\n\n    while (n) {\n      if (n % 2 === 1) {\n        res += str;\n      }\n\n      if (n > 1) {\n        str += str;\n      }\n\n      n >>= 1;\n    }\n\n    return res;\n  };\n\n  generateComponentTrace = function (vm) {\n    if (vm._isVue && vm.$parent) {\n      var tree = [];\n      var currentRecursiveSequence = 0;\n\n      while (vm) {\n        if (tree.length > 0) {\n          var last = tree[tree.length - 1];\n\n          if (last.constructor === vm.constructor) {\n            currentRecursiveSequence++;\n            vm = vm.$parent;\n            continue;\n          } else if (currentRecursiveSequence > 0) {\n            tree[tree.length - 1] = [last, currentRecursiveSequence];\n            currentRecursiveSequence = 0;\n          }\n        }\n\n        tree.push(vm);\n        vm = vm.$parent;\n      }\n\n      return '\\n\\nfound in\\n\\n' + tree.map(function (vm, i) {\n        return \"\" + (i === 0 ? '---> ' : repeat(' ', 5 + i * 2)) + (Array.isArray(vm) ? formatComponentName(vm[0]) + \"... (\" + vm[1] + \" recursive calls)\" : formatComponentName(vm));\n      }).join('\\n');\n    } else {\n      return \"\\n\\n(found in \" + formatComponentName(vm) + \")\";\n    }\n  };\n}\n/*  */\n\n\nvar uid = 0;\n/**\n * A dep is an observable that can have multiple\n * directives subscribing to it.\n */\n\nvar Dep = function Dep() {\n  this.id = uid++;\n  this.subs = [];\n};\n\nDep.prototype.addSub = function addSub(sub) {\n  this.subs.push(sub);\n};\n\nDep.prototype.removeSub = function removeSub(sub) {\n  remove(this.subs, sub);\n};\n\nDep.prototype.depend = function depend() {\n  if (Dep.target) {\n    Dep.target.addDep(this);\n  }\n};\n\nDep.prototype.notify = function notify() {\n  // stabilize the subscriber list first\n  var subs = this.subs.slice();\n\n  if ( true && !config.async) {\n    // subs aren't sorted in scheduler if not running async\n    // we need to sort them now to make sure they fire in correct\n    // order\n    subs.sort(function (a, b) {\n      return a.id - b.id;\n    });\n  }\n\n  for (var i = 0, l = subs.length; i < l; i++) {\n    subs[i].update();\n  }\n}; // The current target watcher being evaluated.\n// This is globally unique because only one watcher\n// can be evaluated at a time.\n\n\nDep.target = null;\nvar targetStack = [];\n\nfunction pushTarget(target) {\n  targetStack.push(target);\n  Dep.target = target;\n}\n\nfunction popTarget() {\n  targetStack.pop();\n  Dep.target = targetStack[targetStack.length - 1];\n}\n/*  */\n\n\nvar VNode = function VNode(tag, data, children, text, elm, context, componentOptions, asyncFactory) {\n  this.tag = tag;\n  this.data = data;\n  this.children = children;\n  this.text = text;\n  this.elm = elm;\n  this.ns = undefined;\n  this.context = context;\n  this.fnContext = undefined;\n  this.fnOptions = undefined;\n  this.fnScopeId = undefined;\n  this.key = data && data.key;\n  this.componentOptions = componentOptions;\n  this.componentInstance = undefined;\n  this.parent = undefined;\n  this.raw = false;\n  this.isStatic = false;\n  this.isRootInsert = true;\n  this.isComment = false;\n  this.isCloned = false;\n  this.isOnce = false;\n  this.asyncFactory = asyncFactory;\n  this.asyncMeta = undefined;\n  this.isAsyncPlaceholder = false;\n};\n\nvar prototypeAccessors = {\n  child: {\n    configurable: true\n  }\n}; // DEPRECATED: alias for componentInstance for backwards compat.\n\n/* istanbul ignore next */\n\nprototypeAccessors.child.get = function () {\n  return this.componentInstance;\n};\n\nObject.defineProperties(VNode.prototype, prototypeAccessors);\n\nvar createEmptyVNode = function (text) {\n  if (text === void 0) text = '';\n  var node = new VNode();\n  node.text = text;\n  node.isComment = true;\n  return node;\n};\n\nfunction createTextVNode(val) {\n  return new VNode(undefined, undefined, undefined, String(val));\n} // optimized shallow clone\n// used for static nodes and slot nodes because they may be reused across\n// multiple renders, cloning them avoids errors when DOM manipulations rely\n// on their elm reference.\n\n\nfunction cloneVNode(vnode) {\n  var cloned = new VNode(vnode.tag, vnode.data, // #7975\n  // clone children array to avoid mutating original in case of cloning\n  // a child.\n  vnode.children && vnode.children.slice(), vnode.text, vnode.elm, vnode.context, vnode.componentOptions, vnode.asyncFactory);\n  cloned.ns = vnode.ns;\n  cloned.isStatic = vnode.isStatic;\n  cloned.key = vnode.key;\n  cloned.isComment = vnode.isComment;\n  cloned.fnContext = vnode.fnContext;\n  cloned.fnOptions = vnode.fnOptions;\n  cloned.fnScopeId = vnode.fnScopeId;\n  cloned.asyncMeta = vnode.asyncMeta;\n  cloned.isCloned = true;\n  return cloned;\n}\n/*\n * not type checking this file because flow doesn't play well with\n * dynamically accessing methods on Array prototype\n */\n\n\nvar arrayProto = Array.prototype;\nvar arrayMethods = Object.create(arrayProto);\nvar methodsToPatch = ['push', 'pop', 'shift', 'unshift', 'splice', 'sort', 'reverse'];\n/**\n * Intercept mutating methods and emit events\n */\n\nmethodsToPatch.forEach(function (method) {\n  // cache original method\n  var original = arrayProto[method];\n  def(arrayMethods, method, function mutator() {\n    var args = [],\n        len = arguments.length;\n\n    while (len--) args[len] = arguments[len];\n\n    var result = original.apply(this, args);\n    var ob = this.__ob__;\n    var inserted;\n\n    switch (method) {\n      case 'push':\n      case 'unshift':\n        inserted = args;\n        break;\n\n      case 'splice':\n        inserted = args.slice(2);\n        break;\n    }\n\n    if (inserted) {\n      ob.observeArray(inserted);\n    } // notify change\n\n\n    ob.dep.notify();\n    return result;\n  });\n});\n/*  */\n\nvar arrayKeys = Object.getOwnPropertyNames(arrayMethods);\n/**\n * In some cases we may want to disable observation inside a component's\n * update computation.\n */\n\nvar shouldObserve = true;\n\nfunction toggleObserving(value) {\n  shouldObserve = value;\n}\n/**\n * Observer class that is attached to each observed\n * object. Once attached, the observer converts the target\n * object's property keys into getter/setters that\n * collect dependencies and dispatch updates.\n */\n\n\nvar Observer = function Observer(value) {\n  this.value = value;\n  this.dep = new Dep();\n  this.vmCount = 0;\n  def(value, '__ob__', this);\n\n  if (Array.isArray(value)) {\n    if (hasProto) {\n      protoAugment(value, arrayMethods);\n    } else {\n      copyAugment(value, arrayMethods, arrayKeys);\n    }\n\n    this.observeArray(value);\n  } else {\n    this.walk(value);\n  }\n};\n/**\n * Walk through all properties and convert them into\n * getter/setters. This method should only be called when\n * value type is Object.\n */\n\n\nObserver.prototype.walk = function walk(obj) {\n  var keys = Object.keys(obj);\n\n  for (var i = 0; i < keys.length; i++) {\n    defineReactive$$1(obj, keys[i]);\n  }\n};\n/**\n * Observe a list of Array items.\n */\n\n\nObserver.prototype.observeArray = function observeArray(items) {\n  for (var i = 0, l = items.length; i < l; i++) {\n    observe(items[i]);\n  }\n}; // helpers\n\n/**\n * Augment a target Object or Array by intercepting\n * the prototype chain using __proto__\n */\n\n\nfunction protoAugment(target, src) {\n  /* eslint-disable no-proto */\n  target.__proto__ = src;\n  /* eslint-enable no-proto */\n}\n/**\n * Augment a target Object or Array by defining\n * hidden properties.\n */\n\n/* istanbul ignore next */\n\n\nfunction copyAugment(target, src, keys) {\n  for (var i = 0, l = keys.length; i < l; i++) {\n    var key = keys[i];\n    def(target, key, src[key]);\n  }\n}\n/**\n * Attempt to create an observer instance for a value,\n * returns the new observer if successfully observed,\n * or the existing observer if the value already has one.\n */\n\n\nfunction observe(value, asRootData) {\n  if (!isObject(value) || value instanceof VNode) {\n    return;\n  }\n\n  var ob;\n\n  if (hasOwn(value, '__ob__') && value.__ob__ instanceof Observer) {\n    ob = value.__ob__;\n  } else if (shouldObserve && !isServerRendering() && (Array.isArray(value) || isPlainObject(value)) && Object.isExtensible(value) && !value._isVue) {\n    ob = new Observer(value);\n  }\n\n  if (asRootData && ob) {\n    ob.vmCount++;\n  }\n\n  return ob;\n}\n/**\n * Define a reactive property on an Object.\n */\n\n\nfunction defineReactive$$1(obj, key, val, customSetter, shallow) {\n  var dep = new Dep();\n  var property = Object.getOwnPropertyDescriptor(obj, key);\n\n  if (property && property.configurable === false) {\n    return;\n  } // cater for pre-defined getter/setters\n\n\n  var getter = property && property.get;\n  var setter = property && property.set;\n\n  if ((!getter || setter) && arguments.length === 2) {\n    val = obj[key];\n  }\n\n  var childOb = !shallow && observe(val);\n  Object.defineProperty(obj, key, {\n    enumerable: true,\n    configurable: true,\n    get: function reactiveGetter() {\n      var value = getter ? getter.call(obj) : val;\n\n      if (Dep.target) {\n        dep.depend();\n\n        if (childOb) {\n          childOb.dep.depend();\n\n          if (Array.isArray(value)) {\n            dependArray(value);\n          }\n        }\n      }\n\n      return value;\n    },\n    set: function reactiveSetter(newVal) {\n      var value = getter ? getter.call(obj) : val;\n      /* eslint-disable no-self-compare */\n\n      if (newVal === value || newVal !== newVal && value !== value) {\n        return;\n      }\n      /* eslint-enable no-self-compare */\n\n\n      if ( true && customSetter) {\n        customSetter();\n      } // #7981: for accessor properties without setter\n\n\n      if (getter && !setter) {\n        return;\n      }\n\n      if (setter) {\n        setter.call(obj, newVal);\n      } else {\n        val = newVal;\n      }\n\n      childOb = !shallow && observe(newVal);\n      dep.notify();\n    }\n  });\n}\n/**\n * Set a property on an object. Adds the new property and\n * triggers change notification if the property doesn't\n * already exist.\n */\n\n\nfunction set(target, key, val) {\n  if ( true && (isUndef(target) || isPrimitive(target))) {\n    warn(\"Cannot set reactive property on undefined, null, or primitive value: \" + target);\n  }\n\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val;\n  }\n\n  if (key in target && !(key in Object.prototype)) {\n    target[key] = val;\n    return val;\n  }\n\n  var ob = target.__ob__;\n\n  if (target._isVue || ob && ob.vmCount) {\n     true && warn('Avoid adding reactive properties to a Vue instance or its root $data ' + 'at runtime - declare it upfront in the data option.');\n    return val;\n  }\n\n  if (!ob) {\n    target[key] = val;\n    return val;\n  }\n\n  defineReactive$$1(ob.value, key, val);\n  ob.dep.notify();\n  return val;\n}\n/**\n * Delete a property and trigger change if necessary.\n */\n\n\nfunction del(target, key) {\n  if ( true && (isUndef(target) || isPrimitive(target))) {\n    warn(\"Cannot delete reactive property on undefined, null, or primitive value: \" + target);\n  }\n\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.splice(key, 1);\n    return;\n  }\n\n  var ob = target.__ob__;\n\n  if (target._isVue || ob && ob.vmCount) {\n     true && warn('Avoid deleting properties on a Vue instance or its root $data ' + '- just set it to null.');\n    return;\n  }\n\n  if (!hasOwn(target, key)) {\n    return;\n  }\n\n  delete target[key];\n\n  if (!ob) {\n    return;\n  }\n\n  ob.dep.notify();\n}\n/**\n * Collect dependencies on array elements when the array is touched, since\n * we cannot intercept array element access like property getters.\n */\n\n\nfunction dependArray(value) {\n  for (var e = void 0, i = 0, l = value.length; i < l; i++) {\n    e = value[i];\n    e && e.__ob__ && e.__ob__.dep.depend();\n\n    if (Array.isArray(e)) {\n      dependArray(e);\n    }\n  }\n}\n/*  */\n\n/**\n * Option overwriting strategies are functions that handle\n * how to merge a parent option value and a child option\n * value into the final value.\n */\n\n\nvar strats = config.optionMergeStrategies;\n/**\n * Options with restrictions\n */\n\nif (true) {\n  strats.el = strats.propsData = function (parent, child, vm, key) {\n    if (!vm) {\n      warn(\"option \\\"\" + key + \"\\\" can only be used during instance \" + 'creation with the `new` keyword.');\n    }\n\n    return defaultStrat(parent, child);\n  };\n}\n/**\n * Helper that recursively merges two data objects together.\n */\n\n\nfunction mergeData(to, from) {\n  if (!from) {\n    return to;\n  }\n\n  var key, toVal, fromVal;\n  var keys = hasSymbol ? Reflect.ownKeys(from) : Object.keys(from);\n\n  for (var i = 0; i < keys.length; i++) {\n    key = keys[i]; // in case the object is already observed...\n\n    if (key === '__ob__') {\n      continue;\n    }\n\n    toVal = to[key];\n    fromVal = from[key];\n\n    if (!hasOwn(to, key)) {\n      set(to, key, fromVal);\n    } else if (toVal !== fromVal && isPlainObject(toVal) && isPlainObject(fromVal)) {\n      mergeData(toVal, fromVal);\n    }\n  }\n\n  return to;\n}\n/**\n * Data\n */\n\n\nfunction mergeDataOrFn(parentVal, childVal, vm) {\n  if (!vm) {\n    // in a Vue.extend merge, both should be functions\n    if (!childVal) {\n      return parentVal;\n    }\n\n    if (!parentVal) {\n      return childVal;\n    } // when parentVal & childVal are both present,\n    // we need to return a function that returns the\n    // merged result of both functions... no need to\n    // check if parentVal is a function here because\n    // it has to be a function to pass previous merges.\n\n\n    return function mergedDataFn() {\n      return mergeData(typeof childVal === 'function' ? childVal.call(this, this) : childVal, typeof parentVal === 'function' ? parentVal.call(this, this) : parentVal);\n    };\n  } else {\n    return function mergedInstanceDataFn() {\n      // instance merge\n      var instanceData = typeof childVal === 'function' ? childVal.call(vm, vm) : childVal;\n      var defaultData = typeof parentVal === 'function' ? parentVal.call(vm, vm) : parentVal;\n\n      if (instanceData) {\n        return mergeData(instanceData, defaultData);\n      } else {\n        return defaultData;\n      }\n    };\n  }\n}\n\nstrats.data = function (parentVal, childVal, vm) {\n  if (!vm) {\n    if (childVal && typeof childVal !== 'function') {\n       true && warn('The \"data\" option should be a function ' + 'that returns a per-instance value in component ' + 'definitions.', vm);\n      return parentVal;\n    }\n\n    return mergeDataOrFn(parentVal, childVal);\n  }\n\n  return mergeDataOrFn(parentVal, childVal, vm);\n};\n/**\n * Hooks and props are merged as arrays.\n */\n\n\nfunction mergeHook(parentVal, childVal) {\n  var res = childVal ? parentVal ? parentVal.concat(childVal) : Array.isArray(childVal) ? childVal : [childVal] : parentVal;\n  return res ? dedupeHooks(res) : res;\n}\n\nfunction dedupeHooks(hooks) {\n  var res = [];\n\n  for (var i = 0; i < hooks.length; i++) {\n    if (res.indexOf(hooks[i]) === -1) {\n      res.push(hooks[i]);\n    }\n  }\n\n  return res;\n}\n\nLIFECYCLE_HOOKS.forEach(function (hook) {\n  strats[hook] = mergeHook;\n});\n/**\n * Assets\n *\n * When a vm is present (instance creation), we need to do\n * a three-way merge between constructor options, instance\n * options and parent options.\n */\n\nfunction mergeAssets(parentVal, childVal, vm, key) {\n  var res = Object.create(parentVal || null);\n\n  if (childVal) {\n     true && assertObjectType(key, childVal, vm);\n    return extend(res, childVal);\n  } else {\n    return res;\n  }\n}\n\nASSET_TYPES.forEach(function (type) {\n  strats[type + 's'] = mergeAssets;\n});\n/**\n * Watchers.\n *\n * Watchers hashes should not overwrite one\n * another, so we merge them as arrays.\n */\n\nstrats.watch = function (parentVal, childVal, vm, key) {\n  // work around Firefox's Object.prototype.watch...\n  if (parentVal === nativeWatch) {\n    parentVal = undefined;\n  }\n\n  if (childVal === nativeWatch) {\n    childVal = undefined;\n  }\n  /* istanbul ignore if */\n\n\n  if (!childVal) {\n    return Object.create(parentVal || null);\n  }\n\n  if (true) {\n    assertObjectType(key, childVal, vm);\n  }\n\n  if (!parentVal) {\n    return childVal;\n  }\n\n  var ret = {};\n  extend(ret, parentVal);\n\n  for (var key$1 in childVal) {\n    var parent = ret[key$1];\n    var child = childVal[key$1];\n\n    if (parent && !Array.isArray(parent)) {\n      parent = [parent];\n    }\n\n    ret[key$1] = parent ? parent.concat(child) : Array.isArray(child) ? child : [child];\n  }\n\n  return ret;\n};\n/**\n * Other object hashes.\n */\n\n\nstrats.props = strats.methods = strats.inject = strats.computed = function (parentVal, childVal, vm, key) {\n  if (childVal && \"development\" !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n\n  if (!parentVal) {\n    return childVal;\n  }\n\n  var ret = Object.create(null);\n  extend(ret, parentVal);\n\n  if (childVal) {\n    extend(ret, childVal);\n  }\n\n  return ret;\n};\n\nstrats.provide = mergeDataOrFn;\n/**\n * Default strategy.\n */\n\nvar defaultStrat = function (parentVal, childVal) {\n  return childVal === undefined ? parentVal : childVal;\n};\n/**\n * Validate component names\n */\n\n\nfunction checkComponents(options) {\n  for (var key in options.components) {\n    validateComponentName(key);\n  }\n}\n\nfunction validateComponentName(name) {\n  if (!new RegExp(\"^[a-zA-Z][\\\\-\\\\.0-9_\" + unicodeRegExp.source + \"]*$\").test(name)) {\n    warn('Invalid component name: \"' + name + '\". Component names ' + 'should conform to valid custom element name in html5 specification.');\n  }\n\n  if (isBuiltInTag(name) || config.isReservedTag(name)) {\n    warn('Do not use built-in or reserved HTML elements as component ' + 'id: ' + name);\n  }\n}\n/**\n * Ensure all props option syntax are normalized into the\n * Object-based format.\n */\n\n\nfunction normalizeProps(options, vm) {\n  var props = options.props;\n\n  if (!props) {\n    return;\n  }\n\n  var res = {};\n  var i, val, name;\n\n  if (Array.isArray(props)) {\n    i = props.length;\n\n    while (i--) {\n      val = props[i];\n\n      if (typeof val === 'string') {\n        name = camelize(val);\n        res[name] = {\n          type: null\n        };\n      } else if (true) {\n        warn('props must be strings when using array syntax.');\n      }\n    }\n  } else if (isPlainObject(props)) {\n    for (var key in props) {\n      val = props[key];\n      name = camelize(key);\n      res[name] = isPlainObject(val) ? val : {\n        type: val\n      };\n    }\n  } else if (true) {\n    warn(\"Invalid value for option \\\"props\\\": expected an Array or an Object, \" + \"but got \" + toRawType(props) + \".\", vm);\n  }\n\n  options.props = res;\n}\n/**\n * Normalize all injections into Object-based format\n */\n\n\nfunction normalizeInject(options, vm) {\n  var inject = options.inject;\n\n  if (!inject) {\n    return;\n  }\n\n  var normalized = options.inject = {};\n\n  if (Array.isArray(inject)) {\n    for (var i = 0; i < inject.length; i++) {\n      normalized[inject[i]] = {\n        from: inject[i]\n      };\n    }\n  } else if (isPlainObject(inject)) {\n    for (var key in inject) {\n      var val = inject[key];\n      normalized[key] = isPlainObject(val) ? extend({\n        from: key\n      }, val) : {\n        from: val\n      };\n    }\n  } else if (true) {\n    warn(\"Invalid value for option \\\"inject\\\": expected an Array or an Object, \" + \"but got \" + toRawType(inject) + \".\", vm);\n  }\n}\n/**\n * Normalize raw function directives into object format.\n */\n\n\nfunction normalizeDirectives(options) {\n  var dirs = options.directives;\n\n  if (dirs) {\n    for (var key in dirs) {\n      var def$$1 = dirs[key];\n\n      if (typeof def$$1 === 'function') {\n        dirs[key] = {\n          bind: def$$1,\n          update: def$$1\n        };\n      }\n    }\n  }\n}\n\nfunction assertObjectType(name, value, vm) {\n  if (!isPlainObject(value)) {\n    warn(\"Invalid value for option \\\"\" + name + \"\\\": expected an Object, \" + \"but got \" + toRawType(value) + \".\", vm);\n  }\n}\n/**\n * Merge two option objects into a new one.\n * Core utility used in both instantiation and inheritance.\n */\n\n\nfunction mergeOptions(parent, child, vm) {\n  if (true) {\n    checkComponents(child);\n  }\n\n  if (typeof child === 'function') {\n    child = child.options;\n  }\n\n  normalizeProps(child, vm);\n  normalizeInject(child, vm);\n  normalizeDirectives(child); // Apply extends and mixins on the child options,\n  // but only if it is a raw options object that isn't\n  // the result of another mergeOptions call.\n  // Only merged options has the _base property.\n\n  if (!child._base) {\n    if (child.extends) {\n      parent = mergeOptions(parent, child.extends, vm);\n    }\n\n    if (child.mixins) {\n      for (var i = 0, l = child.mixins.length; i < l; i++) {\n        parent = mergeOptions(parent, child.mixins[i], vm);\n      }\n    }\n  }\n\n  var options = {};\n  var key;\n\n  for (key in parent) {\n    mergeField(key);\n  }\n\n  for (key in child) {\n    if (!hasOwn(parent, key)) {\n      mergeField(key);\n    }\n  }\n\n  function mergeField(key) {\n    var strat = strats[key] || defaultStrat;\n    options[key] = strat(parent[key], child[key], vm, key);\n  }\n\n  return options;\n}\n/**\n * Resolve an asset.\n * This function is used because child instances need access\n * to assets defined in its ancestor chain.\n */\n\n\nfunction resolveAsset(options, type, id, warnMissing) {\n  /* istanbul ignore if */\n  if (typeof id !== 'string') {\n    return;\n  }\n\n  var assets = options[type]; // check local registration variations first\n\n  if (hasOwn(assets, id)) {\n    return assets[id];\n  }\n\n  var camelizedId = camelize(id);\n\n  if (hasOwn(assets, camelizedId)) {\n    return assets[camelizedId];\n  }\n\n  var PascalCaseId = capitalize(camelizedId);\n\n  if (hasOwn(assets, PascalCaseId)) {\n    return assets[PascalCaseId];\n  } // fallback to prototype chain\n\n\n  var res = assets[id] || assets[camelizedId] || assets[PascalCaseId];\n\n  if ( true && warnMissing && !res) {\n    warn('Failed to resolve ' + type.slice(0, -1) + ': ' + id, options);\n  }\n\n  return res;\n}\n/*  */\n\n\nfunction validateProp(key, propOptions, propsData, vm) {\n  var prop = propOptions[key];\n  var absent = !hasOwn(propsData, key);\n  var value = propsData[key]; // boolean casting\n\n  var booleanIndex = getTypeIndex(Boolean, prop.type);\n\n  if (booleanIndex > -1) {\n    if (absent && !hasOwn(prop, 'default')) {\n      value = false;\n    } else if (value === '' || value === hyphenate(key)) {\n      // only cast empty string / same name to boolean if\n      // boolean has higher priority\n      var stringIndex = getTypeIndex(String, prop.type);\n\n      if (stringIndex < 0 || booleanIndex < stringIndex) {\n        value = true;\n      }\n    }\n  } // check default value\n\n\n  if (value === undefined) {\n    value = getPropDefaultValue(vm, prop, key); // since the default value is a fresh copy,\n    // make sure to observe it.\n\n    var prevShouldObserve = shouldObserve;\n    toggleObserving(true);\n    observe(value);\n    toggleObserving(prevShouldObserve);\n  }\n\n  if (true) {\n    assertProp(prop, key, value, vm, absent);\n  }\n\n  return value;\n}\n/**\n * Get the default value of a prop.\n */\n\n\nfunction getPropDefaultValue(vm, prop, key) {\n  // no default, return undefined\n  if (!hasOwn(prop, 'default')) {\n    return undefined;\n  }\n\n  var def = prop.default; // warn against non-factory defaults for Object & Array\n\n  if ( true && isObject(def)) {\n    warn('Invalid default value for prop \"' + key + '\": ' + 'Props with type Object/Array must use a factory function ' + 'to return the default value.', vm);\n  } // the raw prop value was also undefined from previous render,\n  // return previous default value to avoid unnecessary watcher trigger\n\n\n  if (vm && vm.$options.propsData && vm.$options.propsData[key] === undefined && vm._props[key] !== undefined) {\n    return vm._props[key];\n  } // call factory function for non-Function types\n  // a value is Function if its prototype is function even across different execution context\n\n\n  return typeof def === 'function' && getType(prop.type) !== 'Function' ? def.call(vm) : def;\n}\n/**\n * Assert whether a prop is valid.\n */\n\n\nfunction assertProp(prop, name, value, vm, absent) {\n  if (prop.required && absent) {\n    warn('Missing required prop: \"' + name + '\"', vm);\n    return;\n  }\n\n  if (value == null && !prop.required) {\n    return;\n  }\n\n  var type = prop.type;\n  var valid = !type || type === true;\n  var expectedTypes = [];\n\n  if (type) {\n    if (!Array.isArray(type)) {\n      type = [type];\n    }\n\n    for (var i = 0; i < type.length && !valid; i++) {\n      var assertedType = assertType(value, type[i], vm);\n      expectedTypes.push(assertedType.expectedType || '');\n      valid = assertedType.valid;\n    }\n  }\n\n  var haveExpectedTypes = expectedTypes.some(function (t) {\n    return t;\n  });\n\n  if (!valid && haveExpectedTypes) {\n    warn(getInvalidTypeMessage(name, value, expectedTypes), vm);\n    return;\n  }\n\n  var validator = prop.validator;\n\n  if (validator) {\n    if (!validator(value)) {\n      warn('Invalid prop: custom validator check failed for prop \"' + name + '\".', vm);\n    }\n  }\n}\n\nvar simpleCheckRE = /^(String|Number|Boolean|Function|Symbol|BigInt)$/;\n\nfunction assertType(value, type, vm) {\n  var valid;\n  var expectedType = getType(type);\n\n  if (simpleCheckRE.test(expectedType)) {\n    var t = typeof value;\n    valid = t === expectedType.toLowerCase(); // for primitive wrapper objects\n\n    if (!valid && t === 'object') {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === 'Object') {\n    valid = isPlainObject(value);\n  } else if (expectedType === 'Array') {\n    valid = Array.isArray(value);\n  } else {\n    try {\n      valid = value instanceof type;\n    } catch (e) {\n      warn('Invalid prop type: \"' + String(type) + '\" is not a constructor', vm);\n      valid = false;\n    }\n  }\n\n  return {\n    valid: valid,\n    expectedType: expectedType\n  };\n}\n\nvar functionTypeCheckRE = /^\\s*function (\\w+)/;\n/**\n * Use function string name to check built-in types,\n * because a simple equality check will fail when running\n * across different vms / iframes.\n */\n\nfunction getType(fn) {\n  var match = fn && fn.toString().match(functionTypeCheckRE);\n  return match ? match[1] : '';\n}\n\nfunction isSameType(a, b) {\n  return getType(a) === getType(b);\n}\n\nfunction getTypeIndex(type, expectedTypes) {\n  if (!Array.isArray(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1;\n  }\n\n  for (var i = 0, len = expectedTypes.length; i < len; i++) {\n    if (isSameType(expectedTypes[i], type)) {\n      return i;\n    }\n  }\n\n  return -1;\n}\n\nfunction getInvalidTypeMessage(name, value, expectedTypes) {\n  var message = \"Invalid prop: type check failed for prop \\\"\" + name + \"\\\".\" + \" Expected \" + expectedTypes.map(capitalize).join(', ');\n  var expectedType = expectedTypes[0];\n  var receivedType = toRawType(value); // check if we need to specify expected value\n\n  if (expectedTypes.length === 1 && isExplicable(expectedType) && isExplicable(typeof value) && !isBoolean(expectedType, receivedType)) {\n    message += \" with value \" + styleValue(value, expectedType);\n  }\n\n  message += \", got \" + receivedType + \" \"; // check if we need to specify received value\n\n  if (isExplicable(receivedType)) {\n    message += \"with value \" + styleValue(value, receivedType) + \".\";\n  }\n\n  return message;\n}\n\nfunction styleValue(value, type) {\n  if (type === 'String') {\n    return \"\\\"\" + value + \"\\\"\";\n  } else if (type === 'Number') {\n    return \"\" + Number(value);\n  } else {\n    return \"\" + value;\n  }\n}\n\nvar EXPLICABLE_TYPES = ['string', 'number', 'boolean'];\n\nfunction isExplicable(value) {\n  return EXPLICABLE_TYPES.some(function (elem) {\n    return value.toLowerCase() === elem;\n  });\n}\n\nfunction isBoolean() {\n  var args = [],\n      len = arguments.length;\n\n  while (len--) args[len] = arguments[len];\n\n  return args.some(function (elem) {\n    return elem.toLowerCase() === 'boolean';\n  });\n}\n/*  */\n\n\nfunction handleError(err, vm, info) {\n  // Deactivate deps tracking while processing error handler to avoid possible infinite rendering.\n  // See: https://github.com/vuejs/vuex/issues/1505\n  pushTarget();\n\n  try {\n    if (vm) {\n      var cur = vm;\n\n      while (cur = cur.$parent) {\n        var hooks = cur.$options.errorCaptured;\n\n        if (hooks) {\n          for (var i = 0; i < hooks.length; i++) {\n            try {\n              var capture = hooks[i].call(cur, err, vm, info) === false;\n\n              if (capture) {\n                return;\n              }\n            } catch (e) {\n              globalHandleError(e, cur, 'errorCaptured hook');\n            }\n          }\n        }\n      }\n    }\n\n    globalHandleError(err, vm, info);\n  } finally {\n    popTarget();\n  }\n}\n\nfunction invokeWithErrorHandling(handler, context, args, vm, info) {\n  var res;\n\n  try {\n    res = args ? handler.apply(context, args) : handler.call(context);\n\n    if (res && !res._isVue && isPromise(res) && !res._handled) {\n      res.catch(function (e) {\n        return handleError(e, vm, info + \" (Promise/async)\");\n      }); // issue #9511\n      // avoid catch triggering multiple times when nested calls\n\n      res._handled = true;\n    }\n  } catch (e) {\n    handleError(e, vm, info);\n  }\n\n  return res;\n}\n\nfunction globalHandleError(err, vm, info) {\n  if (config.errorHandler) {\n    try {\n      return config.errorHandler.call(null, err, vm, info);\n    } catch (e) {\n      // if the user intentionally throws the original error in the handler,\n      // do not log it twice\n      if (e !== err) {\n        logError(e, null, 'config.errorHandler');\n      }\n    }\n  }\n\n  logError(err, vm, info);\n}\n\nfunction logError(err, vm, info) {\n  if (true) {\n    warn(\"Error in \" + info + \": \\\"\" + err.toString() + \"\\\"\", vm);\n  }\n  /* istanbul ignore else */\n\n\n  if ((inBrowser || inWeex) && typeof console !== 'undefined') {\n    console.error(err);\n  } else {\n    throw err;\n  }\n}\n/*  */\n\n\nvar isUsingMicroTask = false;\nvar callbacks = [];\nvar pending = false;\n\nfunction flushCallbacks() {\n  pending = false;\n  var copies = callbacks.slice(0);\n  callbacks.length = 0;\n\n  for (var i = 0; i < copies.length; i++) {\n    copies[i]();\n  }\n} // Here we have async deferring wrappers using microtasks.\n// In 2.5 we used (macro) tasks (in combination with microtasks).\n// However, it has subtle problems when state is changed right before repaint\n// (e.g. #6813, out-in transitions).\n// Also, using (macro) tasks in event handler would cause some weird behaviors\n// that cannot be circumvented (e.g. #7109, #7153, #7546, #7834, #8109).\n// So we now use microtasks everywhere, again.\n// A major drawback of this tradeoff is that there are some scenarios\n// where microtasks have too high a priority and fire in between supposedly\n// sequential events (e.g. #4521, #6690, which have workarounds)\n// or even between bubbling of the same event (#6566).\n\n\nvar timerFunc; // The nextTick behavior leverages the microtask queue, which can be accessed\n// via either native Promise.then or MutationObserver.\n// MutationObserver has wider support, however it is seriously bugged in\n// UIWebView in iOS >= 9.3.3 when triggered in touch event handlers. It\n// completely stops working after triggering a few times... so, if native\n// Promise is available, we will use it:\n\n/* istanbul ignore next, $flow-disable-line */\n\nif (typeof Promise !== 'undefined' && isNative(Promise)) {\n  var p = Promise.resolve();\n\n  timerFunc = function () {\n    p.then(flushCallbacks); // In problematic UIWebViews, Promise.then doesn't completely break, but\n    // it can get stuck in a weird state where callbacks are pushed into the\n    // microtask queue but the queue isn't being flushed, until the browser\n    // needs to do some other work, e.g. handle a timer. Therefore we can\n    // \"force\" the microtask queue to be flushed by adding an empty timer.\n\n    if (isIOS) {\n      setTimeout(noop);\n    }\n  };\n\n  isUsingMicroTask = true;\n} else if (!isIE && typeof MutationObserver !== 'undefined' && (isNative(MutationObserver) || // PhantomJS and iOS 7.x\nMutationObserver.toString() === '[object MutationObserverConstructor]')) {\n  // Use MutationObserver where native Promise is not available,\n  // e.g. PhantomJS, iOS7, Android 4.4\n  // (#6466 MutationObserver is unreliable in IE11)\n  var counter = 1;\n  var observer = new MutationObserver(flushCallbacks);\n  var textNode = document.createTextNode(String(counter));\n  observer.observe(textNode, {\n    characterData: true\n  });\n\n  timerFunc = function () {\n    counter = (counter + 1) % 2;\n    textNode.data = String(counter);\n  };\n\n  isUsingMicroTask = true;\n} else if (typeof setImmediate !== 'undefined' && isNative(setImmediate)) {\n  // Fallback to setImmediate.\n  // Technically it leverages the (macro) task queue,\n  // but it is still a better choice than setTimeout.\n  timerFunc = function () {\n    setImmediate(flushCallbacks);\n  };\n} else {\n  // Fallback to setTimeout.\n  timerFunc = function () {\n    setTimeout(flushCallbacks, 0);\n  };\n}\n\nfunction nextTick(cb, ctx) {\n  var _resolve;\n\n  callbacks.push(function () {\n    if (cb) {\n      try {\n        cb.call(ctx);\n      } catch (e) {\n        handleError(e, ctx, 'nextTick');\n      }\n    } else if (_resolve) {\n      _resolve(ctx);\n    }\n  });\n\n  if (!pending) {\n    pending = true;\n    timerFunc();\n  } // $flow-disable-line\n\n\n  if (!cb && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve) {\n      _resolve = resolve;\n    });\n  }\n}\n/*  */\n\n\nvar mark;\nvar measure;\n\nif (true) {\n  var perf = inBrowser && window.performance;\n  /* istanbul ignore if */\n\n  if (perf && perf.mark && perf.measure && perf.clearMarks && perf.clearMeasures) {\n    mark = function (tag) {\n      return perf.mark(tag);\n    };\n\n    measure = function (name, startTag, endTag) {\n      perf.measure(name, startTag, endTag);\n      perf.clearMarks(startTag);\n      perf.clearMarks(endTag); // perf.clearMeasures(name)\n    };\n  }\n}\n/* not type checking this file because flow doesn't play well with Proxy */\n\n\nvar initProxy;\n\nif (true) {\n  var allowedGlobals = makeMap('Infinity,undefined,NaN,isFinite,isNaN,' + 'parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,' + 'Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,' + 'require' // for Webpack/Browserify\n  );\n\n  var warnNonPresent = function (target, key) {\n    warn(\"Property or method \\\"\" + key + \"\\\" is not defined on the instance but \" + 'referenced during render. Make sure that this property is reactive, ' + 'either in the data option, or for class-based components, by ' + 'initializing the property. ' + 'See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.', target);\n  };\n\n  var warnReservedPrefix = function (target, key) {\n    warn(\"Property \\\"\" + key + \"\\\" must be accessed with \\\"$data.\" + key + \"\\\" because \" + 'properties starting with \"$\" or \"_\" are not proxied in the Vue instance to ' + 'prevent conflicts with Vue internals. ' + 'See: https://vuejs.org/v2/api/#data', target);\n  };\n\n  var hasProxy = typeof Proxy !== 'undefined' && isNative(Proxy);\n\n  if (hasProxy) {\n    var isBuiltInModifier = makeMap('stop,prevent,self,ctrl,shift,alt,meta,exact');\n    config.keyCodes = new Proxy(config.keyCodes, {\n      set: function set(target, key, value) {\n        if (isBuiltInModifier(key)) {\n          warn(\"Avoid overwriting built-in modifier in config.keyCodes: .\" + key);\n          return false;\n        } else {\n          target[key] = value;\n          return true;\n        }\n      }\n    });\n  }\n\n  var hasHandler = {\n    has: function has(target, key) {\n      var has = (key in target);\n      var isAllowed = allowedGlobals(key) || typeof key === 'string' && key.charAt(0) === '_' && !(key in target.$data);\n\n      if (!has && !isAllowed) {\n        if (key in target.$data) {\n          warnReservedPrefix(target, key);\n        } else {\n          warnNonPresent(target, key);\n        }\n      }\n\n      return has || !isAllowed;\n    }\n  };\n  var getHandler = {\n    get: function get(target, key) {\n      if (typeof key === 'string' && !(key in target)) {\n        if (key in target.$data) {\n          warnReservedPrefix(target, key);\n        } else {\n          warnNonPresent(target, key);\n        }\n      }\n\n      return target[key];\n    }\n  };\n\n  initProxy = function initProxy(vm) {\n    if (hasProxy) {\n      // determine which proxy handler to use\n      var options = vm.$options;\n      var handlers = options.render && options.render._withStripped ? getHandler : hasHandler;\n      vm._renderProxy = new Proxy(vm, handlers);\n    } else {\n      vm._renderProxy = vm;\n    }\n  };\n}\n/*  */\n\n\nvar seenObjects = new _Set();\n/**\n * Recursively traverse an object to evoke all converted\n * getters, so that every nested property inside the object\n * is collected as a \"deep\" dependency.\n */\n\nfunction traverse(val) {\n  _traverse(val, seenObjects);\n\n  seenObjects.clear();\n}\n\nfunction _traverse(val, seen) {\n  var i, keys;\n  var isA = Array.isArray(val);\n\n  if (!isA && !isObject(val) || Object.isFrozen(val) || val instanceof VNode) {\n    return;\n  }\n\n  if (val.__ob__) {\n    var depId = val.__ob__.dep.id;\n\n    if (seen.has(depId)) {\n      return;\n    }\n\n    seen.add(depId);\n  }\n\n  if (isA) {\n    i = val.length;\n\n    while (i--) {\n      _traverse(val[i], seen);\n    }\n  } else {\n    keys = Object.keys(val);\n    i = keys.length;\n\n    while (i--) {\n      _traverse(val[keys[i]], seen);\n    }\n  }\n}\n/*  */\n\n\nvar normalizeEvent = cached(function (name) {\n  var passive = name.charAt(0) === '&';\n  name = passive ? name.slice(1) : name;\n  var once$$1 = name.charAt(0) === '~'; // Prefixed last, checked first\n\n  name = once$$1 ? name.slice(1) : name;\n  var capture = name.charAt(0) === '!';\n  name = capture ? name.slice(1) : name;\n  return {\n    name: name,\n    once: once$$1,\n    capture: capture,\n    passive: passive\n  };\n});\n\nfunction createFnInvoker(fns, vm) {\n  function invoker() {\n    var arguments$1 = arguments;\n    var fns = invoker.fns;\n\n    if (Array.isArray(fns)) {\n      var cloned = fns.slice();\n\n      for (var i = 0; i < cloned.length; i++) {\n        invokeWithErrorHandling(cloned[i], null, arguments$1, vm, \"v-on handler\");\n      }\n    } else {\n      // return handler return value for single handlers\n      return invokeWithErrorHandling(fns, null, arguments, vm, \"v-on handler\");\n    }\n  }\n\n  invoker.fns = fns;\n  return invoker;\n}\n\nfunction updateListeners(on, oldOn, add, remove$$1, createOnceHandler, vm) {\n  var name, def$$1, cur, old, event;\n\n  for (name in on) {\n    def$$1 = cur = on[name];\n    old = oldOn[name];\n    event = normalizeEvent(name);\n\n    if (isUndef(cur)) {\n       true && warn(\"Invalid handler for event \\\"\" + event.name + \"\\\": got \" + String(cur), vm);\n    } else if (isUndef(old)) {\n      if (isUndef(cur.fns)) {\n        cur = on[name] = createFnInvoker(cur, vm);\n      }\n\n      if (isTrue(event.once)) {\n        cur = on[name] = createOnceHandler(event.name, cur, event.capture);\n      }\n\n      add(event.name, cur, event.capture, event.passive, event.params);\n    } else if (cur !== old) {\n      old.fns = cur;\n      on[name] = old;\n    }\n  }\n\n  for (name in oldOn) {\n    if (isUndef(on[name])) {\n      event = normalizeEvent(name);\n      remove$$1(event.name, oldOn[name], event.capture);\n    }\n  }\n}\n/*  */\n\n\nfunction mergeVNodeHook(def, hookKey, hook) {\n  if (def instanceof VNode) {\n    def = def.data.hook || (def.data.hook = {});\n  }\n\n  var invoker;\n  var oldHook = def[hookKey];\n\n  function wrappedHook() {\n    hook.apply(this, arguments); // important: remove merged hook to ensure it's called only once\n    // and prevent memory leak\n\n    remove(invoker.fns, wrappedHook);\n  }\n\n  if (isUndef(oldHook)) {\n    // no existing hook\n    invoker = createFnInvoker([wrappedHook]);\n  } else {\n    /* istanbul ignore if */\n    if (isDef(oldHook.fns) && isTrue(oldHook.merged)) {\n      // already a merged invoker\n      invoker = oldHook;\n      invoker.fns.push(wrappedHook);\n    } else {\n      // existing plain hook\n      invoker = createFnInvoker([oldHook, wrappedHook]);\n    }\n  }\n\n  invoker.merged = true;\n  def[hookKey] = invoker;\n}\n/*  */\n\n\nfunction extractPropsFromVNodeData(data, Ctor, tag) {\n  // we are only extracting raw values here.\n  // validation and default values are handled in the child\n  // component itself.\n  var propOptions = Ctor.options.props;\n\n  if (isUndef(propOptions)) {\n    return;\n  }\n\n  var res = {};\n  var attrs = data.attrs;\n  var props = data.props;\n\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n\n      if (true) {\n        var keyInLowerCase = key.toLowerCase();\n\n        if (key !== keyInLowerCase && attrs && hasOwn(attrs, keyInLowerCase)) {\n          tip(\"Prop \\\"\" + keyInLowerCase + \"\\\" is passed to component \" + formatComponentName(tag || Ctor) + \", but the declared prop name is\" + \" \\\"\" + key + \"\\\". \" + \"Note that HTML attributes are case-insensitive and camelCased \" + \"props need to use their kebab-case equivalents when using in-DOM \" + \"templates. You should probably use \\\"\" + altKey + \"\\\" instead of \\\"\" + key + \"\\\".\");\n        }\n      }\n\n      checkProp(res, props, key, altKey, true) || checkProp(res, attrs, key, altKey, false);\n    }\n  }\n\n  return res;\n}\n\nfunction checkProp(res, hash, key, altKey, preserve) {\n  if (isDef(hash)) {\n    if (hasOwn(hash, key)) {\n      res[key] = hash[key];\n\n      if (!preserve) {\n        delete hash[key];\n      }\n\n      return true;\n    } else if (hasOwn(hash, altKey)) {\n      res[key] = hash[altKey];\n\n      if (!preserve) {\n        delete hash[altKey];\n      }\n\n      return true;\n    }\n  }\n\n  return false;\n}\n/*  */\n// The template compiler attempts to minimize the need for normalization by\n// statically analyzing the template at compile time.\n//\n// For plain HTML markup, normalization can be completely skipped because the\n// generated render function is guaranteed to return Array<VNode>. There are\n// two cases where extra normalization is needed:\n// 1. When the children contains components - because a functional component\n// may return an Array instead of a single root. In this case, just a simple\n// normalization is needed - if any child is an Array, we flatten the whole\n// thing with Array.prototype.concat. It is guaranteed to be only 1-level deep\n// because functional components already normalize their own children.\n\n\nfunction simpleNormalizeChildren(children) {\n  for (var i = 0; i < children.length; i++) {\n    if (Array.isArray(children[i])) {\n      return Array.prototype.concat.apply([], children);\n    }\n  }\n\n  return children;\n} // 2. When the children contains constructs that always generated nested Arrays,\n// e.g. <template>, <slot>, v-for, or when the children is provided by user\n// with hand-written render functions / JSX. In such cases a full normalization\n// is needed to cater to all possible types of children values.\n\n\nfunction normalizeChildren(children) {\n  return isPrimitive(children) ? [createTextVNode(children)] : Array.isArray(children) ? normalizeArrayChildren(children) : undefined;\n}\n\nfunction isTextNode(node) {\n  return isDef(node) && isDef(node.text) && isFalse(node.isComment);\n}\n\nfunction normalizeArrayChildren(children, nestedIndex) {\n  var res = [];\n  var i, c, lastIndex, last;\n\n  for (i = 0; i < children.length; i++) {\n    c = children[i];\n\n    if (isUndef(c) || typeof c === 'boolean') {\n      continue;\n    }\n\n    lastIndex = res.length - 1;\n    last = res[lastIndex]; //  nested\n\n    if (Array.isArray(c)) {\n      if (c.length > 0) {\n        c = normalizeArrayChildren(c, (nestedIndex || '') + \"_\" + i); // merge adjacent text nodes\n\n        if (isTextNode(c[0]) && isTextNode(last)) {\n          res[lastIndex] = createTextVNode(last.text + c[0].text);\n          c.shift();\n        }\n\n        res.push.apply(res, c);\n      }\n    } else if (isPrimitive(c)) {\n      if (isTextNode(last)) {\n        // merge adjacent text nodes\n        // this is necessary for SSR hydration because text nodes are\n        // essentially merged when rendered to HTML strings\n        res[lastIndex] = createTextVNode(last.text + c);\n      } else if (c !== '') {\n        // convert primitive to vnode\n        res.push(createTextVNode(c));\n      }\n    } else {\n      if (isTextNode(c) && isTextNode(last)) {\n        // merge adjacent text nodes\n        res[lastIndex] = createTextVNode(last.text + c.text);\n      } else {\n        // default key for nested array children (likely generated by v-for)\n        if (isTrue(children._isVList) && isDef(c.tag) && isUndef(c.key) && isDef(nestedIndex)) {\n          c.key = \"__vlist\" + nestedIndex + \"_\" + i + \"__\";\n        }\n\n        res.push(c);\n      }\n    }\n  }\n\n  return res;\n}\n/*  */\n\n\nfunction initProvide(vm) {\n  var provide = vm.$options.provide;\n\n  if (provide) {\n    vm._provided = typeof provide === 'function' ? provide.call(vm) : provide;\n  }\n}\n\nfunction initInjections(vm) {\n  var result = resolveInject(vm.$options.inject, vm);\n\n  if (result) {\n    toggleObserving(false);\n    Object.keys(result).forEach(function (key) {\n      /* istanbul ignore else */\n      if (true) {\n        defineReactive$$1(vm, key, result[key], function () {\n          warn(\"Avoid mutating an injected value directly since the changes will be \" + \"overwritten whenever the provided component re-renders. \" + \"injection being mutated: \\\"\" + key + \"\\\"\", vm);\n        });\n      } else {}\n    });\n    toggleObserving(true);\n  }\n}\n\nfunction resolveInject(inject, vm) {\n  if (inject) {\n    // inject is :any because flow is not smart enough to figure out cached\n    var result = Object.create(null);\n    var keys = hasSymbol ? Reflect.ownKeys(inject) : Object.keys(inject);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]; // #6574 in case the inject object is observed...\n\n      if (key === '__ob__') {\n        continue;\n      }\n\n      var provideKey = inject[key].from;\n      var source = vm;\n\n      while (source) {\n        if (source._provided && hasOwn(source._provided, provideKey)) {\n          result[key] = source._provided[provideKey];\n          break;\n        }\n\n        source = source.$parent;\n      }\n\n      if (!source) {\n        if ('default' in inject[key]) {\n          var provideDefault = inject[key].default;\n          result[key] = typeof provideDefault === 'function' ? provideDefault.call(vm) : provideDefault;\n        } else if (true) {\n          warn(\"Injection \\\"\" + key + \"\\\" not found\", vm);\n        }\n      }\n    }\n\n    return result;\n  }\n}\n/*  */\n\n/**\n * Runtime helper for resolving raw children VNodes into a slot object.\n */\n\n\nfunction resolveSlots(children, context) {\n  if (!children || !children.length) {\n    return {};\n  }\n\n  var slots = {};\n\n  for (var i = 0, l = children.length; i < l; i++) {\n    var child = children[i];\n    var data = child.data; // remove slot attribute if the node is resolved as a Vue slot node\n\n    if (data && data.attrs && data.attrs.slot) {\n      delete data.attrs.slot;\n    } // named slots should only be respected if the vnode was rendered in the\n    // same context.\n\n\n    if ((child.context === context || child.fnContext === context) && data && data.slot != null) {\n      var name = data.slot;\n      var slot = slots[name] || (slots[name] = []);\n\n      if (child.tag === 'template') {\n        slot.push.apply(slot, child.children || []);\n      } else {\n        slot.push(child);\n      }\n    } else {\n      (slots.default || (slots.default = [])).push(child);\n    }\n  } // ignore slots that contains only whitespace\n\n\n  for (var name$1 in slots) {\n    if (slots[name$1].every(isWhitespace)) {\n      delete slots[name$1];\n    }\n  }\n\n  return slots;\n}\n\nfunction isWhitespace(node) {\n  return node.isComment && !node.asyncFactory || node.text === ' ';\n}\n/*  */\n\n\nfunction isAsyncPlaceholder(node) {\n  return node.isComment && node.asyncFactory;\n}\n/*  */\n\n\nfunction normalizeScopedSlots(slots, normalSlots, prevSlots) {\n  var res;\n  var hasNormalSlots = Object.keys(normalSlots).length > 0;\n  var isStable = slots ? !!slots.$stable : !hasNormalSlots;\n  var key = slots && slots.$key;\n\n  if (!slots) {\n    res = {};\n  } else if (slots._normalized) {\n    // fast path 1: child component re-render only, parent did not change\n    return slots._normalized;\n  } else if (isStable && prevSlots && prevSlots !== emptyObject && key === prevSlots.$key && !hasNormalSlots && !prevSlots.$hasNormal) {\n    // fast path 2: stable scoped slots w/ no normal slots to proxy,\n    // only need to normalize once\n    return prevSlots;\n  } else {\n    res = {};\n\n    for (var key$1 in slots) {\n      if (slots[key$1] && key$1[0] !== '$') {\n        res[key$1] = normalizeScopedSlot(normalSlots, key$1, slots[key$1]);\n      }\n    }\n  } // expose normal slots on scopedSlots\n\n\n  for (var key$2 in normalSlots) {\n    if (!(key$2 in res)) {\n      res[key$2] = proxyNormalSlot(normalSlots, key$2);\n    }\n  } // avoriaz seems to mock a non-extensible $scopedSlots object\n  // and when that is passed down this would cause an error\n\n\n  if (slots && Object.isExtensible(slots)) {\n    slots._normalized = res;\n  }\n\n  def(res, '$stable', isStable);\n  def(res, '$key', key);\n  def(res, '$hasNormal', hasNormalSlots);\n  return res;\n}\n\nfunction normalizeScopedSlot(normalSlots, key, fn) {\n  var normalized = function () {\n    var res = arguments.length ? fn.apply(null, arguments) : fn({});\n    res = res && typeof res === 'object' && !Array.isArray(res) ? [res] // single vnode\n    : normalizeChildren(res);\n    var vnode = res && res[0];\n    return res && (!vnode || res.length === 1 && vnode.isComment && !isAsyncPlaceholder(vnode) // #9658, #10391\n    ) ? undefined : res;\n  }; // this is a slot using the new v-slot syntax without scope. although it is\n  // compiled as a scoped slot, render fn users would expect it to be present\n  // on this.$slots because the usage is semantically a normal slot.\n\n\n  if (fn.proxy) {\n    Object.defineProperty(normalSlots, key, {\n      get: normalized,\n      enumerable: true,\n      configurable: true\n    });\n  }\n\n  return normalized;\n}\n\nfunction proxyNormalSlot(slots, key) {\n  return function () {\n    return slots[key];\n  };\n}\n/*  */\n\n/**\n * Runtime helper for rendering v-for lists.\n */\n\n\nfunction renderList(val, render) {\n  var ret, i, l, keys, key;\n\n  if (Array.isArray(val) || typeof val === 'string') {\n    ret = new Array(val.length);\n\n    for (i = 0, l = val.length; i < l; i++) {\n      ret[i] = render(val[i], i);\n    }\n  } else if (typeof val === 'number') {\n    ret = new Array(val);\n\n    for (i = 0; i < val; i++) {\n      ret[i] = render(i + 1, i);\n    }\n  } else if (isObject(val)) {\n    if (hasSymbol && val[Symbol.iterator]) {\n      ret = [];\n      var iterator = val[Symbol.iterator]();\n      var result = iterator.next();\n\n      while (!result.done) {\n        ret.push(render(result.value, ret.length));\n        result = iterator.next();\n      }\n    } else {\n      keys = Object.keys(val);\n      ret = new Array(keys.length);\n\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[i] = render(val[key], key, i);\n      }\n    }\n  }\n\n  if (!isDef(ret)) {\n    ret = [];\n  }\n\n  ret._isVList = true;\n  return ret;\n}\n/*  */\n\n/**\n * Runtime helper for rendering <slot>\n */\n\n\nfunction renderSlot(name, fallbackRender, props, bindObject) {\n  var scopedSlotFn = this.$scopedSlots[name];\n  var nodes;\n\n  if (scopedSlotFn) {\n    // scoped slot\n    props = props || {};\n\n    if (bindObject) {\n      if ( true && !isObject(bindObject)) {\n        warn('slot v-bind without argument expects an Object', this);\n      }\n\n      props = extend(extend({}, bindObject), props);\n    }\n\n    nodes = scopedSlotFn(props) || (typeof fallbackRender === 'function' ? fallbackRender() : fallbackRender);\n  } else {\n    nodes = this.$slots[name] || (typeof fallbackRender === 'function' ? fallbackRender() : fallbackRender);\n  }\n\n  var target = props && props.slot;\n\n  if (target) {\n    return this.$createElement('template', {\n      slot: target\n    }, nodes);\n  } else {\n    return nodes;\n  }\n}\n/*  */\n\n/**\n * Runtime helper for resolving filters\n */\n\n\nfunction resolveFilter(id) {\n  return resolveAsset(this.$options, 'filters', id, true) || identity;\n}\n/*  */\n\n\nfunction isKeyNotMatch(expect, actual) {\n  if (Array.isArray(expect)) {\n    return expect.indexOf(actual) === -1;\n  } else {\n    return expect !== actual;\n  }\n}\n/**\n * Runtime helper for checking keyCodes from config.\n * exposed as Vue.prototype._k\n * passing in eventKeyName as last argument separately for backwards compat\n */\n\n\nfunction checkKeyCodes(eventKeyCode, key, builtInKeyCode, eventKeyName, builtInKeyName) {\n  var mappedKeyCode = config.keyCodes[key] || builtInKeyCode;\n\n  if (builtInKeyName && eventKeyName && !config.keyCodes[key]) {\n    return isKeyNotMatch(builtInKeyName, eventKeyName);\n  } else if (mappedKeyCode) {\n    return isKeyNotMatch(mappedKeyCode, eventKeyCode);\n  } else if (eventKeyName) {\n    return hyphenate(eventKeyName) !== key;\n  }\n\n  return eventKeyCode === undefined;\n}\n/*  */\n\n/**\n * Runtime helper for merging v-bind=\"object\" into a VNode's data.\n */\n\n\nfunction bindObjectProps(data, tag, value, asProp, isSync) {\n  if (value) {\n    if (!isObject(value)) {\n       true && warn('v-bind without argument expects an Object or Array value', this);\n    } else {\n      if (Array.isArray(value)) {\n        value = toObject(value);\n      }\n\n      var hash;\n\n      var loop = function (key) {\n        if (key === 'class' || key === 'style' || isReservedAttribute(key)) {\n          hash = data;\n        } else {\n          var type = data.attrs && data.attrs.type;\n          hash = asProp || config.mustUseProp(tag, type, key) ? data.domProps || (data.domProps = {}) : data.attrs || (data.attrs = {});\n        }\n\n        var camelizedKey = camelize(key);\n        var hyphenatedKey = hyphenate(key);\n\n        if (!(camelizedKey in hash) && !(hyphenatedKey in hash)) {\n          hash[key] = value[key];\n\n          if (isSync) {\n            var on = data.on || (data.on = {});\n\n            on[\"update:\" + key] = function ($event) {\n              value[key] = $event;\n            };\n          }\n        }\n      };\n\n      for (var key in value) loop(key);\n    }\n  }\n\n  return data;\n}\n/*  */\n\n/**\n * Runtime helper for rendering static trees.\n */\n\n\nfunction renderStatic(index, isInFor) {\n  var cached = this._staticTrees || (this._staticTrees = []);\n  var tree = cached[index]; // if has already-rendered static tree and not inside v-for,\n  // we can reuse the same tree.\n\n  if (tree && !isInFor) {\n    return tree;\n  } // otherwise, render a fresh tree.\n\n\n  tree = cached[index] = this.$options.staticRenderFns[index].call(this._renderProxy, null, this // for render fns generated for functional component templates\n  );\n  markStatic(tree, \"__static__\" + index, false);\n  return tree;\n}\n/**\n * Runtime helper for v-once.\n * Effectively it means marking the node as static with a unique key.\n */\n\n\nfunction markOnce(tree, index, key) {\n  markStatic(tree, \"__once__\" + index + (key ? \"_\" + key : \"\"), true);\n  return tree;\n}\n\nfunction markStatic(tree, key, isOnce) {\n  if (Array.isArray(tree)) {\n    for (var i = 0; i < tree.length; i++) {\n      if (tree[i] && typeof tree[i] !== 'string') {\n        markStaticNode(tree[i], key + \"_\" + i, isOnce);\n      }\n    }\n  } else {\n    markStaticNode(tree, key, isOnce);\n  }\n}\n\nfunction markStaticNode(node, key, isOnce) {\n  node.isStatic = true;\n  node.key = key;\n  node.isOnce = isOnce;\n}\n/*  */\n\n\nfunction bindObjectListeners(data, value) {\n  if (value) {\n    if (!isPlainObject(value)) {\n       true && warn('v-on without argument expects an Object value', this);\n    } else {\n      var on = data.on = data.on ? extend({}, data.on) : {};\n\n      for (var key in value) {\n        var existing = on[key];\n        var ours = value[key];\n        on[key] = existing ? [].concat(existing, ours) : ours;\n      }\n    }\n  }\n\n  return data;\n}\n/*  */\n\n\nfunction resolveScopedSlots(fns, // see flow/vnode\nres, // the following are added in 2.6\nhasDynamicKeys, contentHashKey) {\n  res = res || {\n    $stable: !hasDynamicKeys\n  };\n\n  for (var i = 0; i < fns.length; i++) {\n    var slot = fns[i];\n\n    if (Array.isArray(slot)) {\n      resolveScopedSlots(slot, res, hasDynamicKeys);\n    } else if (slot) {\n      // marker for reverse proxying v-slot without scope on this.$slots\n      if (slot.proxy) {\n        slot.fn.proxy = true;\n      }\n\n      res[slot.key] = slot.fn;\n    }\n  }\n\n  if (contentHashKey) {\n    res.$key = contentHashKey;\n  }\n\n  return res;\n}\n/*  */\n\n\nfunction bindDynamicKeys(baseObj, values) {\n  for (var i = 0; i < values.length; i += 2) {\n    var key = values[i];\n\n    if (typeof key === 'string' && key) {\n      baseObj[values[i]] = values[i + 1];\n    } else if ( true && key !== '' && key !== null) {\n      // null is a special value for explicitly removing a binding\n      warn(\"Invalid value for dynamic directive argument (expected string or null): \" + key, this);\n    }\n  }\n\n  return baseObj;\n} // helper to dynamically append modifier runtime markers to event names.\n// ensure only append when value is already string, otherwise it will be cast\n// to string and cause the type check to miss.\n\n\nfunction prependModifier(value, symbol) {\n  return typeof value === 'string' ? symbol + value : value;\n}\n/*  */\n\n\nfunction installRenderHelpers(target) {\n  target._o = markOnce;\n  target._n = toNumber;\n  target._s = toString;\n  target._l = renderList;\n  target._t = renderSlot;\n  target._q = looseEqual;\n  target._i = looseIndexOf;\n  target._m = renderStatic;\n  target._f = resolveFilter;\n  target._k = checkKeyCodes;\n  target._b = bindObjectProps;\n  target._v = createTextVNode;\n  target._e = createEmptyVNode;\n  target._u = resolveScopedSlots;\n  target._g = bindObjectListeners;\n  target._d = bindDynamicKeys;\n  target._p = prependModifier;\n}\n/*  */\n\n\nfunction FunctionalRenderContext(data, props, children, parent, Ctor) {\n  var this$1 = this;\n  var options = Ctor.options; // ensure the createElement function in functional components\n  // gets a unique context - this is necessary for correct named slot check\n\n  var contextVm;\n\n  if (hasOwn(parent, '_uid')) {\n    contextVm = Object.create(parent); // $flow-disable-line\n\n    contextVm._original = parent;\n  } else {\n    // the context vm passed in is a functional context as well.\n    // in this case we want to make sure we are able to get a hold to the\n    // real context instance.\n    contextVm = parent; // $flow-disable-line\n\n    parent = parent._original;\n  }\n\n  var isCompiled = isTrue(options._compiled);\n  var needNormalization = !isCompiled;\n  this.data = data;\n  this.props = props;\n  this.children = children;\n  this.parent = parent;\n  this.listeners = data.on || emptyObject;\n  this.injections = resolveInject(options.inject, parent);\n\n  this.slots = function () {\n    if (!this$1.$slots) {\n      normalizeScopedSlots(data.scopedSlots, this$1.$slots = resolveSlots(children, parent));\n    }\n\n    return this$1.$slots;\n  };\n\n  Object.defineProperty(this, 'scopedSlots', {\n    enumerable: true,\n    get: function get() {\n      return normalizeScopedSlots(data.scopedSlots, this.slots());\n    }\n  }); // support for compiled functional template\n\n  if (isCompiled) {\n    // exposing $options for renderStatic()\n    this.$options = options; // pre-resolve slots for renderSlot()\n\n    this.$slots = this.slots();\n    this.$scopedSlots = normalizeScopedSlots(data.scopedSlots, this.$slots);\n  }\n\n  if (options._scopeId) {\n    this._c = function (a, b, c, d) {\n      var vnode = createElement(contextVm, a, b, c, d, needNormalization);\n\n      if (vnode && !Array.isArray(vnode)) {\n        vnode.fnScopeId = options._scopeId;\n        vnode.fnContext = parent;\n      }\n\n      return vnode;\n    };\n  } else {\n    this._c = function (a, b, c, d) {\n      return createElement(contextVm, a, b, c, d, needNormalization);\n    };\n  }\n}\n\ninstallRenderHelpers(FunctionalRenderContext.prototype);\n\nfunction createFunctionalComponent(Ctor, propsData, data, contextVm, children) {\n  var options = Ctor.options;\n  var props = {};\n  var propOptions = options.props;\n\n  if (isDef(propOptions)) {\n    for (var key in propOptions) {\n      props[key] = validateProp(key, propOptions, propsData || emptyObject);\n    }\n  } else {\n    if (isDef(data.attrs)) {\n      mergeProps(props, data.attrs);\n    }\n\n    if (isDef(data.props)) {\n      mergeProps(props, data.props);\n    }\n  }\n\n  var renderContext = new FunctionalRenderContext(data, props, children, contextVm, Ctor);\n  var vnode = options.render.call(null, renderContext._c, renderContext);\n\n  if (vnode instanceof VNode) {\n    return cloneAndMarkFunctionalResult(vnode, data, renderContext.parent, options, renderContext);\n  } else if (Array.isArray(vnode)) {\n    var vnodes = normalizeChildren(vnode) || [];\n    var res = new Array(vnodes.length);\n\n    for (var i = 0; i < vnodes.length; i++) {\n      res[i] = cloneAndMarkFunctionalResult(vnodes[i], data, renderContext.parent, options, renderContext);\n    }\n\n    return res;\n  }\n}\n\nfunction cloneAndMarkFunctionalResult(vnode, data, contextVm, options, renderContext) {\n  // #7817 clone node before setting fnContext, otherwise if the node is reused\n  // (e.g. it was from a cached normal slot) the fnContext causes named slots\n  // that should not be matched to match.\n  var clone = cloneVNode(vnode);\n  clone.fnContext = contextVm;\n  clone.fnOptions = options;\n\n  if (true) {\n    (clone.devtoolsMeta = clone.devtoolsMeta || {}).renderContext = renderContext;\n  }\n\n  if (data.slot) {\n    (clone.data || (clone.data = {})).slot = data.slot;\n  }\n\n  return clone;\n}\n\nfunction mergeProps(to, from) {\n  for (var key in from) {\n    to[camelize(key)] = from[key];\n  }\n}\n/*  */\n\n/*  */\n\n/*  */\n\n/*  */\n// inline hooks to be invoked on component VNodes during patch\n\n\nvar componentVNodeHooks = {\n  init: function init(vnode, hydrating) {\n    if (vnode.componentInstance && !vnode.componentInstance._isDestroyed && vnode.data.keepAlive) {\n      // kept-alive components, treat as a patch\n      var mountedNode = vnode; // work around flow\n\n      componentVNodeHooks.prepatch(mountedNode, mountedNode);\n    } else {\n      var child = vnode.componentInstance = createComponentInstanceForVnode(vnode, activeInstance);\n      child.$mount(hydrating ? vnode.elm : undefined, hydrating);\n    }\n  },\n  prepatch: function prepatch(oldVnode, vnode) {\n    var options = vnode.componentOptions;\n    var child = vnode.componentInstance = oldVnode.componentInstance;\n    updateChildComponent(child, options.propsData, // updated props\n    options.listeners, // updated listeners\n    vnode, // new parent vnode\n    options.children // new children\n    );\n  },\n  insert: function insert(vnode) {\n    var context = vnode.context;\n    var componentInstance = vnode.componentInstance;\n\n    if (!componentInstance._isMounted) {\n      componentInstance._isMounted = true;\n      callHook(componentInstance, 'mounted');\n    }\n\n    if (vnode.data.keepAlive) {\n      if (context._isMounted) {\n        // vue-router#1212\n        // During updates, a kept-alive component's child components may\n        // change, so directly walking the tree here may call activated hooks\n        // on incorrect children. Instead we push them into a queue which will\n        // be processed after the whole patch process ended.\n        queueActivatedComponent(componentInstance);\n      } else {\n        activateChildComponent(componentInstance, true\n        /* direct */\n        );\n      }\n    }\n  },\n  destroy: function destroy(vnode) {\n    var componentInstance = vnode.componentInstance;\n\n    if (!componentInstance._isDestroyed) {\n      if (!vnode.data.keepAlive) {\n        componentInstance.$destroy();\n      } else {\n        deactivateChildComponent(componentInstance, true\n        /* direct */\n        );\n      }\n    }\n  }\n};\nvar hooksToMerge = Object.keys(componentVNodeHooks);\n\nfunction createComponent(Ctor, data, context, children, tag) {\n  if (isUndef(Ctor)) {\n    return;\n  }\n\n  var baseCtor = context.$options._base; // plain options object: turn it into a constructor\n\n  if (isObject(Ctor)) {\n    Ctor = baseCtor.extend(Ctor);\n  } // if at this stage it's not a constructor or an async component factory,\n  // reject.\n\n\n  if (typeof Ctor !== 'function') {\n    if (true) {\n      warn(\"Invalid Component definition: \" + String(Ctor), context);\n    }\n\n    return;\n  } // async component\n\n\n  var asyncFactory;\n\n  if (isUndef(Ctor.cid)) {\n    asyncFactory = Ctor;\n    Ctor = resolveAsyncComponent(asyncFactory, baseCtor);\n\n    if (Ctor === undefined) {\n      // return a placeholder node for async component, which is rendered\n      // as a comment node but preserves all the raw information for the node.\n      // the information will be used for async server-rendering and hydration.\n      return createAsyncPlaceholder(asyncFactory, data, context, children, tag);\n    }\n  }\n\n  data = data || {}; // resolve constructor options in case global mixins are applied after\n  // component constructor creation\n\n  resolveConstructorOptions(Ctor); // transform component v-model data into props & events\n\n  if (isDef(data.model)) {\n    transformModel(Ctor.options, data);\n  } // extract props\n\n\n  var propsData = extractPropsFromVNodeData(data, Ctor, tag); // functional component\n\n  if (isTrue(Ctor.options.functional)) {\n    return createFunctionalComponent(Ctor, propsData, data, context, children);\n  } // extract listeners, since these needs to be treated as\n  // child component listeners instead of DOM listeners\n\n\n  var listeners = data.on; // replace with listeners with .native modifier\n  // so it gets processed during parent component patch.\n\n  data.on = data.nativeOn;\n\n  if (isTrue(Ctor.options.abstract)) {\n    // abstract components do not keep anything\n    // other than props & listeners & slot\n    // work around flow\n    var slot = data.slot;\n    data = {};\n\n    if (slot) {\n      data.slot = slot;\n    }\n  } // install component management hooks onto the placeholder node\n\n\n  installComponentHooks(data); // return a placeholder vnode\n\n  var name = Ctor.options.name || tag;\n  var vnode = new VNode(\"vue-component-\" + Ctor.cid + (name ? \"-\" + name : ''), data, undefined, undefined, undefined, context, {\n    Ctor: Ctor,\n    propsData: propsData,\n    listeners: listeners,\n    tag: tag,\n    children: children\n  }, asyncFactory);\n  return vnode;\n}\n\nfunction createComponentInstanceForVnode( // we know it's MountedComponentVNode but flow doesn't\nvnode, // activeInstance in lifecycle state\nparent) {\n  var options = {\n    _isComponent: true,\n    _parentVnode: vnode,\n    parent: parent\n  }; // check inline-template render functions\n\n  var inlineTemplate = vnode.data.inlineTemplate;\n\n  if (isDef(inlineTemplate)) {\n    options.render = inlineTemplate.render;\n    options.staticRenderFns = inlineTemplate.staticRenderFns;\n  }\n\n  return new vnode.componentOptions.Ctor(options);\n}\n\nfunction installComponentHooks(data) {\n  var hooks = data.hook || (data.hook = {});\n\n  for (var i = 0; i < hooksToMerge.length; i++) {\n    var key = hooksToMerge[i];\n    var existing = hooks[key];\n    var toMerge = componentVNodeHooks[key];\n\n    if (existing !== toMerge && !(existing && existing._merged)) {\n      hooks[key] = existing ? mergeHook$1(toMerge, existing) : toMerge;\n    }\n  }\n}\n\nfunction mergeHook$1(f1, f2) {\n  var merged = function (a, b) {\n    // flow complains about extra args which is why we use any\n    f1(a, b);\n    f2(a, b);\n  };\n\n  merged._merged = true;\n  return merged;\n} // transform component v-model info (value and callback) into\n// prop and event handler respectively.\n\n\nfunction transformModel(options, data) {\n  var prop = options.model && options.model.prop || 'value';\n  var event = options.model && options.model.event || 'input';\n  (data.attrs || (data.attrs = {}))[prop] = data.model.value;\n  var on = data.on || (data.on = {});\n  var existing = on[event];\n  var callback = data.model.callback;\n\n  if (isDef(existing)) {\n    if (Array.isArray(existing) ? existing.indexOf(callback) === -1 : existing !== callback) {\n      on[event] = [callback].concat(existing);\n    }\n  } else {\n    on[event] = callback;\n  }\n}\n/*  */\n\n\nvar SIMPLE_NORMALIZE = 1;\nvar ALWAYS_NORMALIZE = 2; // wrapper function for providing a more flexible interface\n// without getting yelled at by flow\n\nfunction createElement(context, tag, data, children, normalizationType, alwaysNormalize) {\n  if (Array.isArray(data) || isPrimitive(data)) {\n    normalizationType = children;\n    children = data;\n    data = undefined;\n  }\n\n  if (isTrue(alwaysNormalize)) {\n    normalizationType = ALWAYS_NORMALIZE;\n  }\n\n  return _createElement(context, tag, data, children, normalizationType);\n}\n\nfunction _createElement(context, tag, data, children, normalizationType) {\n  if (isDef(data) && isDef(data.__ob__)) {\n     true && warn(\"Avoid using observed data object as vnode data: \" + JSON.stringify(data) + \"\\n\" + 'Always create fresh vnode data objects in each render!', context);\n    return createEmptyVNode();\n  } // object syntax in v-bind\n\n\n  if (isDef(data) && isDef(data.is)) {\n    tag = data.is;\n  }\n\n  if (!tag) {\n    // in case of component :is set to falsy value\n    return createEmptyVNode();\n  } // warn against non-primitive key\n\n\n  if ( true && isDef(data) && isDef(data.key) && !isPrimitive(data.key)) {\n    {\n      warn('Avoid using non-primitive value as key, ' + 'use string/number value instead.', context);\n    }\n  } // support single function children as default scoped slot\n\n\n  if (Array.isArray(children) && typeof children[0] === 'function') {\n    data = data || {};\n    data.scopedSlots = {\n      default: children[0]\n    };\n    children.length = 0;\n  }\n\n  if (normalizationType === ALWAYS_NORMALIZE) {\n    children = normalizeChildren(children);\n  } else if (normalizationType === SIMPLE_NORMALIZE) {\n    children = simpleNormalizeChildren(children);\n  }\n\n  var vnode, ns;\n\n  if (typeof tag === 'string') {\n    var Ctor;\n    ns = context.$vnode && context.$vnode.ns || config.getTagNamespace(tag);\n\n    if (config.isReservedTag(tag)) {\n      // platform built-in elements\n      if ( true && isDef(data) && isDef(data.nativeOn) && data.tag !== 'component') {\n        warn(\"The .native modifier for v-on is only valid on components but it was used on <\" + tag + \">.\", context);\n      }\n\n      vnode = new VNode(config.parsePlatformTagName(tag), data, children, undefined, undefined, context);\n    } else if ((!data || !data.pre) && isDef(Ctor = resolveAsset(context.$options, 'components', tag))) {\n      // component\n      vnode = createComponent(Ctor, data, context, children, tag);\n    } else {\n      // unknown or unlisted namespaced elements\n      // check at runtime because it may get assigned a namespace when its\n      // parent normalizes children\n      vnode = new VNode(tag, data, children, undefined, undefined, context);\n    }\n  } else {\n    // direct component options / constructor\n    vnode = createComponent(tag, data, context, children);\n  }\n\n  if (Array.isArray(vnode)) {\n    return vnode;\n  } else if (isDef(vnode)) {\n    if (isDef(ns)) {\n      applyNS(vnode, ns);\n    }\n\n    if (isDef(data)) {\n      registerDeepBindings(data);\n    }\n\n    return vnode;\n  } else {\n    return createEmptyVNode();\n  }\n}\n\nfunction applyNS(vnode, ns, force) {\n  vnode.ns = ns;\n\n  if (vnode.tag === 'foreignObject') {\n    // use default namespace inside foreignObject\n    ns = undefined;\n    force = true;\n  }\n\n  if (isDef(vnode.children)) {\n    for (var i = 0, l = vnode.children.length; i < l; i++) {\n      var child = vnode.children[i];\n\n      if (isDef(child.tag) && (isUndef(child.ns) || isTrue(force) && child.tag !== 'svg')) {\n        applyNS(child, ns, force);\n      }\n    }\n  }\n} // ref #5318\n// necessary to ensure parent re-render when deep bindings like :style and\n// :class are used on slot nodes\n\n\nfunction registerDeepBindings(data) {\n  if (isObject(data.style)) {\n    traverse(data.style);\n  }\n\n  if (isObject(data.class)) {\n    traverse(data.class);\n  }\n}\n/*  */\n\n\nfunction initRender(vm) {\n  vm._vnode = null; // the root of the child tree\n\n  vm._staticTrees = null; // v-once cached trees\n\n  var options = vm.$options;\n  var parentVnode = vm.$vnode = options._parentVnode; // the placeholder node in parent tree\n\n  var renderContext = parentVnode && parentVnode.context;\n  vm.$slots = resolveSlots(options._renderChildren, renderContext);\n  vm.$scopedSlots = emptyObject; // bind the createElement fn to this instance\n  // so that we get proper render context inside it.\n  // args order: tag, data, children, normalizationType, alwaysNormalize\n  // internal version is used by render functions compiled from templates\n\n  vm._c = function (a, b, c, d) {\n    return createElement(vm, a, b, c, d, false);\n  }; // normalization is always applied for the public version, used in\n  // user-written render functions.\n\n\n  vm.$createElement = function (a, b, c, d) {\n    return createElement(vm, a, b, c, d, true);\n  }; // $attrs & $listeners are exposed for easier HOC creation.\n  // they need to be reactive so that HOCs using them are always updated\n\n\n  var parentData = parentVnode && parentVnode.data;\n  /* istanbul ignore else */\n\n  if (true) {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$attrs is readonly.\", vm);\n    }, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$listeners is readonly.\", vm);\n    }, true);\n  } else {}\n}\n\nvar currentRenderingInstance = null;\n\nfunction renderMixin(Vue) {\n  // install runtime convenience helpers\n  installRenderHelpers(Vue.prototype);\n\n  Vue.prototype.$nextTick = function (fn) {\n    return nextTick(fn, this);\n  };\n\n  Vue.prototype._render = function () {\n    var vm = this;\n    var ref = vm.$options;\n    var render = ref.render;\n    var _parentVnode = ref._parentVnode;\n\n    if (_parentVnode) {\n      vm.$scopedSlots = normalizeScopedSlots(_parentVnode.data.scopedSlots, vm.$slots, vm.$scopedSlots);\n    } // set parent vnode. this allows render functions to have access\n    // to the data on the placeholder node.\n\n\n    vm.$vnode = _parentVnode; // render self\n\n    var vnode;\n\n    try {\n      // There's no need to maintain a stack because all render fns are called\n      // separately from one another. Nested component's render fns are called\n      // when parent component is patched.\n      currentRenderingInstance = vm;\n      vnode = render.call(vm._renderProxy, vm.$createElement);\n    } catch (e) {\n      handleError(e, vm, \"render\"); // return error render result,\n      // or previous vnode to prevent render error causing blank component\n\n      /* istanbul ignore else */\n\n      if ( true && vm.$options.renderError) {\n        try {\n          vnode = vm.$options.renderError.call(vm._renderProxy, vm.$createElement, e);\n        } catch (e) {\n          handleError(e, vm, \"renderError\");\n          vnode = vm._vnode;\n        }\n      } else {\n        vnode = vm._vnode;\n      }\n    } finally {\n      currentRenderingInstance = null;\n    } // if the returned array contains only a single node, allow it\n\n\n    if (Array.isArray(vnode) && vnode.length === 1) {\n      vnode = vnode[0];\n    } // return empty vnode in case the render function errored out\n\n\n    if (!(vnode instanceof VNode)) {\n      if ( true && Array.isArray(vnode)) {\n        warn('Multiple root nodes returned from render function. Render function ' + 'should return a single root node.', vm);\n      }\n\n      vnode = createEmptyVNode();\n    } // set parent\n\n\n    vnode.parent = _parentVnode;\n    return vnode;\n  };\n}\n/*  */\n\n\nfunction ensureCtor(comp, base) {\n  if (comp.__esModule || hasSymbol && comp[Symbol.toStringTag] === 'Module') {\n    comp = comp.default;\n  }\n\n  return isObject(comp) ? base.extend(comp) : comp;\n}\n\nfunction createAsyncPlaceholder(factory, data, context, children, tag) {\n  var node = createEmptyVNode();\n  node.asyncFactory = factory;\n  node.asyncMeta = {\n    data: data,\n    context: context,\n    children: children,\n    tag: tag\n  };\n  return node;\n}\n\nfunction resolveAsyncComponent(factory, baseCtor) {\n  if (isTrue(factory.error) && isDef(factory.errorComp)) {\n    return factory.errorComp;\n  }\n\n  if (isDef(factory.resolved)) {\n    return factory.resolved;\n  }\n\n  var owner = currentRenderingInstance;\n\n  if (owner && isDef(factory.owners) && factory.owners.indexOf(owner) === -1) {\n    // already pending\n    factory.owners.push(owner);\n  }\n\n  if (isTrue(factory.loading) && isDef(factory.loadingComp)) {\n    return factory.loadingComp;\n  }\n\n  if (owner && !isDef(factory.owners)) {\n    var owners = factory.owners = [owner];\n    var sync = true;\n    var timerLoading = null;\n    var timerTimeout = null;\n    owner.$on('hook:destroyed', function () {\n      return remove(owners, owner);\n    });\n\n    var forceRender = function (renderCompleted) {\n      for (var i = 0, l = owners.length; i < l; i++) {\n        owners[i].$forceUpdate();\n      }\n\n      if (renderCompleted) {\n        owners.length = 0;\n\n        if (timerLoading !== null) {\n          clearTimeout(timerLoading);\n          timerLoading = null;\n        }\n\n        if (timerTimeout !== null) {\n          clearTimeout(timerTimeout);\n          timerTimeout = null;\n        }\n      }\n    };\n\n    var resolve = once(function (res) {\n      // cache resolved\n      factory.resolved = ensureCtor(res, baseCtor); // invoke callbacks only if this is not a synchronous resolve\n      // (async resolves are shimmed as synchronous during SSR)\n\n      if (!sync) {\n        forceRender(true);\n      } else {\n        owners.length = 0;\n      }\n    });\n    var reject = once(function (reason) {\n       true && warn(\"Failed to resolve async component: \" + String(factory) + (reason ? \"\\nReason: \" + reason : ''));\n\n      if (isDef(factory.errorComp)) {\n        factory.error = true;\n        forceRender(true);\n      }\n    });\n    var res = factory(resolve, reject);\n\n    if (isObject(res)) {\n      if (isPromise(res)) {\n        // () => Promise\n        if (isUndef(factory.resolved)) {\n          res.then(resolve, reject);\n        }\n      } else if (isPromise(res.component)) {\n        res.component.then(resolve, reject);\n\n        if (isDef(res.error)) {\n          factory.errorComp = ensureCtor(res.error, baseCtor);\n        }\n\n        if (isDef(res.loading)) {\n          factory.loadingComp = ensureCtor(res.loading, baseCtor);\n\n          if (res.delay === 0) {\n            factory.loading = true;\n          } else {\n            timerLoading = setTimeout(function () {\n              timerLoading = null;\n\n              if (isUndef(factory.resolved) && isUndef(factory.error)) {\n                factory.loading = true;\n                forceRender(false);\n              }\n            }, res.delay || 200);\n          }\n        }\n\n        if (isDef(res.timeout)) {\n          timerTimeout = setTimeout(function () {\n            timerTimeout = null;\n\n            if (isUndef(factory.resolved)) {\n              reject( true ? \"timeout (\" + res.timeout + \"ms)\" : undefined);\n            }\n          }, res.timeout);\n        }\n      }\n    }\n\n    sync = false; // return in case resolved synchronously\n\n    return factory.loading ? factory.loadingComp : factory.resolved;\n  }\n}\n/*  */\n\n\nfunction getFirstComponentChild(children) {\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      var c = children[i];\n\n      if (isDef(c) && (isDef(c.componentOptions) || isAsyncPlaceholder(c))) {\n        return c;\n      }\n    }\n  }\n}\n/*  */\n\n/*  */\n\n\nfunction initEvents(vm) {\n  vm._events = Object.create(null);\n  vm._hasHookEvent = false; // init parent attached events\n\n  var listeners = vm.$options._parentListeners;\n\n  if (listeners) {\n    updateComponentListeners(vm, listeners);\n  }\n}\n\nvar target;\n\nfunction add(event, fn) {\n  target.$on(event, fn);\n}\n\nfunction remove$1(event, fn) {\n  target.$off(event, fn);\n}\n\nfunction createOnceHandler(event, fn) {\n  var _target = target;\n  return function onceHandler() {\n    var res = fn.apply(null, arguments);\n\n    if (res !== null) {\n      _target.$off(event, onceHandler);\n    }\n  };\n}\n\nfunction updateComponentListeners(vm, listeners, oldListeners) {\n  target = vm;\n  updateListeners(listeners, oldListeners || {}, add, remove$1, createOnceHandler, vm);\n  target = undefined;\n}\n\nfunction eventsMixin(Vue) {\n  var hookRE = /^hook:/;\n\n  Vue.prototype.$on = function (event, fn) {\n    var vm = this;\n\n    if (Array.isArray(event)) {\n      for (var i = 0, l = event.length; i < l; i++) {\n        vm.$on(event[i], fn);\n      }\n    } else {\n      (vm._events[event] || (vm._events[event] = [])).push(fn); // optimize hook:event cost by using a boolean flag marked at registration\n      // instead of a hash lookup\n\n      if (hookRE.test(event)) {\n        vm._hasHookEvent = true;\n      }\n    }\n\n    return vm;\n  };\n\n  Vue.prototype.$once = function (event, fn) {\n    var vm = this;\n\n    function on() {\n      vm.$off(event, on);\n      fn.apply(vm, arguments);\n    }\n\n    on.fn = fn;\n    vm.$on(event, on);\n    return vm;\n  };\n\n  Vue.prototype.$off = function (event, fn) {\n    var vm = this; // all\n\n    if (!arguments.length) {\n      vm._events = Object.create(null);\n      return vm;\n    } // array of events\n\n\n    if (Array.isArray(event)) {\n      for (var i$1 = 0, l = event.length; i$1 < l; i$1++) {\n        vm.$off(event[i$1], fn);\n      }\n\n      return vm;\n    } // specific event\n\n\n    var cbs = vm._events[event];\n\n    if (!cbs) {\n      return vm;\n    }\n\n    if (!fn) {\n      vm._events[event] = null;\n      return vm;\n    } // specific handler\n\n\n    var cb;\n    var i = cbs.length;\n\n    while (i--) {\n      cb = cbs[i];\n\n      if (cb === fn || cb.fn === fn) {\n        cbs.splice(i, 1);\n        break;\n      }\n    }\n\n    return vm;\n  };\n\n  Vue.prototype.$emit = function (event) {\n    var vm = this;\n\n    if (true) {\n      var lowerCaseEvent = event.toLowerCase();\n\n      if (lowerCaseEvent !== event && vm._events[lowerCaseEvent]) {\n        tip(\"Event \\\"\" + lowerCaseEvent + \"\\\" is emitted in component \" + formatComponentName(vm) + \" but the handler is registered for \\\"\" + event + \"\\\". \" + \"Note that HTML attributes are case-insensitive and you cannot use \" + \"v-on to listen to camelCase events when using in-DOM templates. \" + \"You should probably use \\\"\" + hyphenate(event) + \"\\\" instead of \\\"\" + event + \"\\\".\");\n      }\n    }\n\n    var cbs = vm._events[event];\n\n    if (cbs) {\n      cbs = cbs.length > 1 ? toArray(cbs) : cbs;\n      var args = toArray(arguments, 1);\n      var info = \"event handler for \\\"\" + event + \"\\\"\";\n\n      for (var i = 0, l = cbs.length; i < l; i++) {\n        invokeWithErrorHandling(cbs[i], vm, args, vm, info);\n      }\n    }\n\n    return vm;\n  };\n}\n/*  */\n\n\nvar activeInstance = null;\nvar isUpdatingChildComponent = false;\n\nfunction setActiveInstance(vm) {\n  var prevActiveInstance = activeInstance;\n  activeInstance = vm;\n  return function () {\n    activeInstance = prevActiveInstance;\n  };\n}\n\nfunction initLifecycle(vm) {\n  var options = vm.$options; // locate first non-abstract parent\n\n  var parent = options.parent;\n\n  if (parent && !options.abstract) {\n    while (parent.$options.abstract && parent.$parent) {\n      parent = parent.$parent;\n    }\n\n    parent.$children.push(vm);\n  }\n\n  vm.$parent = parent;\n  vm.$root = parent ? parent.$root : vm;\n  vm.$children = [];\n  vm.$refs = {};\n  vm._watcher = null;\n  vm._inactive = null;\n  vm._directInactive = false;\n  vm._isMounted = false;\n  vm._isDestroyed = false;\n  vm._isBeingDestroyed = false;\n}\n\nfunction lifecycleMixin(Vue) {\n  Vue.prototype._update = function (vnode, hydrating) {\n    var vm = this;\n    var prevEl = vm.$el;\n    var prevVnode = vm._vnode;\n    var restoreActiveInstance = setActiveInstance(vm);\n    vm._vnode = vnode; // Vue.prototype.__patch__ is injected in entry points\n    // based on the rendering backend used.\n\n    if (!prevVnode) {\n      // initial render\n      vm.$el = vm.__patch__(vm.$el, vnode, hydrating, false\n      /* removeOnly */\n      );\n    } else {\n      // updates\n      vm.$el = vm.__patch__(prevVnode, vnode);\n    }\n\n    restoreActiveInstance(); // update __vue__ reference\n\n    if (prevEl) {\n      prevEl.__vue__ = null;\n    }\n\n    if (vm.$el) {\n      vm.$el.__vue__ = vm;\n    } // if parent is an HOC, update its $el as well\n\n\n    if (vm.$vnode && vm.$parent && vm.$vnode === vm.$parent._vnode) {\n      vm.$parent.$el = vm.$el;\n    } // updated hook is called by the scheduler to ensure that children are\n    // updated in a parent's updated hook.\n\n  };\n\n  Vue.prototype.$forceUpdate = function () {\n    var vm = this;\n\n    if (vm._watcher) {\n      vm._watcher.update();\n    }\n  };\n\n  Vue.prototype.$destroy = function () {\n    var vm = this;\n\n    if (vm._isBeingDestroyed) {\n      return;\n    }\n\n    callHook(vm, 'beforeDestroy');\n    vm._isBeingDestroyed = true; // remove self from parent\n\n    var parent = vm.$parent;\n\n    if (parent && !parent._isBeingDestroyed && !vm.$options.abstract) {\n      remove(parent.$children, vm);\n    } // teardown watchers\n\n\n    if (vm._watcher) {\n      vm._watcher.teardown();\n    }\n\n    var i = vm._watchers.length;\n\n    while (i--) {\n      vm._watchers[i].teardown();\n    } // remove reference from data ob\n    // frozen object may not have observer.\n\n\n    if (vm._data.__ob__) {\n      vm._data.__ob__.vmCount--;\n    } // call the last hook...\n\n\n    vm._isDestroyed = true; // invoke destroy hooks on current rendered tree\n\n    vm.__patch__(vm._vnode, null); // fire destroyed hook\n\n\n    callHook(vm, 'destroyed'); // turn off all instance listeners.\n\n    vm.$off(); // remove __vue__ reference\n\n    if (vm.$el) {\n      vm.$el.__vue__ = null;\n    } // release circular reference (#6759)\n\n\n    if (vm.$vnode) {\n      vm.$vnode.parent = null;\n    }\n  };\n}\n\nfunction mountComponent(vm, el, hydrating) {\n  vm.$el = el;\n\n  if (!vm.$options.render) {\n    vm.$options.render = createEmptyVNode;\n\n    if (true) {\n      /* istanbul ignore if */\n      if (vm.$options.template && vm.$options.template.charAt(0) !== '#' || vm.$options.el || el) {\n        warn('You are using the runtime-only build of Vue where the template ' + 'compiler is not available. Either pre-compile the templates into ' + 'render functions, or use the compiler-included build.', vm);\n      } else {\n        warn('Failed to mount component: template or render function not defined.', vm);\n      }\n    }\n  }\n\n  callHook(vm, 'beforeMount');\n  var updateComponent;\n  /* istanbul ignore if */\n\n  if ( true && config.performance && mark) {\n    updateComponent = function () {\n      var name = vm._name;\n      var id = vm._uid;\n      var startTag = \"vue-perf-start:\" + id;\n      var endTag = \"vue-perf-end:\" + id;\n      mark(startTag);\n\n      var vnode = vm._render();\n\n      mark(endTag);\n      measure(\"vue \" + name + \" render\", startTag, endTag);\n      mark(startTag);\n\n      vm._update(vnode, hydrating);\n\n      mark(endTag);\n      measure(\"vue \" + name + \" patch\", startTag, endTag);\n    };\n  } else {\n    updateComponent = function () {\n      vm._update(vm._render(), hydrating);\n    };\n  } // we set this to vm._watcher inside the watcher's constructor\n  // since the watcher's initial patch may call $forceUpdate (e.g. inside child\n  // component's mounted hook), which relies on vm._watcher being already defined\n\n\n  new Watcher(vm, updateComponent, noop, {\n    before: function before() {\n      if (vm._isMounted && !vm._isDestroyed) {\n        callHook(vm, 'beforeUpdate');\n      }\n    }\n  }, true\n  /* isRenderWatcher */\n  );\n  hydrating = false; // manually mounted instance, call mounted on self\n  // mounted is called for render-created child components in its inserted hook\n\n  if (vm.$vnode == null) {\n    vm._isMounted = true;\n    callHook(vm, 'mounted');\n  }\n\n  return vm;\n}\n\nfunction updateChildComponent(vm, propsData, listeners, parentVnode, renderChildren) {\n  if (true) {\n    isUpdatingChildComponent = true;\n  } // determine whether component has slot children\n  // we need to do this before overwriting $options._renderChildren.\n  // check if there are dynamic scopedSlots (hand-written or compiled but with\n  // dynamic slot names). Static scoped slots compiled from template has the\n  // \"$stable\" marker.\n\n\n  var newScopedSlots = parentVnode.data.scopedSlots;\n  var oldScopedSlots = vm.$scopedSlots;\n  var hasDynamicScopedSlot = !!(newScopedSlots && !newScopedSlots.$stable || oldScopedSlots !== emptyObject && !oldScopedSlots.$stable || newScopedSlots && vm.$scopedSlots.$key !== newScopedSlots.$key || !newScopedSlots && vm.$scopedSlots.$key); // Any static slot children from the parent may have changed during parent's\n  // update. Dynamic scoped slots may also have changed. In such cases, a forced\n  // update is necessary to ensure correctness.\n\n  var needsForceUpdate = !!(renderChildren || // has new static slots\n  vm.$options._renderChildren || // has old static slots\n  hasDynamicScopedSlot);\n  vm.$options._parentVnode = parentVnode;\n  vm.$vnode = parentVnode; // update vm's placeholder node without re-render\n\n  if (vm._vnode) {\n    // update child tree's parent\n    vm._vnode.parent = parentVnode;\n  }\n\n  vm.$options._renderChildren = renderChildren; // update $attrs and $listeners hash\n  // these are also reactive so they may trigger child update if the child\n  // used them during render\n\n  vm.$attrs = parentVnode.data.attrs || emptyObject;\n  vm.$listeners = listeners || emptyObject; // update props\n\n  if (propsData && vm.$options.props) {\n    toggleObserving(false);\n    var props = vm._props;\n    var propKeys = vm.$options._propKeys || [];\n\n    for (var i = 0; i < propKeys.length; i++) {\n      var key = propKeys[i];\n      var propOptions = vm.$options.props; // wtf flow?\n\n      props[key] = validateProp(key, propOptions, propsData, vm);\n    }\n\n    toggleObserving(true); // keep a copy of raw propsData\n\n    vm.$options.propsData = propsData;\n  } // update listeners\n\n\n  listeners = listeners || emptyObject;\n  var oldListeners = vm.$options._parentListeners;\n  vm.$options._parentListeners = listeners;\n  updateComponentListeners(vm, listeners, oldListeners); // resolve slots + force update if has children\n\n  if (needsForceUpdate) {\n    vm.$slots = resolveSlots(renderChildren, parentVnode.context);\n    vm.$forceUpdate();\n  }\n\n  if (true) {\n    isUpdatingChildComponent = false;\n  }\n}\n\nfunction isInInactiveTree(vm) {\n  while (vm && (vm = vm.$parent)) {\n    if (vm._inactive) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction activateChildComponent(vm, direct) {\n  if (direct) {\n    vm._directInactive = false;\n\n    if (isInInactiveTree(vm)) {\n      return;\n    }\n  } else if (vm._directInactive) {\n    return;\n  }\n\n  if (vm._inactive || vm._inactive === null) {\n    vm._inactive = false;\n\n    for (var i = 0; i < vm.$children.length; i++) {\n      activateChildComponent(vm.$children[i]);\n    }\n\n    callHook(vm, 'activated');\n  }\n}\n\nfunction deactivateChildComponent(vm, direct) {\n  if (direct) {\n    vm._directInactive = true;\n\n    if (isInInactiveTree(vm)) {\n      return;\n    }\n  }\n\n  if (!vm._inactive) {\n    vm._inactive = true;\n\n    for (var i = 0; i < vm.$children.length; i++) {\n      deactivateChildComponent(vm.$children[i]);\n    }\n\n    callHook(vm, 'deactivated');\n  }\n}\n\nfunction callHook(vm, hook) {\n  // #7573 disable dep collection when invoking lifecycle hooks\n  pushTarget();\n  var handlers = vm.$options[hook];\n  var info = hook + \" hook\";\n\n  if (handlers) {\n    for (var i = 0, j = handlers.length; i < j; i++) {\n      invokeWithErrorHandling(handlers[i], vm, null, vm, info);\n    }\n  }\n\n  if (vm._hasHookEvent) {\n    vm.$emit('hook:' + hook);\n  }\n\n  popTarget();\n}\n/*  */\n\n\nvar MAX_UPDATE_COUNT = 100;\nvar queue = [];\nvar activatedChildren = [];\nvar has = {};\nvar circular = {};\nvar waiting = false;\nvar flushing = false;\nvar index = 0;\n/**\n * Reset the scheduler's state.\n */\n\nfunction resetSchedulerState() {\n  index = queue.length = activatedChildren.length = 0;\n  has = {};\n\n  if (true) {\n    circular = {};\n  }\n\n  waiting = flushing = false;\n} // Async edge case #6566 requires saving the timestamp when event listeners are\n// attached. However, calling performance.now() has a perf overhead especially\n// if the page has thousands of event listeners. Instead, we take a timestamp\n// every time the scheduler flushes and use that for all event listeners\n// attached during that flush.\n\n\nvar currentFlushTimestamp = 0; // Async edge case fix requires storing an event listener's attach timestamp.\n\nvar getNow = Date.now; // Determine what event timestamp the browser is using. Annoyingly, the\n// timestamp can either be hi-res (relative to page load) or low-res\n// (relative to UNIX epoch), so in order to compare time we have to use the\n// same timestamp type when saving the flush timestamp.\n// All IE versions use low-res event timestamps, and have problematic clock\n// implementations (#9632)\n\nif (inBrowser && !isIE) {\n  var performance = window.performance;\n\n  if (performance && typeof performance.now === 'function' && getNow() > document.createEvent('Event').timeStamp) {\n    // if the event timestamp, although evaluated AFTER the Date.now(), is\n    // smaller than it, it means the event is using a hi-res timestamp,\n    // and we need to use the hi-res version for event listener timestamps as\n    // well.\n    getNow = function () {\n      return performance.now();\n    };\n  }\n}\n/**\n * Flush both queues and run the watchers.\n */\n\n\nfunction flushSchedulerQueue() {\n  currentFlushTimestamp = getNow();\n  flushing = true;\n  var watcher, id; // Sort queue before flush.\n  // This ensures that:\n  // 1. Components are updated from parent to child. (because parent is always\n  //    created before the child)\n  // 2. A component's user watchers are run before its render watcher (because\n  //    user watchers are created before the render watcher)\n  // 3. If a component is destroyed during a parent component's watcher run,\n  //    its watchers can be skipped.\n\n  queue.sort(function (a, b) {\n    return a.id - b.id;\n  }); // do not cache length because more watchers might be pushed\n  // as we run existing watchers\n\n  for (index = 0; index < queue.length; index++) {\n    watcher = queue[index];\n\n    if (watcher.before) {\n      watcher.before();\n    }\n\n    id = watcher.id;\n    has[id] = null;\n    watcher.run(); // in dev build, check and stop circular updates.\n\n    if ( true && has[id] != null) {\n      circular[id] = (circular[id] || 0) + 1;\n\n      if (circular[id] > MAX_UPDATE_COUNT) {\n        warn('You may have an infinite update loop ' + (watcher.user ? \"in watcher with expression \\\"\" + watcher.expression + \"\\\"\" : \"in a component render function.\"), watcher.vm);\n        break;\n      }\n    }\n  } // keep copies of post queues before resetting state\n\n\n  var activatedQueue = activatedChildren.slice();\n  var updatedQueue = queue.slice();\n  resetSchedulerState(); // call component updated and activated hooks\n\n  callActivatedHooks(activatedQueue);\n  callUpdatedHooks(updatedQueue); // devtool hook\n\n  /* istanbul ignore if */\n\n  if (devtools && config.devtools) {\n    devtools.emit('flush');\n  }\n}\n\nfunction callUpdatedHooks(queue) {\n  var i = queue.length;\n\n  while (i--) {\n    var watcher = queue[i];\n    var vm = watcher.vm;\n\n    if (vm._watcher === watcher && vm._isMounted && !vm._isDestroyed) {\n      callHook(vm, 'updated');\n    }\n  }\n}\n/**\n * Queue a kept-alive component that was activated during patch.\n * The queue will be processed after the entire tree has been patched.\n */\n\n\nfunction queueActivatedComponent(vm) {\n  // setting _inactive to false here so that a render function can\n  // rely on checking whether it's in an inactive tree (e.g. router-view)\n  vm._inactive = false;\n  activatedChildren.push(vm);\n}\n\nfunction callActivatedHooks(queue) {\n  for (var i = 0; i < queue.length; i++) {\n    queue[i]._inactive = true;\n    activateChildComponent(queue[i], true\n    /* true */\n    );\n  }\n}\n/**\n * Push a watcher into the watcher queue.\n * Jobs with duplicate IDs will be skipped unless it's\n * pushed when the queue is being flushed.\n */\n\n\nfunction queueWatcher(watcher) {\n  var id = watcher.id;\n\n  if (has[id] == null) {\n    has[id] = true;\n\n    if (!flushing) {\n      queue.push(watcher);\n    } else {\n      // if already flushing, splice the watcher based on its id\n      // if already past its id, it will be run next immediately.\n      var i = queue.length - 1;\n\n      while (i > index && queue[i].id > watcher.id) {\n        i--;\n      }\n\n      queue.splice(i + 1, 0, watcher);\n    } // queue the flush\n\n\n    if (!waiting) {\n      waiting = true;\n\n      if ( true && !config.async) {\n        flushSchedulerQueue();\n        return;\n      }\n\n      nextTick(flushSchedulerQueue);\n    }\n  }\n}\n/*  */\n\n\nvar uid$2 = 0;\n/**\n * A watcher parses an expression, collects dependencies,\n * and fires callback when the expression value changes.\n * This is used for both the $watch() api and directives.\n */\n\nvar Watcher = function Watcher(vm, expOrFn, cb, options, isRenderWatcher) {\n  this.vm = vm;\n\n  if (isRenderWatcher) {\n    vm._watcher = this;\n  }\n\n  vm._watchers.push(this); // options\n\n\n  if (options) {\n    this.deep = !!options.deep;\n    this.user = !!options.user;\n    this.lazy = !!options.lazy;\n    this.sync = !!options.sync;\n    this.before = options.before;\n  } else {\n    this.deep = this.user = this.lazy = this.sync = false;\n  }\n\n  this.cb = cb;\n  this.id = ++uid$2; // uid for batching\n\n  this.active = true;\n  this.dirty = this.lazy; // for lazy watchers\n\n  this.deps = [];\n  this.newDeps = [];\n  this.depIds = new _Set();\n  this.newDepIds = new _Set();\n  this.expression =  true ? expOrFn.toString() : undefined; // parse expression for getter\n\n  if (typeof expOrFn === 'function') {\n    this.getter = expOrFn;\n  } else {\n    this.getter = parsePath(expOrFn);\n\n    if (!this.getter) {\n      this.getter = noop;\n       true && warn(\"Failed watching path: \\\"\" + expOrFn + \"\\\" \" + 'Watcher only accepts simple dot-delimited paths. ' + 'For full control, use a function instead.', vm);\n    }\n  }\n\n  this.value = this.lazy ? undefined : this.get();\n};\n/**\n * Evaluate the getter, and re-collect dependencies.\n */\n\n\nWatcher.prototype.get = function get() {\n  pushTarget(this);\n  var value;\n  var vm = this.vm;\n\n  try {\n    value = this.getter.call(vm, vm);\n  } catch (e) {\n    if (this.user) {\n      handleError(e, vm, \"getter for watcher \\\"\" + this.expression + \"\\\"\");\n    } else {\n      throw e;\n    }\n  } finally {\n    // \"touch\" every property so they are all tracked as\n    // dependencies for deep watching\n    if (this.deep) {\n      traverse(value);\n    }\n\n    popTarget();\n    this.cleanupDeps();\n  }\n\n  return value;\n};\n/**\n * Add a dependency to this directive.\n */\n\n\nWatcher.prototype.addDep = function addDep(dep) {\n  var id = dep.id;\n\n  if (!this.newDepIds.has(id)) {\n    this.newDepIds.add(id);\n    this.newDeps.push(dep);\n\n    if (!this.depIds.has(id)) {\n      dep.addSub(this);\n    }\n  }\n};\n/**\n * Clean up for dependency collection.\n */\n\n\nWatcher.prototype.cleanupDeps = function cleanupDeps() {\n  var i = this.deps.length;\n\n  while (i--) {\n    var dep = this.deps[i];\n\n    if (!this.newDepIds.has(dep.id)) {\n      dep.removeSub(this);\n    }\n  }\n\n  var tmp = this.depIds;\n  this.depIds = this.newDepIds;\n  this.newDepIds = tmp;\n  this.newDepIds.clear();\n  tmp = this.deps;\n  this.deps = this.newDeps;\n  this.newDeps = tmp;\n  this.newDeps.length = 0;\n};\n/**\n * Subscriber interface.\n * Will be called when a dependency changes.\n */\n\n\nWatcher.prototype.update = function update() {\n  /* istanbul ignore else */\n  if (this.lazy) {\n    this.dirty = true;\n  } else if (this.sync) {\n    this.run();\n  } else {\n    queueWatcher(this);\n  }\n};\n/**\n * Scheduler job interface.\n * Will be called by the scheduler.\n */\n\n\nWatcher.prototype.run = function run() {\n  if (this.active) {\n    var value = this.get();\n\n    if (value !== this.value || // Deep watchers and watchers on Object/Arrays should fire even\n    // when the value is the same, because the value may\n    // have mutated.\n    isObject(value) || this.deep) {\n      // set new value\n      var oldValue = this.value;\n      this.value = value;\n\n      if (this.user) {\n        var info = \"callback for watcher \\\"\" + this.expression + \"\\\"\";\n        invokeWithErrorHandling(this.cb, this.vm, [value, oldValue], this.vm, info);\n      } else {\n        this.cb.call(this.vm, value, oldValue);\n      }\n    }\n  }\n};\n/**\n * Evaluate the value of the watcher.\n * This only gets called for lazy watchers.\n */\n\n\nWatcher.prototype.evaluate = function evaluate() {\n  this.value = this.get();\n  this.dirty = false;\n};\n/**\n * Depend on all deps collected by this watcher.\n */\n\n\nWatcher.prototype.depend = function depend() {\n  var i = this.deps.length;\n\n  while (i--) {\n    this.deps[i].depend();\n  }\n};\n/**\n * Remove self from all dependencies' subscriber list.\n */\n\n\nWatcher.prototype.teardown = function teardown() {\n  if (this.active) {\n    // remove self from vm's watcher list\n    // this is a somewhat expensive operation so we skip it\n    // if the vm is being destroyed.\n    if (!this.vm._isBeingDestroyed) {\n      remove(this.vm._watchers, this);\n    }\n\n    var i = this.deps.length;\n\n    while (i--) {\n      this.deps[i].removeSub(this);\n    }\n\n    this.active = false;\n  }\n};\n/*  */\n\n\nvar sharedPropertyDefinition = {\n  enumerable: true,\n  configurable: true,\n  get: noop,\n  set: noop\n};\n\nfunction proxy(target, sourceKey, key) {\n  sharedPropertyDefinition.get = function proxyGetter() {\n    return this[sourceKey][key];\n  };\n\n  sharedPropertyDefinition.set = function proxySetter(val) {\n    this[sourceKey][key] = val;\n  };\n\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction initState(vm) {\n  vm._watchers = [];\n  var opts = vm.$options;\n\n  if (opts.props) {\n    initProps(vm, opts.props);\n  }\n\n  if (opts.methods) {\n    initMethods(vm, opts.methods);\n  }\n\n  if (opts.data) {\n    initData(vm);\n  } else {\n    observe(vm._data = {}, true\n    /* asRootData */\n    );\n  }\n\n  if (opts.computed) {\n    initComputed(vm, opts.computed);\n  }\n\n  if (opts.watch && opts.watch !== nativeWatch) {\n    initWatch(vm, opts.watch);\n  }\n}\n\nfunction initProps(vm, propsOptions) {\n  var propsData = vm.$options.propsData || {};\n  var props = vm._props = {}; // cache prop keys so that future props updates can iterate using Array\n  // instead of dynamic object key enumeration.\n\n  var keys = vm.$options._propKeys = [];\n  var isRoot = !vm.$parent; // root instance props should be converted\n\n  if (!isRoot) {\n    toggleObserving(false);\n  }\n\n  var loop = function (key) {\n    keys.push(key);\n    var value = validateProp(key, propsOptions, propsData, vm);\n    /* istanbul ignore else */\n\n    if (true) {\n      var hyphenatedKey = hyphenate(key);\n\n      if (isReservedAttribute(hyphenatedKey) || config.isReservedAttr(hyphenatedKey)) {\n        warn(\"\\\"\" + hyphenatedKey + \"\\\" is a reserved attribute and cannot be used as component prop.\", vm);\n      }\n\n      defineReactive$$1(props, key, value, function () {\n        if (!isRoot && !isUpdatingChildComponent) {\n          warn(\"Avoid mutating a prop directly since the value will be \" + \"overwritten whenever the parent component re-renders. \" + \"Instead, use a data or computed property based on the prop's \" + \"value. Prop being mutated: \\\"\" + key + \"\\\"\", vm);\n        }\n      });\n    } else {} // static props are already proxied on the component's prototype\n    // during Vue.extend(). We only need to proxy props defined at\n    // instantiation here.\n\n\n    if (!(key in vm)) {\n      proxy(vm, \"_props\", key);\n    }\n  };\n\n  for (var key in propsOptions) loop(key);\n\n  toggleObserving(true);\n}\n\nfunction initData(vm) {\n  var data = vm.$options.data;\n  data = vm._data = typeof data === 'function' ? getData(data, vm) : data || {};\n\n  if (!isPlainObject(data)) {\n    data = {};\n     true && warn('data functions should return an object:\\n' + 'https://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function', vm);\n  } // proxy data on instance\n\n\n  var keys = Object.keys(data);\n  var props = vm.$options.props;\n  var methods = vm.$options.methods;\n  var i = keys.length;\n\n  while (i--) {\n    var key = keys[i];\n\n    if (true) {\n      if (methods && hasOwn(methods, key)) {\n        warn(\"Method \\\"\" + key + \"\\\" has already been defined as a data property.\", vm);\n      }\n    }\n\n    if (props && hasOwn(props, key)) {\n       true && warn(\"The data property \\\"\" + key + \"\\\" is already declared as a prop. \" + \"Use prop default value instead.\", vm);\n    } else if (!isReserved(key)) {\n      proxy(vm, \"_data\", key);\n    }\n  } // observe data\n\n\n  observe(data, true\n  /* asRootData */\n  );\n}\n\nfunction getData(data, vm) {\n  // #7573 disable dep collection when invoking data getters\n  pushTarget();\n\n  try {\n    return data.call(vm, vm);\n  } catch (e) {\n    handleError(e, vm, \"data()\");\n    return {};\n  } finally {\n    popTarget();\n  }\n}\n\nvar computedWatcherOptions = {\n  lazy: true\n};\n\nfunction initComputed(vm, computed) {\n  // $flow-disable-line\n  var watchers = vm._computedWatchers = Object.create(null); // computed properties are just getters during SSR\n\n  var isSSR = isServerRendering();\n\n  for (var key in computed) {\n    var userDef = computed[key];\n    var getter = typeof userDef === 'function' ? userDef : userDef.get;\n\n    if ( true && getter == null) {\n      warn(\"Getter is missing for computed property \\\"\" + key + \"\\\".\", vm);\n    }\n\n    if (!isSSR) {\n      // create internal watcher for the computed property.\n      watchers[key] = new Watcher(vm, getter || noop, noop, computedWatcherOptions);\n    } // component-defined computed properties are already defined on the\n    // component prototype. We only need to define computed properties defined\n    // at instantiation here.\n\n\n    if (!(key in vm)) {\n      defineComputed(vm, key, userDef);\n    } else if (true) {\n      if (key in vm.$data) {\n        warn(\"The computed property \\\"\" + key + \"\\\" is already defined in data.\", vm);\n      } else if (vm.$options.props && key in vm.$options.props) {\n        warn(\"The computed property \\\"\" + key + \"\\\" is already defined as a prop.\", vm);\n      } else if (vm.$options.methods && key in vm.$options.methods) {\n        warn(\"The computed property \\\"\" + key + \"\\\" is already defined as a method.\", vm);\n      }\n    }\n  }\n}\n\nfunction defineComputed(target, key, userDef) {\n  var shouldCache = !isServerRendering();\n\n  if (typeof userDef === 'function') {\n    sharedPropertyDefinition.get = shouldCache ? createComputedGetter(key) : createGetterInvoker(userDef);\n    sharedPropertyDefinition.set = noop;\n  } else {\n    sharedPropertyDefinition.get = userDef.get ? shouldCache && userDef.cache !== false ? createComputedGetter(key) : createGetterInvoker(userDef.get) : noop;\n    sharedPropertyDefinition.set = userDef.set || noop;\n  }\n\n  if ( true && sharedPropertyDefinition.set === noop) {\n    sharedPropertyDefinition.set = function () {\n      warn(\"Computed property \\\"\" + key + \"\\\" was assigned to but it has no setter.\", this);\n    };\n  }\n\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction createComputedGetter(key) {\n  return function computedGetter() {\n    var watcher = this._computedWatchers && this._computedWatchers[key];\n\n    if (watcher) {\n      if (watcher.dirty) {\n        watcher.evaluate();\n      }\n\n      if (Dep.target) {\n        watcher.depend();\n      }\n\n      return watcher.value;\n    }\n  };\n}\n\nfunction createGetterInvoker(fn) {\n  return function computedGetter() {\n    return fn.call(this, this);\n  };\n}\n\nfunction initMethods(vm, methods) {\n  var props = vm.$options.props;\n\n  for (var key in methods) {\n    if (true) {\n      if (typeof methods[key] !== 'function') {\n        warn(\"Method \\\"\" + key + \"\\\" has type \\\"\" + typeof methods[key] + \"\\\" in the component definition. \" + \"Did you reference the function correctly?\", vm);\n      }\n\n      if (props && hasOwn(props, key)) {\n        warn(\"Method \\\"\" + key + \"\\\" has already been defined as a prop.\", vm);\n      }\n\n      if (key in vm && isReserved(key)) {\n        warn(\"Method \\\"\" + key + \"\\\" conflicts with an existing Vue instance method. \" + \"Avoid defining component methods that start with _ or $.\");\n      }\n    }\n\n    vm[key] = typeof methods[key] !== 'function' ? noop : bind(methods[key], vm);\n  }\n}\n\nfunction initWatch(vm, watch) {\n  for (var key in watch) {\n    var handler = watch[key];\n\n    if (Array.isArray(handler)) {\n      for (var i = 0; i < handler.length; i++) {\n        createWatcher(vm, key, handler[i]);\n      }\n    } else {\n      createWatcher(vm, key, handler);\n    }\n  }\n}\n\nfunction createWatcher(vm, expOrFn, handler, options) {\n  if (isPlainObject(handler)) {\n    options = handler;\n    handler = handler.handler;\n  }\n\n  if (typeof handler === 'string') {\n    handler = vm[handler];\n  }\n\n  return vm.$watch(expOrFn, handler, options);\n}\n\nfunction stateMixin(Vue) {\n  // flow somehow has problems with directly declared definition object\n  // when using Object.defineProperty, so we have to procedurally build up\n  // the object here.\n  var dataDef = {};\n\n  dataDef.get = function () {\n    return this._data;\n  };\n\n  var propsDef = {};\n\n  propsDef.get = function () {\n    return this._props;\n  };\n\n  if (true) {\n    dataDef.set = function () {\n      warn('Avoid replacing instance root $data. ' + 'Use nested data properties instead.', this);\n    };\n\n    propsDef.set = function () {\n      warn(\"$props is readonly.\", this);\n    };\n  }\n\n  Object.defineProperty(Vue.prototype, '$data', dataDef);\n  Object.defineProperty(Vue.prototype, '$props', propsDef);\n  Vue.prototype.$set = set;\n  Vue.prototype.$delete = del;\n\n  Vue.prototype.$watch = function (expOrFn, cb, options) {\n    var vm = this;\n\n    if (isPlainObject(cb)) {\n      return createWatcher(vm, expOrFn, cb, options);\n    }\n\n    options = options || {};\n    options.user = true;\n    var watcher = new Watcher(vm, expOrFn, cb, options);\n\n    if (options.immediate) {\n      var info = \"callback for immediate watcher \\\"\" + watcher.expression + \"\\\"\";\n      pushTarget();\n      invokeWithErrorHandling(cb, vm, [watcher.value], vm, info);\n      popTarget();\n    }\n\n    return function unwatchFn() {\n      watcher.teardown();\n    };\n  };\n}\n/*  */\n\n\nvar uid$3 = 0;\n\nfunction initMixin(Vue) {\n  Vue.prototype._init = function (options) {\n    var vm = this; // a uid\n\n    vm._uid = uid$3++;\n    var startTag, endTag;\n    /* istanbul ignore if */\n\n    if ( true && config.performance && mark) {\n      startTag = \"vue-perf-start:\" + vm._uid;\n      endTag = \"vue-perf-end:\" + vm._uid;\n      mark(startTag);\n    } // a flag to avoid this being observed\n\n\n    vm._isVue = true; // merge options\n\n    if (options && options._isComponent) {\n      // optimize internal component instantiation\n      // since dynamic options merging is pretty slow, and none of the\n      // internal component options needs special treatment.\n      initInternalComponent(vm, options);\n    } else {\n      vm.$options = mergeOptions(resolveConstructorOptions(vm.constructor), options || {}, vm);\n    }\n    /* istanbul ignore else */\n\n\n    if (true) {\n      initProxy(vm);\n    } else {} // expose real self\n\n\n    vm._self = vm;\n    initLifecycle(vm);\n    initEvents(vm);\n    initRender(vm);\n    callHook(vm, 'beforeCreate');\n    initInjections(vm); // resolve injections before data/props\n\n    initState(vm);\n    initProvide(vm); // resolve provide after data/props\n\n    callHook(vm, 'created');\n    /* istanbul ignore if */\n\n    if ( true && config.performance && mark) {\n      vm._name = formatComponentName(vm, false);\n      mark(endTag);\n      measure(\"vue \" + vm._name + \" init\", startTag, endTag);\n    }\n\n    if (vm.$options.el) {\n      vm.$mount(vm.$options.el);\n    }\n  };\n}\n\nfunction initInternalComponent(vm, options) {\n  var opts = vm.$options = Object.create(vm.constructor.options); // doing this because it's faster than dynamic enumeration.\n\n  var parentVnode = options._parentVnode;\n  opts.parent = options.parent;\n  opts._parentVnode = parentVnode;\n  var vnodeComponentOptions = parentVnode.componentOptions;\n  opts.propsData = vnodeComponentOptions.propsData;\n  opts._parentListeners = vnodeComponentOptions.listeners;\n  opts._renderChildren = vnodeComponentOptions.children;\n  opts._componentTag = vnodeComponentOptions.tag;\n\n  if (options.render) {\n    opts.render = options.render;\n    opts.staticRenderFns = options.staticRenderFns;\n  }\n}\n\nfunction resolveConstructorOptions(Ctor) {\n  var options = Ctor.options;\n\n  if (Ctor.super) {\n    var superOptions = resolveConstructorOptions(Ctor.super);\n    var cachedSuperOptions = Ctor.superOptions;\n\n    if (superOptions !== cachedSuperOptions) {\n      // super option changed,\n      // need to resolve new options.\n      Ctor.superOptions = superOptions; // check if there are any late-modified/attached options (#4976)\n\n      var modifiedOptions = resolveModifiedOptions(Ctor); // update base extend options\n\n      if (modifiedOptions) {\n        extend(Ctor.extendOptions, modifiedOptions);\n      }\n\n      options = Ctor.options = mergeOptions(superOptions, Ctor.extendOptions);\n\n      if (options.name) {\n        options.components[options.name] = Ctor;\n      }\n    }\n  }\n\n  return options;\n}\n\nfunction resolveModifiedOptions(Ctor) {\n  var modified;\n  var latest = Ctor.options;\n  var sealed = Ctor.sealedOptions;\n\n  for (var key in latest) {\n    if (latest[key] !== sealed[key]) {\n      if (!modified) {\n        modified = {};\n      }\n\n      modified[key] = latest[key];\n    }\n  }\n\n  return modified;\n}\n\nfunction Vue(options) {\n  if ( true && !(this instanceof Vue)) {\n    warn('Vue is a constructor and should be called with the `new` keyword');\n  }\n\n  this._init(options);\n}\n\ninitMixin(Vue);\nstateMixin(Vue);\neventsMixin(Vue);\nlifecycleMixin(Vue);\nrenderMixin(Vue);\n/*  */\n\nfunction initUse(Vue) {\n  Vue.use = function (plugin) {\n    var installedPlugins = this._installedPlugins || (this._installedPlugins = []);\n\n    if (installedPlugins.indexOf(plugin) > -1) {\n      return this;\n    } // additional parameters\n\n\n    var args = toArray(arguments, 1);\n    args.unshift(this);\n\n    if (typeof plugin.install === 'function') {\n      plugin.install.apply(plugin, args);\n    } else if (typeof plugin === 'function') {\n      plugin.apply(null, args);\n    }\n\n    installedPlugins.push(plugin);\n    return this;\n  };\n}\n/*  */\n\n\nfunction initMixin$1(Vue) {\n  Vue.mixin = function (mixin) {\n    this.options = mergeOptions(this.options, mixin);\n    return this;\n  };\n}\n/*  */\n\n\nfunction initExtend(Vue) {\n  /**\n   * Each instance constructor, including Vue, has a unique\n   * cid. This enables us to create wrapped \"child\n   * constructors\" for prototypal inheritance and cache them.\n   */\n  Vue.cid = 0;\n  var cid = 1;\n  /**\n   * Class inheritance\n   */\n\n  Vue.extend = function (extendOptions) {\n    extendOptions = extendOptions || {};\n    var Super = this;\n    var SuperId = Super.cid;\n    var cachedCtors = extendOptions._Ctor || (extendOptions._Ctor = {});\n\n    if (cachedCtors[SuperId]) {\n      return cachedCtors[SuperId];\n    }\n\n    var name = extendOptions.name || Super.options.name;\n\n    if ( true && name) {\n      validateComponentName(name);\n    }\n\n    var Sub = function VueComponent(options) {\n      this._init(options);\n    };\n\n    Sub.prototype = Object.create(Super.prototype);\n    Sub.prototype.constructor = Sub;\n    Sub.cid = cid++;\n    Sub.options = mergeOptions(Super.options, extendOptions);\n    Sub['super'] = Super; // For props and computed properties, we define the proxy getters on\n    // the Vue instances at extension time, on the extended prototype. This\n    // avoids Object.defineProperty calls for each instance created.\n\n    if (Sub.options.props) {\n      initProps$1(Sub);\n    }\n\n    if (Sub.options.computed) {\n      initComputed$1(Sub);\n    } // allow further extension/mixin/plugin usage\n\n\n    Sub.extend = Super.extend;\n    Sub.mixin = Super.mixin;\n    Sub.use = Super.use; // create asset registers, so extended classes\n    // can have their private assets too.\n\n    ASSET_TYPES.forEach(function (type) {\n      Sub[type] = Super[type];\n    }); // enable recursive self-lookup\n\n    if (name) {\n      Sub.options.components[name] = Sub;\n    } // keep a reference to the super options at extension time.\n    // later at instantiation we can check if Super's options have\n    // been updated.\n\n\n    Sub.superOptions = Super.options;\n    Sub.extendOptions = extendOptions;\n    Sub.sealedOptions = extend({}, Sub.options); // cache constructor\n\n    cachedCtors[SuperId] = Sub;\n    return Sub;\n  };\n}\n\nfunction initProps$1(Comp) {\n  var props = Comp.options.props;\n\n  for (var key in props) {\n    proxy(Comp.prototype, \"_props\", key);\n  }\n}\n\nfunction initComputed$1(Comp) {\n  var computed = Comp.options.computed;\n\n  for (var key in computed) {\n    defineComputed(Comp.prototype, key, computed[key]);\n  }\n}\n/*  */\n\n\nfunction initAssetRegisters(Vue) {\n  /**\n   * Create asset registration methods.\n   */\n  ASSET_TYPES.forEach(function (type) {\n    Vue[type] = function (id, definition) {\n      if (!definition) {\n        return this.options[type + 's'][id];\n      } else {\n        /* istanbul ignore if */\n        if ( true && type === 'component') {\n          validateComponentName(id);\n        }\n\n        if (type === 'component' && isPlainObject(definition)) {\n          definition.name = definition.name || id;\n          definition = this.options._base.extend(definition);\n        }\n\n        if (type === 'directive' && typeof definition === 'function') {\n          definition = {\n            bind: definition,\n            update: definition\n          };\n        }\n\n        this.options[type + 's'][id] = definition;\n        return definition;\n      }\n    };\n  });\n}\n/*  */\n\n\nfunction getComponentName(opts) {\n  return opts && (opts.Ctor.options.name || opts.tag);\n}\n\nfunction matches(pattern, name) {\n  if (Array.isArray(pattern)) {\n    return pattern.indexOf(name) > -1;\n  } else if (typeof pattern === 'string') {\n    return pattern.split(',').indexOf(name) > -1;\n  } else if (isRegExp(pattern)) {\n    return pattern.test(name);\n  }\n  /* istanbul ignore next */\n\n\n  return false;\n}\n\nfunction pruneCache(keepAliveInstance, filter) {\n  var cache = keepAliveInstance.cache;\n  var keys = keepAliveInstance.keys;\n  var _vnode = keepAliveInstance._vnode;\n\n  for (var key in cache) {\n    var entry = cache[key];\n\n    if (entry) {\n      var name = entry.name;\n\n      if (name && !filter(name)) {\n        pruneCacheEntry(cache, key, keys, _vnode);\n      }\n    }\n  }\n}\n\nfunction pruneCacheEntry(cache, key, keys, current) {\n  var entry = cache[key];\n\n  if (entry && (!current || entry.tag !== current.tag)) {\n    entry.componentInstance.$destroy();\n  }\n\n  cache[key] = null;\n  remove(keys, key);\n}\n\nvar patternTypes = [String, RegExp, Array];\nvar KeepAlive = {\n  name: 'keep-alive',\n  abstract: true,\n  props: {\n    include: patternTypes,\n    exclude: patternTypes,\n    max: [String, Number]\n  },\n  methods: {\n    cacheVNode: function cacheVNode() {\n      var ref = this;\n      var cache = ref.cache;\n      var keys = ref.keys;\n      var vnodeToCache = ref.vnodeToCache;\n      var keyToCache = ref.keyToCache;\n\n      if (vnodeToCache) {\n        var tag = vnodeToCache.tag;\n        var componentInstance = vnodeToCache.componentInstance;\n        var componentOptions = vnodeToCache.componentOptions;\n        cache[keyToCache] = {\n          name: getComponentName(componentOptions),\n          tag: tag,\n          componentInstance: componentInstance\n        };\n        keys.push(keyToCache); // prune oldest entry\n\n        if (this.max && keys.length > parseInt(this.max)) {\n          pruneCacheEntry(cache, keys[0], keys, this._vnode);\n        }\n\n        this.vnodeToCache = null;\n      }\n    }\n  },\n  created: function created() {\n    this.cache = Object.create(null);\n    this.keys = [];\n  },\n  destroyed: function destroyed() {\n    for (var key in this.cache) {\n      pruneCacheEntry(this.cache, key, this.keys);\n    }\n  },\n  mounted: function mounted() {\n    var this$1 = this;\n    this.cacheVNode();\n    this.$watch('include', function (val) {\n      pruneCache(this$1, function (name) {\n        return matches(val, name);\n      });\n    });\n    this.$watch('exclude', function (val) {\n      pruneCache(this$1, function (name) {\n        return !matches(val, name);\n      });\n    });\n  },\n  updated: function updated() {\n    this.cacheVNode();\n  },\n  render: function render() {\n    var slot = this.$slots.default;\n    var vnode = getFirstComponentChild(slot);\n    var componentOptions = vnode && vnode.componentOptions;\n\n    if (componentOptions) {\n      // check pattern\n      var name = getComponentName(componentOptions);\n      var ref = this;\n      var include = ref.include;\n      var exclude = ref.exclude;\n\n      if ( // not included\n      include && (!name || !matches(include, name)) || // excluded\n      exclude && name && matches(exclude, name)) {\n        return vnode;\n      }\n\n      var ref$1 = this;\n      var cache = ref$1.cache;\n      var keys = ref$1.keys;\n      var key = vnode.key == null // same constructor may get registered as different local components\n      // so cid alone is not enough (#3269)\n      ? componentOptions.Ctor.cid + (componentOptions.tag ? \"::\" + componentOptions.tag : '') : vnode.key;\n\n      if (cache[key]) {\n        vnode.componentInstance = cache[key].componentInstance; // make current key freshest\n\n        remove(keys, key);\n        keys.push(key);\n      } else {\n        // delay setting the cache until update\n        this.vnodeToCache = vnode;\n        this.keyToCache = key;\n      }\n\n      vnode.data.keepAlive = true;\n    }\n\n    return vnode || slot && slot[0];\n  }\n};\nvar builtInComponents = {\n  KeepAlive: KeepAlive\n};\n/*  */\n\nfunction initGlobalAPI(Vue) {\n  // config\n  var configDef = {};\n\n  configDef.get = function () {\n    return config;\n  };\n\n  if (true) {\n    configDef.set = function () {\n      warn('Do not replace the Vue.config object, set individual fields instead.');\n    };\n  }\n\n  Object.defineProperty(Vue, 'config', configDef); // exposed util methods.\n  // NOTE: these are not considered part of the public API - avoid relying on\n  // them unless you are aware of the risk.\n\n  Vue.util = {\n    warn: warn,\n    extend: extend,\n    mergeOptions: mergeOptions,\n    defineReactive: defineReactive$$1\n  };\n  Vue.set = set;\n  Vue.delete = del;\n  Vue.nextTick = nextTick; // 2.6 explicit observable API\n\n  Vue.observable = function (obj) {\n    observe(obj);\n    return obj;\n  };\n\n  Vue.options = Object.create(null);\n  ASSET_TYPES.forEach(function (type) {\n    Vue.options[type + 's'] = Object.create(null);\n  }); // this is used to identify the \"base\" constructor to extend all plain-object\n  // components with in Weex's multi-instance scenarios.\n\n  Vue.options._base = Vue;\n  extend(Vue.options.components, builtInComponents);\n  initUse(Vue);\n  initMixin$1(Vue);\n  initExtend(Vue);\n  initAssetRegisters(Vue);\n}\n\ninitGlobalAPI(Vue);\nObject.defineProperty(Vue.prototype, '$isServer', {\n  get: isServerRendering\n});\nObject.defineProperty(Vue.prototype, '$ssrContext', {\n  get: function get() {\n    /* istanbul ignore next */\n    return this.$vnode && this.$vnode.ssrContext;\n  }\n}); // expose FunctionalRenderContext for ssr runtime helper installation\n\nObject.defineProperty(Vue, 'FunctionalRenderContext', {\n  value: FunctionalRenderContext\n});\nVue.version = '2.6.14';\n/*  */\n// these are reserved for web because they are directly compiled away\n// during template compilation\n\nvar isReservedAttr = makeMap('style,class'); // attributes that should be using props for binding\n\nvar acceptValue = makeMap('input,textarea,option,select,progress');\n\nvar mustUseProp = function (tag, type, attr) {\n  return attr === 'value' && acceptValue(tag) && type !== 'button' || attr === 'selected' && tag === 'option' || attr === 'checked' && tag === 'input' || attr === 'muted' && tag === 'video';\n};\n\nvar isEnumeratedAttr = makeMap('contenteditable,draggable,spellcheck');\nvar isValidContentEditableValue = makeMap('events,caret,typing,plaintext-only');\n\nvar convertEnumeratedValue = function (key, value) {\n  return isFalsyAttrValue(value) || value === 'false' ? 'false' // allow arbitrary string value for contenteditable\n  : key === 'contenteditable' && isValidContentEditableValue(value) ? value : 'true';\n};\n\nvar isBooleanAttr = makeMap('allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,' + 'default,defaultchecked,defaultmuted,defaultselected,defer,disabled,' + 'enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,' + 'muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,' + 'required,reversed,scoped,seamless,selected,sortable,' + 'truespeed,typemustmatch,visible');\nvar xlinkNS = 'http://www.w3.org/1999/xlink';\n\nvar isXlink = function (name) {\n  return name.charAt(5) === ':' && name.slice(0, 5) === 'xlink';\n};\n\nvar getXlinkProp = function (name) {\n  return isXlink(name) ? name.slice(6, name.length) : '';\n};\n\nvar isFalsyAttrValue = function (val) {\n  return val == null || val === false;\n};\n/*  */\n\n\nfunction genClassForVnode(vnode) {\n  var data = vnode.data;\n  var parentNode = vnode;\n  var childNode = vnode;\n\n  while (isDef(childNode.componentInstance)) {\n    childNode = childNode.componentInstance._vnode;\n\n    if (childNode && childNode.data) {\n      data = mergeClassData(childNode.data, data);\n    }\n  }\n\n  while (isDef(parentNode = parentNode.parent)) {\n    if (parentNode && parentNode.data) {\n      data = mergeClassData(data, parentNode.data);\n    }\n  }\n\n  return renderClass(data.staticClass, data.class);\n}\n\nfunction mergeClassData(child, parent) {\n  return {\n    staticClass: concat(child.staticClass, parent.staticClass),\n    class: isDef(child.class) ? [child.class, parent.class] : parent.class\n  };\n}\n\nfunction renderClass(staticClass, dynamicClass) {\n  if (isDef(staticClass) || isDef(dynamicClass)) {\n    return concat(staticClass, stringifyClass(dynamicClass));\n  }\n  /* istanbul ignore next */\n\n\n  return '';\n}\n\nfunction concat(a, b) {\n  return a ? b ? a + ' ' + b : a : b || '';\n}\n\nfunction stringifyClass(value) {\n  if (Array.isArray(value)) {\n    return stringifyArray(value);\n  }\n\n  if (isObject(value)) {\n    return stringifyObject(value);\n  }\n\n  if (typeof value === 'string') {\n    return value;\n  }\n  /* istanbul ignore next */\n\n\n  return '';\n}\n\nfunction stringifyArray(value) {\n  var res = '';\n  var stringified;\n\n  for (var i = 0, l = value.length; i < l; i++) {\n    if (isDef(stringified = stringifyClass(value[i])) && stringified !== '') {\n      if (res) {\n        res += ' ';\n      }\n\n      res += stringified;\n    }\n  }\n\n  return res;\n}\n\nfunction stringifyObject(value) {\n  var res = '';\n\n  for (var key in value) {\n    if (value[key]) {\n      if (res) {\n        res += ' ';\n      }\n\n      res += key;\n    }\n  }\n\n  return res;\n}\n/*  */\n\n\nvar namespaceMap = {\n  svg: 'http://www.w3.org/2000/svg',\n  math: 'http://www.w3.org/1998/Math/MathML'\n};\nvar isHTMLTag = makeMap('html,body,base,head,link,meta,style,title,' + 'address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,' + 'div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,' + 'a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,' + 's,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,' + 'embed,object,param,source,canvas,script,noscript,del,ins,' + 'caption,col,colgroup,table,thead,tbody,td,th,tr,' + 'button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,' + 'output,progress,select,textarea,' + 'details,dialog,menu,menuitem,summary,' + 'content,element,shadow,template,blockquote,iframe,tfoot'); // this map is intentionally selective, only covering SVG elements that may\n// contain child elements.\n\nvar isSVG = makeMap('svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,' + 'foreignobject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,' + 'polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view', true);\n\nvar isPreTag = function (tag) {\n  return tag === 'pre';\n};\n\nvar isReservedTag = function (tag) {\n  return isHTMLTag(tag) || isSVG(tag);\n};\n\nfunction getTagNamespace(tag) {\n  if (isSVG(tag)) {\n    return 'svg';\n  } // basic support for MathML\n  // note it doesn't support other MathML elements being component roots\n\n\n  if (tag === 'math') {\n    return 'math';\n  }\n}\n\nvar unknownElementCache = Object.create(null);\n\nfunction isUnknownElement(tag) {\n  /* istanbul ignore if */\n  if (!inBrowser) {\n    return true;\n  }\n\n  if (isReservedTag(tag)) {\n    return false;\n  }\n\n  tag = tag.toLowerCase();\n  /* istanbul ignore if */\n\n  if (unknownElementCache[tag] != null) {\n    return unknownElementCache[tag];\n  }\n\n  var el = document.createElement(tag);\n\n  if (tag.indexOf('-') > -1) {\n    // http://stackoverflow.com/a/28210364/1070244\n    return unknownElementCache[tag] = el.constructor === window.HTMLUnknownElement || el.constructor === window.HTMLElement;\n  } else {\n    return unknownElementCache[tag] = /HTMLUnknownElement/.test(el.toString());\n  }\n}\n\nvar isTextInputType = makeMap('text,number,password,search,email,tel,url');\n/*  */\n\n/**\n * Query an element selector if it's not an element already.\n */\n\nfunction query(el) {\n  if (typeof el === 'string') {\n    var selected = document.querySelector(el);\n\n    if (!selected) {\n       true && warn('Cannot find element: ' + el);\n      return document.createElement('div');\n    }\n\n    return selected;\n  } else {\n    return el;\n  }\n}\n/*  */\n\n\nfunction createElement$1(tagName, vnode) {\n  var elm = document.createElement(tagName);\n\n  if (tagName !== 'select') {\n    return elm;\n  } // false or null will remove the attribute but undefined will not\n\n\n  if (vnode.data && vnode.data.attrs && vnode.data.attrs.multiple !== undefined) {\n    elm.setAttribute('multiple', 'multiple');\n  }\n\n  return elm;\n}\n\nfunction createElementNS(namespace, tagName) {\n  return document.createElementNS(namespaceMap[namespace], tagName);\n}\n\nfunction createTextNode(text) {\n  return document.createTextNode(text);\n}\n\nfunction createComment(text) {\n  return document.createComment(text);\n}\n\nfunction insertBefore(parentNode, newNode, referenceNode) {\n  parentNode.insertBefore(newNode, referenceNode);\n}\n\nfunction removeChild(node, child) {\n  node.removeChild(child);\n}\n\nfunction appendChild(node, child) {\n  node.appendChild(child);\n}\n\nfunction parentNode(node) {\n  return node.parentNode;\n}\n\nfunction nextSibling(node) {\n  return node.nextSibling;\n}\n\nfunction tagName(node) {\n  return node.tagName;\n}\n\nfunction setTextContent(node, text) {\n  node.textContent = text;\n}\n\nfunction setStyleScope(node, scopeId) {\n  node.setAttribute(scopeId, '');\n}\n\nvar nodeOps = /*#__PURE__*/Object.freeze({\n  createElement: createElement$1,\n  createElementNS: createElementNS,\n  createTextNode: createTextNode,\n  createComment: createComment,\n  insertBefore: insertBefore,\n  removeChild: removeChild,\n  appendChild: appendChild,\n  parentNode: parentNode,\n  nextSibling: nextSibling,\n  tagName: tagName,\n  setTextContent: setTextContent,\n  setStyleScope: setStyleScope\n});\n/*  */\n\nvar ref = {\n  create: function create(_, vnode) {\n    registerRef(vnode);\n  },\n  update: function update(oldVnode, vnode) {\n    if (oldVnode.data.ref !== vnode.data.ref) {\n      registerRef(oldVnode, true);\n      registerRef(vnode);\n    }\n  },\n  destroy: function destroy(vnode) {\n    registerRef(vnode, true);\n  }\n};\n\nfunction registerRef(vnode, isRemoval) {\n  var key = vnode.data.ref;\n\n  if (!isDef(key)) {\n    return;\n  }\n\n  var vm = vnode.context;\n  var ref = vnode.componentInstance || vnode.elm;\n  var refs = vm.$refs;\n\n  if (isRemoval) {\n    if (Array.isArray(refs[key])) {\n      remove(refs[key], ref);\n    } else if (refs[key] === ref) {\n      refs[key] = undefined;\n    }\n  } else {\n    if (vnode.data.refInFor) {\n      if (!Array.isArray(refs[key])) {\n        refs[key] = [ref];\n      } else if (refs[key].indexOf(ref) < 0) {\n        // $flow-disable-line\n        refs[key].push(ref);\n      }\n    } else {\n      refs[key] = ref;\n    }\n  }\n}\n/**\n * Virtual DOM patching algorithm based on Snabbdom by\n * Simon Friis Vindum (@paldepind)\n * Licensed under the MIT License\n * https://github.com/paldepind/snabbdom/blob/master/LICENSE\n *\n * modified by Evan You (@yyx990803)\n *\n * Not type-checking this because this file is perf-critical and the cost\n * of making flow understand it is not worth it.\n */\n\n\nvar emptyNode = new VNode('', {}, []);\nvar hooks = ['create', 'activate', 'update', 'remove', 'destroy'];\n\nfunction sameVnode(a, b) {\n  return a.key === b.key && a.asyncFactory === b.asyncFactory && (a.tag === b.tag && a.isComment === b.isComment && isDef(a.data) === isDef(b.data) && sameInputType(a, b) || isTrue(a.isAsyncPlaceholder) && isUndef(b.asyncFactory.error));\n}\n\nfunction sameInputType(a, b) {\n  if (a.tag !== 'input') {\n    return true;\n  }\n\n  var i;\n  var typeA = isDef(i = a.data) && isDef(i = i.attrs) && i.type;\n  var typeB = isDef(i = b.data) && isDef(i = i.attrs) && i.type;\n  return typeA === typeB || isTextInputType(typeA) && isTextInputType(typeB);\n}\n\nfunction createKeyToOldIdx(children, beginIdx, endIdx) {\n  var i, key;\n  var map = {};\n\n  for (i = beginIdx; i <= endIdx; ++i) {\n    key = children[i].key;\n\n    if (isDef(key)) {\n      map[key] = i;\n    }\n  }\n\n  return map;\n}\n\nfunction createPatchFunction(backend) {\n  var i, j;\n  var cbs = {};\n  var modules = backend.modules;\n  var nodeOps = backend.nodeOps;\n\n  for (i = 0; i < hooks.length; ++i) {\n    cbs[hooks[i]] = [];\n\n    for (j = 0; j < modules.length; ++j) {\n      if (isDef(modules[j][hooks[i]])) {\n        cbs[hooks[i]].push(modules[j][hooks[i]]);\n      }\n    }\n  }\n\n  function emptyNodeAt(elm) {\n    return new VNode(nodeOps.tagName(elm).toLowerCase(), {}, [], undefined, elm);\n  }\n\n  function createRmCb(childElm, listeners) {\n    function remove$$1() {\n      if (--remove$$1.listeners === 0) {\n        removeNode(childElm);\n      }\n    }\n\n    remove$$1.listeners = listeners;\n    return remove$$1;\n  }\n\n  function removeNode(el) {\n    var parent = nodeOps.parentNode(el); // element may have already been removed due to v-html / v-text\n\n    if (isDef(parent)) {\n      nodeOps.removeChild(parent, el);\n    }\n  }\n\n  function isUnknownElement$$1(vnode, inVPre) {\n    return !inVPre && !vnode.ns && !(config.ignoredElements.length && config.ignoredElements.some(function (ignore) {\n      return isRegExp(ignore) ? ignore.test(vnode.tag) : ignore === vnode.tag;\n    })) && config.isUnknownElement(vnode.tag);\n  }\n\n  var creatingElmInVPre = 0;\n\n  function createElm(vnode, insertedVnodeQueue, parentElm, refElm, nested, ownerArray, index) {\n    if (isDef(vnode.elm) && isDef(ownerArray)) {\n      // This vnode was used in a previous render!\n      // now it's used as a new node, overwriting its elm would cause\n      // potential patch errors down the road when it's used as an insertion\n      // reference node. Instead, we clone the node on-demand before creating\n      // associated DOM element for it.\n      vnode = ownerArray[index] = cloneVNode(vnode);\n    }\n\n    vnode.isRootInsert = !nested; // for transition enter check\n\n    if (createComponent(vnode, insertedVnodeQueue, parentElm, refElm)) {\n      return;\n    }\n\n    var data = vnode.data;\n    var children = vnode.children;\n    var tag = vnode.tag;\n\n    if (isDef(tag)) {\n      if (true) {\n        if (data && data.pre) {\n          creatingElmInVPre++;\n        }\n\n        if (isUnknownElement$$1(vnode, creatingElmInVPre)) {\n          warn('Unknown custom element: <' + tag + '> - did you ' + 'register the component correctly? For recursive components, ' + 'make sure to provide the \"name\" option.', vnode.context);\n        }\n      }\n\n      vnode.elm = vnode.ns ? nodeOps.createElementNS(vnode.ns, tag) : nodeOps.createElement(tag, vnode);\n      setScope(vnode);\n      /* istanbul ignore if */\n\n      {\n        createChildren(vnode, children, insertedVnodeQueue);\n\n        if (isDef(data)) {\n          invokeCreateHooks(vnode, insertedVnodeQueue);\n        }\n\n        insert(parentElm, vnode.elm, refElm);\n      }\n\n      if ( true && data && data.pre) {\n        creatingElmInVPre--;\n      }\n    } else if (isTrue(vnode.isComment)) {\n      vnode.elm = nodeOps.createComment(vnode.text);\n      insert(parentElm, vnode.elm, refElm);\n    } else {\n      vnode.elm = nodeOps.createTextNode(vnode.text);\n      insert(parentElm, vnode.elm, refElm);\n    }\n  }\n\n  function createComponent(vnode, insertedVnodeQueue, parentElm, refElm) {\n    var i = vnode.data;\n\n    if (isDef(i)) {\n      var isReactivated = isDef(vnode.componentInstance) && i.keepAlive;\n\n      if (isDef(i = i.hook) && isDef(i = i.init)) {\n        i(vnode, false\n        /* hydrating */\n        );\n      } // after calling the init hook, if the vnode is a child component\n      // it should've created a child instance and mounted it. the child\n      // component also has set the placeholder vnode's elm.\n      // in that case we can just return the element and be done.\n\n\n      if (isDef(vnode.componentInstance)) {\n        initComponent(vnode, insertedVnodeQueue);\n        insert(parentElm, vnode.elm, refElm);\n\n        if (isTrue(isReactivated)) {\n          reactivateComponent(vnode, insertedVnodeQueue, parentElm, refElm);\n        }\n\n        return true;\n      }\n    }\n  }\n\n  function initComponent(vnode, insertedVnodeQueue) {\n    if (isDef(vnode.data.pendingInsert)) {\n      insertedVnodeQueue.push.apply(insertedVnodeQueue, vnode.data.pendingInsert);\n      vnode.data.pendingInsert = null;\n    }\n\n    vnode.elm = vnode.componentInstance.$el;\n\n    if (isPatchable(vnode)) {\n      invokeCreateHooks(vnode, insertedVnodeQueue);\n      setScope(vnode);\n    } else {\n      // empty component root.\n      // skip all element-related modules except for ref (#3455)\n      registerRef(vnode); // make sure to invoke the insert hook\n\n      insertedVnodeQueue.push(vnode);\n    }\n  }\n\n  function reactivateComponent(vnode, insertedVnodeQueue, parentElm, refElm) {\n    var i; // hack for #4339: a reactivated component with inner transition\n    // does not trigger because the inner node's created hooks are not called\n    // again. It's not ideal to involve module-specific logic in here but\n    // there doesn't seem to be a better way to do it.\n\n    var innerNode = vnode;\n\n    while (innerNode.componentInstance) {\n      innerNode = innerNode.componentInstance._vnode;\n\n      if (isDef(i = innerNode.data) && isDef(i = i.transition)) {\n        for (i = 0; i < cbs.activate.length; ++i) {\n          cbs.activate[i](emptyNode, innerNode);\n        }\n\n        insertedVnodeQueue.push(innerNode);\n        break;\n      }\n    } // unlike a newly created component,\n    // a reactivated keep-alive component doesn't insert itself\n\n\n    insert(parentElm, vnode.elm, refElm);\n  }\n\n  function insert(parent, elm, ref$$1) {\n    if (isDef(parent)) {\n      if (isDef(ref$$1)) {\n        if (nodeOps.parentNode(ref$$1) === parent) {\n          nodeOps.insertBefore(parent, elm, ref$$1);\n        }\n      } else {\n        nodeOps.appendChild(parent, elm);\n      }\n    }\n  }\n\n  function createChildren(vnode, children, insertedVnodeQueue) {\n    if (Array.isArray(children)) {\n      if (true) {\n        checkDuplicateKeys(children);\n      }\n\n      for (var i = 0; i < children.length; ++i) {\n        createElm(children[i], insertedVnodeQueue, vnode.elm, null, true, children, i);\n      }\n    } else if (isPrimitive(vnode.text)) {\n      nodeOps.appendChild(vnode.elm, nodeOps.createTextNode(String(vnode.text)));\n    }\n  }\n\n  function isPatchable(vnode) {\n    while (vnode.componentInstance) {\n      vnode = vnode.componentInstance._vnode;\n    }\n\n    return isDef(vnode.tag);\n  }\n\n  function invokeCreateHooks(vnode, insertedVnodeQueue) {\n    for (var i$1 = 0; i$1 < cbs.create.length; ++i$1) {\n      cbs.create[i$1](emptyNode, vnode);\n    }\n\n    i = vnode.data.hook; // Reuse variable\n\n    if (isDef(i)) {\n      if (isDef(i.create)) {\n        i.create(emptyNode, vnode);\n      }\n\n      if (isDef(i.insert)) {\n        insertedVnodeQueue.push(vnode);\n      }\n    }\n  } // set scope id attribute for scoped CSS.\n  // this is implemented as a special case to avoid the overhead\n  // of going through the normal attribute patching process.\n\n\n  function setScope(vnode) {\n    var i;\n\n    if (isDef(i = vnode.fnScopeId)) {\n      nodeOps.setStyleScope(vnode.elm, i);\n    } else {\n      var ancestor = vnode;\n\n      while (ancestor) {\n        if (isDef(i = ancestor.context) && isDef(i = i.$options._scopeId)) {\n          nodeOps.setStyleScope(vnode.elm, i);\n        }\n\n        ancestor = ancestor.parent;\n      }\n    } // for slot content they should also get the scopeId from the host instance.\n\n\n    if (isDef(i = activeInstance) && i !== vnode.context && i !== vnode.fnContext && isDef(i = i.$options._scopeId)) {\n      nodeOps.setStyleScope(vnode.elm, i);\n    }\n  }\n\n  function addVnodes(parentElm, refElm, vnodes, startIdx, endIdx, insertedVnodeQueue) {\n    for (; startIdx <= endIdx; ++startIdx) {\n      createElm(vnodes[startIdx], insertedVnodeQueue, parentElm, refElm, false, vnodes, startIdx);\n    }\n  }\n\n  function invokeDestroyHook(vnode) {\n    var i, j;\n    var data = vnode.data;\n\n    if (isDef(data)) {\n      if (isDef(i = data.hook) && isDef(i = i.destroy)) {\n        i(vnode);\n      }\n\n      for (i = 0; i < cbs.destroy.length; ++i) {\n        cbs.destroy[i](vnode);\n      }\n    }\n\n    if (isDef(i = vnode.children)) {\n      for (j = 0; j < vnode.children.length; ++j) {\n        invokeDestroyHook(vnode.children[j]);\n      }\n    }\n  }\n\n  function removeVnodes(vnodes, startIdx, endIdx) {\n    for (; startIdx <= endIdx; ++startIdx) {\n      var ch = vnodes[startIdx];\n\n      if (isDef(ch)) {\n        if (isDef(ch.tag)) {\n          removeAndInvokeRemoveHook(ch);\n          invokeDestroyHook(ch);\n        } else {\n          // Text node\n          removeNode(ch.elm);\n        }\n      }\n    }\n  }\n\n  function removeAndInvokeRemoveHook(vnode, rm) {\n    if (isDef(rm) || isDef(vnode.data)) {\n      var i;\n      var listeners = cbs.remove.length + 1;\n\n      if (isDef(rm)) {\n        // we have a recursively passed down rm callback\n        // increase the listeners count\n        rm.listeners += listeners;\n      } else {\n        // directly removing\n        rm = createRmCb(vnode.elm, listeners);\n      } // recursively invoke hooks on child component root node\n\n\n      if (isDef(i = vnode.componentInstance) && isDef(i = i._vnode) && isDef(i.data)) {\n        removeAndInvokeRemoveHook(i, rm);\n      }\n\n      for (i = 0; i < cbs.remove.length; ++i) {\n        cbs.remove[i](vnode, rm);\n      }\n\n      if (isDef(i = vnode.data.hook) && isDef(i = i.remove)) {\n        i(vnode, rm);\n      } else {\n        rm();\n      }\n    } else {\n      removeNode(vnode.elm);\n    }\n  }\n\n  function updateChildren(parentElm, oldCh, newCh, insertedVnodeQueue, removeOnly) {\n    var oldStartIdx = 0;\n    var newStartIdx = 0;\n    var oldEndIdx = oldCh.length - 1;\n    var oldStartVnode = oldCh[0];\n    var oldEndVnode = oldCh[oldEndIdx];\n    var newEndIdx = newCh.length - 1;\n    var newStartVnode = newCh[0];\n    var newEndVnode = newCh[newEndIdx];\n    var oldKeyToIdx, idxInOld, vnodeToMove, refElm; // removeOnly is a special flag used only by <transition-group>\n    // to ensure removed elements stay in correct relative positions\n    // during leaving transitions\n\n    var canMove = !removeOnly;\n\n    if (true) {\n      checkDuplicateKeys(newCh);\n    }\n\n    while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n      if (isUndef(oldStartVnode)) {\n        oldStartVnode = oldCh[++oldStartIdx]; // Vnode has been moved left\n      } else if (isUndef(oldEndVnode)) {\n        oldEndVnode = oldCh[--oldEndIdx];\n      } else if (sameVnode(oldStartVnode, newStartVnode)) {\n        patchVnode(oldStartVnode, newStartVnode, insertedVnodeQueue, newCh, newStartIdx);\n        oldStartVnode = oldCh[++oldStartIdx];\n        newStartVnode = newCh[++newStartIdx];\n      } else if (sameVnode(oldEndVnode, newEndVnode)) {\n        patchVnode(oldEndVnode, newEndVnode, insertedVnodeQueue, newCh, newEndIdx);\n        oldEndVnode = oldCh[--oldEndIdx];\n        newEndVnode = newCh[--newEndIdx];\n      } else if (sameVnode(oldStartVnode, newEndVnode)) {\n        // Vnode moved right\n        patchVnode(oldStartVnode, newEndVnode, insertedVnodeQueue, newCh, newEndIdx);\n        canMove && nodeOps.insertBefore(parentElm, oldStartVnode.elm, nodeOps.nextSibling(oldEndVnode.elm));\n        oldStartVnode = oldCh[++oldStartIdx];\n        newEndVnode = newCh[--newEndIdx];\n      } else if (sameVnode(oldEndVnode, newStartVnode)) {\n        // Vnode moved left\n        patchVnode(oldEndVnode, newStartVnode, insertedVnodeQueue, newCh, newStartIdx);\n        canMove && nodeOps.insertBefore(parentElm, oldEndVnode.elm, oldStartVnode.elm);\n        oldEndVnode = oldCh[--oldEndIdx];\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        if (isUndef(oldKeyToIdx)) {\n          oldKeyToIdx = createKeyToOldIdx(oldCh, oldStartIdx, oldEndIdx);\n        }\n\n        idxInOld = isDef(newStartVnode.key) ? oldKeyToIdx[newStartVnode.key] : findIdxInOld(newStartVnode, oldCh, oldStartIdx, oldEndIdx);\n\n        if (isUndef(idxInOld)) {\n          // New element\n          createElm(newStartVnode, insertedVnodeQueue, parentElm, oldStartVnode.elm, false, newCh, newStartIdx);\n        } else {\n          vnodeToMove = oldCh[idxInOld];\n\n          if (sameVnode(vnodeToMove, newStartVnode)) {\n            patchVnode(vnodeToMove, newStartVnode, insertedVnodeQueue, newCh, newStartIdx);\n            oldCh[idxInOld] = undefined;\n            canMove && nodeOps.insertBefore(parentElm, vnodeToMove.elm, oldStartVnode.elm);\n          } else {\n            // same key but different element. treat as new element\n            createElm(newStartVnode, insertedVnodeQueue, parentElm, oldStartVnode.elm, false, newCh, newStartIdx);\n          }\n        }\n\n        newStartVnode = newCh[++newStartIdx];\n      }\n    }\n\n    if (oldStartIdx > oldEndIdx) {\n      refElm = isUndef(newCh[newEndIdx + 1]) ? null : newCh[newEndIdx + 1].elm;\n      addVnodes(parentElm, refElm, newCh, newStartIdx, newEndIdx, insertedVnodeQueue);\n    } else if (newStartIdx > newEndIdx) {\n      removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n    }\n  }\n\n  function checkDuplicateKeys(children) {\n    var seenKeys = {};\n\n    for (var i = 0; i < children.length; i++) {\n      var vnode = children[i];\n      var key = vnode.key;\n\n      if (isDef(key)) {\n        if (seenKeys[key]) {\n          warn(\"Duplicate keys detected: '\" + key + \"'. This may cause an update error.\", vnode.context);\n        } else {\n          seenKeys[key] = true;\n        }\n      }\n    }\n  }\n\n  function findIdxInOld(node, oldCh, start, end) {\n    for (var i = start; i < end; i++) {\n      var c = oldCh[i];\n\n      if (isDef(c) && sameVnode(node, c)) {\n        return i;\n      }\n    }\n  }\n\n  function patchVnode(oldVnode, vnode, insertedVnodeQueue, ownerArray, index, removeOnly) {\n    if (oldVnode === vnode) {\n      return;\n    }\n\n    if (isDef(vnode.elm) && isDef(ownerArray)) {\n      // clone reused vnode\n      vnode = ownerArray[index] = cloneVNode(vnode);\n    }\n\n    var elm = vnode.elm = oldVnode.elm;\n\n    if (isTrue(oldVnode.isAsyncPlaceholder)) {\n      if (isDef(vnode.asyncFactory.resolved)) {\n        hydrate(oldVnode.elm, vnode, insertedVnodeQueue);\n      } else {\n        vnode.isAsyncPlaceholder = true;\n      }\n\n      return;\n    } // reuse element for static trees.\n    // note we only do this if the vnode is cloned -\n    // if the new node is not cloned it means the render functions have been\n    // reset by the hot-reload-api and we need to do a proper re-render.\n\n\n    if (isTrue(vnode.isStatic) && isTrue(oldVnode.isStatic) && vnode.key === oldVnode.key && (isTrue(vnode.isCloned) || isTrue(vnode.isOnce))) {\n      vnode.componentInstance = oldVnode.componentInstance;\n      return;\n    }\n\n    var i;\n    var data = vnode.data;\n\n    if (isDef(data) && isDef(i = data.hook) && isDef(i = i.prepatch)) {\n      i(oldVnode, vnode);\n    }\n\n    var oldCh = oldVnode.children;\n    var ch = vnode.children;\n\n    if (isDef(data) && isPatchable(vnode)) {\n      for (i = 0; i < cbs.update.length; ++i) {\n        cbs.update[i](oldVnode, vnode);\n      }\n\n      if (isDef(i = data.hook) && isDef(i = i.update)) {\n        i(oldVnode, vnode);\n      }\n    }\n\n    if (isUndef(vnode.text)) {\n      if (isDef(oldCh) && isDef(ch)) {\n        if (oldCh !== ch) {\n          updateChildren(elm, oldCh, ch, insertedVnodeQueue, removeOnly);\n        }\n      } else if (isDef(ch)) {\n        if (true) {\n          checkDuplicateKeys(ch);\n        }\n\n        if (isDef(oldVnode.text)) {\n          nodeOps.setTextContent(elm, '');\n        }\n\n        addVnodes(elm, null, ch, 0, ch.length - 1, insertedVnodeQueue);\n      } else if (isDef(oldCh)) {\n        removeVnodes(oldCh, 0, oldCh.length - 1);\n      } else if (isDef(oldVnode.text)) {\n        nodeOps.setTextContent(elm, '');\n      }\n    } else if (oldVnode.text !== vnode.text) {\n      nodeOps.setTextContent(elm, vnode.text);\n    }\n\n    if (isDef(data)) {\n      if (isDef(i = data.hook) && isDef(i = i.postpatch)) {\n        i(oldVnode, vnode);\n      }\n    }\n  }\n\n  function invokeInsertHook(vnode, queue, initial) {\n    // delay insert hooks for component root nodes, invoke them after the\n    // element is really inserted\n    if (isTrue(initial) && isDef(vnode.parent)) {\n      vnode.parent.data.pendingInsert = queue;\n    } else {\n      for (var i = 0; i < queue.length; ++i) {\n        queue[i].data.hook.insert(queue[i]);\n      }\n    }\n  }\n\n  var hydrationBailed = false; // list of modules that can skip create hook during hydration because they\n  // are already rendered on the client or has no need for initialization\n  // Note: style is excluded because it relies on initial clone for future\n  // deep updates (#7063).\n\n  var isRenderedModule = makeMap('attrs,class,staticClass,staticStyle,key'); // Note: this is a browser-only function so we can assume elms are DOM nodes.\n\n  function hydrate(elm, vnode, insertedVnodeQueue, inVPre) {\n    var i;\n    var tag = vnode.tag;\n    var data = vnode.data;\n    var children = vnode.children;\n    inVPre = inVPre || data && data.pre;\n    vnode.elm = elm;\n\n    if (isTrue(vnode.isComment) && isDef(vnode.asyncFactory)) {\n      vnode.isAsyncPlaceholder = true;\n      return true;\n    } // assert node match\n\n\n    if (true) {\n      if (!assertNodeMatch(elm, vnode, inVPre)) {\n        return false;\n      }\n    }\n\n    if (isDef(data)) {\n      if (isDef(i = data.hook) && isDef(i = i.init)) {\n        i(vnode, true\n        /* hydrating */\n        );\n      }\n\n      if (isDef(i = vnode.componentInstance)) {\n        // child component. it should have hydrated its own tree.\n        initComponent(vnode, insertedVnodeQueue);\n        return true;\n      }\n    }\n\n    if (isDef(tag)) {\n      if (isDef(children)) {\n        // empty element, allow client to pick up and populate children\n        if (!elm.hasChildNodes()) {\n          createChildren(vnode, children, insertedVnodeQueue);\n        } else {\n          // v-html and domProps: innerHTML\n          if (isDef(i = data) && isDef(i = i.domProps) && isDef(i = i.innerHTML)) {\n            if (i !== elm.innerHTML) {\n              /* istanbul ignore if */\n              if ( true && typeof console !== 'undefined' && !hydrationBailed) {\n                hydrationBailed = true;\n                console.warn('Parent: ', elm);\n                console.warn('server innerHTML: ', i);\n                console.warn('client innerHTML: ', elm.innerHTML);\n              }\n\n              return false;\n            }\n          } else {\n            // iterate and compare children lists\n            var childrenMatch = true;\n            var childNode = elm.firstChild;\n\n            for (var i$1 = 0; i$1 < children.length; i$1++) {\n              if (!childNode || !hydrate(childNode, children[i$1], insertedVnodeQueue, inVPre)) {\n                childrenMatch = false;\n                break;\n              }\n\n              childNode = childNode.nextSibling;\n            } // if childNode is not null, it means the actual childNodes list is\n            // longer than the virtual children list.\n\n\n            if (!childrenMatch || childNode) {\n              /* istanbul ignore if */\n              if ( true && typeof console !== 'undefined' && !hydrationBailed) {\n                hydrationBailed = true;\n                console.warn('Parent: ', elm);\n                console.warn('Mismatching childNodes vs. VNodes: ', elm.childNodes, children);\n              }\n\n              return false;\n            }\n          }\n        }\n      }\n\n      if (isDef(data)) {\n        var fullInvoke = false;\n\n        for (var key in data) {\n          if (!isRenderedModule(key)) {\n            fullInvoke = true;\n            invokeCreateHooks(vnode, insertedVnodeQueue);\n            break;\n          }\n        }\n\n        if (!fullInvoke && data['class']) {\n          // ensure collecting deps for deep class bindings for future updates\n          traverse(data['class']);\n        }\n      }\n    } else if (elm.data !== vnode.text) {\n      elm.data = vnode.text;\n    }\n\n    return true;\n  }\n\n  function assertNodeMatch(node, vnode, inVPre) {\n    if (isDef(vnode.tag)) {\n      return vnode.tag.indexOf('vue-component') === 0 || !isUnknownElement$$1(vnode, inVPre) && vnode.tag.toLowerCase() === (node.tagName && node.tagName.toLowerCase());\n    } else {\n      return node.nodeType === (vnode.isComment ? 8 : 3);\n    }\n  }\n\n  return function patch(oldVnode, vnode, hydrating, removeOnly) {\n    if (isUndef(vnode)) {\n      if (isDef(oldVnode)) {\n        invokeDestroyHook(oldVnode);\n      }\n\n      return;\n    }\n\n    var isInitialPatch = false;\n    var insertedVnodeQueue = [];\n\n    if (isUndef(oldVnode)) {\n      // empty mount (likely as component), create new root element\n      isInitialPatch = true;\n      createElm(vnode, insertedVnodeQueue);\n    } else {\n      var isRealElement = isDef(oldVnode.nodeType);\n\n      if (!isRealElement && sameVnode(oldVnode, vnode)) {\n        // patch existing root node\n        patchVnode(oldVnode, vnode, insertedVnodeQueue, null, null, removeOnly);\n      } else {\n        if (isRealElement) {\n          // mounting to a real element\n          // check if this is server-rendered content and if we can perform\n          // a successful hydration.\n          if (oldVnode.nodeType === 1 && oldVnode.hasAttribute(SSR_ATTR)) {\n            oldVnode.removeAttribute(SSR_ATTR);\n            hydrating = true;\n          }\n\n          if (isTrue(hydrating)) {\n            if (hydrate(oldVnode, vnode, insertedVnodeQueue)) {\n              invokeInsertHook(vnode, insertedVnodeQueue, true);\n              return oldVnode;\n            } else if (true) {\n              warn('The client-side rendered virtual DOM tree is not matching ' + 'server-rendered content. This is likely caused by incorrect ' + 'HTML markup, for example nesting block-level elements inside ' + '<p>, or missing <tbody>. Bailing hydration and performing ' + 'full client-side render.');\n            }\n          } // either not server-rendered, or hydration failed.\n          // create an empty node and replace it\n\n\n          oldVnode = emptyNodeAt(oldVnode);\n        } // replacing existing element\n\n\n        var oldElm = oldVnode.elm;\n        var parentElm = nodeOps.parentNode(oldElm); // create new node\n\n        createElm(vnode, insertedVnodeQueue, // extremely rare edge case: do not insert if old element is in a\n        // leaving transition. Only happens when combining transition +\n        // keep-alive + HOCs. (#4590)\n        oldElm._leaveCb ? null : parentElm, nodeOps.nextSibling(oldElm)); // update parent placeholder node element, recursively\n\n        if (isDef(vnode.parent)) {\n          var ancestor = vnode.parent;\n          var patchable = isPatchable(vnode);\n\n          while (ancestor) {\n            for (var i = 0; i < cbs.destroy.length; ++i) {\n              cbs.destroy[i](ancestor);\n            }\n\n            ancestor.elm = vnode.elm;\n\n            if (patchable) {\n              for (var i$1 = 0; i$1 < cbs.create.length; ++i$1) {\n                cbs.create[i$1](emptyNode, ancestor);\n              } // #6513\n              // invoke insert hooks that may have been merged by create hooks.\n              // e.g. for directives that uses the \"inserted\" hook.\n\n\n              var insert = ancestor.data.hook.insert;\n\n              if (insert.merged) {\n                // start at index 1 to avoid re-invoking component mounted hook\n                for (var i$2 = 1; i$2 < insert.fns.length; i$2++) {\n                  insert.fns[i$2]();\n                }\n              }\n            } else {\n              registerRef(ancestor);\n            }\n\n            ancestor = ancestor.parent;\n          }\n        } // destroy old node\n\n\n        if (isDef(parentElm)) {\n          removeVnodes([oldVnode], 0, 0);\n        } else if (isDef(oldVnode.tag)) {\n          invokeDestroyHook(oldVnode);\n        }\n      }\n    }\n\n    invokeInsertHook(vnode, insertedVnodeQueue, isInitialPatch);\n    return vnode.elm;\n  };\n}\n/*  */\n\n\nvar directives = {\n  create: updateDirectives,\n  update: updateDirectives,\n  destroy: function unbindDirectives(vnode) {\n    updateDirectives(vnode, emptyNode);\n  }\n};\n\nfunction updateDirectives(oldVnode, vnode) {\n  if (oldVnode.data.directives || vnode.data.directives) {\n    _update(oldVnode, vnode);\n  }\n}\n\nfunction _update(oldVnode, vnode) {\n  var isCreate = oldVnode === emptyNode;\n  var isDestroy = vnode === emptyNode;\n  var oldDirs = normalizeDirectives$1(oldVnode.data.directives, oldVnode.context);\n  var newDirs = normalizeDirectives$1(vnode.data.directives, vnode.context);\n  var dirsWithInsert = [];\n  var dirsWithPostpatch = [];\n  var key, oldDir, dir;\n\n  for (key in newDirs) {\n    oldDir = oldDirs[key];\n    dir = newDirs[key];\n\n    if (!oldDir) {\n      // new directive, bind\n      callHook$1(dir, 'bind', vnode, oldVnode);\n\n      if (dir.def && dir.def.inserted) {\n        dirsWithInsert.push(dir);\n      }\n    } else {\n      // existing directive, update\n      dir.oldValue = oldDir.value;\n      dir.oldArg = oldDir.arg;\n      callHook$1(dir, 'update', vnode, oldVnode);\n\n      if (dir.def && dir.def.componentUpdated) {\n        dirsWithPostpatch.push(dir);\n      }\n    }\n  }\n\n  if (dirsWithInsert.length) {\n    var callInsert = function () {\n      for (var i = 0; i < dirsWithInsert.length; i++) {\n        callHook$1(dirsWithInsert[i], 'inserted', vnode, oldVnode);\n      }\n    };\n\n    if (isCreate) {\n      mergeVNodeHook(vnode, 'insert', callInsert);\n    } else {\n      callInsert();\n    }\n  }\n\n  if (dirsWithPostpatch.length) {\n    mergeVNodeHook(vnode, 'postpatch', function () {\n      for (var i = 0; i < dirsWithPostpatch.length; i++) {\n        callHook$1(dirsWithPostpatch[i], 'componentUpdated', vnode, oldVnode);\n      }\n    });\n  }\n\n  if (!isCreate) {\n    for (key in oldDirs) {\n      if (!newDirs[key]) {\n        // no longer present, unbind\n        callHook$1(oldDirs[key], 'unbind', oldVnode, oldVnode, isDestroy);\n      }\n    }\n  }\n}\n\nvar emptyModifiers = Object.create(null);\n\nfunction normalizeDirectives$1(dirs, vm) {\n  var res = Object.create(null);\n\n  if (!dirs) {\n    // $flow-disable-line\n    return res;\n  }\n\n  var i, dir;\n\n  for (i = 0; i < dirs.length; i++) {\n    dir = dirs[i];\n\n    if (!dir.modifiers) {\n      // $flow-disable-line\n      dir.modifiers = emptyModifiers;\n    }\n\n    res[getRawDirName(dir)] = dir;\n    dir.def = resolveAsset(vm.$options, 'directives', dir.name, true);\n  } // $flow-disable-line\n\n\n  return res;\n}\n\nfunction getRawDirName(dir) {\n  return dir.rawName || dir.name + \".\" + Object.keys(dir.modifiers || {}).join('.');\n}\n\nfunction callHook$1(dir, hook, vnode, oldVnode, isDestroy) {\n  var fn = dir.def && dir.def[hook];\n\n  if (fn) {\n    try {\n      fn(vnode.elm, dir, vnode, oldVnode, isDestroy);\n    } catch (e) {\n      handleError(e, vnode.context, \"directive \" + dir.name + \" \" + hook + \" hook\");\n    }\n  }\n}\n\nvar baseModules = [ref, directives];\n/*  */\n\nfunction updateAttrs(oldVnode, vnode) {\n  var opts = vnode.componentOptions;\n\n  if (isDef(opts) && opts.Ctor.options.inheritAttrs === false) {\n    return;\n  }\n\n  if (isUndef(oldVnode.data.attrs) && isUndef(vnode.data.attrs)) {\n    return;\n  }\n\n  var key, cur, old;\n  var elm = vnode.elm;\n  var oldAttrs = oldVnode.data.attrs || {};\n  var attrs = vnode.data.attrs || {}; // clone observed objects, as the user probably wants to mutate it\n\n  if (isDef(attrs.__ob__)) {\n    attrs = vnode.data.attrs = extend({}, attrs);\n  }\n\n  for (key in attrs) {\n    cur = attrs[key];\n    old = oldAttrs[key];\n\n    if (old !== cur) {\n      setAttr(elm, key, cur, vnode.data.pre);\n    }\n  } // #4391: in IE9, setting type can reset value for input[type=radio]\n  // #6666: IE/Edge forces progress value down to 1 before setting a max\n\n  /* istanbul ignore if */\n\n\n  if ((isIE || isEdge) && attrs.value !== oldAttrs.value) {\n    setAttr(elm, 'value', attrs.value);\n  }\n\n  for (key in oldAttrs) {\n    if (isUndef(attrs[key])) {\n      if (isXlink(key)) {\n        elm.removeAttributeNS(xlinkNS, getXlinkProp(key));\n      } else if (!isEnumeratedAttr(key)) {\n        elm.removeAttribute(key);\n      }\n    }\n  }\n}\n\nfunction setAttr(el, key, value, isInPre) {\n  if (isInPre || el.tagName.indexOf('-') > -1) {\n    baseSetAttr(el, key, value);\n  } else if (isBooleanAttr(key)) {\n    // set attribute for blank value\n    // e.g. <option disabled>Select one</option>\n    if (isFalsyAttrValue(value)) {\n      el.removeAttribute(key);\n    } else {\n      // technically allowfullscreen is a boolean attribute for <iframe>,\n      // but Flash expects a value of \"true\" when used on <embed> tag\n      value = key === 'allowfullscreen' && el.tagName === 'EMBED' ? 'true' : key;\n      el.setAttribute(key, value);\n    }\n  } else if (isEnumeratedAttr(key)) {\n    el.setAttribute(key, convertEnumeratedValue(key, value));\n  } else if (isXlink(key)) {\n    if (isFalsyAttrValue(value)) {\n      el.removeAttributeNS(xlinkNS, getXlinkProp(key));\n    } else {\n      el.setAttributeNS(xlinkNS, key, value);\n    }\n  } else {\n    baseSetAttr(el, key, value);\n  }\n}\n\nfunction baseSetAttr(el, key, value) {\n  if (isFalsyAttrValue(value)) {\n    el.removeAttribute(key);\n  } else {\n    // #7138: IE10 & 11 fires input event when setting placeholder on\n    // <textarea>... block the first input event and remove the blocker\n    // immediately.\n\n    /* istanbul ignore if */\n    if (isIE && !isIE9 && el.tagName === 'TEXTAREA' && key === 'placeholder' && value !== '' && !el.__ieph) {\n      var blocker = function (e) {\n        e.stopImmediatePropagation();\n        el.removeEventListener('input', blocker);\n      };\n\n      el.addEventListener('input', blocker); // $flow-disable-line\n\n      el.__ieph = true;\n      /* IE placeholder patched */\n    }\n\n    el.setAttribute(key, value);\n  }\n}\n\nvar attrs = {\n  create: updateAttrs,\n  update: updateAttrs\n};\n/*  */\n\nfunction updateClass(oldVnode, vnode) {\n  var el = vnode.elm;\n  var data = vnode.data;\n  var oldData = oldVnode.data;\n\n  if (isUndef(data.staticClass) && isUndef(data.class) && (isUndef(oldData) || isUndef(oldData.staticClass) && isUndef(oldData.class))) {\n    return;\n  }\n\n  var cls = genClassForVnode(vnode); // handle transition classes\n\n  var transitionClass = el._transitionClasses;\n\n  if (isDef(transitionClass)) {\n    cls = concat(cls, stringifyClass(transitionClass));\n  } // set the class\n\n\n  if (cls !== el._prevClass) {\n    el.setAttribute('class', cls);\n    el._prevClass = cls;\n  }\n}\n\nvar klass = {\n  create: updateClass,\n  update: updateClass\n};\n/*  */\n\nvar validDivisionCharRE = /[\\w).+\\-_$\\]]/;\n\nfunction parseFilters(exp) {\n  var inSingle = false;\n  var inDouble = false;\n  var inTemplateString = false;\n  var inRegex = false;\n  var curly = 0;\n  var square = 0;\n  var paren = 0;\n  var lastFilterIndex = 0;\n  var c, prev, i, expression, filters;\n\n  for (i = 0; i < exp.length; i++) {\n    prev = c;\n    c = exp.charCodeAt(i);\n\n    if (inSingle) {\n      if (c === 0x27 && prev !== 0x5C) {\n        inSingle = false;\n      }\n    } else if (inDouble) {\n      if (c === 0x22 && prev !== 0x5C) {\n        inDouble = false;\n      }\n    } else if (inTemplateString) {\n      if (c === 0x60 && prev !== 0x5C) {\n        inTemplateString = false;\n      }\n    } else if (inRegex) {\n      if (c === 0x2f && prev !== 0x5C) {\n        inRegex = false;\n      }\n    } else if (c === 0x7C && // pipe\n    exp.charCodeAt(i + 1) !== 0x7C && exp.charCodeAt(i - 1) !== 0x7C && !curly && !square && !paren) {\n      if (expression === undefined) {\n        // first filter, end of expression\n        lastFilterIndex = i + 1;\n        expression = exp.slice(0, i).trim();\n      } else {\n        pushFilter();\n      }\n    } else {\n      switch (c) {\n        case 0x22:\n          inDouble = true;\n          break;\n        // \"\n\n        case 0x27:\n          inSingle = true;\n          break;\n        // '\n\n        case 0x60:\n          inTemplateString = true;\n          break;\n        // `\n\n        case 0x28:\n          paren++;\n          break;\n        // (\n\n        case 0x29:\n          paren--;\n          break;\n        // )\n\n        case 0x5B:\n          square++;\n          break;\n        // [\n\n        case 0x5D:\n          square--;\n          break;\n        // ]\n\n        case 0x7B:\n          curly++;\n          break;\n        // {\n\n        case 0x7D:\n          curly--;\n          break;\n        // }\n      }\n\n      if (c === 0x2f) {\n        // /\n        var j = i - 1;\n        var p = void 0; // find first non-whitespace prev char\n\n        for (; j >= 0; j--) {\n          p = exp.charAt(j);\n\n          if (p !== ' ') {\n            break;\n          }\n        }\n\n        if (!p || !validDivisionCharRE.test(p)) {\n          inRegex = true;\n        }\n      }\n    }\n  }\n\n  if (expression === undefined) {\n    expression = exp.slice(0, i).trim();\n  } else if (lastFilterIndex !== 0) {\n    pushFilter();\n  }\n\n  function pushFilter() {\n    (filters || (filters = [])).push(exp.slice(lastFilterIndex, i).trim());\n    lastFilterIndex = i + 1;\n  }\n\n  if (filters) {\n    for (i = 0; i < filters.length; i++) {\n      expression = wrapFilter(expression, filters[i]);\n    }\n  }\n\n  return expression;\n}\n\nfunction wrapFilter(exp, filter) {\n  var i = filter.indexOf('(');\n\n  if (i < 0) {\n    // _f: resolveFilter\n    return \"_f(\\\"\" + filter + \"\\\")(\" + exp + \")\";\n  } else {\n    var name = filter.slice(0, i);\n    var args = filter.slice(i + 1);\n    return \"_f(\\\"\" + name + \"\\\")(\" + exp + (args !== ')' ? ',' + args : args);\n  }\n}\n/*  */\n\n/* eslint-disable no-unused-vars */\n\n\nfunction baseWarn(msg, range) {\n  console.error(\"[Vue compiler]: \" + msg);\n}\n/* eslint-enable no-unused-vars */\n\n\nfunction pluckModuleFunction(modules, key) {\n  return modules ? modules.map(function (m) {\n    return m[key];\n  }).filter(function (_) {\n    return _;\n  }) : [];\n}\n\nfunction addProp(el, name, value, range, dynamic) {\n  (el.props || (el.props = [])).push(rangeSetItem({\n    name: name,\n    value: value,\n    dynamic: dynamic\n  }, range));\n  el.plain = false;\n}\n\nfunction addAttr(el, name, value, range, dynamic) {\n  var attrs = dynamic ? el.dynamicAttrs || (el.dynamicAttrs = []) : el.attrs || (el.attrs = []);\n  attrs.push(rangeSetItem({\n    name: name,\n    value: value,\n    dynamic: dynamic\n  }, range));\n  el.plain = false;\n} // add a raw attr (use this in preTransforms)\n\n\nfunction addRawAttr(el, name, value, range) {\n  el.attrsMap[name] = value;\n  el.attrsList.push(rangeSetItem({\n    name: name,\n    value: value\n  }, range));\n}\n\nfunction addDirective(el, name, rawName, value, arg, isDynamicArg, modifiers, range) {\n  (el.directives || (el.directives = [])).push(rangeSetItem({\n    name: name,\n    rawName: rawName,\n    value: value,\n    arg: arg,\n    isDynamicArg: isDynamicArg,\n    modifiers: modifiers\n  }, range));\n  el.plain = false;\n}\n\nfunction prependModifierMarker(symbol, name, dynamic) {\n  return dynamic ? \"_p(\" + name + \",\\\"\" + symbol + \"\\\")\" : symbol + name; // mark the event as captured\n}\n\nfunction addHandler(el, name, value, modifiers, important, warn, range, dynamic) {\n  modifiers = modifiers || emptyObject; // warn prevent and passive modifier\n\n  /* istanbul ignore if */\n\n  if ( true && warn && modifiers.prevent && modifiers.passive) {\n    warn('passive and prevent can\\'t be used together. ' + 'Passive handler can\\'t prevent default event.', range);\n  } // normalize click.right and click.middle since they don't actually fire\n  // this is technically browser-specific, but at least for now browsers are\n  // the only target envs that have right/middle clicks.\n\n\n  if (modifiers.right) {\n    if (dynamic) {\n      name = \"(\" + name + \")==='click'?'contextmenu':(\" + name + \")\";\n    } else if (name === 'click') {\n      name = 'contextmenu';\n      delete modifiers.right;\n    }\n  } else if (modifiers.middle) {\n    if (dynamic) {\n      name = \"(\" + name + \")==='click'?'mouseup':(\" + name + \")\";\n    } else if (name === 'click') {\n      name = 'mouseup';\n    }\n  } // check capture modifier\n\n\n  if (modifiers.capture) {\n    delete modifiers.capture;\n    name = prependModifierMarker('!', name, dynamic);\n  }\n\n  if (modifiers.once) {\n    delete modifiers.once;\n    name = prependModifierMarker('~', name, dynamic);\n  }\n  /* istanbul ignore if */\n\n\n  if (modifiers.passive) {\n    delete modifiers.passive;\n    name = prependModifierMarker('&', name, dynamic);\n  }\n\n  var events;\n\n  if (modifiers.native) {\n    delete modifiers.native;\n    events = el.nativeEvents || (el.nativeEvents = {});\n  } else {\n    events = el.events || (el.events = {});\n  }\n\n  var newHandler = rangeSetItem({\n    value: value.trim(),\n    dynamic: dynamic\n  }, range);\n\n  if (modifiers !== emptyObject) {\n    newHandler.modifiers = modifiers;\n  }\n\n  var handlers = events[name];\n  /* istanbul ignore if */\n\n  if (Array.isArray(handlers)) {\n    important ? handlers.unshift(newHandler) : handlers.push(newHandler);\n  } else if (handlers) {\n    events[name] = important ? [newHandler, handlers] : [handlers, newHandler];\n  } else {\n    events[name] = newHandler;\n  }\n\n  el.plain = false;\n}\n\nfunction getRawBindingAttr(el, name) {\n  return el.rawAttrsMap[':' + name] || el.rawAttrsMap['v-bind:' + name] || el.rawAttrsMap[name];\n}\n\nfunction getBindingAttr(el, name, getStatic) {\n  var dynamicValue = getAndRemoveAttr(el, ':' + name) || getAndRemoveAttr(el, 'v-bind:' + name);\n\n  if (dynamicValue != null) {\n    return parseFilters(dynamicValue);\n  } else if (getStatic !== false) {\n    var staticValue = getAndRemoveAttr(el, name);\n\n    if (staticValue != null) {\n      return JSON.stringify(staticValue);\n    }\n  }\n} // note: this only removes the attr from the Array (attrsList) so that it\n// doesn't get processed by processAttrs.\n// By default it does NOT remove it from the map (attrsMap) because the map is\n// needed during codegen.\n\n\nfunction getAndRemoveAttr(el, name, removeFromMap) {\n  var val;\n\n  if ((val = el.attrsMap[name]) != null) {\n    var list = el.attrsList;\n\n    for (var i = 0, l = list.length; i < l; i++) {\n      if (list[i].name === name) {\n        list.splice(i, 1);\n        break;\n      }\n    }\n  }\n\n  if (removeFromMap) {\n    delete el.attrsMap[name];\n  }\n\n  return val;\n}\n\nfunction getAndRemoveAttrByRegex(el, name) {\n  var list = el.attrsList;\n\n  for (var i = 0, l = list.length; i < l; i++) {\n    var attr = list[i];\n\n    if (name.test(attr.name)) {\n      list.splice(i, 1);\n      return attr;\n    }\n  }\n}\n\nfunction rangeSetItem(item, range) {\n  if (range) {\n    if (range.start != null) {\n      item.start = range.start;\n    }\n\n    if (range.end != null) {\n      item.end = range.end;\n    }\n  }\n\n  return item;\n}\n/*  */\n\n/**\n * Cross-platform code generation for component v-model\n */\n\n\nfunction genComponentModel(el, value, modifiers) {\n  var ref = modifiers || {};\n  var number = ref.number;\n  var trim = ref.trim;\n  var baseValueExpression = '$$v';\n  var valueExpression = baseValueExpression;\n\n  if (trim) {\n    valueExpression = \"(typeof \" + baseValueExpression + \" === 'string'\" + \"? \" + baseValueExpression + \".trim()\" + \": \" + baseValueExpression + \")\";\n  }\n\n  if (number) {\n    valueExpression = \"_n(\" + valueExpression + \")\";\n  }\n\n  var assignment = genAssignmentCode(value, valueExpression);\n  el.model = {\n    value: \"(\" + value + \")\",\n    expression: JSON.stringify(value),\n    callback: \"function (\" + baseValueExpression + \") {\" + assignment + \"}\"\n  };\n}\n/**\n * Cross-platform codegen helper for generating v-model value assignment code.\n */\n\n\nfunction genAssignmentCode(value, assignment) {\n  var res = parseModel(value);\n\n  if (res.key === null) {\n    return value + \"=\" + assignment;\n  } else {\n    return \"$set(\" + res.exp + \", \" + res.key + \", \" + assignment + \")\";\n  }\n}\n/**\n * Parse a v-model expression into a base path and a final key segment.\n * Handles both dot-path and possible square brackets.\n *\n * Possible cases:\n *\n * - test\n * - test[key]\n * - test[test1[key]]\n * - test[\"a\"][key]\n * - xxx.test[a[a].test1[key]]\n * - test.xxx.a[\"asa\"][test1[key]]\n *\n */\n\n\nvar len, str, chr, index$1, expressionPos, expressionEndPos;\n\nfunction parseModel(val) {\n  // Fix https://github.com/vuejs/vue/pull/7730\n  // allow v-model=\"obj.val \" (trailing whitespace)\n  val = val.trim();\n  len = val.length;\n\n  if (val.indexOf('[') < 0 || val.lastIndexOf(']') < len - 1) {\n    index$1 = val.lastIndexOf('.');\n\n    if (index$1 > -1) {\n      return {\n        exp: val.slice(0, index$1),\n        key: '\"' + val.slice(index$1 + 1) + '\"'\n      };\n    } else {\n      return {\n        exp: val,\n        key: null\n      };\n    }\n  }\n\n  str = val;\n  index$1 = expressionPos = expressionEndPos = 0;\n\n  while (!eof()) {\n    chr = next();\n    /* istanbul ignore if */\n\n    if (isStringStart(chr)) {\n      parseString(chr);\n    } else if (chr === 0x5B) {\n      parseBracket(chr);\n    }\n  }\n\n  return {\n    exp: val.slice(0, expressionPos),\n    key: val.slice(expressionPos + 1, expressionEndPos)\n  };\n}\n\nfunction next() {\n  return str.charCodeAt(++index$1);\n}\n\nfunction eof() {\n  return index$1 >= len;\n}\n\nfunction isStringStart(chr) {\n  return chr === 0x22 || chr === 0x27;\n}\n\nfunction parseBracket(chr) {\n  var inBracket = 1;\n  expressionPos = index$1;\n\n  while (!eof()) {\n    chr = next();\n\n    if (isStringStart(chr)) {\n      parseString(chr);\n      continue;\n    }\n\n    if (chr === 0x5B) {\n      inBracket++;\n    }\n\n    if (chr === 0x5D) {\n      inBracket--;\n    }\n\n    if (inBracket === 0) {\n      expressionEndPos = index$1;\n      break;\n    }\n  }\n}\n\nfunction parseString(chr) {\n  var stringQuote = chr;\n\n  while (!eof()) {\n    chr = next();\n\n    if (chr === stringQuote) {\n      break;\n    }\n  }\n}\n/*  */\n\n\nvar warn$1; // in some cases, the event used has to be determined at runtime\n// so we used some reserved tokens during compile.\n\nvar RANGE_TOKEN = '__r';\nvar CHECKBOX_RADIO_TOKEN = '__c';\n\nfunction model(el, dir, _warn) {\n  warn$1 = _warn;\n  var value = dir.value;\n  var modifiers = dir.modifiers;\n  var tag = el.tag;\n  var type = el.attrsMap.type;\n\n  if (true) {\n    // inputs with type=\"file\" are read only and setting the input's\n    // value will throw an error.\n    if (tag === 'input' && type === 'file') {\n      warn$1(\"<\" + el.tag + \" v-model=\\\"\" + value + \"\\\" type=\\\"file\\\">:\\n\" + \"File inputs are read only. Use a v-on:change listener instead.\", el.rawAttrsMap['v-model']);\n    }\n  }\n\n  if (el.component) {\n    genComponentModel(el, value, modifiers); // component v-model doesn't need extra runtime\n\n    return false;\n  } else if (tag === 'select') {\n    genSelect(el, value, modifiers);\n  } else if (tag === 'input' && type === 'checkbox') {\n    genCheckboxModel(el, value, modifiers);\n  } else if (tag === 'input' && type === 'radio') {\n    genRadioModel(el, value, modifiers);\n  } else if (tag === 'input' || tag === 'textarea') {\n    genDefaultModel(el, value, modifiers);\n  } else if (!config.isReservedTag(tag)) {\n    genComponentModel(el, value, modifiers); // component v-model doesn't need extra runtime\n\n    return false;\n  } else if (true) {\n    warn$1(\"<\" + el.tag + \" v-model=\\\"\" + value + \"\\\">: \" + \"v-model is not supported on this element type. \" + 'If you are working with contenteditable, it\\'s recommended to ' + 'wrap a library dedicated for that purpose inside a custom component.', el.rawAttrsMap['v-model']);\n  } // ensure runtime directive metadata\n\n\n  return true;\n}\n\nfunction genCheckboxModel(el, value, modifiers) {\n  var number = modifiers && modifiers.number;\n  var valueBinding = getBindingAttr(el, 'value') || 'null';\n  var trueValueBinding = getBindingAttr(el, 'true-value') || 'true';\n  var falseValueBinding = getBindingAttr(el, 'false-value') || 'false';\n  addProp(el, 'checked', \"Array.isArray(\" + value + \")\" + \"?_i(\" + value + \",\" + valueBinding + \")>-1\" + (trueValueBinding === 'true' ? \":(\" + value + \")\" : \":_q(\" + value + \",\" + trueValueBinding + \")\"));\n  addHandler(el, 'change', \"var $$a=\" + value + \",\" + '$$el=$event.target,' + \"$$c=$$el.checked?(\" + trueValueBinding + \"):(\" + falseValueBinding + \");\" + 'if(Array.isArray($$a)){' + \"var $$v=\" + (number ? '_n(' + valueBinding + ')' : valueBinding) + \",\" + '$$i=_i($$a,$$v);' + \"if($$el.checked){$$i<0&&(\" + genAssignmentCode(value, '$$a.concat([$$v])') + \")}\" + \"else{$$i>-1&&(\" + genAssignmentCode(value, '$$a.slice(0,$$i).concat($$a.slice($$i+1))') + \")}\" + \"}else{\" + genAssignmentCode(value, '$$c') + \"}\", null, true);\n}\n\nfunction genRadioModel(el, value, modifiers) {\n  var number = modifiers && modifiers.number;\n  var valueBinding = getBindingAttr(el, 'value') || 'null';\n  valueBinding = number ? \"_n(\" + valueBinding + \")\" : valueBinding;\n  addProp(el, 'checked', \"_q(\" + value + \",\" + valueBinding + \")\");\n  addHandler(el, 'change', genAssignmentCode(value, valueBinding), null, true);\n}\n\nfunction genSelect(el, value, modifiers) {\n  var number = modifiers && modifiers.number;\n  var selectedVal = \"Array.prototype.filter\" + \".call($event.target.options,function(o){return o.selected})\" + \".map(function(o){var val = \\\"_value\\\" in o ? o._value : o.value;\" + \"return \" + (number ? '_n(val)' : 'val') + \"})\";\n  var assignment = '$event.target.multiple ? $$selectedVal : $$selectedVal[0]';\n  var code = \"var $$selectedVal = \" + selectedVal + \";\";\n  code = code + \" \" + genAssignmentCode(value, assignment);\n  addHandler(el, 'change', code, null, true);\n}\n\nfunction genDefaultModel(el, value, modifiers) {\n  var type = el.attrsMap.type; // warn if v-bind:value conflicts with v-model\n  // except for inputs with v-bind:type\n\n  if (true) {\n    var value$1 = el.attrsMap['v-bind:value'] || el.attrsMap[':value'];\n    var typeBinding = el.attrsMap['v-bind:type'] || el.attrsMap[':type'];\n\n    if (value$1 && !typeBinding) {\n      var binding = el.attrsMap['v-bind:value'] ? 'v-bind:value' : ':value';\n      warn$1(binding + \"=\\\"\" + value$1 + \"\\\" conflicts with v-model on the same element \" + 'because the latter already expands to a value binding internally', el.rawAttrsMap[binding]);\n    }\n  }\n\n  var ref = modifiers || {};\n  var lazy = ref.lazy;\n  var number = ref.number;\n  var trim = ref.trim;\n  var needCompositionGuard = !lazy && type !== 'range';\n  var event = lazy ? 'change' : type === 'range' ? RANGE_TOKEN : 'input';\n  var valueExpression = '$event.target.value';\n\n  if (trim) {\n    valueExpression = \"$event.target.value.trim()\";\n  }\n\n  if (number) {\n    valueExpression = \"_n(\" + valueExpression + \")\";\n  }\n\n  var code = genAssignmentCode(value, valueExpression);\n\n  if (needCompositionGuard) {\n    code = \"if($event.target.composing)return;\" + code;\n  }\n\n  addProp(el, 'value', \"(\" + value + \")\");\n  addHandler(el, event, code, null, true);\n\n  if (trim || number) {\n    addHandler(el, 'blur', '$forceUpdate()');\n  }\n}\n/*  */\n// normalize v-model event tokens that can only be determined at runtime.\n// it's important to place the event as the first in the array because\n// the whole point is ensuring the v-model callback gets called before\n// user-attached handlers.\n\n\nfunction normalizeEvents(on) {\n  /* istanbul ignore if */\n  if (isDef(on[RANGE_TOKEN])) {\n    // IE input[type=range] only supports `change` event\n    var event = isIE ? 'change' : 'input';\n    on[event] = [].concat(on[RANGE_TOKEN], on[event] || []);\n    delete on[RANGE_TOKEN];\n  } // This was originally intended to fix #4521 but no longer necessary\n  // after 2.5. Keeping it for backwards compat with generated code from < 2.4\n\n  /* istanbul ignore if */\n\n\n  if (isDef(on[CHECKBOX_RADIO_TOKEN])) {\n    on.change = [].concat(on[CHECKBOX_RADIO_TOKEN], on.change || []);\n    delete on[CHECKBOX_RADIO_TOKEN];\n  }\n}\n\nvar target$1;\n\nfunction createOnceHandler$1(event, handler, capture) {\n  var _target = target$1; // save current target element in closure\n\n  return function onceHandler() {\n    var res = handler.apply(null, arguments);\n\n    if (res !== null) {\n      remove$2(event, onceHandler, capture, _target);\n    }\n  };\n} // #9446: Firefox <= 53 (in particular, ESR 52) has incorrect Event.timeStamp\n// implementation and does not fire microtasks in between event propagation, so\n// safe to exclude.\n\n\nvar useMicrotaskFix = isUsingMicroTask && !(isFF && Number(isFF[1]) <= 53);\n\nfunction add$1(name, handler, capture, passive) {\n  // async edge case #6566: inner click event triggers patch, event handler\n  // attached to outer element during patch, and triggered again. This\n  // happens because browsers fire microtask ticks between event propagation.\n  // the solution is simple: we save the timestamp when a handler is attached,\n  // and the handler would only fire if the event passed to it was fired\n  // AFTER it was attached.\n  if (useMicrotaskFix) {\n    var attachedTimestamp = currentFlushTimestamp;\n    var original = handler;\n\n    handler = original._wrapper = function (e) {\n      if ( // no bubbling, should always fire.\n      // this is just a safety net in case event.timeStamp is unreliable in\n      // certain weird environments...\n      e.target === e.currentTarget || // event is fired after handler attachment\n      e.timeStamp >= attachedTimestamp || // bail for environments that have buggy event.timeStamp implementations\n      // #9462 iOS 9 bug: event.timeStamp is 0 after history.pushState\n      // #9681 QtWebEngine event.timeStamp is negative value\n      e.timeStamp <= 0 || // #9448 bail if event is fired in another document in a multi-page\n      // electron/nw.js app, since event.timeStamp will be using a different\n      // starting reference\n      e.target.ownerDocument !== document) {\n        return original.apply(this, arguments);\n      }\n    };\n  }\n\n  target$1.addEventListener(name, handler, supportsPassive ? {\n    capture: capture,\n    passive: passive\n  } : capture);\n}\n\nfunction remove$2(name, handler, capture, _target) {\n  (_target || target$1).removeEventListener(name, handler._wrapper || handler, capture);\n}\n\nfunction updateDOMListeners(oldVnode, vnode) {\n  if (isUndef(oldVnode.data.on) && isUndef(vnode.data.on)) {\n    return;\n  }\n\n  var on = vnode.data.on || {};\n  var oldOn = oldVnode.data.on || {};\n  target$1 = vnode.elm;\n  normalizeEvents(on);\n  updateListeners(on, oldOn, add$1, remove$2, createOnceHandler$1, vnode.context);\n  target$1 = undefined;\n}\n\nvar events = {\n  create: updateDOMListeners,\n  update: updateDOMListeners\n};\n/*  */\n\nvar svgContainer;\n\nfunction updateDOMProps(oldVnode, vnode) {\n  if (isUndef(oldVnode.data.domProps) && isUndef(vnode.data.domProps)) {\n    return;\n  }\n\n  var key, cur;\n  var elm = vnode.elm;\n  var oldProps = oldVnode.data.domProps || {};\n  var props = vnode.data.domProps || {}; // clone observed objects, as the user probably wants to mutate it\n\n  if (isDef(props.__ob__)) {\n    props = vnode.data.domProps = extend({}, props);\n  }\n\n  for (key in oldProps) {\n    if (!(key in props)) {\n      elm[key] = '';\n    }\n  }\n\n  for (key in props) {\n    cur = props[key]; // ignore children if the node has textContent or innerHTML,\n    // as these will throw away existing DOM nodes and cause removal errors\n    // on subsequent patches (#3360)\n\n    if (key === 'textContent' || key === 'innerHTML') {\n      if (vnode.children) {\n        vnode.children.length = 0;\n      }\n\n      if (cur === oldProps[key]) {\n        continue;\n      } // #6601 work around Chrome version <= 55 bug where single textNode\n      // replaced by innerHTML/textContent retains its parentNode property\n\n\n      if (elm.childNodes.length === 1) {\n        elm.removeChild(elm.childNodes[0]);\n      }\n    }\n\n    if (key === 'value' && elm.tagName !== 'PROGRESS') {\n      // store value as _value as well since\n      // non-string values will be stringified\n      elm._value = cur; // avoid resetting cursor position when value is the same\n\n      var strCur = isUndef(cur) ? '' : String(cur);\n\n      if (shouldUpdateValue(elm, strCur)) {\n        elm.value = strCur;\n      }\n    } else if (key === 'innerHTML' && isSVG(elm.tagName) && isUndef(elm.innerHTML)) {\n      // IE doesn't support innerHTML for SVG elements\n      svgContainer = svgContainer || document.createElement('div');\n      svgContainer.innerHTML = \"<svg>\" + cur + \"</svg>\";\n      var svg = svgContainer.firstChild;\n\n      while (elm.firstChild) {\n        elm.removeChild(elm.firstChild);\n      }\n\n      while (svg.firstChild) {\n        elm.appendChild(svg.firstChild);\n      }\n    } else if ( // skip the update if old and new VDOM state is the same.\n    // `value` is handled separately because the DOM value may be temporarily\n    // out of sync with VDOM state due to focus, composition and modifiers.\n    // This  #4521 by skipping the unnecessary `checked` update.\n    cur !== oldProps[key]) {\n      // some property updates can throw\n      // e.g. `value` on <progress> w/ non-finite value\n      try {\n        elm[key] = cur;\n      } catch (e) {}\n    }\n  }\n} // check platforms/web/util/attrs.js acceptValue\n\n\nfunction shouldUpdateValue(elm, checkVal) {\n  return !elm.composing && (elm.tagName === 'OPTION' || isNotInFocusAndDirty(elm, checkVal) || isDirtyWithModifiers(elm, checkVal));\n}\n\nfunction isNotInFocusAndDirty(elm, checkVal) {\n  // return true when textbox (.number and .trim) loses focus and its value is\n  // not equal to the updated value\n  var notInFocus = true; // #6157\n  // work around IE bug when accessing document.activeElement in an iframe\n\n  try {\n    notInFocus = document.activeElement !== elm;\n  } catch (e) {}\n\n  return notInFocus && elm.value !== checkVal;\n}\n\nfunction isDirtyWithModifiers(elm, newVal) {\n  var value = elm.value;\n  var modifiers = elm._vModifiers; // injected by v-model runtime\n\n  if (isDef(modifiers)) {\n    if (modifiers.number) {\n      return toNumber(value) !== toNumber(newVal);\n    }\n\n    if (modifiers.trim) {\n      return value.trim() !== newVal.trim();\n    }\n  }\n\n  return value !== newVal;\n}\n\nvar domProps = {\n  create: updateDOMProps,\n  update: updateDOMProps\n};\n/*  */\n\nvar parseStyleText = cached(function (cssText) {\n  var res = {};\n  var listDelimiter = /;(?![^(]*\\))/g;\n  var propertyDelimiter = /:(.+)/;\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      var tmp = item.split(propertyDelimiter);\n      tmp.length > 1 && (res[tmp[0].trim()] = tmp[1].trim());\n    }\n  });\n  return res;\n}); // merge static and dynamic style data on the same vnode\n\nfunction normalizeStyleData(data) {\n  var style = normalizeStyleBinding(data.style); // static style is pre-processed into an object during compilation\n  // and is always a fresh object, so it's safe to merge into it\n\n  return data.staticStyle ? extend(data.staticStyle, style) : style;\n} // normalize possible array / string values into Object\n\n\nfunction normalizeStyleBinding(bindingStyle) {\n  if (Array.isArray(bindingStyle)) {\n    return toObject(bindingStyle);\n  }\n\n  if (typeof bindingStyle === 'string') {\n    return parseStyleText(bindingStyle);\n  }\n\n  return bindingStyle;\n}\n/**\n * parent component style should be after child's\n * so that parent component's style could override it\n */\n\n\nfunction getStyle(vnode, checkChild) {\n  var res = {};\n  var styleData;\n\n  if (checkChild) {\n    var childNode = vnode;\n\n    while (childNode.componentInstance) {\n      childNode = childNode.componentInstance._vnode;\n\n      if (childNode && childNode.data && (styleData = normalizeStyleData(childNode.data))) {\n        extend(res, styleData);\n      }\n    }\n  }\n\n  if (styleData = normalizeStyleData(vnode.data)) {\n    extend(res, styleData);\n  }\n\n  var parentNode = vnode;\n\n  while (parentNode = parentNode.parent) {\n    if (parentNode.data && (styleData = normalizeStyleData(parentNode.data))) {\n      extend(res, styleData);\n    }\n  }\n\n  return res;\n}\n/*  */\n\n\nvar cssVarRE = /^--/;\nvar importantRE = /\\s*!important$/;\n\nvar setProp = function (el, name, val) {\n  /* istanbul ignore if */\n  if (cssVarRE.test(name)) {\n    el.style.setProperty(name, val);\n  } else if (importantRE.test(val)) {\n    el.style.setProperty(hyphenate(name), val.replace(importantRE, ''), 'important');\n  } else {\n    var normalizedName = normalize(name);\n\n    if (Array.isArray(val)) {\n      // Support values array created by autoprefixer, e.g.\n      // {display: [\"-webkit-box\", \"-ms-flexbox\", \"flex\"]}\n      // Set them one by one, and the browser will only set those it can recognize\n      for (var i = 0, len = val.length; i < len; i++) {\n        el.style[normalizedName] = val[i];\n      }\n    } else {\n      el.style[normalizedName] = val;\n    }\n  }\n};\n\nvar vendorNames = ['Webkit', 'Moz', 'ms'];\nvar emptyStyle;\nvar normalize = cached(function (prop) {\n  emptyStyle = emptyStyle || document.createElement('div').style;\n  prop = camelize(prop);\n\n  if (prop !== 'filter' && prop in emptyStyle) {\n    return prop;\n  }\n\n  var capName = prop.charAt(0).toUpperCase() + prop.slice(1);\n\n  for (var i = 0; i < vendorNames.length; i++) {\n    var name = vendorNames[i] + capName;\n\n    if (name in emptyStyle) {\n      return name;\n    }\n  }\n});\n\nfunction updateStyle(oldVnode, vnode) {\n  var data = vnode.data;\n  var oldData = oldVnode.data;\n\n  if (isUndef(data.staticStyle) && isUndef(data.style) && isUndef(oldData.staticStyle) && isUndef(oldData.style)) {\n    return;\n  }\n\n  var cur, name;\n  var el = vnode.elm;\n  var oldStaticStyle = oldData.staticStyle;\n  var oldStyleBinding = oldData.normalizedStyle || oldData.style || {}; // if static style exists, stylebinding already merged into it when doing normalizeStyleData\n\n  var oldStyle = oldStaticStyle || oldStyleBinding;\n  var style = normalizeStyleBinding(vnode.data.style) || {}; // store normalized style under a different key for next diff\n  // make sure to clone it if it's reactive, since the user likely wants\n  // to mutate it.\n\n  vnode.data.normalizedStyle = isDef(style.__ob__) ? extend({}, style) : style;\n  var newStyle = getStyle(vnode, true);\n\n  for (name in oldStyle) {\n    if (isUndef(newStyle[name])) {\n      setProp(el, name, '');\n    }\n  }\n\n  for (name in newStyle) {\n    cur = newStyle[name];\n\n    if (cur !== oldStyle[name]) {\n      // ie9 setting to null has no effect, must use empty string\n      setProp(el, name, cur == null ? '' : cur);\n    }\n  }\n}\n\nvar style = {\n  create: updateStyle,\n  update: updateStyle\n};\n/*  */\n\nvar whitespaceRE = /\\s+/;\n/**\n * Add class with compatibility for SVG since classList is not supported on\n * SVG elements in IE\n */\n\nfunction addClass(el, cls) {\n  /* istanbul ignore if */\n  if (!cls || !(cls = cls.trim())) {\n    return;\n  }\n  /* istanbul ignore else */\n\n\n  if (el.classList) {\n    if (cls.indexOf(' ') > -1) {\n      cls.split(whitespaceRE).forEach(function (c) {\n        return el.classList.add(c);\n      });\n    } else {\n      el.classList.add(cls);\n    }\n  } else {\n    var cur = \" \" + (el.getAttribute('class') || '') + \" \";\n\n    if (cur.indexOf(' ' + cls + ' ') < 0) {\n      el.setAttribute('class', (cur + cls).trim());\n    }\n  }\n}\n/**\n * Remove class with compatibility for SVG since classList is not supported on\n * SVG elements in IE\n */\n\n\nfunction removeClass(el, cls) {\n  /* istanbul ignore if */\n  if (!cls || !(cls = cls.trim())) {\n    return;\n  }\n  /* istanbul ignore else */\n\n\n  if (el.classList) {\n    if (cls.indexOf(' ') > -1) {\n      cls.split(whitespaceRE).forEach(function (c) {\n        return el.classList.remove(c);\n      });\n    } else {\n      el.classList.remove(cls);\n    }\n\n    if (!el.classList.length) {\n      el.removeAttribute('class');\n    }\n  } else {\n    var cur = \" \" + (el.getAttribute('class') || '') + \" \";\n    var tar = ' ' + cls + ' ';\n\n    while (cur.indexOf(tar) >= 0) {\n      cur = cur.replace(tar, ' ');\n    }\n\n    cur = cur.trim();\n\n    if (cur) {\n      el.setAttribute('class', cur);\n    } else {\n      el.removeAttribute('class');\n    }\n  }\n}\n/*  */\n\n\nfunction resolveTransition(def$$1) {\n  if (!def$$1) {\n    return;\n  }\n  /* istanbul ignore else */\n\n\n  if (typeof def$$1 === 'object') {\n    var res = {};\n\n    if (def$$1.css !== false) {\n      extend(res, autoCssTransition(def$$1.name || 'v'));\n    }\n\n    extend(res, def$$1);\n    return res;\n  } else if (typeof def$$1 === 'string') {\n    return autoCssTransition(def$$1);\n  }\n}\n\nvar autoCssTransition = cached(function (name) {\n  return {\n    enterClass: name + \"-enter\",\n    enterToClass: name + \"-enter-to\",\n    enterActiveClass: name + \"-enter-active\",\n    leaveClass: name + \"-leave\",\n    leaveToClass: name + \"-leave-to\",\n    leaveActiveClass: name + \"-leave-active\"\n  };\n});\nvar hasTransition = inBrowser && !isIE9;\nvar TRANSITION = 'transition';\nvar ANIMATION = 'animation'; // Transition property/event sniffing\n\nvar transitionProp = 'transition';\nvar transitionEndEvent = 'transitionend';\nvar animationProp = 'animation';\nvar animationEndEvent = 'animationend';\n\nif (hasTransition) {\n  /* istanbul ignore if */\n  if (window.ontransitionend === undefined && window.onwebkittransitionend !== undefined) {\n    transitionProp = 'WebkitTransition';\n    transitionEndEvent = 'webkitTransitionEnd';\n  }\n\n  if (window.onanimationend === undefined && window.onwebkitanimationend !== undefined) {\n    animationProp = 'WebkitAnimation';\n    animationEndEvent = 'webkitAnimationEnd';\n  }\n} // binding to window is necessary to make hot reload work in IE in strict mode\n\n\nvar raf = inBrowser ? window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : setTimeout :\n/* istanbul ignore next */\nfunction (fn) {\n  return fn();\n};\n\nfunction nextFrame(fn) {\n  raf(function () {\n    raf(fn);\n  });\n}\n\nfunction addTransitionClass(el, cls) {\n  var transitionClasses = el._transitionClasses || (el._transitionClasses = []);\n\n  if (transitionClasses.indexOf(cls) < 0) {\n    transitionClasses.push(cls);\n    addClass(el, cls);\n  }\n}\n\nfunction removeTransitionClass(el, cls) {\n  if (el._transitionClasses) {\n    remove(el._transitionClasses, cls);\n  }\n\n  removeClass(el, cls);\n}\n\nfunction whenTransitionEnds(el, expectedType, cb) {\n  var ref = getTransitionInfo(el, expectedType);\n  var type = ref.type;\n  var timeout = ref.timeout;\n  var propCount = ref.propCount;\n\n  if (!type) {\n    return cb();\n  }\n\n  var event = type === TRANSITION ? transitionEndEvent : animationEndEvent;\n  var ended = 0;\n\n  var end = function () {\n    el.removeEventListener(event, onEnd);\n    cb();\n  };\n\n  var onEnd = function (e) {\n    if (e.target === el) {\n      if (++ended >= propCount) {\n        end();\n      }\n    }\n  };\n\n  setTimeout(function () {\n    if (ended < propCount) {\n      end();\n    }\n  }, timeout + 1);\n  el.addEventListener(event, onEnd);\n}\n\nvar transformRE = /\\b(transform|all)(,|$)/;\n\nfunction getTransitionInfo(el, expectedType) {\n  var styles = window.getComputedStyle(el); // JSDOM may return undefined for transition properties\n\n  var transitionDelays = (styles[transitionProp + 'Delay'] || '').split(', ');\n  var transitionDurations = (styles[transitionProp + 'Duration'] || '').split(', ');\n  var transitionTimeout = getTimeout(transitionDelays, transitionDurations);\n  var animationDelays = (styles[animationProp + 'Delay'] || '').split(', ');\n  var animationDurations = (styles[animationProp + 'Duration'] || '').split(', ');\n  var animationTimeout = getTimeout(animationDelays, animationDurations);\n  var type;\n  var timeout = 0;\n  var propCount = 0;\n  /* istanbul ignore if */\n\n  if (expectedType === TRANSITION) {\n    if (transitionTimeout > 0) {\n      type = TRANSITION;\n      timeout = transitionTimeout;\n      propCount = transitionDurations.length;\n    }\n  } else if (expectedType === ANIMATION) {\n    if (animationTimeout > 0) {\n      type = ANIMATION;\n      timeout = animationTimeout;\n      propCount = animationDurations.length;\n    }\n  } else {\n    timeout = Math.max(transitionTimeout, animationTimeout);\n    type = timeout > 0 ? transitionTimeout > animationTimeout ? TRANSITION : ANIMATION : null;\n    propCount = type ? type === TRANSITION ? transitionDurations.length : animationDurations.length : 0;\n  }\n\n  var hasTransform = type === TRANSITION && transformRE.test(styles[transitionProp + 'Property']);\n  return {\n    type: type,\n    timeout: timeout,\n    propCount: propCount,\n    hasTransform: hasTransform\n  };\n}\n\nfunction getTimeout(delays, durations) {\n  /* istanbul ignore next */\n  while (delays.length < durations.length) {\n    delays = delays.concat(delays);\n  }\n\n  return Math.max.apply(null, durations.map(function (d, i) {\n    return toMs(d) + toMs(delays[i]);\n  }));\n} // Old versions of Chromium (below 61.0.3163.100) formats floating pointer numbers\n// in a locale-dependent way, using a comma instead of a dot.\n// If comma is not replaced with a dot, the input will be rounded down (i.e. acting\n// as a floor function) causing unexpected behaviors\n\n\nfunction toMs(s) {\n  return Number(s.slice(0, -1).replace(',', '.')) * 1000;\n}\n/*  */\n\n\nfunction enter(vnode, toggleDisplay) {\n  var el = vnode.elm; // call leave callback now\n\n  if (isDef(el._leaveCb)) {\n    el._leaveCb.cancelled = true;\n\n    el._leaveCb();\n  }\n\n  var data = resolveTransition(vnode.data.transition);\n\n  if (isUndef(data)) {\n    return;\n  }\n  /* istanbul ignore if */\n\n\n  if (isDef(el._enterCb) || el.nodeType !== 1) {\n    return;\n  }\n\n  var css = data.css;\n  var type = data.type;\n  var enterClass = data.enterClass;\n  var enterToClass = data.enterToClass;\n  var enterActiveClass = data.enterActiveClass;\n  var appearClass = data.appearClass;\n  var appearToClass = data.appearToClass;\n  var appearActiveClass = data.appearActiveClass;\n  var beforeEnter = data.beforeEnter;\n  var enter = data.enter;\n  var afterEnter = data.afterEnter;\n  var enterCancelled = data.enterCancelled;\n  var beforeAppear = data.beforeAppear;\n  var appear = data.appear;\n  var afterAppear = data.afterAppear;\n  var appearCancelled = data.appearCancelled;\n  var duration = data.duration; // activeInstance will always be the <transition> component managing this\n  // transition. One edge case to check is when the <transition> is placed\n  // as the root node of a child component. In that case we need to check\n  // <transition>'s parent for appear check.\n\n  var context = activeInstance;\n  var transitionNode = activeInstance.$vnode;\n\n  while (transitionNode && transitionNode.parent) {\n    context = transitionNode.context;\n    transitionNode = transitionNode.parent;\n  }\n\n  var isAppear = !context._isMounted || !vnode.isRootInsert;\n\n  if (isAppear && !appear && appear !== '') {\n    return;\n  }\n\n  var startClass = isAppear && appearClass ? appearClass : enterClass;\n  var activeClass = isAppear && appearActiveClass ? appearActiveClass : enterActiveClass;\n  var toClass = isAppear && appearToClass ? appearToClass : enterToClass;\n  var beforeEnterHook = isAppear ? beforeAppear || beforeEnter : beforeEnter;\n  var enterHook = isAppear ? typeof appear === 'function' ? appear : enter : enter;\n  var afterEnterHook = isAppear ? afterAppear || afterEnter : afterEnter;\n  var enterCancelledHook = isAppear ? appearCancelled || enterCancelled : enterCancelled;\n  var explicitEnterDuration = toNumber(isObject(duration) ? duration.enter : duration);\n\n  if ( true && explicitEnterDuration != null) {\n    checkDuration(explicitEnterDuration, 'enter', vnode);\n  }\n\n  var expectsCSS = css !== false && !isIE9;\n  var userWantsControl = getHookArgumentsLength(enterHook);\n  var cb = el._enterCb = once(function () {\n    if (expectsCSS) {\n      removeTransitionClass(el, toClass);\n      removeTransitionClass(el, activeClass);\n    }\n\n    if (cb.cancelled) {\n      if (expectsCSS) {\n        removeTransitionClass(el, startClass);\n      }\n\n      enterCancelledHook && enterCancelledHook(el);\n    } else {\n      afterEnterHook && afterEnterHook(el);\n    }\n\n    el._enterCb = null;\n  });\n\n  if (!vnode.data.show) {\n    // remove pending leave element on enter by injecting an insert hook\n    mergeVNodeHook(vnode, 'insert', function () {\n      var parent = el.parentNode;\n      var pendingNode = parent && parent._pending && parent._pending[vnode.key];\n\n      if (pendingNode && pendingNode.tag === vnode.tag && pendingNode.elm._leaveCb) {\n        pendingNode.elm._leaveCb();\n      }\n\n      enterHook && enterHook(el, cb);\n    });\n  } // start enter transition\n\n\n  beforeEnterHook && beforeEnterHook(el);\n\n  if (expectsCSS) {\n    addTransitionClass(el, startClass);\n    addTransitionClass(el, activeClass);\n    nextFrame(function () {\n      removeTransitionClass(el, startClass);\n\n      if (!cb.cancelled) {\n        addTransitionClass(el, toClass);\n\n        if (!userWantsControl) {\n          if (isValidDuration(explicitEnterDuration)) {\n            setTimeout(cb, explicitEnterDuration);\n          } else {\n            whenTransitionEnds(el, type, cb);\n          }\n        }\n      }\n    });\n  }\n\n  if (vnode.data.show) {\n    toggleDisplay && toggleDisplay();\n    enterHook && enterHook(el, cb);\n  }\n\n  if (!expectsCSS && !userWantsControl) {\n    cb();\n  }\n}\n\nfunction leave(vnode, rm) {\n  var el = vnode.elm; // call enter callback now\n\n  if (isDef(el._enterCb)) {\n    el._enterCb.cancelled = true;\n\n    el._enterCb();\n  }\n\n  var data = resolveTransition(vnode.data.transition);\n\n  if (isUndef(data) || el.nodeType !== 1) {\n    return rm();\n  }\n  /* istanbul ignore if */\n\n\n  if (isDef(el._leaveCb)) {\n    return;\n  }\n\n  var css = data.css;\n  var type = data.type;\n  var leaveClass = data.leaveClass;\n  var leaveToClass = data.leaveToClass;\n  var leaveActiveClass = data.leaveActiveClass;\n  var beforeLeave = data.beforeLeave;\n  var leave = data.leave;\n  var afterLeave = data.afterLeave;\n  var leaveCancelled = data.leaveCancelled;\n  var delayLeave = data.delayLeave;\n  var duration = data.duration;\n  var expectsCSS = css !== false && !isIE9;\n  var userWantsControl = getHookArgumentsLength(leave);\n  var explicitLeaveDuration = toNumber(isObject(duration) ? duration.leave : duration);\n\n  if ( true && isDef(explicitLeaveDuration)) {\n    checkDuration(explicitLeaveDuration, 'leave', vnode);\n  }\n\n  var cb = el._leaveCb = once(function () {\n    if (el.parentNode && el.parentNode._pending) {\n      el.parentNode._pending[vnode.key] = null;\n    }\n\n    if (expectsCSS) {\n      removeTransitionClass(el, leaveToClass);\n      removeTransitionClass(el, leaveActiveClass);\n    }\n\n    if (cb.cancelled) {\n      if (expectsCSS) {\n        removeTransitionClass(el, leaveClass);\n      }\n\n      leaveCancelled && leaveCancelled(el);\n    } else {\n      rm();\n      afterLeave && afterLeave(el);\n    }\n\n    el._leaveCb = null;\n  });\n\n  if (delayLeave) {\n    delayLeave(performLeave);\n  } else {\n    performLeave();\n  }\n\n  function performLeave() {\n    // the delayed leave may have already been cancelled\n    if (cb.cancelled) {\n      return;\n    } // record leaving element\n\n\n    if (!vnode.data.show && el.parentNode) {\n      (el.parentNode._pending || (el.parentNode._pending = {}))[vnode.key] = vnode;\n    }\n\n    beforeLeave && beforeLeave(el);\n\n    if (expectsCSS) {\n      addTransitionClass(el, leaveClass);\n      addTransitionClass(el, leaveActiveClass);\n      nextFrame(function () {\n        removeTransitionClass(el, leaveClass);\n\n        if (!cb.cancelled) {\n          addTransitionClass(el, leaveToClass);\n\n          if (!userWantsControl) {\n            if (isValidDuration(explicitLeaveDuration)) {\n              setTimeout(cb, explicitLeaveDuration);\n            } else {\n              whenTransitionEnds(el, type, cb);\n            }\n          }\n        }\n      });\n    }\n\n    leave && leave(el, cb);\n\n    if (!expectsCSS && !userWantsControl) {\n      cb();\n    }\n  }\n} // only used in dev mode\n\n\nfunction checkDuration(val, name, vnode) {\n  if (typeof val !== 'number') {\n    warn(\"<transition> explicit \" + name + \" duration is not a valid number - \" + \"got \" + JSON.stringify(val) + \".\", vnode.context);\n  } else if (isNaN(val)) {\n    warn(\"<transition> explicit \" + name + \" duration is NaN - \" + 'the duration expression might be incorrect.', vnode.context);\n  }\n}\n\nfunction isValidDuration(val) {\n  return typeof val === 'number' && !isNaN(val);\n}\n/**\n * Normalize a transition hook's argument length. The hook may be:\n * - a merged hook (invoker) with the original in .fns\n * - a wrapped component method (check ._length)\n * - a plain function (.length)\n */\n\n\nfunction getHookArgumentsLength(fn) {\n  if (isUndef(fn)) {\n    return false;\n  }\n\n  var invokerFns = fn.fns;\n\n  if (isDef(invokerFns)) {\n    // invoker\n    return getHookArgumentsLength(Array.isArray(invokerFns) ? invokerFns[0] : invokerFns);\n  } else {\n    return (fn._length || fn.length) > 1;\n  }\n}\n\nfunction _enter(_, vnode) {\n  if (vnode.data.show !== true) {\n    enter(vnode);\n  }\n}\n\nvar transition = inBrowser ? {\n  create: _enter,\n  activate: _enter,\n  remove: function remove$$1(vnode, rm) {\n    /* istanbul ignore else */\n    if (vnode.data.show !== true) {\n      leave(vnode, rm);\n    } else {\n      rm();\n    }\n  }\n} : {};\nvar platformModules = [attrs, klass, events, domProps, style, transition];\n/*  */\n// the directive module should be applied last, after all\n// built-in modules have been applied.\n\nvar modules = platformModules.concat(baseModules);\nvar patch = createPatchFunction({\n  nodeOps: nodeOps,\n  modules: modules\n});\n/**\n * Not type checking this file because flow doesn't like attaching\n * properties to Elements.\n */\n\n/* istanbul ignore if */\n\nif (isIE9) {\n  // http://www.matts411.com/post/internet-explorer-9-oninput/\n  document.addEventListener('selectionchange', function () {\n    var el = document.activeElement;\n\n    if (el && el.vmodel) {\n      trigger(el, 'input');\n    }\n  });\n}\n\nvar directive = {\n  inserted: function inserted(el, binding, vnode, oldVnode) {\n    if (vnode.tag === 'select') {\n      // #6903\n      if (oldVnode.elm && !oldVnode.elm._vOptions) {\n        mergeVNodeHook(vnode, 'postpatch', function () {\n          directive.componentUpdated(el, binding, vnode);\n        });\n      } else {\n        setSelected(el, binding, vnode.context);\n      }\n\n      el._vOptions = [].map.call(el.options, getValue);\n    } else if (vnode.tag === 'textarea' || isTextInputType(el.type)) {\n      el._vModifiers = binding.modifiers;\n\n      if (!binding.modifiers.lazy) {\n        el.addEventListener('compositionstart', onCompositionStart);\n        el.addEventListener('compositionend', onCompositionEnd); // Safari < 10.2 & UIWebView doesn't fire compositionend when\n        // switching focus before confirming composition choice\n        // this also fixes the issue where some browsers e.g. iOS Chrome\n        // fires \"change\" instead of \"input\" on autocomplete.\n\n        el.addEventListener('change', onCompositionEnd);\n        /* istanbul ignore if */\n\n        if (isIE9) {\n          el.vmodel = true;\n        }\n      }\n    }\n  },\n  componentUpdated: function componentUpdated(el, binding, vnode) {\n    if (vnode.tag === 'select') {\n      setSelected(el, binding, vnode.context); // in case the options rendered by v-for have changed,\n      // it's possible that the value is out-of-sync with the rendered options.\n      // detect such cases and filter out values that no longer has a matching\n      // option in the DOM.\n\n      var prevOptions = el._vOptions;\n      var curOptions = el._vOptions = [].map.call(el.options, getValue);\n\n      if (curOptions.some(function (o, i) {\n        return !looseEqual(o, prevOptions[i]);\n      })) {\n        // trigger change event if\n        // no matching option found for at least one value\n        var needReset = el.multiple ? binding.value.some(function (v) {\n          return hasNoMatchingOption(v, curOptions);\n        }) : binding.value !== binding.oldValue && hasNoMatchingOption(binding.value, curOptions);\n\n        if (needReset) {\n          trigger(el, 'change');\n        }\n      }\n    }\n  }\n};\n\nfunction setSelected(el, binding, vm) {\n  actuallySetSelected(el, binding, vm);\n  /* istanbul ignore if */\n\n  if (isIE || isEdge) {\n    setTimeout(function () {\n      actuallySetSelected(el, binding, vm);\n    }, 0);\n  }\n}\n\nfunction actuallySetSelected(el, binding, vm) {\n  var value = binding.value;\n  var isMultiple = el.multiple;\n\n  if (isMultiple && !Array.isArray(value)) {\n     true && warn(\"<select multiple v-model=\\\"\" + binding.expression + \"\\\"> \" + \"expects an Array value for its binding, but got \" + Object.prototype.toString.call(value).slice(8, -1), vm);\n    return;\n  }\n\n  var selected, option;\n\n  for (var i = 0, l = el.options.length; i < l; i++) {\n    option = el.options[i];\n\n    if (isMultiple) {\n      selected = looseIndexOf(value, getValue(option)) > -1;\n\n      if (option.selected !== selected) {\n        option.selected = selected;\n      }\n    } else {\n      if (looseEqual(getValue(option), value)) {\n        if (el.selectedIndex !== i) {\n          el.selectedIndex = i;\n        }\n\n        return;\n      }\n    }\n  }\n\n  if (!isMultiple) {\n    el.selectedIndex = -1;\n  }\n}\n\nfunction hasNoMatchingOption(value, options) {\n  return options.every(function (o) {\n    return !looseEqual(o, value);\n  });\n}\n\nfunction getValue(option) {\n  return '_value' in option ? option._value : option.value;\n}\n\nfunction onCompositionStart(e) {\n  e.target.composing = true;\n}\n\nfunction onCompositionEnd(e) {\n  // prevent triggering an input event for no reason\n  if (!e.target.composing) {\n    return;\n  }\n\n  e.target.composing = false;\n  trigger(e.target, 'input');\n}\n\nfunction trigger(el, type) {\n  var e = document.createEvent('HTMLEvents');\n  e.initEvent(type, true, true);\n  el.dispatchEvent(e);\n}\n/*  */\n// recursively search for possible transition defined inside the component root\n\n\nfunction locateNode(vnode) {\n  return vnode.componentInstance && (!vnode.data || !vnode.data.transition) ? locateNode(vnode.componentInstance._vnode) : vnode;\n}\n\nvar show = {\n  bind: function bind(el, ref, vnode) {\n    var value = ref.value;\n    vnode = locateNode(vnode);\n    var transition$$1 = vnode.data && vnode.data.transition;\n    var originalDisplay = el.__vOriginalDisplay = el.style.display === 'none' ? '' : el.style.display;\n\n    if (value && transition$$1) {\n      vnode.data.show = true;\n      enter(vnode, function () {\n        el.style.display = originalDisplay;\n      });\n    } else {\n      el.style.display = value ? originalDisplay : 'none';\n    }\n  },\n  update: function update(el, ref, vnode) {\n    var value = ref.value;\n    var oldValue = ref.oldValue;\n    /* istanbul ignore if */\n\n    if (!value === !oldValue) {\n      return;\n    }\n\n    vnode = locateNode(vnode);\n    var transition$$1 = vnode.data && vnode.data.transition;\n\n    if (transition$$1) {\n      vnode.data.show = true;\n\n      if (value) {\n        enter(vnode, function () {\n          el.style.display = el.__vOriginalDisplay;\n        });\n      } else {\n        leave(vnode, function () {\n          el.style.display = 'none';\n        });\n      }\n    } else {\n      el.style.display = value ? el.__vOriginalDisplay : 'none';\n    }\n  },\n  unbind: function unbind(el, binding, vnode, oldVnode, isDestroy) {\n    if (!isDestroy) {\n      el.style.display = el.__vOriginalDisplay;\n    }\n  }\n};\nvar platformDirectives = {\n  model: directive,\n  show: show\n};\n/*  */\n\nvar transitionProps = {\n  name: String,\n  appear: Boolean,\n  css: Boolean,\n  mode: String,\n  type: String,\n  enterClass: String,\n  leaveClass: String,\n  enterToClass: String,\n  leaveToClass: String,\n  enterActiveClass: String,\n  leaveActiveClass: String,\n  appearClass: String,\n  appearActiveClass: String,\n  appearToClass: String,\n  duration: [Number, String, Object]\n}; // in case the child is also an abstract component, e.g. <keep-alive>\n// we want to recursively retrieve the real component to be rendered\n\nfunction getRealChild(vnode) {\n  var compOptions = vnode && vnode.componentOptions;\n\n  if (compOptions && compOptions.Ctor.options.abstract) {\n    return getRealChild(getFirstComponentChild(compOptions.children));\n  } else {\n    return vnode;\n  }\n}\n\nfunction extractTransitionData(comp) {\n  var data = {};\n  var options = comp.$options; // props\n\n  for (var key in options.propsData) {\n    data[key] = comp[key];\n  } // events.\n  // extract listeners and pass them directly to the transition methods\n\n\n  var listeners = options._parentListeners;\n\n  for (var key$1 in listeners) {\n    data[camelize(key$1)] = listeners[key$1];\n  }\n\n  return data;\n}\n\nfunction placeholder(h, rawChild) {\n  if (/\\d-keep-alive$/.test(rawChild.tag)) {\n    return h('keep-alive', {\n      props: rawChild.componentOptions.propsData\n    });\n  }\n}\n\nfunction hasParentTransition(vnode) {\n  while (vnode = vnode.parent) {\n    if (vnode.data.transition) {\n      return true;\n    }\n  }\n}\n\nfunction isSameChild(child, oldChild) {\n  return oldChild.key === child.key && oldChild.tag === child.tag;\n}\n\nvar isNotTextNode = function (c) {\n  return c.tag || isAsyncPlaceholder(c);\n};\n\nvar isVShowDirective = function (d) {\n  return d.name === 'show';\n};\n\nvar Transition = {\n  name: 'transition',\n  props: transitionProps,\n  abstract: true,\n  render: function render(h) {\n    var this$1 = this;\n    var children = this.$slots.default;\n\n    if (!children) {\n      return;\n    } // filter out text nodes (possible whitespaces)\n\n\n    children = children.filter(isNotTextNode);\n    /* istanbul ignore if */\n\n    if (!children.length) {\n      return;\n    } // warn multiple elements\n\n\n    if ( true && children.length > 1) {\n      warn('<transition> can only be used on a single element. Use ' + '<transition-group> for lists.', this.$parent);\n    }\n\n    var mode = this.mode; // warn invalid mode\n\n    if ( true && mode && mode !== 'in-out' && mode !== 'out-in') {\n      warn('invalid <transition> mode: ' + mode, this.$parent);\n    }\n\n    var rawChild = children[0]; // if this is a component root node and the component's\n    // parent container node also has transition, skip.\n\n    if (hasParentTransition(this.$vnode)) {\n      return rawChild;\n    } // apply transition data to child\n    // use getRealChild() to ignore abstract components e.g. keep-alive\n\n\n    var child = getRealChild(rawChild);\n    /* istanbul ignore if */\n\n    if (!child) {\n      return rawChild;\n    }\n\n    if (this._leaving) {\n      return placeholder(h, rawChild);\n    } // ensure a key that is unique to the vnode type and to this transition\n    // component instance. This key will be used to remove pending leaving nodes\n    // during entering.\n\n\n    var id = \"__transition-\" + this._uid + \"-\";\n    child.key = child.key == null ? child.isComment ? id + 'comment' : id + child.tag : isPrimitive(child.key) ? String(child.key).indexOf(id) === 0 ? child.key : id + child.key : child.key;\n    var data = (child.data || (child.data = {})).transition = extractTransitionData(this);\n    var oldRawChild = this._vnode;\n    var oldChild = getRealChild(oldRawChild); // mark v-show\n    // so that the transition module can hand over the control to the directive\n\n    if (child.data.directives && child.data.directives.some(isVShowDirective)) {\n      child.data.show = true;\n    }\n\n    if (oldChild && oldChild.data && !isSameChild(child, oldChild) && !isAsyncPlaceholder(oldChild) && // #6687 component root is a comment node\n    !(oldChild.componentInstance && oldChild.componentInstance._vnode.isComment)) {\n      // replace old child transition data with fresh one\n      // important for dynamic transitions!\n      var oldData = oldChild.data.transition = extend({}, data); // handle transition mode\n\n      if (mode === 'out-in') {\n        // return placeholder node and queue update when leave finishes\n        this._leaving = true;\n        mergeVNodeHook(oldData, 'afterLeave', function () {\n          this$1._leaving = false;\n          this$1.$forceUpdate();\n        });\n        return placeholder(h, rawChild);\n      } else if (mode === 'in-out') {\n        if (isAsyncPlaceholder(child)) {\n          return oldRawChild;\n        }\n\n        var delayedLeave;\n\n        var performLeave = function () {\n          delayedLeave();\n        };\n\n        mergeVNodeHook(data, 'afterEnter', performLeave);\n        mergeVNodeHook(data, 'enterCancelled', performLeave);\n        mergeVNodeHook(oldData, 'delayLeave', function (leave) {\n          delayedLeave = leave;\n        });\n      }\n    }\n\n    return rawChild;\n  }\n};\n/*  */\n\nvar props = extend({\n  tag: String,\n  moveClass: String\n}, transitionProps);\ndelete props.mode;\nvar TransitionGroup = {\n  props: props,\n  beforeMount: function beforeMount() {\n    var this$1 = this;\n    var update = this._update;\n\n    this._update = function (vnode, hydrating) {\n      var restoreActiveInstance = setActiveInstance(this$1); // force removing pass\n\n      this$1.__patch__(this$1._vnode, this$1.kept, false, // hydrating\n      true // removeOnly (!important, avoids unnecessary moves)\n      );\n\n      this$1._vnode = this$1.kept;\n      restoreActiveInstance();\n      update.call(this$1, vnode, hydrating);\n    };\n  },\n  render: function render(h) {\n    var tag = this.tag || this.$vnode.data.tag || 'span';\n    var map = Object.create(null);\n    var prevChildren = this.prevChildren = this.children;\n    var rawChildren = this.$slots.default || [];\n    var children = this.children = [];\n    var transitionData = extractTransitionData(this);\n\n    for (var i = 0; i < rawChildren.length; i++) {\n      var c = rawChildren[i];\n\n      if (c.tag) {\n        if (c.key != null && String(c.key).indexOf('__vlist') !== 0) {\n          children.push(c);\n          map[c.key] = c;\n          (c.data || (c.data = {})).transition = transitionData;\n        } else if (true) {\n          var opts = c.componentOptions;\n          var name = opts ? opts.Ctor.options.name || opts.tag || '' : c.tag;\n          warn(\"<transition-group> children must be keyed: <\" + name + \">\");\n        }\n      }\n    }\n\n    if (prevChildren) {\n      var kept = [];\n      var removed = [];\n\n      for (var i$1 = 0; i$1 < prevChildren.length; i$1++) {\n        var c$1 = prevChildren[i$1];\n        c$1.data.transition = transitionData;\n        c$1.data.pos = c$1.elm.getBoundingClientRect();\n\n        if (map[c$1.key]) {\n          kept.push(c$1);\n        } else {\n          removed.push(c$1);\n        }\n      }\n\n      this.kept = h(tag, null, kept);\n      this.removed = removed;\n    }\n\n    return h(tag, null, children);\n  },\n  updated: function updated() {\n    var children = this.prevChildren;\n    var moveClass = this.moveClass || (this.name || 'v') + '-move';\n\n    if (!children.length || !this.hasMove(children[0].elm, moveClass)) {\n      return;\n    } // we divide the work into three loops to avoid mixing DOM reads and writes\n    // in each iteration - which helps prevent layout thrashing.\n\n\n    children.forEach(callPendingCbs);\n    children.forEach(recordPosition);\n    children.forEach(applyTranslation); // force reflow to put everything in position\n    // assign to this to avoid being removed in tree-shaking\n    // $flow-disable-line\n\n    this._reflow = document.body.offsetHeight;\n    children.forEach(function (c) {\n      if (c.data.moved) {\n        var el = c.elm;\n        var s = el.style;\n        addTransitionClass(el, moveClass);\n        s.transform = s.WebkitTransform = s.transitionDuration = '';\n        el.addEventListener(transitionEndEvent, el._moveCb = function cb(e) {\n          if (e && e.target !== el) {\n            return;\n          }\n\n          if (!e || /transform$/.test(e.propertyName)) {\n            el.removeEventListener(transitionEndEvent, cb);\n            el._moveCb = null;\n            removeTransitionClass(el, moveClass);\n          }\n        });\n      }\n    });\n  },\n  methods: {\n    hasMove: function hasMove(el, moveClass) {\n      /* istanbul ignore if */\n      if (!hasTransition) {\n        return false;\n      }\n      /* istanbul ignore if */\n\n\n      if (this._hasMove) {\n        return this._hasMove;\n      } // Detect whether an element with the move class applied has\n      // CSS transitions. Since the element may be inside an entering\n      // transition at this very moment, we make a clone of it and remove\n      // all other transition classes applied to ensure only the move class\n      // is applied.\n\n\n      var clone = el.cloneNode();\n\n      if (el._transitionClasses) {\n        el._transitionClasses.forEach(function (cls) {\n          removeClass(clone, cls);\n        });\n      }\n\n      addClass(clone, moveClass);\n      clone.style.display = 'none';\n      this.$el.appendChild(clone);\n      var info = getTransitionInfo(clone);\n      this.$el.removeChild(clone);\n      return this._hasMove = info.hasTransform;\n    }\n  }\n};\n\nfunction callPendingCbs(c) {\n  /* istanbul ignore if */\n  if (c.elm._moveCb) {\n    c.elm._moveCb();\n  }\n  /* istanbul ignore if */\n\n\n  if (c.elm._enterCb) {\n    c.elm._enterCb();\n  }\n}\n\nfunction recordPosition(c) {\n  c.data.newPos = c.elm.getBoundingClientRect();\n}\n\nfunction applyTranslation(c) {\n  var oldPos = c.data.pos;\n  var newPos = c.data.newPos;\n  var dx = oldPos.left - newPos.left;\n  var dy = oldPos.top - newPos.top;\n\n  if (dx || dy) {\n    c.data.moved = true;\n    var s = c.elm.style;\n    s.transform = s.WebkitTransform = \"translate(\" + dx + \"px,\" + dy + \"px)\";\n    s.transitionDuration = '0s';\n  }\n}\n\nvar platformComponents = {\n  Transition: Transition,\n  TransitionGroup: TransitionGroup\n};\n/*  */\n// install platform specific utils\n\nVue.config.mustUseProp = mustUseProp;\nVue.config.isReservedTag = isReservedTag;\nVue.config.isReservedAttr = isReservedAttr;\nVue.config.getTagNamespace = getTagNamespace;\nVue.config.isUnknownElement = isUnknownElement; // install platform runtime directives & components\n\nextend(Vue.options.directives, platformDirectives);\nextend(Vue.options.components, platformComponents); // install platform patch function\n\nVue.prototype.__patch__ = inBrowser ? patch : noop; // public mount method\n\nVue.prototype.$mount = function (el, hydrating) {\n  el = el && inBrowser ? query(el) : undefined;\n  return mountComponent(this, el, hydrating);\n}; // devtools global hook\n\n/* istanbul ignore next */\n\n\nif (inBrowser) {\n  setTimeout(function () {\n    if (config.devtools) {\n      if (devtools) {\n        devtools.emit('init', Vue);\n      } else if (true) {\n        console[console.info ? 'info' : 'log']('Download the Vue Devtools extension for a better development experience:\\n' + 'https://github.com/vuejs/vue-devtools');\n      }\n    }\n\n    if ( true && config.productionTip !== false && typeof console !== 'undefined') {\n      console[console.info ? 'info' : 'log'](\"You are running Vue in development mode.\\n\" + \"Make sure to turn on production mode when deploying for production.\\n\" + \"See more tips at https://vuejs.org/guide/deployment.html\");\n    }\n  }, 0);\n}\n/*  */\n\n\nvar defaultTagRE = /\\{\\{((?:.|\\r?\\n)+?)\\}\\}/g;\nvar regexEscapeRE = /[-.*+?^${}()|[\\]\\/\\\\]/g;\nvar buildRegex = cached(function (delimiters) {\n  var open = delimiters[0].replace(regexEscapeRE, '\\\\$&');\n  var close = delimiters[1].replace(regexEscapeRE, '\\\\$&');\n  return new RegExp(open + '((?:.|\\\\n)+?)' + close, 'g');\n});\n\nfunction parseText(text, delimiters) {\n  var tagRE = delimiters ? buildRegex(delimiters) : defaultTagRE;\n\n  if (!tagRE.test(text)) {\n    return;\n  }\n\n  var tokens = [];\n  var rawTokens = [];\n  var lastIndex = tagRE.lastIndex = 0;\n  var match, index, tokenValue;\n\n  while (match = tagRE.exec(text)) {\n    index = match.index; // push text token\n\n    if (index > lastIndex) {\n      rawTokens.push(tokenValue = text.slice(lastIndex, index));\n      tokens.push(JSON.stringify(tokenValue));\n    } // tag token\n\n\n    var exp = parseFilters(match[1].trim());\n    tokens.push(\"_s(\" + exp + \")\");\n    rawTokens.push({\n      '@binding': exp\n    });\n    lastIndex = index + match[0].length;\n  }\n\n  if (lastIndex < text.length) {\n    rawTokens.push(tokenValue = text.slice(lastIndex));\n    tokens.push(JSON.stringify(tokenValue));\n  }\n\n  return {\n    expression: tokens.join('+'),\n    tokens: rawTokens\n  };\n}\n/*  */\n\n\nfunction transformNode(el, options) {\n  var warn = options.warn || baseWarn;\n  var staticClass = getAndRemoveAttr(el, 'class');\n\n  if ( true && staticClass) {\n    var res = parseText(staticClass, options.delimiters);\n\n    if (res) {\n      warn(\"class=\\\"\" + staticClass + \"\\\": \" + 'Interpolation inside attributes has been removed. ' + 'Use v-bind or the colon shorthand instead. For example, ' + 'instead of <div class=\"{{ val }}\">, use <div :class=\"val\">.', el.rawAttrsMap['class']);\n    }\n  }\n\n  if (staticClass) {\n    el.staticClass = JSON.stringify(staticClass);\n  }\n\n  var classBinding = getBindingAttr(el, 'class', false\n  /* getStatic */\n  );\n\n  if (classBinding) {\n    el.classBinding = classBinding;\n  }\n}\n\nfunction genData(el) {\n  var data = '';\n\n  if (el.staticClass) {\n    data += \"staticClass:\" + el.staticClass + \",\";\n  }\n\n  if (el.classBinding) {\n    data += \"class:\" + el.classBinding + \",\";\n  }\n\n  return data;\n}\n\nvar klass$1 = {\n  staticKeys: ['staticClass'],\n  transformNode: transformNode,\n  genData: genData\n};\n/*  */\n\nfunction transformNode$1(el, options) {\n  var warn = options.warn || baseWarn;\n  var staticStyle = getAndRemoveAttr(el, 'style');\n\n  if (staticStyle) {\n    /* istanbul ignore if */\n    if (true) {\n      var res = parseText(staticStyle, options.delimiters);\n\n      if (res) {\n        warn(\"style=\\\"\" + staticStyle + \"\\\": \" + 'Interpolation inside attributes has been removed. ' + 'Use v-bind or the colon shorthand instead. For example, ' + 'instead of <div style=\"{{ val }}\">, use <div :style=\"val\">.', el.rawAttrsMap['style']);\n      }\n    }\n\n    el.staticStyle = JSON.stringify(parseStyleText(staticStyle));\n  }\n\n  var styleBinding = getBindingAttr(el, 'style', false\n  /* getStatic */\n  );\n\n  if (styleBinding) {\n    el.styleBinding = styleBinding;\n  }\n}\n\nfunction genData$1(el) {\n  var data = '';\n\n  if (el.staticStyle) {\n    data += \"staticStyle:\" + el.staticStyle + \",\";\n  }\n\n  if (el.styleBinding) {\n    data += \"style:(\" + el.styleBinding + \"),\";\n  }\n\n  return data;\n}\n\nvar style$1 = {\n  staticKeys: ['staticStyle'],\n  transformNode: transformNode$1,\n  genData: genData$1\n};\n/*  */\n\nvar decoder;\nvar he = {\n  decode: function decode(html) {\n    decoder = decoder || document.createElement('div');\n    decoder.innerHTML = html;\n    return decoder.textContent;\n  }\n};\n/*  */\n\nvar isUnaryTag = makeMap('area,base,br,col,embed,frame,hr,img,input,isindex,keygen,' + 'link,meta,param,source,track,wbr'); // Elements that you can, intentionally, leave open\n// (and which close themselves)\n\nvar canBeLeftOpenTag = makeMap('colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr,source'); // HTML5 tags https://html.spec.whatwg.org/multipage/indices.html#elements-3\n// Phrasing Content https://html.spec.whatwg.org/multipage/dom.html#phrasing-content\n\nvar isNonPhrasingTag = makeMap('address,article,aside,base,blockquote,body,caption,col,colgroup,dd,' + 'details,dialog,div,dl,dt,fieldset,figcaption,figure,footer,form,' + 'h1,h2,h3,h4,h5,h6,head,header,hgroup,hr,html,legend,li,menuitem,meta,' + 'optgroup,option,param,rp,rt,source,style,summary,tbody,td,tfoot,th,thead,' + 'title,tr,track');\n/**\n * Not type-checking this file because it's mostly vendor code.\n */\n// Regular Expressions for parsing tags and attributes\n\nvar attribute = /^\\s*([^\\s\"'<>\\/=]+)(?:\\s*(=)\\s*(?:\"([^\"]*)\"+|'([^']*)'+|([^\\s\"'=<>`]+)))?/;\nvar dynamicArgAttribute = /^\\s*((?:v-[\\w-]+:|@|:|#)\\[[^=]+?\\][^\\s\"'<>\\/=]*)(?:\\s*(=)\\s*(?:\"([^\"]*)\"+|'([^']*)'+|([^\\s\"'=<>`]+)))?/;\nvar ncname = \"[a-zA-Z_][\\\\-\\\\.0-9_a-zA-Z\" + unicodeRegExp.source + \"]*\";\nvar qnameCapture = \"((?:\" + ncname + \"\\\\:)?\" + ncname + \")\";\nvar startTagOpen = new RegExp(\"^<\" + qnameCapture);\nvar startTagClose = /^\\s*(\\/?)>/;\nvar endTag = new RegExp(\"^<\\\\/\" + qnameCapture + \"[^>]*>\");\nvar doctype = /^<!DOCTYPE [^>]+>/i; // #7298: escape - to avoid being passed as HTML comment when inlined in page\n\nvar comment = /^<!\\--/;\nvar conditionalComment = /^<!\\[/; // Special Elements (can contain anything)\n\nvar isPlainTextElement = makeMap('script,style,textarea', true);\nvar reCache = {};\nvar decodingMap = {\n  '&lt;': '<',\n  '&gt;': '>',\n  '&quot;': '\"',\n  '&amp;': '&',\n  '&#10;': '\\n',\n  '&#9;': '\\t',\n  '&#39;': \"'\"\n};\nvar encodedAttr = /&(?:lt|gt|quot|amp|#39);/g;\nvar encodedAttrWithNewLines = /&(?:lt|gt|quot|amp|#39|#10|#9);/g; // #5992\n\nvar isIgnoreNewlineTag = makeMap('pre,textarea', true);\n\nvar shouldIgnoreFirstNewline = function (tag, html) {\n  return tag && isIgnoreNewlineTag(tag) && html[0] === '\\n';\n};\n\nfunction decodeAttr(value, shouldDecodeNewlines) {\n  var re = shouldDecodeNewlines ? encodedAttrWithNewLines : encodedAttr;\n  return value.replace(re, function (match) {\n    return decodingMap[match];\n  });\n}\n\nfunction parseHTML(html, options) {\n  var stack = [];\n  var expectHTML = options.expectHTML;\n  var isUnaryTag$$1 = options.isUnaryTag || no;\n  var canBeLeftOpenTag$$1 = options.canBeLeftOpenTag || no;\n  var index = 0;\n  var last, lastTag;\n\n  while (html) {\n    last = html; // Make sure we're not in a plaintext content element like script/style\n\n    if (!lastTag || !isPlainTextElement(lastTag)) {\n      var textEnd = html.indexOf('<');\n\n      if (textEnd === 0) {\n        // Comment:\n        if (comment.test(html)) {\n          var commentEnd = html.indexOf('-->');\n\n          if (commentEnd >= 0) {\n            if (options.shouldKeepComment) {\n              options.comment(html.substring(4, commentEnd), index, index + commentEnd + 3);\n            }\n\n            advance(commentEnd + 3);\n            continue;\n          }\n        } // http://en.wikipedia.org/wiki/Conditional_comment#Downlevel-revealed_conditional_comment\n\n\n        if (conditionalComment.test(html)) {\n          var conditionalEnd = html.indexOf(']>');\n\n          if (conditionalEnd >= 0) {\n            advance(conditionalEnd + 2);\n            continue;\n          }\n        } // Doctype:\n\n\n        var doctypeMatch = html.match(doctype);\n\n        if (doctypeMatch) {\n          advance(doctypeMatch[0].length);\n          continue;\n        } // End tag:\n\n\n        var endTagMatch = html.match(endTag);\n\n        if (endTagMatch) {\n          var curIndex = index;\n          advance(endTagMatch[0].length);\n          parseEndTag(endTagMatch[1], curIndex, index);\n          continue;\n        } // Start tag:\n\n\n        var startTagMatch = parseStartTag();\n\n        if (startTagMatch) {\n          handleStartTag(startTagMatch);\n\n          if (shouldIgnoreFirstNewline(startTagMatch.tagName, html)) {\n            advance(1);\n          }\n\n          continue;\n        }\n      }\n\n      var text = void 0,\n          rest = void 0,\n          next = void 0;\n\n      if (textEnd >= 0) {\n        rest = html.slice(textEnd);\n\n        while (!endTag.test(rest) && !startTagOpen.test(rest) && !comment.test(rest) && !conditionalComment.test(rest)) {\n          // < in plain text, be forgiving and treat it as text\n          next = rest.indexOf('<', 1);\n\n          if (next < 0) {\n            break;\n          }\n\n          textEnd += next;\n          rest = html.slice(textEnd);\n        }\n\n        text = html.substring(0, textEnd);\n      }\n\n      if (textEnd < 0) {\n        text = html;\n      }\n\n      if (text) {\n        advance(text.length);\n      }\n\n      if (options.chars && text) {\n        options.chars(text, index - text.length, index);\n      }\n    } else {\n      var endTagLength = 0;\n      var stackedTag = lastTag.toLowerCase();\n      var reStackedTag = reCache[stackedTag] || (reCache[stackedTag] = new RegExp('([\\\\s\\\\S]*?)(</' + stackedTag + '[^>]*>)', 'i'));\n      var rest$1 = html.replace(reStackedTag, function (all, text, endTag) {\n        endTagLength = endTag.length;\n\n        if (!isPlainTextElement(stackedTag) && stackedTag !== 'noscript') {\n          text = text.replace(/<!\\--([\\s\\S]*?)-->/g, '$1') // #7298\n          .replace(/<!\\[CDATA\\[([\\s\\S]*?)]]>/g, '$1');\n        }\n\n        if (shouldIgnoreFirstNewline(stackedTag, text)) {\n          text = text.slice(1);\n        }\n\n        if (options.chars) {\n          options.chars(text);\n        }\n\n        return '';\n      });\n      index += html.length - rest$1.length;\n      html = rest$1;\n      parseEndTag(stackedTag, index - endTagLength, index);\n    }\n\n    if (html === last) {\n      options.chars && options.chars(html);\n\n      if ( true && !stack.length && options.warn) {\n        options.warn(\"Mal-formatted tag at end of template: \\\"\" + html + \"\\\"\", {\n          start: index + html.length\n        });\n      }\n\n      break;\n    }\n  } // Clean up any remaining tags\n\n\n  parseEndTag();\n\n  function advance(n) {\n    index += n;\n    html = html.substring(n);\n  }\n\n  function parseStartTag() {\n    var start = html.match(startTagOpen);\n\n    if (start) {\n      var match = {\n        tagName: start[1],\n        attrs: [],\n        start: index\n      };\n      advance(start[0].length);\n      var end, attr;\n\n      while (!(end = html.match(startTagClose)) && (attr = html.match(dynamicArgAttribute) || html.match(attribute))) {\n        attr.start = index;\n        advance(attr[0].length);\n        attr.end = index;\n        match.attrs.push(attr);\n      }\n\n      if (end) {\n        match.unarySlash = end[1];\n        advance(end[0].length);\n        match.end = index;\n        return match;\n      }\n    }\n  }\n\n  function handleStartTag(match) {\n    var tagName = match.tagName;\n    var unarySlash = match.unarySlash;\n\n    if (expectHTML) {\n      if (lastTag === 'p' && isNonPhrasingTag(tagName)) {\n        parseEndTag(lastTag);\n      }\n\n      if (canBeLeftOpenTag$$1(tagName) && lastTag === tagName) {\n        parseEndTag(tagName);\n      }\n    }\n\n    var unary = isUnaryTag$$1(tagName) || !!unarySlash;\n    var l = match.attrs.length;\n    var attrs = new Array(l);\n\n    for (var i = 0; i < l; i++) {\n      var args = match.attrs[i];\n      var value = args[3] || args[4] || args[5] || '';\n      var shouldDecodeNewlines = tagName === 'a' && args[1] === 'href' ? options.shouldDecodeNewlinesForHref : options.shouldDecodeNewlines;\n      attrs[i] = {\n        name: args[1],\n        value: decodeAttr(value, shouldDecodeNewlines)\n      };\n\n      if ( true && options.outputSourceRange) {\n        attrs[i].start = args.start + args[0].match(/^\\s*/).length;\n        attrs[i].end = args.end;\n      }\n    }\n\n    if (!unary) {\n      stack.push({\n        tag: tagName,\n        lowerCasedTag: tagName.toLowerCase(),\n        attrs: attrs,\n        start: match.start,\n        end: match.end\n      });\n      lastTag = tagName;\n    }\n\n    if (options.start) {\n      options.start(tagName, attrs, unary, match.start, match.end);\n    }\n  }\n\n  function parseEndTag(tagName, start, end) {\n    var pos, lowerCasedTagName;\n\n    if (start == null) {\n      start = index;\n    }\n\n    if (end == null) {\n      end = index;\n    } // Find the closest opened tag of the same type\n\n\n    if (tagName) {\n      lowerCasedTagName = tagName.toLowerCase();\n\n      for (pos = stack.length - 1; pos >= 0; pos--) {\n        if (stack[pos].lowerCasedTag === lowerCasedTagName) {\n          break;\n        }\n      }\n    } else {\n      // If no tag name is provided, clean shop\n      pos = 0;\n    }\n\n    if (pos >= 0) {\n      // Close all the open elements, up the stack\n      for (var i = stack.length - 1; i >= pos; i--) {\n        if ( true && (i > pos || !tagName) && options.warn) {\n          options.warn(\"tag <\" + stack[i].tag + \"> has no matching end tag.\", {\n            start: stack[i].start,\n            end: stack[i].end\n          });\n        }\n\n        if (options.end) {\n          options.end(stack[i].tag, start, end);\n        }\n      } // Remove the open elements from the stack\n\n\n      stack.length = pos;\n      lastTag = pos && stack[pos - 1].tag;\n    } else if (lowerCasedTagName === 'br') {\n      if (options.start) {\n        options.start(tagName, [], true, start, end);\n      }\n    } else if (lowerCasedTagName === 'p') {\n      if (options.start) {\n        options.start(tagName, [], false, start, end);\n      }\n\n      if (options.end) {\n        options.end(tagName, start, end);\n      }\n    }\n  }\n}\n/*  */\n\n\nvar onRE = /^@|^v-on:/;\nvar dirRE = /^v-|^@|^:|^#/;\nvar forAliasRE = /([\\s\\S]*?)\\s+(?:in|of)\\s+([\\s\\S]*)/;\nvar forIteratorRE = /,([^,\\}\\]]*)(?:,([^,\\}\\]]*))?$/;\nvar stripParensRE = /^\\(|\\)$/g;\nvar dynamicArgRE = /^\\[.*\\]$/;\nvar argRE = /:(.*)$/;\nvar bindRE = /^:|^\\.|^v-bind:/;\nvar modifierRE = /\\.[^.\\]]+(?=[^\\]]*$)/g;\nvar slotRE = /^v-slot(:|$)|^#/;\nvar lineBreakRE = /[\\r\\n]/;\nvar whitespaceRE$1 = /[ \\f\\t\\r\\n]+/g;\nvar invalidAttributeRE = /[\\s\"'<>\\/=]/;\nvar decodeHTMLCached = cached(he.decode);\nvar emptySlotScopeToken = \"_empty_\"; // configurable state\n\nvar warn$2;\nvar delimiters;\nvar transforms;\nvar preTransforms;\nvar postTransforms;\nvar platformIsPreTag;\nvar platformMustUseProp;\nvar platformGetTagNamespace;\nvar maybeComponent;\n\nfunction createASTElement(tag, attrs, parent) {\n  return {\n    type: 1,\n    tag: tag,\n    attrsList: attrs,\n    attrsMap: makeAttrsMap(attrs),\n    rawAttrsMap: {},\n    parent: parent,\n    children: []\n  };\n}\n/**\n * Convert HTML string to AST.\n */\n\n\nfunction parse(template, options) {\n  warn$2 = options.warn || baseWarn;\n  platformIsPreTag = options.isPreTag || no;\n  platformMustUseProp = options.mustUseProp || no;\n  platformGetTagNamespace = options.getTagNamespace || no;\n  var isReservedTag = options.isReservedTag || no;\n\n  maybeComponent = function (el) {\n    return !!(el.component || el.attrsMap[':is'] || el.attrsMap['v-bind:is'] || !(el.attrsMap.is ? isReservedTag(el.attrsMap.is) : isReservedTag(el.tag)));\n  };\n\n  transforms = pluckModuleFunction(options.modules, 'transformNode');\n  preTransforms = pluckModuleFunction(options.modules, 'preTransformNode');\n  postTransforms = pluckModuleFunction(options.modules, 'postTransformNode');\n  delimiters = options.delimiters;\n  var stack = [];\n  var preserveWhitespace = options.preserveWhitespace !== false;\n  var whitespaceOption = options.whitespace;\n  var root;\n  var currentParent;\n  var inVPre = false;\n  var inPre = false;\n  var warned = false;\n\n  function warnOnce(msg, range) {\n    if (!warned) {\n      warned = true;\n      warn$2(msg, range);\n    }\n  }\n\n  function closeElement(element) {\n    trimEndingWhitespace(element);\n\n    if (!inVPre && !element.processed) {\n      element = processElement(element, options);\n    } // tree management\n\n\n    if (!stack.length && element !== root) {\n      // allow root elements with v-if, v-else-if and v-else\n      if (root.if && (element.elseif || element.else)) {\n        if (true) {\n          checkRootConstraints(element);\n        }\n\n        addIfCondition(root, {\n          exp: element.elseif,\n          block: element\n        });\n      } else if (true) {\n        warnOnce(\"Component template should contain exactly one root element. \" + \"If you are using v-if on multiple elements, \" + \"use v-else-if to chain them instead.\", {\n          start: element.start\n        });\n      }\n    }\n\n    if (currentParent && !element.forbidden) {\n      if (element.elseif || element.else) {\n        processIfConditions(element, currentParent);\n      } else {\n        if (element.slotScope) {\n          // scoped slot\n          // keep it in the children list so that v-else(-if) conditions can\n          // find it as the prev node.\n          var name = element.slotTarget || '\"default\"';\n          (currentParent.scopedSlots || (currentParent.scopedSlots = {}))[name] = element;\n        }\n\n        currentParent.children.push(element);\n        element.parent = currentParent;\n      }\n    } // final children cleanup\n    // filter out scoped slots\n\n\n    element.children = element.children.filter(function (c) {\n      return !c.slotScope;\n    }); // remove trailing whitespace node again\n\n    trimEndingWhitespace(element); // check pre state\n\n    if (element.pre) {\n      inVPre = false;\n    }\n\n    if (platformIsPreTag(element.tag)) {\n      inPre = false;\n    } // apply post-transforms\n\n\n    for (var i = 0; i < postTransforms.length; i++) {\n      postTransforms[i](element, options);\n    }\n  }\n\n  function trimEndingWhitespace(el) {\n    // remove trailing whitespace node\n    if (!inPre) {\n      var lastNode;\n\n      while ((lastNode = el.children[el.children.length - 1]) && lastNode.type === 3 && lastNode.text === ' ') {\n        el.children.pop();\n      }\n    }\n  }\n\n  function checkRootConstraints(el) {\n    if (el.tag === 'slot' || el.tag === 'template') {\n      warnOnce(\"Cannot use <\" + el.tag + \"> as component root element because it may \" + 'contain multiple nodes.', {\n        start: el.start\n      });\n    }\n\n    if (el.attrsMap.hasOwnProperty('v-for')) {\n      warnOnce('Cannot use v-for on stateful component root element because ' + 'it renders multiple elements.', el.rawAttrsMap['v-for']);\n    }\n  }\n\n  parseHTML(template, {\n    warn: warn$2,\n    expectHTML: options.expectHTML,\n    isUnaryTag: options.isUnaryTag,\n    canBeLeftOpenTag: options.canBeLeftOpenTag,\n    shouldDecodeNewlines: options.shouldDecodeNewlines,\n    shouldDecodeNewlinesForHref: options.shouldDecodeNewlinesForHref,\n    shouldKeepComment: options.comments,\n    outputSourceRange: options.outputSourceRange,\n    start: function start(tag, attrs, unary, start$1, end) {\n      // check namespace.\n      // inherit parent ns if there is one\n      var ns = currentParent && currentParent.ns || platformGetTagNamespace(tag); // handle IE svg bug\n\n      /* istanbul ignore if */\n\n      if (isIE && ns === 'svg') {\n        attrs = guardIESVGBug(attrs);\n      }\n\n      var element = createASTElement(tag, attrs, currentParent);\n\n      if (ns) {\n        element.ns = ns;\n      }\n\n      if (true) {\n        if (options.outputSourceRange) {\n          element.start = start$1;\n          element.end = end;\n          element.rawAttrsMap = element.attrsList.reduce(function (cumulated, attr) {\n            cumulated[attr.name] = attr;\n            return cumulated;\n          }, {});\n        }\n\n        attrs.forEach(function (attr) {\n          if (invalidAttributeRE.test(attr.name)) {\n            warn$2(\"Invalid dynamic argument expression: attribute names cannot contain \" + \"spaces, quotes, <, >, / or =.\", {\n              start: attr.start + attr.name.indexOf(\"[\"),\n              end: attr.start + attr.name.length\n            });\n          }\n        });\n      }\n\n      if (isForbiddenTag(element) && !isServerRendering()) {\n        element.forbidden = true;\n         true && warn$2('Templates should only be responsible for mapping the state to the ' + 'UI. Avoid placing tags with side-effects in your templates, such as ' + \"<\" + tag + \">\" + ', as they will not be parsed.', {\n          start: element.start\n        });\n      } // apply pre-transforms\n\n\n      for (var i = 0; i < preTransforms.length; i++) {\n        element = preTransforms[i](element, options) || element;\n      }\n\n      if (!inVPre) {\n        processPre(element);\n\n        if (element.pre) {\n          inVPre = true;\n        }\n      }\n\n      if (platformIsPreTag(element.tag)) {\n        inPre = true;\n      }\n\n      if (inVPre) {\n        processRawAttrs(element);\n      } else if (!element.processed) {\n        // structural directives\n        processFor(element);\n        processIf(element);\n        processOnce(element);\n      }\n\n      if (!root) {\n        root = element;\n\n        if (true) {\n          checkRootConstraints(root);\n        }\n      }\n\n      if (!unary) {\n        currentParent = element;\n        stack.push(element);\n      } else {\n        closeElement(element);\n      }\n    },\n    end: function end(tag, start, end$1) {\n      var element = stack[stack.length - 1]; // pop stack\n\n      stack.length -= 1;\n      currentParent = stack[stack.length - 1];\n\n      if ( true && options.outputSourceRange) {\n        element.end = end$1;\n      }\n\n      closeElement(element);\n    },\n    chars: function chars(text, start, end) {\n      if (!currentParent) {\n        if (true) {\n          if (text === template) {\n            warnOnce('Component template requires a root element, rather than just text.', {\n              start: start\n            });\n          } else if (text = text.trim()) {\n            warnOnce(\"text \\\"\" + text + \"\\\" outside root element will be ignored.\", {\n              start: start\n            });\n          }\n        }\n\n        return;\n      } // IE textarea placeholder bug\n\n      /* istanbul ignore if */\n\n\n      if (isIE && currentParent.tag === 'textarea' && currentParent.attrsMap.placeholder === text) {\n        return;\n      }\n\n      var children = currentParent.children;\n\n      if (inPre || text.trim()) {\n        text = isTextTag(currentParent) ? text : decodeHTMLCached(text);\n      } else if (!children.length) {\n        // remove the whitespace-only node right after an opening tag\n        text = '';\n      } else if (whitespaceOption) {\n        if (whitespaceOption === 'condense') {\n          // in condense mode, remove the whitespace node if it contains\n          // line break, otherwise condense to a single space\n          text = lineBreakRE.test(text) ? '' : ' ';\n        } else {\n          text = ' ';\n        }\n      } else {\n        text = preserveWhitespace ? ' ' : '';\n      }\n\n      if (text) {\n        if (!inPre && whitespaceOption === 'condense') {\n          // condense consecutive whitespaces into single space\n          text = text.replace(whitespaceRE$1, ' ');\n        }\n\n        var res;\n        var child;\n\n        if (!inVPre && text !== ' ' && (res = parseText(text, delimiters))) {\n          child = {\n            type: 2,\n            expression: res.expression,\n            tokens: res.tokens,\n            text: text\n          };\n        } else if (text !== ' ' || !children.length || children[children.length - 1].text !== ' ') {\n          child = {\n            type: 3,\n            text: text\n          };\n        }\n\n        if (child) {\n          if ( true && options.outputSourceRange) {\n            child.start = start;\n            child.end = end;\n          }\n\n          children.push(child);\n        }\n      }\n    },\n    comment: function comment(text, start, end) {\n      // adding anything as a sibling to the root node is forbidden\n      // comments should still be allowed, but ignored\n      if (currentParent) {\n        var child = {\n          type: 3,\n          text: text,\n          isComment: true\n        };\n\n        if ( true && options.outputSourceRange) {\n          child.start = start;\n          child.end = end;\n        }\n\n        currentParent.children.push(child);\n      }\n    }\n  });\n  return root;\n}\n\nfunction processPre(el) {\n  if (getAndRemoveAttr(el, 'v-pre') != null) {\n    el.pre = true;\n  }\n}\n\nfunction processRawAttrs(el) {\n  var list = el.attrsList;\n  var len = list.length;\n\n  if (len) {\n    var attrs = el.attrs = new Array(len);\n\n    for (var i = 0; i < len; i++) {\n      attrs[i] = {\n        name: list[i].name,\n        value: JSON.stringify(list[i].value)\n      };\n\n      if (list[i].start != null) {\n        attrs[i].start = list[i].start;\n        attrs[i].end = list[i].end;\n      }\n    }\n  } else if (!el.pre) {\n    // non root node in pre blocks with no attributes\n    el.plain = true;\n  }\n}\n\nfunction processElement(element, options) {\n  processKey(element); // determine whether this is a plain element after\n  // removing structural attributes\n\n  element.plain = !element.key && !element.scopedSlots && !element.attrsList.length;\n  processRef(element);\n  processSlotContent(element);\n  processSlotOutlet(element);\n  processComponent(element);\n\n  for (var i = 0; i < transforms.length; i++) {\n    element = transforms[i](element, options) || element;\n  }\n\n  processAttrs(element);\n  return element;\n}\n\nfunction processKey(el) {\n  var exp = getBindingAttr(el, 'key');\n\n  if (exp) {\n    if (true) {\n      if (el.tag === 'template') {\n        warn$2(\"<template> cannot be keyed. Place the key on real elements instead.\", getRawBindingAttr(el, 'key'));\n      }\n\n      if (el.for) {\n        var iterator = el.iterator2 || el.iterator1;\n        var parent = el.parent;\n\n        if (iterator && iterator === exp && parent && parent.tag === 'transition-group') {\n          warn$2(\"Do not use v-for index as key on <transition-group> children, \" + \"this is the same as not using keys.\", getRawBindingAttr(el, 'key'), true\n          /* tip */\n          );\n        }\n      }\n    }\n\n    el.key = exp;\n  }\n}\n\nfunction processRef(el) {\n  var ref = getBindingAttr(el, 'ref');\n\n  if (ref) {\n    el.ref = ref;\n    el.refInFor = checkInFor(el);\n  }\n}\n\nfunction processFor(el) {\n  var exp;\n\n  if (exp = getAndRemoveAttr(el, 'v-for')) {\n    var res = parseFor(exp);\n\n    if (res) {\n      extend(el, res);\n    } else if (true) {\n      warn$2(\"Invalid v-for expression: \" + exp, el.rawAttrsMap['v-for']);\n    }\n  }\n}\n\nfunction parseFor(exp) {\n  var inMatch = exp.match(forAliasRE);\n\n  if (!inMatch) {\n    return;\n  }\n\n  var res = {};\n  res.for = inMatch[2].trim();\n  var alias = inMatch[1].trim().replace(stripParensRE, '');\n  var iteratorMatch = alias.match(forIteratorRE);\n\n  if (iteratorMatch) {\n    res.alias = alias.replace(forIteratorRE, '').trim();\n    res.iterator1 = iteratorMatch[1].trim();\n\n    if (iteratorMatch[2]) {\n      res.iterator2 = iteratorMatch[2].trim();\n    }\n  } else {\n    res.alias = alias;\n  }\n\n  return res;\n}\n\nfunction processIf(el) {\n  var exp = getAndRemoveAttr(el, 'v-if');\n\n  if (exp) {\n    el.if = exp;\n    addIfCondition(el, {\n      exp: exp,\n      block: el\n    });\n  } else {\n    if (getAndRemoveAttr(el, 'v-else') != null) {\n      el.else = true;\n    }\n\n    var elseif = getAndRemoveAttr(el, 'v-else-if');\n\n    if (elseif) {\n      el.elseif = elseif;\n    }\n  }\n}\n\nfunction processIfConditions(el, parent) {\n  var prev = findPrevElement(parent.children);\n\n  if (prev && prev.if) {\n    addIfCondition(prev, {\n      exp: el.elseif,\n      block: el\n    });\n  } else if (true) {\n    warn$2(\"v-\" + (el.elseif ? 'else-if=\"' + el.elseif + '\"' : 'else') + \" \" + \"used on element <\" + el.tag + \"> without corresponding v-if.\", el.rawAttrsMap[el.elseif ? 'v-else-if' : 'v-else']);\n  }\n}\n\nfunction findPrevElement(children) {\n  var i = children.length;\n\n  while (i--) {\n    if (children[i].type === 1) {\n      return children[i];\n    } else {\n      if ( true && children[i].text !== ' ') {\n        warn$2(\"text \\\"\" + children[i].text.trim() + \"\\\" between v-if and v-else(-if) \" + \"will be ignored.\", children[i]);\n      }\n\n      children.pop();\n    }\n  }\n}\n\nfunction addIfCondition(el, condition) {\n  if (!el.ifConditions) {\n    el.ifConditions = [];\n  }\n\n  el.ifConditions.push(condition);\n}\n\nfunction processOnce(el) {\n  var once$$1 = getAndRemoveAttr(el, 'v-once');\n\n  if (once$$1 != null) {\n    el.once = true;\n  }\n} // handle content being passed to a component as slot,\n// e.g. <template slot=\"xxx\">, <div slot-scope=\"xxx\">\n\n\nfunction processSlotContent(el) {\n  var slotScope;\n\n  if (el.tag === 'template') {\n    slotScope = getAndRemoveAttr(el, 'scope');\n    /* istanbul ignore if */\n\n    if ( true && slotScope) {\n      warn$2(\"the \\\"scope\\\" attribute for scoped slots have been deprecated and \" + \"replaced by \\\"slot-scope\\\" since 2.5. The new \\\"slot-scope\\\" attribute \" + \"can also be used on plain elements in addition to <template> to \" + \"denote scoped slots.\", el.rawAttrsMap['scope'], true);\n    }\n\n    el.slotScope = slotScope || getAndRemoveAttr(el, 'slot-scope');\n  } else if (slotScope = getAndRemoveAttr(el, 'slot-scope')) {\n    /* istanbul ignore if */\n    if ( true && el.attrsMap['v-for']) {\n      warn$2(\"Ambiguous combined usage of slot-scope and v-for on <\" + el.tag + \"> \" + \"(v-for takes higher priority). Use a wrapper <template> for the \" + \"scoped slot to make it clearer.\", el.rawAttrsMap['slot-scope'], true);\n    }\n\n    el.slotScope = slotScope;\n  } // slot=\"xxx\"\n\n\n  var slotTarget = getBindingAttr(el, 'slot');\n\n  if (slotTarget) {\n    el.slotTarget = slotTarget === '\"\"' ? '\"default\"' : slotTarget;\n    el.slotTargetDynamic = !!(el.attrsMap[':slot'] || el.attrsMap['v-bind:slot']); // preserve slot as an attribute for native shadow DOM compat\n    // only for non-scoped slots.\n\n    if (el.tag !== 'template' && !el.slotScope) {\n      addAttr(el, 'slot', slotTarget, getRawBindingAttr(el, 'slot'));\n    }\n  } // 2.6 v-slot syntax\n\n\n  {\n    if (el.tag === 'template') {\n      // v-slot on <template>\n      var slotBinding = getAndRemoveAttrByRegex(el, slotRE);\n\n      if (slotBinding) {\n        if (true) {\n          if (el.slotTarget || el.slotScope) {\n            warn$2(\"Unexpected mixed usage of different slot syntaxes.\", el);\n          }\n\n          if (el.parent && !maybeComponent(el.parent)) {\n            warn$2(\"<template v-slot> can only appear at the root level inside \" + \"the receiving component\", el);\n          }\n        }\n\n        var ref = getSlotName(slotBinding);\n        var name = ref.name;\n        var dynamic = ref.dynamic;\n        el.slotTarget = name;\n        el.slotTargetDynamic = dynamic;\n        el.slotScope = slotBinding.value || emptySlotScopeToken; // force it into a scoped slot for perf\n      }\n    } else {\n      // v-slot on component, denotes default slot\n      var slotBinding$1 = getAndRemoveAttrByRegex(el, slotRE);\n\n      if (slotBinding$1) {\n        if (true) {\n          if (!maybeComponent(el)) {\n            warn$2(\"v-slot can only be used on components or <template>.\", slotBinding$1);\n          }\n\n          if (el.slotScope || el.slotTarget) {\n            warn$2(\"Unexpected mixed usage of different slot syntaxes.\", el);\n          }\n\n          if (el.scopedSlots) {\n            warn$2(\"To avoid scope ambiguity, the default slot should also use \" + \"<template> syntax when there are other named slots.\", slotBinding$1);\n          }\n        } // add the component's children to its default slot\n\n\n        var slots = el.scopedSlots || (el.scopedSlots = {});\n        var ref$1 = getSlotName(slotBinding$1);\n        var name$1 = ref$1.name;\n        var dynamic$1 = ref$1.dynamic;\n        var slotContainer = slots[name$1] = createASTElement('template', [], el);\n        slotContainer.slotTarget = name$1;\n        slotContainer.slotTargetDynamic = dynamic$1;\n        slotContainer.children = el.children.filter(function (c) {\n          if (!c.slotScope) {\n            c.parent = slotContainer;\n            return true;\n          }\n        });\n        slotContainer.slotScope = slotBinding$1.value || emptySlotScopeToken; // remove children as they are returned from scopedSlots now\n\n        el.children = []; // mark el non-plain so data gets generated\n\n        el.plain = false;\n      }\n    }\n  }\n}\n\nfunction getSlotName(binding) {\n  var name = binding.name.replace(slotRE, '');\n\n  if (!name) {\n    if (binding.name[0] !== '#') {\n      name = 'default';\n    } else if (true) {\n      warn$2(\"v-slot shorthand syntax requires a slot name.\", binding);\n    }\n  }\n\n  return dynamicArgRE.test(name) // dynamic [name]\n  ? {\n    name: name.slice(1, -1),\n    dynamic: true\n  } // static name\n  : {\n    name: \"\\\"\" + name + \"\\\"\",\n    dynamic: false\n  };\n} // handle <slot/> outlets\n\n\nfunction processSlotOutlet(el) {\n  if (el.tag === 'slot') {\n    el.slotName = getBindingAttr(el, 'name');\n\n    if ( true && el.key) {\n      warn$2(\"`key` does not work on <slot> because slots are abstract outlets \" + \"and can possibly expand into multiple elements. \" + \"Use the key on a wrapping element instead.\", getRawBindingAttr(el, 'key'));\n    }\n  }\n}\n\nfunction processComponent(el) {\n  var binding;\n\n  if (binding = getBindingAttr(el, 'is')) {\n    el.component = binding;\n  }\n\n  if (getAndRemoveAttr(el, 'inline-template') != null) {\n    el.inlineTemplate = true;\n  }\n}\n\nfunction processAttrs(el) {\n  var list = el.attrsList;\n  var i, l, name, rawName, value, modifiers, syncGen, isDynamic;\n\n  for (i = 0, l = list.length; i < l; i++) {\n    name = rawName = list[i].name;\n    value = list[i].value;\n\n    if (dirRE.test(name)) {\n      // mark element as dynamic\n      el.hasBindings = true; // modifiers\n\n      modifiers = parseModifiers(name.replace(dirRE, '')); // support .foo shorthand syntax for the .prop modifier\n\n      if (modifiers) {\n        name = name.replace(modifierRE, '');\n      }\n\n      if (bindRE.test(name)) {\n        // v-bind\n        name = name.replace(bindRE, '');\n        value = parseFilters(value);\n        isDynamic = dynamicArgRE.test(name);\n\n        if (isDynamic) {\n          name = name.slice(1, -1);\n        }\n\n        if ( true && value.trim().length === 0) {\n          warn$2(\"The value for a v-bind expression cannot be empty. Found in \\\"v-bind:\" + name + \"\\\"\");\n        }\n\n        if (modifiers) {\n          if (modifiers.prop && !isDynamic) {\n            name = camelize(name);\n\n            if (name === 'innerHtml') {\n              name = 'innerHTML';\n            }\n          }\n\n          if (modifiers.camel && !isDynamic) {\n            name = camelize(name);\n          }\n\n          if (modifiers.sync) {\n            syncGen = genAssignmentCode(value, \"$event\");\n\n            if (!isDynamic) {\n              addHandler(el, \"update:\" + camelize(name), syncGen, null, false, warn$2, list[i]);\n\n              if (hyphenate(name) !== camelize(name)) {\n                addHandler(el, \"update:\" + hyphenate(name), syncGen, null, false, warn$2, list[i]);\n              }\n            } else {\n              // handler w/ dynamic event name\n              addHandler(el, \"\\\"update:\\\"+(\" + name + \")\", syncGen, null, false, warn$2, list[i], true // dynamic\n              );\n            }\n          }\n        }\n\n        if (modifiers && modifiers.prop || !el.component && platformMustUseProp(el.tag, el.attrsMap.type, name)) {\n          addProp(el, name, value, list[i], isDynamic);\n        } else {\n          addAttr(el, name, value, list[i], isDynamic);\n        }\n      } else if (onRE.test(name)) {\n        // v-on\n        name = name.replace(onRE, '');\n        isDynamic = dynamicArgRE.test(name);\n\n        if (isDynamic) {\n          name = name.slice(1, -1);\n        }\n\n        addHandler(el, name, value, modifiers, false, warn$2, list[i], isDynamic);\n      } else {\n        // normal directives\n        name = name.replace(dirRE, ''); // parse arg\n\n        var argMatch = name.match(argRE);\n        var arg = argMatch && argMatch[1];\n        isDynamic = false;\n\n        if (arg) {\n          name = name.slice(0, -(arg.length + 1));\n\n          if (dynamicArgRE.test(arg)) {\n            arg = arg.slice(1, -1);\n            isDynamic = true;\n          }\n        }\n\n        addDirective(el, name, rawName, value, arg, isDynamic, modifiers, list[i]);\n\n        if ( true && name === 'model') {\n          checkForAliasModel(el, value);\n        }\n      }\n    } else {\n      // literal attribute\n      if (true) {\n        var res = parseText(value, delimiters);\n\n        if (res) {\n          warn$2(name + \"=\\\"\" + value + \"\\\": \" + 'Interpolation inside attributes has been removed. ' + 'Use v-bind or the colon shorthand instead. For example, ' + 'instead of <div id=\"{{ val }}\">, use <div :id=\"val\">.', list[i]);\n        }\n      }\n\n      addAttr(el, name, JSON.stringify(value), list[i]); // #6887 firefox doesn't update muted state if set via attribute\n      // even immediately after element creation\n\n      if (!el.component && name === 'muted' && platformMustUseProp(el.tag, el.attrsMap.type, name)) {\n        addProp(el, name, 'true', list[i]);\n      }\n    }\n  }\n}\n\nfunction checkInFor(el) {\n  var parent = el;\n\n  while (parent) {\n    if (parent.for !== undefined) {\n      return true;\n    }\n\n    parent = parent.parent;\n  }\n\n  return false;\n}\n\nfunction parseModifiers(name) {\n  var match = name.match(modifierRE);\n\n  if (match) {\n    var ret = {};\n    match.forEach(function (m) {\n      ret[m.slice(1)] = true;\n    });\n    return ret;\n  }\n}\n\nfunction makeAttrsMap(attrs) {\n  var map = {};\n\n  for (var i = 0, l = attrs.length; i < l; i++) {\n    if ( true && map[attrs[i].name] && !isIE && !isEdge) {\n      warn$2('duplicate attribute: ' + attrs[i].name, attrs[i]);\n    }\n\n    map[attrs[i].name] = attrs[i].value;\n  }\n\n  return map;\n} // for script (e.g. type=\"x/template\") or style, do not decode content\n\n\nfunction isTextTag(el) {\n  return el.tag === 'script' || el.tag === 'style';\n}\n\nfunction isForbiddenTag(el) {\n  return el.tag === 'style' || el.tag === 'script' && (!el.attrsMap.type || el.attrsMap.type === 'text/javascript');\n}\n\nvar ieNSBug = /^xmlns:NS\\d+/;\nvar ieNSPrefix = /^NS\\d+:/;\n/* istanbul ignore next */\n\nfunction guardIESVGBug(attrs) {\n  var res = [];\n\n  for (var i = 0; i < attrs.length; i++) {\n    var attr = attrs[i];\n\n    if (!ieNSBug.test(attr.name)) {\n      attr.name = attr.name.replace(ieNSPrefix, '');\n      res.push(attr);\n    }\n  }\n\n  return res;\n}\n\nfunction checkForAliasModel(el, value) {\n  var _el = el;\n\n  while (_el) {\n    if (_el.for && _el.alias === value) {\n      warn$2(\"<\" + el.tag + \" v-model=\\\"\" + value + \"\\\">: \" + \"You are binding v-model directly to a v-for iteration alias. \" + \"This will not be able to modify the v-for source array because \" + \"writing to the alias is like modifying a function local variable. \" + \"Consider using an array of objects and use v-model on an object property instead.\", el.rawAttrsMap['v-model']);\n    }\n\n    _el = _el.parent;\n  }\n}\n/*  */\n\n\nfunction preTransformNode(el, options) {\n  if (el.tag === 'input') {\n    var map = el.attrsMap;\n\n    if (!map['v-model']) {\n      return;\n    }\n\n    var typeBinding;\n\n    if (map[':type'] || map['v-bind:type']) {\n      typeBinding = getBindingAttr(el, 'type');\n    }\n\n    if (!map.type && !typeBinding && map['v-bind']) {\n      typeBinding = \"(\" + map['v-bind'] + \").type\";\n    }\n\n    if (typeBinding) {\n      var ifCondition = getAndRemoveAttr(el, 'v-if', true);\n      var ifConditionExtra = ifCondition ? \"&&(\" + ifCondition + \")\" : \"\";\n      var hasElse = getAndRemoveAttr(el, 'v-else', true) != null;\n      var elseIfCondition = getAndRemoveAttr(el, 'v-else-if', true); // 1. checkbox\n\n      var branch0 = cloneASTElement(el); // process for on the main node\n\n      processFor(branch0);\n      addRawAttr(branch0, 'type', 'checkbox');\n      processElement(branch0, options);\n      branch0.processed = true; // prevent it from double-processed\n\n      branch0.if = \"(\" + typeBinding + \")==='checkbox'\" + ifConditionExtra;\n      addIfCondition(branch0, {\n        exp: branch0.if,\n        block: branch0\n      }); // 2. add radio else-if condition\n\n      var branch1 = cloneASTElement(el);\n      getAndRemoveAttr(branch1, 'v-for', true);\n      addRawAttr(branch1, 'type', 'radio');\n      processElement(branch1, options);\n      addIfCondition(branch0, {\n        exp: \"(\" + typeBinding + \")==='radio'\" + ifConditionExtra,\n        block: branch1\n      }); // 3. other\n\n      var branch2 = cloneASTElement(el);\n      getAndRemoveAttr(branch2, 'v-for', true);\n      addRawAttr(branch2, ':type', typeBinding);\n      processElement(branch2, options);\n      addIfCondition(branch0, {\n        exp: ifCondition,\n        block: branch2\n      });\n\n      if (hasElse) {\n        branch0.else = true;\n      } else if (elseIfCondition) {\n        branch0.elseif = elseIfCondition;\n      }\n\n      return branch0;\n    }\n  }\n}\n\nfunction cloneASTElement(el) {\n  return createASTElement(el.tag, el.attrsList.slice(), el.parent);\n}\n\nvar model$1 = {\n  preTransformNode: preTransformNode\n};\nvar modules$1 = [klass$1, style$1, model$1];\n/*  */\n\nfunction text(el, dir) {\n  if (dir.value) {\n    addProp(el, 'textContent', \"_s(\" + dir.value + \")\", dir);\n  }\n}\n/*  */\n\n\nfunction html(el, dir) {\n  if (dir.value) {\n    addProp(el, 'innerHTML', \"_s(\" + dir.value + \")\", dir);\n  }\n}\n\nvar directives$1 = {\n  model: model,\n  text: text,\n  html: html\n};\n/*  */\n\nvar baseOptions = {\n  expectHTML: true,\n  modules: modules$1,\n  directives: directives$1,\n  isPreTag: isPreTag,\n  isUnaryTag: isUnaryTag,\n  mustUseProp: mustUseProp,\n  canBeLeftOpenTag: canBeLeftOpenTag,\n  isReservedTag: isReservedTag,\n  getTagNamespace: getTagNamespace,\n  staticKeys: genStaticKeys(modules$1)\n};\n/*  */\n\nvar isStaticKey;\nvar isPlatformReservedTag;\nvar genStaticKeysCached = cached(genStaticKeys$1);\n/**\n * Goal of the optimizer: walk the generated template AST tree\n * and detect sub-trees that are purely static, i.e. parts of\n * the DOM that never needs to change.\n *\n * Once we detect these sub-trees, we can:\n *\n * 1. Hoist them into constants, so that we no longer need to\n *    create fresh nodes for them on each re-render;\n * 2. Completely skip them in the patching process.\n */\n\nfunction optimize(root, options) {\n  if (!root) {\n    return;\n  }\n\n  isStaticKey = genStaticKeysCached(options.staticKeys || '');\n  isPlatformReservedTag = options.isReservedTag || no; // first pass: mark all non-static nodes.\n\n  markStatic$1(root); // second pass: mark static roots.\n\n  markStaticRoots(root, false);\n}\n\nfunction genStaticKeys$1(keys) {\n  return makeMap('type,tag,attrsList,attrsMap,plain,parent,children,attrs,start,end,rawAttrsMap' + (keys ? ',' + keys : ''));\n}\n\nfunction markStatic$1(node) {\n  node.static = isStatic(node);\n\n  if (node.type === 1) {\n    // do not make component slot content static. this avoids\n    // 1. components not able to mutate slot nodes\n    // 2. static slot content fails for hot-reloading\n    if (!isPlatformReservedTag(node.tag) && node.tag !== 'slot' && node.attrsMap['inline-template'] == null) {\n      return;\n    }\n\n    for (var i = 0, l = node.children.length; i < l; i++) {\n      var child = node.children[i];\n      markStatic$1(child);\n\n      if (!child.static) {\n        node.static = false;\n      }\n    }\n\n    if (node.ifConditions) {\n      for (var i$1 = 1, l$1 = node.ifConditions.length; i$1 < l$1; i$1++) {\n        var block = node.ifConditions[i$1].block;\n        markStatic$1(block);\n\n        if (!block.static) {\n          node.static = false;\n        }\n      }\n    }\n  }\n}\n\nfunction markStaticRoots(node, isInFor) {\n  if (node.type === 1) {\n    if (node.static || node.once) {\n      node.staticInFor = isInFor;\n    } // For a node to qualify as a static root, it should have children that\n    // are not just static text. Otherwise the cost of hoisting out will\n    // outweigh the benefits and it's better off to just always render it fresh.\n\n\n    if (node.static && node.children.length && !(node.children.length === 1 && node.children[0].type === 3)) {\n      node.staticRoot = true;\n      return;\n    } else {\n      node.staticRoot = false;\n    }\n\n    if (node.children) {\n      for (var i = 0, l = node.children.length; i < l; i++) {\n        markStaticRoots(node.children[i], isInFor || !!node.for);\n      }\n    }\n\n    if (node.ifConditions) {\n      for (var i$1 = 1, l$1 = node.ifConditions.length; i$1 < l$1; i$1++) {\n        markStaticRoots(node.ifConditions[i$1].block, isInFor);\n      }\n    }\n  }\n}\n\nfunction isStatic(node) {\n  if (node.type === 2) {\n    // expression\n    return false;\n  }\n\n  if (node.type === 3) {\n    // text\n    return true;\n  }\n\n  return !!(node.pre || !node.hasBindings && // no dynamic bindings\n  !node.if && !node.for && // not v-if or v-for or v-else\n  !isBuiltInTag(node.tag) && // not a built-in\n  isPlatformReservedTag(node.tag) && // not a component\n  !isDirectChildOfTemplateFor(node) && Object.keys(node).every(isStaticKey));\n}\n\nfunction isDirectChildOfTemplateFor(node) {\n  while (node.parent) {\n    node = node.parent;\n\n    if (node.tag !== 'template') {\n      return false;\n    }\n\n    if (node.for) {\n      return true;\n    }\n  }\n\n  return false;\n}\n/*  */\n\n\nvar fnExpRE = /^([\\w$_]+|\\([^)]*?\\))\\s*=>|^function(?:\\s+[\\w$]+)?\\s*\\(/;\nvar fnInvokeRE = /\\([^)]*?\\);*$/;\nvar simplePathRE = /^[A-Za-z_$][\\w$]*(?:\\.[A-Za-z_$][\\w$]*|\\['[^']*?']|\\[\"[^\"]*?\"]|\\[\\d+]|\\[[A-Za-z_$][\\w$]*])*$/; // KeyboardEvent.keyCode aliases\n\nvar keyCodes = {\n  esc: 27,\n  tab: 9,\n  enter: 13,\n  space: 32,\n  up: 38,\n  left: 37,\n  right: 39,\n  down: 40,\n  'delete': [8, 46]\n}; // KeyboardEvent.key aliases\n\nvar keyNames = {\n  // #7880: IE11 and Edge use `Esc` for Escape key name.\n  esc: ['Esc', 'Escape'],\n  tab: 'Tab',\n  enter: 'Enter',\n  // #9112: IE11 uses `Spacebar` for Space key name.\n  space: [' ', 'Spacebar'],\n  // #7806: IE11 uses key names without `Arrow` prefix for arrow keys.\n  up: ['Up', 'ArrowUp'],\n  left: ['Left', 'ArrowLeft'],\n  right: ['Right', 'ArrowRight'],\n  down: ['Down', 'ArrowDown'],\n  // #9112: IE11 uses `Del` for Delete key name.\n  'delete': ['Backspace', 'Delete', 'Del']\n}; // #4868: modifiers that prevent the execution of the listener\n// need to explicitly return null so that we can determine whether to remove\n// the listener for .once\n\nvar genGuard = function (condition) {\n  return \"if(\" + condition + \")return null;\";\n};\n\nvar modifierCode = {\n  stop: '$event.stopPropagation();',\n  prevent: '$event.preventDefault();',\n  self: genGuard(\"$event.target !== $event.currentTarget\"),\n  ctrl: genGuard(\"!$event.ctrlKey\"),\n  shift: genGuard(\"!$event.shiftKey\"),\n  alt: genGuard(\"!$event.altKey\"),\n  meta: genGuard(\"!$event.metaKey\"),\n  left: genGuard(\"'button' in $event && $event.button !== 0\"),\n  middle: genGuard(\"'button' in $event && $event.button !== 1\"),\n  right: genGuard(\"'button' in $event && $event.button !== 2\")\n};\n\nfunction genHandlers(events, isNative) {\n  var prefix = isNative ? 'nativeOn:' : 'on:';\n  var staticHandlers = \"\";\n  var dynamicHandlers = \"\";\n\n  for (var name in events) {\n    var handlerCode = genHandler(events[name]);\n\n    if (events[name] && events[name].dynamic) {\n      dynamicHandlers += name + \",\" + handlerCode + \",\";\n    } else {\n      staticHandlers += \"\\\"\" + name + \"\\\":\" + handlerCode + \",\";\n    }\n  }\n\n  staticHandlers = \"{\" + staticHandlers.slice(0, -1) + \"}\";\n\n  if (dynamicHandlers) {\n    return prefix + \"_d(\" + staticHandlers + \",[\" + dynamicHandlers.slice(0, -1) + \"])\";\n  } else {\n    return prefix + staticHandlers;\n  }\n}\n\nfunction genHandler(handler) {\n  if (!handler) {\n    return 'function(){}';\n  }\n\n  if (Array.isArray(handler)) {\n    return \"[\" + handler.map(function (handler) {\n      return genHandler(handler);\n    }).join(',') + \"]\";\n  }\n\n  var isMethodPath = simplePathRE.test(handler.value);\n  var isFunctionExpression = fnExpRE.test(handler.value);\n  var isFunctionInvocation = simplePathRE.test(handler.value.replace(fnInvokeRE, ''));\n\n  if (!handler.modifiers) {\n    if (isMethodPath || isFunctionExpression) {\n      return handler.value;\n    }\n\n    return \"function($event){\" + (isFunctionInvocation ? \"return \" + handler.value : handler.value) + \"}\"; // inline statement\n  } else {\n    var code = '';\n    var genModifierCode = '';\n    var keys = [];\n\n    for (var key in handler.modifiers) {\n      if (modifierCode[key]) {\n        genModifierCode += modifierCode[key]; // left/right\n\n        if (keyCodes[key]) {\n          keys.push(key);\n        }\n      } else if (key === 'exact') {\n        var modifiers = handler.modifiers;\n        genModifierCode += genGuard(['ctrl', 'shift', 'alt', 'meta'].filter(function (keyModifier) {\n          return !modifiers[keyModifier];\n        }).map(function (keyModifier) {\n          return \"$event.\" + keyModifier + \"Key\";\n        }).join('||'));\n      } else {\n        keys.push(key);\n      }\n    }\n\n    if (keys.length) {\n      code += genKeyFilter(keys);\n    } // Make sure modifiers like prevent and stop get executed after key filtering\n\n\n    if (genModifierCode) {\n      code += genModifierCode;\n    }\n\n    var handlerCode = isMethodPath ? \"return \" + handler.value + \".apply(null, arguments)\" : isFunctionExpression ? \"return (\" + handler.value + \").apply(null, arguments)\" : isFunctionInvocation ? \"return \" + handler.value : handler.value;\n    return \"function($event){\" + code + handlerCode + \"}\";\n  }\n}\n\nfunction genKeyFilter(keys) {\n  return (// make sure the key filters only apply to KeyboardEvents\n    // #9441: can't use 'keyCode' in $event because Chrome autofill fires fake\n    // key events that do not have keyCode property...\n    \"if(!$event.type.indexOf('key')&&\" + keys.map(genFilterCode).join('&&') + \")return null;\"\n  );\n}\n\nfunction genFilterCode(key) {\n  var keyVal = parseInt(key, 10);\n\n  if (keyVal) {\n    return \"$event.keyCode!==\" + keyVal;\n  }\n\n  var keyCode = keyCodes[key];\n  var keyName = keyNames[key];\n  return \"_k($event.keyCode,\" + JSON.stringify(key) + \",\" + JSON.stringify(keyCode) + \",\" + \"$event.key,\" + \"\" + JSON.stringify(keyName) + \")\";\n}\n/*  */\n\n\nfunction on(el, dir) {\n  if ( true && dir.modifiers) {\n    warn(\"v-on without argument does not support modifiers.\");\n  }\n\n  el.wrapListeners = function (code) {\n    return \"_g(\" + code + \",\" + dir.value + \")\";\n  };\n}\n/*  */\n\n\nfunction bind$1(el, dir) {\n  el.wrapData = function (code) {\n    return \"_b(\" + code + \",'\" + el.tag + \"',\" + dir.value + \",\" + (dir.modifiers && dir.modifiers.prop ? 'true' : 'false') + (dir.modifiers && dir.modifiers.sync ? ',true' : '') + \")\";\n  };\n}\n/*  */\n\n\nvar baseDirectives = {\n  on: on,\n  bind: bind$1,\n  cloak: noop\n};\n/*  */\n\nvar CodegenState = function CodegenState(options) {\n  this.options = options;\n  this.warn = options.warn || baseWarn;\n  this.transforms = pluckModuleFunction(options.modules, 'transformCode');\n  this.dataGenFns = pluckModuleFunction(options.modules, 'genData');\n  this.directives = extend(extend({}, baseDirectives), options.directives);\n  var isReservedTag = options.isReservedTag || no;\n\n  this.maybeComponent = function (el) {\n    return !!el.component || !isReservedTag(el.tag);\n  };\n\n  this.onceId = 0;\n  this.staticRenderFns = [];\n  this.pre = false;\n};\n\nfunction generate(ast, options) {\n  var state = new CodegenState(options); // fix #11483, Root level <script> tags should not be rendered.\n\n  var code = ast ? ast.tag === 'script' ? 'null' : genElement(ast, state) : '_c(\"div\")';\n  return {\n    render: \"with(this){return \" + code + \"}\",\n    staticRenderFns: state.staticRenderFns\n  };\n}\n\nfunction genElement(el, state) {\n  if (el.parent) {\n    el.pre = el.pre || el.parent.pre;\n  }\n\n  if (el.staticRoot && !el.staticProcessed) {\n    return genStatic(el, state);\n  } else if (el.once && !el.onceProcessed) {\n    return genOnce(el, state);\n  } else if (el.for && !el.forProcessed) {\n    return genFor(el, state);\n  } else if (el.if && !el.ifProcessed) {\n    return genIf(el, state);\n  } else if (el.tag === 'template' && !el.slotTarget && !state.pre) {\n    return genChildren(el, state) || 'void 0';\n  } else if (el.tag === 'slot') {\n    return genSlot(el, state);\n  } else {\n    // component or element\n    var code;\n\n    if (el.component) {\n      code = genComponent(el.component, el, state);\n    } else {\n      var data;\n\n      if (!el.plain || el.pre && state.maybeComponent(el)) {\n        data = genData$2(el, state);\n      }\n\n      var children = el.inlineTemplate ? null : genChildren(el, state, true);\n      code = \"_c('\" + el.tag + \"'\" + (data ? \",\" + data : '') + (children ? \",\" + children : '') + \")\";\n    } // module transforms\n\n\n    for (var i = 0; i < state.transforms.length; i++) {\n      code = state.transforms[i](el, code);\n    }\n\n    return code;\n  }\n} // hoist static sub-trees out\n\n\nfunction genStatic(el, state) {\n  el.staticProcessed = true; // Some elements (templates) need to behave differently inside of a v-pre\n  // node.  All pre nodes are static roots, so we can use this as a location to\n  // wrap a state change and reset it upon exiting the pre node.\n\n  var originalPreState = state.pre;\n\n  if (el.pre) {\n    state.pre = el.pre;\n  }\n\n  state.staticRenderFns.push(\"with(this){return \" + genElement(el, state) + \"}\");\n  state.pre = originalPreState;\n  return \"_m(\" + (state.staticRenderFns.length - 1) + (el.staticInFor ? ',true' : '') + \")\";\n} // v-once\n\n\nfunction genOnce(el, state) {\n  el.onceProcessed = true;\n\n  if (el.if && !el.ifProcessed) {\n    return genIf(el, state);\n  } else if (el.staticInFor) {\n    var key = '';\n    var parent = el.parent;\n\n    while (parent) {\n      if (parent.for) {\n        key = parent.key;\n        break;\n      }\n\n      parent = parent.parent;\n    }\n\n    if (!key) {\n       true && state.warn(\"v-once can only be used inside v-for that is keyed. \", el.rawAttrsMap['v-once']);\n      return genElement(el, state);\n    }\n\n    return \"_o(\" + genElement(el, state) + \",\" + state.onceId++ + \",\" + key + \")\";\n  } else {\n    return genStatic(el, state);\n  }\n}\n\nfunction genIf(el, state, altGen, altEmpty) {\n  el.ifProcessed = true; // avoid recursion\n\n  return genIfConditions(el.ifConditions.slice(), state, altGen, altEmpty);\n}\n\nfunction genIfConditions(conditions, state, altGen, altEmpty) {\n  if (!conditions.length) {\n    return altEmpty || '_e()';\n  }\n\n  var condition = conditions.shift();\n\n  if (condition.exp) {\n    return \"(\" + condition.exp + \")?\" + genTernaryExp(condition.block) + \":\" + genIfConditions(conditions, state, altGen, altEmpty);\n  } else {\n    return \"\" + genTernaryExp(condition.block);\n  } // v-if with v-once should generate code like (a)?_m(0):_m(1)\n\n\n  function genTernaryExp(el) {\n    return altGen ? altGen(el, state) : el.once ? genOnce(el, state) : genElement(el, state);\n  }\n}\n\nfunction genFor(el, state, altGen, altHelper) {\n  var exp = el.for;\n  var alias = el.alias;\n  var iterator1 = el.iterator1 ? \",\" + el.iterator1 : '';\n  var iterator2 = el.iterator2 ? \",\" + el.iterator2 : '';\n\n  if ( true && state.maybeComponent(el) && el.tag !== 'slot' && el.tag !== 'template' && !el.key) {\n    state.warn(\"<\" + el.tag + \" v-for=\\\"\" + alias + \" in \" + exp + \"\\\">: component lists rendered with \" + \"v-for should have explicit keys. \" + \"See https://vuejs.org/guide/list.html#key for more info.\", el.rawAttrsMap['v-for'], true\n    /* tip */\n    );\n  }\n\n  el.forProcessed = true; // avoid recursion\n\n  return (altHelper || '_l') + \"((\" + exp + \"),\" + \"function(\" + alias + iterator1 + iterator2 + \"){\" + \"return \" + (altGen || genElement)(el, state) + '})';\n}\n\nfunction genData$2(el, state) {\n  var data = '{'; // directives first.\n  // directives may mutate the el's other properties before they are generated.\n\n  var dirs = genDirectives(el, state);\n\n  if (dirs) {\n    data += dirs + ',';\n  } // key\n\n\n  if (el.key) {\n    data += \"key:\" + el.key + \",\";\n  } // ref\n\n\n  if (el.ref) {\n    data += \"ref:\" + el.ref + \",\";\n  }\n\n  if (el.refInFor) {\n    data += \"refInFor:true,\";\n  } // pre\n\n\n  if (el.pre) {\n    data += \"pre:true,\";\n  } // record original tag name for components using \"is\" attribute\n\n\n  if (el.component) {\n    data += \"tag:\\\"\" + el.tag + \"\\\",\";\n  } // module data generation functions\n\n\n  for (var i = 0; i < state.dataGenFns.length; i++) {\n    data += state.dataGenFns[i](el);\n  } // attributes\n\n\n  if (el.attrs) {\n    data += \"attrs:\" + genProps(el.attrs) + \",\";\n  } // DOM props\n\n\n  if (el.props) {\n    data += \"domProps:\" + genProps(el.props) + \",\";\n  } // event handlers\n\n\n  if (el.events) {\n    data += genHandlers(el.events, false) + \",\";\n  }\n\n  if (el.nativeEvents) {\n    data += genHandlers(el.nativeEvents, true) + \",\";\n  } // slot target\n  // only for non-scoped slots\n\n\n  if (el.slotTarget && !el.slotScope) {\n    data += \"slot:\" + el.slotTarget + \",\";\n  } // scoped slots\n\n\n  if (el.scopedSlots) {\n    data += genScopedSlots(el, el.scopedSlots, state) + \",\";\n  } // component v-model\n\n\n  if (el.model) {\n    data += \"model:{value:\" + el.model.value + \",callback:\" + el.model.callback + \",expression:\" + el.model.expression + \"},\";\n  } // inline-template\n\n\n  if (el.inlineTemplate) {\n    var inlineTemplate = genInlineTemplate(el, state);\n\n    if (inlineTemplate) {\n      data += inlineTemplate + \",\";\n    }\n  }\n\n  data = data.replace(/,$/, '') + '}'; // v-bind dynamic argument wrap\n  // v-bind with dynamic arguments must be applied using the same v-bind object\n  // merge helper so that class/style/mustUseProp attrs are handled correctly.\n\n  if (el.dynamicAttrs) {\n    data = \"_b(\" + data + \",\\\"\" + el.tag + \"\\\",\" + genProps(el.dynamicAttrs) + \")\";\n  } // v-bind data wrap\n\n\n  if (el.wrapData) {\n    data = el.wrapData(data);\n  } // v-on data wrap\n\n\n  if (el.wrapListeners) {\n    data = el.wrapListeners(data);\n  }\n\n  return data;\n}\n\nfunction genDirectives(el, state) {\n  var dirs = el.directives;\n\n  if (!dirs) {\n    return;\n  }\n\n  var res = 'directives:[';\n  var hasRuntime = false;\n  var i, l, dir, needRuntime;\n\n  for (i = 0, l = dirs.length; i < l; i++) {\n    dir = dirs[i];\n    needRuntime = true;\n    var gen = state.directives[dir.name];\n\n    if (gen) {\n      // compile-time directive that manipulates AST.\n      // returns true if it also needs a runtime counterpart.\n      needRuntime = !!gen(el, dir, state.warn);\n    }\n\n    if (needRuntime) {\n      hasRuntime = true;\n      res += \"{name:\\\"\" + dir.name + \"\\\",rawName:\\\"\" + dir.rawName + \"\\\"\" + (dir.value ? \",value:(\" + dir.value + \"),expression:\" + JSON.stringify(dir.value) : '') + (dir.arg ? \",arg:\" + (dir.isDynamicArg ? dir.arg : \"\\\"\" + dir.arg + \"\\\"\") : '') + (dir.modifiers ? \",modifiers:\" + JSON.stringify(dir.modifiers) : '') + \"},\";\n    }\n  }\n\n  if (hasRuntime) {\n    return res.slice(0, -1) + ']';\n  }\n}\n\nfunction genInlineTemplate(el, state) {\n  var ast = el.children[0];\n\n  if ( true && (el.children.length !== 1 || ast.type !== 1)) {\n    state.warn('Inline-template components must have exactly one child element.', {\n      start: el.start\n    });\n  }\n\n  if (ast && ast.type === 1) {\n    var inlineRenderFns = generate(ast, state.options);\n    return \"inlineTemplate:{render:function(){\" + inlineRenderFns.render + \"},staticRenderFns:[\" + inlineRenderFns.staticRenderFns.map(function (code) {\n      return \"function(){\" + code + \"}\";\n    }).join(',') + \"]}\";\n  }\n}\n\nfunction genScopedSlots(el, slots, state) {\n  // by default scoped slots are considered \"stable\", this allows child\n  // components with only scoped slots to skip forced updates from parent.\n  // but in some cases we have to bail-out of this optimization\n  // for example if the slot contains dynamic names, has v-if or v-for on them...\n  var needsForceUpdate = el.for || Object.keys(slots).some(function (key) {\n    var slot = slots[key];\n    return slot.slotTargetDynamic || slot.if || slot.for || containsSlotChild(slot) // is passing down slot from parent which may be dynamic\n    ;\n  }); // #9534: if a component with scoped slots is inside a conditional branch,\n  // it's possible for the same component to be reused but with different\n  // compiled slot content. To avoid that, we generate a unique key based on\n  // the generated code of all the slot contents.\n\n  var needsKey = !!el.if; // OR when it is inside another scoped slot or v-for (the reactivity may be\n  // disconnected due to the intermediate scope variable)\n  // #9438, #9506\n  // TODO: this can be further optimized by properly analyzing in-scope bindings\n  // and skip force updating ones that do not actually use scope variables.\n\n  if (!needsForceUpdate) {\n    var parent = el.parent;\n\n    while (parent) {\n      if (parent.slotScope && parent.slotScope !== emptySlotScopeToken || parent.for) {\n        needsForceUpdate = true;\n        break;\n      }\n\n      if (parent.if) {\n        needsKey = true;\n      }\n\n      parent = parent.parent;\n    }\n  }\n\n  var generatedSlots = Object.keys(slots).map(function (key) {\n    return genScopedSlot(slots[key], state);\n  }).join(',');\n  return \"scopedSlots:_u([\" + generatedSlots + \"]\" + (needsForceUpdate ? \",null,true\" : \"\") + (!needsForceUpdate && needsKey ? \",null,false,\" + hash(generatedSlots) : \"\") + \")\";\n}\n\nfunction hash(str) {\n  var hash = 5381;\n  var i = str.length;\n\n  while (i) {\n    hash = hash * 33 ^ str.charCodeAt(--i);\n  }\n\n  return hash >>> 0;\n}\n\nfunction containsSlotChild(el) {\n  if (el.type === 1) {\n    if (el.tag === 'slot') {\n      return true;\n    }\n\n    return el.children.some(containsSlotChild);\n  }\n\n  return false;\n}\n\nfunction genScopedSlot(el, state) {\n  var isLegacySyntax = el.attrsMap['slot-scope'];\n\n  if (el.if && !el.ifProcessed && !isLegacySyntax) {\n    return genIf(el, state, genScopedSlot, \"null\");\n  }\n\n  if (el.for && !el.forProcessed) {\n    return genFor(el, state, genScopedSlot);\n  }\n\n  var slotScope = el.slotScope === emptySlotScopeToken ? \"\" : String(el.slotScope);\n  var fn = \"function(\" + slotScope + \"){\" + \"return \" + (el.tag === 'template' ? el.if && isLegacySyntax ? \"(\" + el.if + \")?\" + (genChildren(el, state) || 'undefined') + \":undefined\" : genChildren(el, state) || 'undefined' : genElement(el, state)) + \"}\"; // reverse proxy v-slot without scope on this.$slots\n\n  var reverseProxy = slotScope ? \"\" : \",proxy:true\";\n  return \"{key:\" + (el.slotTarget || \"\\\"default\\\"\") + \",fn:\" + fn + reverseProxy + \"}\";\n}\n\nfunction genChildren(el, state, checkSkip, altGenElement, altGenNode) {\n  var children = el.children;\n\n  if (children.length) {\n    var el$1 = children[0]; // optimize single v-for\n\n    if (children.length === 1 && el$1.for && el$1.tag !== 'template' && el$1.tag !== 'slot') {\n      var normalizationType = checkSkip ? state.maybeComponent(el$1) ? \",1\" : \",0\" : \"\";\n      return \"\" + (altGenElement || genElement)(el$1, state) + normalizationType;\n    }\n\n    var normalizationType$1 = checkSkip ? getNormalizationType(children, state.maybeComponent) : 0;\n    var gen = altGenNode || genNode;\n    return \"[\" + children.map(function (c) {\n      return gen(c, state);\n    }).join(',') + \"]\" + (normalizationType$1 ? \",\" + normalizationType$1 : '');\n  }\n} // determine the normalization needed for the children array.\n// 0: no normalization needed\n// 1: simple normalization needed (possible 1-level deep nested array)\n// 2: full normalization needed\n\n\nfunction getNormalizationType(children, maybeComponent) {\n  var res = 0;\n\n  for (var i = 0; i < children.length; i++) {\n    var el = children[i];\n\n    if (el.type !== 1) {\n      continue;\n    }\n\n    if (needsNormalization(el) || el.ifConditions && el.ifConditions.some(function (c) {\n      return needsNormalization(c.block);\n    })) {\n      res = 2;\n      break;\n    }\n\n    if (maybeComponent(el) || el.ifConditions && el.ifConditions.some(function (c) {\n      return maybeComponent(c.block);\n    })) {\n      res = 1;\n    }\n  }\n\n  return res;\n}\n\nfunction needsNormalization(el) {\n  return el.for !== undefined || el.tag === 'template' || el.tag === 'slot';\n}\n\nfunction genNode(node, state) {\n  if (node.type === 1) {\n    return genElement(node, state);\n  } else if (node.type === 3 && node.isComment) {\n    return genComment(node);\n  } else {\n    return genText(node);\n  }\n}\n\nfunction genText(text) {\n  return \"_v(\" + (text.type === 2 ? text.expression // no need for () because already wrapped in _s()\n  : transformSpecialNewlines(JSON.stringify(text.text))) + \")\";\n}\n\nfunction genComment(comment) {\n  return \"_e(\" + JSON.stringify(comment.text) + \")\";\n}\n\nfunction genSlot(el, state) {\n  var slotName = el.slotName || '\"default\"';\n  var children = genChildren(el, state);\n  var res = \"_t(\" + slotName + (children ? \",function(){return \" + children + \"}\" : '');\n  var attrs = el.attrs || el.dynamicAttrs ? genProps((el.attrs || []).concat(el.dynamicAttrs || []).map(function (attr) {\n    return {\n      // slot props are camelized\n      name: camelize(attr.name),\n      value: attr.value,\n      dynamic: attr.dynamic\n    };\n  })) : null;\n  var bind$$1 = el.attrsMap['v-bind'];\n\n  if ((attrs || bind$$1) && !children) {\n    res += \",null\";\n  }\n\n  if (attrs) {\n    res += \",\" + attrs;\n  }\n\n  if (bind$$1) {\n    res += (attrs ? '' : ',null') + \",\" + bind$$1;\n  }\n\n  return res + ')';\n} // componentName is el.component, take it as argument to shun flow's pessimistic refinement\n\n\nfunction genComponent(componentName, el, state) {\n  var children = el.inlineTemplate ? null : genChildren(el, state, true);\n  return \"_c(\" + componentName + \",\" + genData$2(el, state) + (children ? \",\" + children : '') + \")\";\n}\n\nfunction genProps(props) {\n  var staticProps = \"\";\n  var dynamicProps = \"\";\n\n  for (var i = 0; i < props.length; i++) {\n    var prop = props[i];\n    var value = transformSpecialNewlines(prop.value);\n\n    if (prop.dynamic) {\n      dynamicProps += prop.name + \",\" + value + \",\";\n    } else {\n      staticProps += \"\\\"\" + prop.name + \"\\\":\" + value + \",\";\n    }\n  }\n\n  staticProps = \"{\" + staticProps.slice(0, -1) + \"}\";\n\n  if (dynamicProps) {\n    return \"_d(\" + staticProps + \",[\" + dynamicProps.slice(0, -1) + \"])\";\n  } else {\n    return staticProps;\n  }\n} // #3895, #4268\n\n\nfunction transformSpecialNewlines(text) {\n  return text.replace(/\\u2028/g, '\\\\u2028').replace(/\\u2029/g, '\\\\u2029');\n}\n/*  */\n// these keywords should not appear inside expressions, but operators like\n// typeof, instanceof and in are allowed\n\n\nvar prohibitedKeywordRE = new RegExp('\\\\b' + ('do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,' + 'super,throw,while,yield,delete,export,import,return,switch,default,' + 'extends,finally,continue,debugger,function,arguments').split(',').join('\\\\b|\\\\b') + '\\\\b'); // these unary operators should not be used as property/method names\n\nvar unaryOperatorsRE = new RegExp('\\\\b' + 'delete,typeof,void'.split(',').join('\\\\s*\\\\([^\\\\)]*\\\\)|\\\\b') + '\\\\s*\\\\([^\\\\)]*\\\\)'); // strip strings in expressions\n\nvar stripStringRE = /'(?:[^'\\\\]|\\\\.)*'|\"(?:[^\"\\\\]|\\\\.)*\"|`(?:[^`\\\\]|\\\\.)*\\$\\{|\\}(?:[^`\\\\]|\\\\.)*`|`(?:[^`\\\\]|\\\\.)*`/g; // detect problematic expressions in a template\n\nfunction detectErrors(ast, warn) {\n  if (ast) {\n    checkNode(ast, warn);\n  }\n}\n\nfunction checkNode(node, warn) {\n  if (node.type === 1) {\n    for (var name in node.attrsMap) {\n      if (dirRE.test(name)) {\n        var value = node.attrsMap[name];\n\n        if (value) {\n          var range = node.rawAttrsMap[name];\n\n          if (name === 'v-for') {\n            checkFor(node, \"v-for=\\\"\" + value + \"\\\"\", warn, range);\n          } else if (name === 'v-slot' || name[0] === '#') {\n            checkFunctionParameterExpression(value, name + \"=\\\"\" + value + \"\\\"\", warn, range);\n          } else if (onRE.test(name)) {\n            checkEvent(value, name + \"=\\\"\" + value + \"\\\"\", warn, range);\n          } else {\n            checkExpression(value, name + \"=\\\"\" + value + \"\\\"\", warn, range);\n          }\n        }\n      }\n    }\n\n    if (node.children) {\n      for (var i = 0; i < node.children.length; i++) {\n        checkNode(node.children[i], warn);\n      }\n    }\n  } else if (node.type === 2) {\n    checkExpression(node.expression, node.text, warn, node);\n  }\n}\n\nfunction checkEvent(exp, text, warn, range) {\n  var stripped = exp.replace(stripStringRE, '');\n  var keywordMatch = stripped.match(unaryOperatorsRE);\n\n  if (keywordMatch && stripped.charAt(keywordMatch.index - 1) !== '$') {\n    warn(\"avoid using JavaScript unary operator as property name: \" + \"\\\"\" + keywordMatch[0] + \"\\\" in expression \" + text.trim(), range);\n  }\n\n  checkExpression(exp, text, warn, range);\n}\n\nfunction checkFor(node, text, warn, range) {\n  checkExpression(node.for || '', text, warn, range);\n  checkIdentifier(node.alias, 'v-for alias', text, warn, range);\n  checkIdentifier(node.iterator1, 'v-for iterator', text, warn, range);\n  checkIdentifier(node.iterator2, 'v-for iterator', text, warn, range);\n}\n\nfunction checkIdentifier(ident, type, text, warn, range) {\n  if (typeof ident === 'string') {\n    try {\n      new Function(\"var \" + ident + \"=_\");\n    } catch (e) {\n      warn(\"invalid \" + type + \" \\\"\" + ident + \"\\\" in expression: \" + text.trim(), range);\n    }\n  }\n}\n\nfunction checkExpression(exp, text, warn, range) {\n  try {\n    new Function(\"return \" + exp);\n  } catch (e) {\n    var keywordMatch = exp.replace(stripStringRE, '').match(prohibitedKeywordRE);\n\n    if (keywordMatch) {\n      warn(\"avoid using JavaScript keyword as property name: \" + \"\\\"\" + keywordMatch[0] + \"\\\"\\n  Raw expression: \" + text.trim(), range);\n    } else {\n      warn(\"invalid expression: \" + e.message + \" in\\n\\n\" + \"    \" + exp + \"\\n\\n\" + \"  Raw expression: \" + text.trim() + \"\\n\", range);\n    }\n  }\n}\n\nfunction checkFunctionParameterExpression(exp, text, warn, range) {\n  try {\n    new Function(exp, '');\n  } catch (e) {\n    warn(\"invalid function parameter expression: \" + e.message + \" in\\n\\n\" + \"    \" + exp + \"\\n\\n\" + \"  Raw expression: \" + text.trim() + \"\\n\", range);\n  }\n}\n/*  */\n\n\nvar range = 2;\n\nfunction generateCodeFrame(source, start, end) {\n  if (start === void 0) start = 0;\n  if (end === void 0) end = source.length;\n  var lines = source.split(/\\r?\\n/);\n  var count = 0;\n  var res = [];\n\n  for (var i = 0; i < lines.length; i++) {\n    count += lines[i].length + 1;\n\n    if (count >= start) {\n      for (var j = i - range; j <= i + range || end > count; j++) {\n        if (j < 0 || j >= lines.length) {\n          continue;\n        }\n\n        res.push(\"\" + (j + 1) + repeat$1(\" \", 3 - String(j + 1).length) + \"|  \" + lines[j]);\n        var lineLength = lines[j].length;\n\n        if (j === i) {\n          // push underline\n          var pad = start - (count - lineLength) + 1;\n          var length = end > count ? lineLength - pad : end - start;\n          res.push(\"   |  \" + repeat$1(\" \", pad) + repeat$1(\"^\", length));\n        } else if (j > i) {\n          if (end > count) {\n            var length$1 = Math.min(end - count, lineLength);\n            res.push(\"   |  \" + repeat$1(\"^\", length$1));\n          }\n\n          count += lineLength + 1;\n        }\n      }\n\n      break;\n    }\n  }\n\n  return res.join('\\n');\n}\n\nfunction repeat$1(str, n) {\n  var result = '';\n\n  if (n > 0) {\n    while (true) {\n      // eslint-disable-line\n      if (n & 1) {\n        result += str;\n      }\n\n      n >>>= 1;\n\n      if (n <= 0) {\n        break;\n      }\n\n      str += str;\n    }\n  }\n\n  return result;\n}\n/*  */\n\n\nfunction createFunction(code, errors) {\n  try {\n    return new Function(code);\n  } catch (err) {\n    errors.push({\n      err: err,\n      code: code\n    });\n    return noop;\n  }\n}\n\nfunction createCompileToFunctionFn(compile) {\n  var cache = Object.create(null);\n  return function compileToFunctions(template, options, vm) {\n    options = extend({}, options);\n    var warn$$1 = options.warn || warn;\n    delete options.warn;\n    /* istanbul ignore if */\n\n    if (true) {\n      // detect possible CSP restriction\n      try {\n        new Function('return 1');\n      } catch (e) {\n        if (e.toString().match(/unsafe-eval|CSP/)) {\n          warn$$1('It seems you are using the standalone build of Vue.js in an ' + 'environment with Content Security Policy that prohibits unsafe-eval. ' + 'The template compiler cannot work in this environment. Consider ' + 'relaxing the policy to allow unsafe-eval or pre-compiling your ' + 'templates into render functions.');\n        }\n      }\n    } // check cache\n\n\n    var key = options.delimiters ? String(options.delimiters) + template : template;\n\n    if (cache[key]) {\n      return cache[key];\n    } // compile\n\n\n    var compiled = compile(template, options); // check compilation errors/tips\n\n    if (true) {\n      if (compiled.errors && compiled.errors.length) {\n        if (options.outputSourceRange) {\n          compiled.errors.forEach(function (e) {\n            warn$$1(\"Error compiling template:\\n\\n\" + e.msg + \"\\n\\n\" + generateCodeFrame(template, e.start, e.end), vm);\n          });\n        } else {\n          warn$$1(\"Error compiling template:\\n\\n\" + template + \"\\n\\n\" + compiled.errors.map(function (e) {\n            return \"- \" + e;\n          }).join('\\n') + '\\n', vm);\n        }\n      }\n\n      if (compiled.tips && compiled.tips.length) {\n        if (options.outputSourceRange) {\n          compiled.tips.forEach(function (e) {\n            return tip(e.msg, vm);\n          });\n        } else {\n          compiled.tips.forEach(function (msg) {\n            return tip(msg, vm);\n          });\n        }\n      }\n    } // turn code into functions\n\n\n    var res = {};\n    var fnGenErrors = [];\n    res.render = createFunction(compiled.render, fnGenErrors);\n    res.staticRenderFns = compiled.staticRenderFns.map(function (code) {\n      return createFunction(code, fnGenErrors);\n    }); // check function generation errors.\n    // this should only happen if there is a bug in the compiler itself.\n    // mostly for codegen development use\n\n    /* istanbul ignore if */\n\n    if (true) {\n      if ((!compiled.errors || !compiled.errors.length) && fnGenErrors.length) {\n        warn$$1(\"Failed to generate render function:\\n\\n\" + fnGenErrors.map(function (ref) {\n          var err = ref.err;\n          var code = ref.code;\n          return err.toString() + \" in\\n\\n\" + code + \"\\n\";\n        }).join('\\n'), vm);\n      }\n    }\n\n    return cache[key] = res;\n  };\n}\n/*  */\n\n\nfunction createCompilerCreator(baseCompile) {\n  return function createCompiler(baseOptions) {\n    function compile(template, options) {\n      var finalOptions = Object.create(baseOptions);\n      var errors = [];\n      var tips = [];\n\n      var warn = function (msg, range, tip) {\n        (tip ? tips : errors).push(msg);\n      };\n\n      if (options) {\n        if ( true && options.outputSourceRange) {\n          // $flow-disable-line\n          var leadingSpaceLength = template.match(/^\\s*/)[0].length;\n\n          warn = function (msg, range, tip) {\n            var data = {\n              msg: msg\n            };\n\n            if (range) {\n              if (range.start != null) {\n                data.start = range.start + leadingSpaceLength;\n              }\n\n              if (range.end != null) {\n                data.end = range.end + leadingSpaceLength;\n              }\n            }\n\n            (tip ? tips : errors).push(data);\n          };\n        } // merge custom modules\n\n\n        if (options.modules) {\n          finalOptions.modules = (baseOptions.modules || []).concat(options.modules);\n        } // merge custom directives\n\n\n        if (options.directives) {\n          finalOptions.directives = extend(Object.create(baseOptions.directives || null), options.directives);\n        } // copy other options\n\n\n        for (var key in options) {\n          if (key !== 'modules' && key !== 'directives') {\n            finalOptions[key] = options[key];\n          }\n        }\n      }\n\n      finalOptions.warn = warn;\n      var compiled = baseCompile(template.trim(), finalOptions);\n\n      if (true) {\n        detectErrors(compiled.ast, warn);\n      }\n\n      compiled.errors = errors;\n      compiled.tips = tips;\n      return compiled;\n    }\n\n    return {\n      compile: compile,\n      compileToFunctions: createCompileToFunctionFn(compile)\n    };\n  };\n}\n/*  */\n// `createCompilerCreator` allows creating compilers that use alternative\n// parser/optimizer/codegen, e.g the SSR optimizing compiler.\n// Here we just export a default compiler using the default parts.\n\n\nvar createCompiler = createCompilerCreator(function baseCompile(template, options) {\n  var ast = parse(template.trim(), options);\n\n  if (options.optimize !== false) {\n    optimize(ast, options);\n  }\n\n  var code = generate(ast, options);\n  return {\n    ast: ast,\n    render: code.render,\n    staticRenderFns: code.staticRenderFns\n  };\n});\n/*  */\n\nvar ref$1 = createCompiler(baseOptions);\nvar compile = ref$1.compile;\nvar compileToFunctions = ref$1.compileToFunctions;\n/*  */\n// check whether current browser encodes a char inside attribute values\n\nvar div;\n\nfunction getShouldDecode(href) {\n  div = div || document.createElement('div');\n  div.innerHTML = href ? \"<a href=\\\"\\n\\\"/>\" : \"<div a=\\\"\\n\\\"/>\";\n  return div.innerHTML.indexOf('&#10;') > 0;\n} // #3663: IE encodes newlines inside attribute values while other browsers don't\n\n\nvar shouldDecodeNewlines = inBrowser ? getShouldDecode(false) : false; // #6828: chrome encodes content in a[href]\n\nvar shouldDecodeNewlinesForHref = inBrowser ? getShouldDecode(true) : false;\n/*  */\n\nvar idToTemplate = cached(function (id) {\n  var el = query(id);\n  return el && el.innerHTML;\n});\nvar mount = Vue.prototype.$mount;\n\nVue.prototype.$mount = function (el, hydrating) {\n  el = el && query(el);\n  /* istanbul ignore if */\n\n  if (el === document.body || el === document.documentElement) {\n     true && warn(\"Do not mount Vue to <html> or <body> - mount to normal elements instead.\");\n    return this;\n  }\n\n  var options = this.$options; // resolve template/el and convert to render function\n\n  if (!options.render) {\n    var template = options.template;\n\n    if (template) {\n      if (typeof template === 'string') {\n        if (template.charAt(0) === '#') {\n          template = idToTemplate(template);\n          /* istanbul ignore if */\n\n          if ( true && !template) {\n            warn(\"Template element not found or is empty: \" + options.template, this);\n          }\n        }\n      } else if (template.nodeType) {\n        template = template.innerHTML;\n      } else {\n        if (true) {\n          warn('invalid template option:' + template, this);\n        }\n\n        return this;\n      }\n    } else if (el) {\n      template = getOuterHTML(el);\n    }\n\n    if (template) {\n      /* istanbul ignore if */\n      if ( true && config.performance && mark) {\n        mark('compile');\n      }\n\n      var ref = compileToFunctions(template, {\n        outputSourceRange: \"development\" !== 'production',\n        shouldDecodeNewlines: shouldDecodeNewlines,\n        shouldDecodeNewlinesForHref: shouldDecodeNewlinesForHref,\n        delimiters: options.delimiters,\n        comments: options.comments\n      }, this);\n      var render = ref.render;\n      var staticRenderFns = ref.staticRenderFns;\n      options.render = render;\n      options.staticRenderFns = staticRenderFns;\n      /* istanbul ignore if */\n\n      if ( true && config.performance && mark) {\n        mark('compile end');\n        measure(\"vue \" + this._name + \" compile\", 'compile', 'compile end');\n      }\n    }\n  }\n\n  return mount.call(this, el, hydrating);\n};\n/**\n * Get outerHTML of elements, taking care\n * of SVG elements in IE as well.\n */\n\n\nfunction getOuterHTML(el) {\n  if (el.outerHTML) {\n    return el.outerHTML;\n  } else {\n    var container = document.createElement('div');\n    container.appendChild(el.cloneNode(true));\n    return container.innerHTML;\n  }\n}\n\nVue.compile = compileToFunctions;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Vue);\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../webpack/buildin/global.js */ \"./node_modules/webpack/buildin/global.js\"), __webpack_require__(/*! ./../../timers-browserify/main.js */ \"./node_modules/timers-browserify/main.js\").setImmediate))\n\n//# sourceURL=webpack:///./node_modules/vue/dist/vue.esm.js?");

/***/ }),

/***/ "./node_modules/webpack/buildin/global.js":
/*!***********************************!*\
  !*** (webpack)/buildin/global.js ***!
  \***********************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("var g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;\n\n//# sourceURL=webpack:///(webpack)/buildin/global.js?");

/***/ }),

/***/ 15:
/*!****************************!*\
  !*** multi vue vue-router ***!
  \****************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("__webpack_require__(/*! vue */\"./node_modules/vue/dist/vue.esm.js\");\nmodule.exports = __webpack_require__(/*! vue-router */\"./node_modules/vue-router/dist/vue-router.esm.js\");\n\n\n//# sourceURL=webpack:///multi_vue_vue-router?");

/***/ })

/******/ });