<?php
namespace WeDevs\WSL_ERP\Admin;

// don't call the file directly
if ( ! defined('ABSPATH') ) {
    exit;
}

use WeDevs\ERP\Framework\Traits\Hooker;
use WeDevs\WSL_ERP\Admin\Menu\Extensions;

class Admin {

    use Hooker;

    private $update;

    private $message;

    public function __construct() {
        // get instance of update class
        $this->update = isset( wp_WSL_ERP()->update )
            ? wp_WSL_ERP()->update
            : \WeDevs\WSL_ERP\Admin\Update::init();

        $this->message = '';

        $this->action( 'admin_menu', 'admin_menu', 99 );
        $this->action( 'admin_menu', 'remove_addons_menu', 999 );
        $this->filter( 'pre_erp_hr_employee_args', 'add_new_employee', 10, 1 );
    }

    public function add_new_employee( $data ) {

        if ( ! $this->update->is_valid_license() ) {
            // if there is no valid license, don't bother user
            return $data;
        }

        // check existing user count
        if ( $this->update->count_users() < $this->update->get_licensed_user() ) {
            // user is in limit, so don't bother user
            return $data;
        }

        $message = __( 'Current WSL ERP user limit has been exceeded. Please upgrade the number of users in order to add new Employee.', 'wsl-erp' );

        return new \WP_Error( 'user-limit-exceeded', $message );
    }

    public function remove_addons_menu() {
        // remove addons menu
        remove_submenu_page('erp', 'erp-addons');

        // remove modules menu
        remove_submenu_page('erp', 'erp-modules');

    }

    public function admin_menu() {
        // Create main WSL ERP menu if it doesn't exist
        $this->create_main_erp_menu();

        $extension_slug = add_submenu_page( 'erp', __( 'Modules', 'wsl-erp' ), __( 'Modules', 'wsl-erp' ), 'manage_options', 'erp-extensions', [ $this, 'extension_menu' ] );
        add_action( 'load-' . $extension_slug , array( $this,'load_extensions' ) );

        if (
            wp_WSL_ERP()->module->is_active( 'hubspot' ) ||
            wp_WSL_ERP()->module->is_active( 'mailchimp' ) ||
            wp_WSL_ERP()->module->is_active( 'salesforce' ) ||
            wp_WSL_ERP()->module->is_active( 'help_scout' ) ||
            wp_WSL_ERP()->module->is_active( 'awesome_support' )
        ) {
            $this->add_crm_integration_menu();
        }
    }

    /**
     * Create main WSL ERP menu
     *
     * @since 0.0.1
     *
     * @return void
     */
    private function create_main_erp_menu() {
        // Check if main ERP menu already exists
        global $menu;
        $erp_menu_exists = false;

        if ( is_array( $menu ) ) {
            foreach ( $menu as $menu_item ) {
                if ( isset( $menu_item[2] ) && $menu_item[2] === 'erp' ) {
                    $erp_menu_exists = true;
                    break;
                }
            }
        }

        // If main ERP menu doesn't exist, create it
        if ( ! $erp_menu_exists ) {
            add_menu_page(
                __( 'WSL ERP', 'wsl-erp' ),
                __( 'WSL ERP', 'wsl-erp' ),
                'manage_options',
                'erp',
                [ $this, 'main_dashboard_page' ],
                'dashicons-businessman',
                30
            );

            // Add Dashboard submenu
            add_submenu_page(
                'erp',
                __( 'Dashboard', 'wsl-erp' ),
                __( 'Dashboard', 'wsl-erp' ),
                'manage_options',
                'erp',
                [ $this, 'main_dashboard_page' ]
            );
        }
    }

    /**
     * Add CRM Integration menu
     *
     * @since 0.0.1
     *
     * @return void
     */
    private function add_crm_integration_menu() {
        add_submenu_page(
            'erp',
            __( 'Integrations', 'wsl-erp' ),
            __( 'Integrations', 'wsl-erp' ),
            'manage_options',
            'erp-integrations',
            [ $this, 'integration_page' ]
        );
    }

    /**
     * Main dashboard page
     *
     * @since 0.0.1
     *
     * @return void
     */
    public function main_dashboard_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'WSL ERP Dashboard', 'wsl-erp' ); ?></h1>
            <div class="wsl-erp-dashboard">
                <div class="wsl-erp-welcome">
                    <h2><?php _e( 'Welcome to WSL ERP', 'wsl-erp' ); ?></h2>
                    <p><?php _e( 'Your complete business management solution with HR, CRM, and Accounting modules.', 'wsl-erp' ); ?></p>
                </div>

                <div class="wsl-erp-modules">
                    <h3><?php _e( 'Available Modules', 'wsl-erp' ); ?></h3>
                    <div class="module-grid">
                        <div class="module-card">
                            <h4><?php _e( 'Human Resources', 'wsl-erp' ); ?></h4>
                            <p><?php _e( 'Manage employees, payroll, attendance, and HR workflows.', 'wsl-erp' ); ?></p>
                        </div>
                        <div class="module-card">
                            <h4><?php _e( 'Customer Relations', 'wsl-erp' ); ?></h4>
                            <p><?php _e( 'Track customers, leads, deals, and sales activities.', 'wsl-erp' ); ?></p>
                        </div>
                        <div class="module-card">
                            <h4><?php _e( 'Accounting', 'wsl-erp' ); ?></h4>
                            <p><?php _e( 'Manage finances, invoices, expenses, and reports.', 'wsl-erp' ); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <style>
        .wsl-erp-dashboard { margin-top: 20px; }
        .wsl-erp-welcome { background: #fff; padding: 20px; border: 1px solid #ddd; margin-bottom: 20px; }
        .module-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .module-card { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .module-card h4 { margin-top: 0; color: #0073aa; }
        </style>
        <?php
    }

    public function integration_page() {
        $integrations = [ 'hubspot', 'mailchimp', 'salesforce', 'help_scout', 'awesome_support' ];

        foreach ( $integrations as $integration ) {
            if ( wp_WSL_ERP()->module->is_active( $integration ) ) {
                $default = $integration;
                break;
            }
        }

        $sub_section = isset( $_GET['sub-section'] ) ? sanitize_text_field( wp_unslash( $_GET['sub-section'] ) ) : $default;

        if ( wp_WSL_ERP()->module->is_active( $sub_section ) ) {
            $action = 'erp_crm_' . $sub_section . '_page';
            do_action( $action );
        }
    }

    public function extension_menu() {
        $obj = Extensions::init();
        $obj->entry();
    }

    public function load_extensions() {
        $obj = Extensions::init();
        $obj->on_load_page();
    }
}
