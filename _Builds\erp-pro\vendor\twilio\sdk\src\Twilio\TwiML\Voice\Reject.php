<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\TwiML\Voice;

use <PERSON><PERSON><PERSON>\TwiML\TwiML;

class Reject extends TwiML {
    /**
     * Reject constructor.
     *
     * @param array $attributes Optional attributes
     */
    public function __construct($attributes = array()) {
        parent::__construct('Reject', null, $attributes);
    }

    /**
     * Add Reason attribute.
     *
     * @param string $reason Rejection reason
     * @return static $this.
     */
    public function setReason($reason) {
        return $this->setAttribute('reason', $reason);
    }
}
